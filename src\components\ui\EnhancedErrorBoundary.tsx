import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Al<PERSON><PERSON>riangle, RefreshCw, Home, Bug, ChevronDown, ChevronUp } from 'lucide-react';

interface EnhancedErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
  onError?: (error: Error, errorInfo: ErrorInfo, errorId: string) => void;
  enableRetry?: boolean;
  maxRetries?: number;
  showErrorDetails?: boolean;
  level?: 'page' | 'section' | 'component';
  className?: string;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  showDetails: boolean;
  errorId: string;
}

export class EnhancedErrorBoundary extends Component<EnhancedErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;
  private maxRetries = 3;

  constructor(props: EnhancedErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      showDetails: false,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    return {
      hasError: true,
      error,
      errorId,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    const { onError, componentName } = this.props;
    const { errorId } = this.state;

    this.setState({
      error,
      errorInfo,
    });

    // Log error details
    console.error(`🚨 Error Boundary Caught Error in ${componentName || 'Unknown Component'}:`, {
      error,
      errorInfo,
      errorId,
      componentStack: errorInfo.componentStack,
      errorBoundary: this.constructor.name,
    });

    // Report error to monitoring service
    this.reportError(error, errorInfo, errorId);

    // Call custom error handler
    if (onError) {
      onError(error, errorInfo, errorId);
    }
  }

  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {
    // In a real application, you would send this to your error monitoring service
    const errorReport = {
      errorId: this.state.errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      level: this.props.level || 'component'
    };

    // Simulate sending to error service
    console.log('Error report:', errorReport);
  };

  private handleRetry = () => {
    const { maxRetries = 3 } = this.props;
    const { retryCount } = this.state;

    if (retryCount < maxRetries) {
      console.log(`🔄 Retrying component render (attempt ${retryCount + 1}/${maxRetries})`);

      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1,
        showDetails: false,
        errorId: '',
      }));

      // Add a small delay before retry to allow for any cleanup
      this.retryTimeoutId = setTimeout(() => {
        this.forceUpdate();
      }, 100);
    }
  };

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  private toggleDetails = () => {
    this.setState(prevState => ({
      showDetails: !prevState.showDetails,
    }));
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  private copyErrorDetails = () => {
    const errorDetails = {
      errorId: this.state.errorId,
      message: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      timestamp: new Date().toISOString()
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Error details copied to clipboard');
      })
      .catch(() => {
        console.log('Failed to copy error details');
      });
  };

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided
      if (this.props.fallback) {
        return this.props.fallback;
      }

      const { level = 'component' } = this.props;
      const canRetry = this.retryCount < this.maxRetries;

      // Different error UI based on level
      if (level === 'page') {
        return (
          <div className="min-h-screen bg-background flex items-center justify-center p-4">
            <div className="max-w-md w-full bg-surface border border-border rounded-lg p-8 text-center">
              <div className="mb-6">
                <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h1 className="text-2xl font-bold text-text mb-2">Something went wrong</h1>
                <p className="text-text-secondary">
                  We're sorry, but something unexpected happened. Please try refreshing the page.
                </p>
              </div>

              <div className="space-y-3">
                <button
                  onClick={this.handleReload}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Reload Page</span>
                </button>

                <button
                  onClick={this.handleGoHome}
                  className="w-full flex items-center justify-center space-x-2 px-4 py-2 border border-border text-text rounded-lg hover:bg-surface-hover transition-colors"
                >
                  <Home className="w-4 h-4" />
                  <span>Go Home</span>
                </button>
              </div>

              {this.props.showDetails && (
                <details className="mt-6 text-left">
                  <summary className="cursor-pointer text-sm text-text-secondary hover:text-text">
                    Error Details
                  </summary>
                  <div className="mt-2 p-3 bg-red-50 border border-red-200 rounded text-xs font-mono text-red-800 overflow-auto max-h-40">
                    <div className="mb-2">
                      <strong>Error ID:</strong> {this.state.errorId}
                    </div>
                    <div className="mb-2">
                      <strong>Message:</strong> {this.state.error?.message}
                    </div>
                    {this.state.error?.stack && (
                      <div>
                        <strong>Stack:</strong>
                        <pre className="mt-1 whitespace-pre-wrap">{this.state.error.stack}</pre>
                      </div>
                    )}
                  </div>
                  <button
                    onClick={this.copyErrorDetails}
                    className="mt-2 text-xs text-text-secondary hover:text-text"
                  >
                    Copy Error Details
                  </button>
                </details>
              )}
            </div>
          </div>
        );
      }

      if (level === 'section') {
        return (
          <div className="bg-surface border border-red-200 rounded-lg p-6">
            <div className="flex items-start space-x-4">
              <AlertTriangle className="w-6 h-6 text-red-500 flex-shrink-0 mt-1" />
              <div className="flex-1">
                <h3 className="text-lg font-semibold text-text mb-2">Section Error</h3>
                <p className="text-text-secondary mb-4">
                  This section encountered an error and couldn't load properly.
                </p>
                
                <div className="flex items-center space-x-3">
                  {canRetry && (
                    <button
                      onClick={this.handleRetry}
                      className="flex items-center space-x-2 px-3 py-1 bg-primary text-white rounded text-sm hover:bg-primary-hover transition-colors"
                    >
                      <RefreshCw className="w-3 h-3" />
                      <span>Retry ({this.maxRetries - this.retryCount} left)</span>
                    </button>
                  )}
                  
                  <button
                    onClick={this.handleReload}
                    className="flex items-center space-x-2 px-3 py-1 border border-border text-text rounded text-sm hover:bg-surface-hover transition-colors"
                  >
                    <RefreshCw className="w-3 h-3" />
                    <span>Reload Page</span>
                  </button>
                </div>

                {this.props.showDetails && (
                  <details className="mt-4">
                    <summary className="cursor-pointer text-sm text-text-secondary hover:text-text">
                      Technical Details
                    </summary>
                    <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-xs text-red-800">
                      <div><strong>Error:</strong> {this.state.error?.message}</div>
                      <div><strong>ID:</strong> {this.state.errorId}</div>
                    </div>
                  </details>
                )}
              </div>
            </div>
          </div>
        );
      }

      // Component level error (default)
      return (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-red-500 flex-shrink-0" />
            <div className="flex-1">
              <h4 className="text-sm font-medium text-red-800">Component Error</h4>
              <p className="text-xs text-red-600 mt-1">
                This component failed to render properly.
              </p>
            </div>
            {canRetry && (
              <button
                onClick={this.handleRetry}
                className="flex items-center space-x-1 px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors"
              >
                <RefreshCw className="w-3 h-3" />
                <span>Retry</span>
              </button>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easy wrapping
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <EnhancedErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </EnhancedErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for error reporting
export const useErrorHandler = () => {
  const reportError = (error: Error, context?: string) => {
    console.error(`Error in ${context || 'unknown context'}:`, error);
    
    // In a real app, send to error monitoring service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };
    
    console.log('Error report:', errorReport);
  };

  return { reportError };
};
