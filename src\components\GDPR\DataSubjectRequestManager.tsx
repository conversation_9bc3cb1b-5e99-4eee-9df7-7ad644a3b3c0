import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { DataSubjectRequest } from '../../types/compliance';
import { DataSubjectRequestService } from '../../services/dataSubjectRequestService';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button, IconButton } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import {
  Users,
  Search,
  Filter,
  Plus,
  Eye,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  Download,
  Mail,
  Calendar,
  User,
  ChevronRight,
  MoreHorizontal,
  Edit,
  Trash2,
  Save,
  X,
  RefreshCw,
  Phone,
  MessageSquare,
  Paperclip,
  Send
} from 'lucide-react';

interface DataSubjectRequestManagerProps {
  className?: string;
}

export const DataSubjectRequestManager: React.FC<DataSubjectRequestManagerProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();
  const [requests, setRequests] = useState<DataSubjectRequest[]>([]);
  const [filteredRequests, setFilteredRequests] = useState<DataSubjectRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: [] as string[],
    type: [] as string[],
    priority: [] as string[]
  });
  const [selectedRequest, setSelectedRequest] = useState<DataSubjectRequest | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [editingRequest, setEditingRequest] = useState<DataSubjectRequest | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    type: 'access' as const,
    priority: 'medium' as const,
    description: '',
    notes: ''
  });

  useEffect(() => {
    loadRequests();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [requests, searchTerm, selectedFilters]);

  const loadRequests = async () => {
    try {
      setLoading(true);
      const data = await DataSubjectRequestService.getAllRequests();
      setRequests(data);
    } catch (error) {
      console.error('Error loading requests:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Loading Requests',
        message: 'Failed to load data subject requests. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateRequest = async () => {
    try {
      const newRequest = await DataSubjectRequestService.createRequest(formData);
      setRequests(prev => [newRequest, ...prev]);
      setShowCreateModal(false);
      resetForm();
      notifications.addNotification({
        type: 'success',
        title: 'Request Created',
        message: 'Data subject request has been created successfully.'
      });
    } catch (error) {
      console.error('Error creating request:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Creating Request',
        message: 'Failed to create data subject request. Please try again.'
      });
    }
  };

  const handleUpdateRequest = async () => {
    if (!editingRequest) return;

    try {
      const updatedRequest = await DataSubjectRequestService.updateRequest(editingRequest.id, formData);
      setRequests(prev => prev.map(req => req.id === editingRequest.id ? updatedRequest : req));
      setShowEditModal(false);
      setEditingRequest(null);
      resetForm();
      notifications.addNotification({
        type: 'success',
        title: 'Request Updated',
        message: 'Data subject request has been updated successfully.'
      });
    } catch (error) {
      console.error('Error updating request:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Updating Request',
        message: 'Failed to update data subject request. Please try again.'
      });
    }
  };

  const handleDeleteRequest = async () => {
    if (!selectedRequest) return;

    try {
      await DataSubjectRequestService.deleteRequest(selectedRequest.id);
      setRequests(prev => prev.filter(req => req.id !== selectedRequest.id));
      setShowDeleteModal(false);
      setSelectedRequest(null);
      notifications.addNotification({
        type: 'success',
        title: 'Request Deleted',
        message: 'Data subject request has been deleted successfully.'
      });
    } catch (error) {
      console.error('Error deleting request:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Deleting Request',
        message: 'Failed to delete data subject request. Please try again.'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      email: '',
      type: 'access',
      priority: 'medium',
      description: '',
      notes: ''
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (request: DataSubjectRequest) => {
    setEditingRequest(request);
    setFormData({
      email: request.email,
      type: request.type,
      priority: request.priority,
      description: request.description,
      notes: request.notes || ''
    });
    setShowEditModal(true);
  };

  const openViewModal = (request: DataSubjectRequest) => {
    setSelectedRequest(request);
    setShowViewModal(true);
  };

  const openDeleteModal = (request: DataSubjectRequest) => {
    setSelectedRequest(request);
    setShowDeleteModal(true);
  };

  const applyFilters = () => {
    let filtered = [...requests];

    // Apply search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(req => 
        req.email.toLowerCase().includes(term) ||
        req.firstName.toLowerCase().includes(term) ||
        req.lastName.toLowerCase().includes(term) ||
        req.description.toLowerCase().includes(term)
      );
    }

    // Apply status filter
    if (selectedFilters.status.length > 0) {
      filtered = filtered.filter(req => selectedFilters.status.includes(req.status));
    }

    // Apply type filter
    if (selectedFilters.type.length > 0) {
      filtered = filtered.filter(req => selectedFilters.type.includes(req.type));
    }

    // Apply priority filter
    if (selectedFilters.priority.length > 0) {
      filtered = filtered.filter(req => selectedFilters.priority.includes(req.priority));
    }

    setFilteredRequests(filtered);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'in_progress': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'pending': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'rejected': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'cancelled': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'erasure': return <Users className="w-4 h-4" />;
      case 'portability': return <FileText className="w-4 h-4" />;
      case 'access': return <Eye className="w-4 h-4" />;
      case 'rectification': return <CheckCircle className="w-4 h-4" />;
      case 'objection': return <AlertTriangle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    if (diffInHours < 1) return 'Less than 1 hour ago';
    if (diffInHours < 24) return `${diffInHours} hour${diffInHours > 1 ? 's' : ''} ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  };

  const getDaysUntilDue = (date: Date) => {
    const now = new Date();
    const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffInDays;
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-text-secondary">Loading requests...</span>
      </div>
    );
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Users className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Data Subject Requests</h2>
            <p className="text-text-secondary">Manage and process GDPR data subject requests</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="secondary"
            onClick={loadRequests}
          >
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button
            variant="secondary"
          >
            <Download className="w-4 h-4 mr-2" />
            Export
          </Button>
          <Button
            variant="primary"
            onClick={openCreateModal}
          >
            <Plus className="w-4 h-4 mr-2" />
            New Request
          </Button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
          <input
            type="text"
            placeholder="Search requests by email, name, or description..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={`w-full pl-10 pr-4 py-2 bg-surface border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary`}
          />
        </div>
        <button
          onClick={() => setShowFilters(!showFilters)}
          className={`flex items-center gap-2 px-4 py-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors ${showFilters ? 'bg-card' : ''}`}
        >
          <Filter className="w-4 h-4" />
          Filters
        </button>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className={`p-4 bg-surface border border-border rounded-lg space-y-4`}>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Status Filter */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">Status</label>
              <div className="space-y-2">
                {['pending', 'in_progress', 'completed', 'rejected', 'cancelled'].map(status => (
                  <label key={status} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedFilters.status.includes(status)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedFilters(prev => ({
                            ...prev,
                            status: [...prev.status, status]
                          }));
                        } else {
                          setSelectedFilters(prev => ({
                            ...prev,
                            status: prev.status.filter(s => s !== status)
                          }));
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm text-text capitalize">{status.replace('_', ' ')}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Type Filter */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">Type</label>
              <div className="space-y-2">
                {['access', 'erasure', 'portability', 'rectification', 'objection'].map(type => (
                  <label key={type} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedFilters.type.includes(type)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedFilters(prev => ({
                            ...prev,
                            type: [...prev.type, type]
                          }));
                        } else {
                          setSelectedFilters(prev => ({
                            ...prev,
                            type: prev.type.filter(t => t !== type)
                          }));
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm text-text capitalize">{type}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Priority Filter */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">Priority</label>
              <div className="space-y-2">
                {['low', 'medium', 'high', 'urgent'].map(priority => (
                  <label key={priority} className="flex items-center">
                    <input
                      type="checkbox"
                      checked={selectedFilters.priority.includes(priority)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedFilters(prev => ({
                            ...prev,
                            priority: [...prev.priority, priority]
                          }));
                        } else {
                          setSelectedFilters(prev => ({
                            ...prev,
                            priority: prev.priority.filter(p => p !== priority)
                          }));
                        }
                      }}
                      className="mr-2"
                    />
                    <span className="text-sm text-text capitalize">{priority}</span>
                  </label>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Requests List */}
      <div className="space-y-4">
        {filteredRequests.length === 0 ? (
          <div className="text-center py-12">
            <Users className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">No requests found</h3>
            <p className="text-text-secondary">Try adjusting your search or filters</p>
          </div>
        ) : (
          filteredRequests.map((request) => (
            <div
              key={request.id}
              className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-primary/30 hover:shadow-lg'} rounded-xl transition-all duration-300 cursor-pointer`}
              onClick={() => setSelectedRequest(request)}
            >
              {mode === 'light' && (
                <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              )}
              
              <div className="relative z-10 flex items-center gap-4">
                <div className="relative">
                  <div className={`w-12 h-12 ${mode === 'dark' ? 'bg-text/10' : 'bg-gradient-to-br from-primary/25 to-primary/15'} rounded-xl flex items-center justify-center`}>
                    {getTypeIcon(request.type)}
                  </div>
                  {request.priority === 'urgent' && (
                    <div className={`absolute -top-1 -right-1 w-4 h-4 ${mode === 'dark' ? 'bg-text' : 'bg-red-500'} rounded-full flex items-center justify-center`}>
                      <span className={`text-xs font-bold ${mode === 'dark' ? 'text-background' : 'text-white'}`}>!</span>
                    </div>
                  )}
                </div>
                
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h4 className="text-lg font-bold text-text capitalize">{request.type.replace('_', ' ')} Request</h4>
                    <span className={`px-3 py-1 ${getStatusColor(request.status)} text-xs font-bold rounded-full uppercase`}>
                      {request.status.replace('_', ' ')}
                    </span>
                    <span className={`px-3 py-1 ${getPriorityColor(request.priority)} text-xs font-bold rounded-full uppercase`}>
                      {request.priority}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-4 text-sm text-text-secondary mb-3">
                    <div className="flex items-center gap-1">
                      <User className="w-4 h-4" />
                      <span>{request.firstName} {request.lastName}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Mail className="w-4 h-4" />
                      <span>{request.email}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      <span>Submitted {formatTimeAgo(request.submittedAt)}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text-secondary">Due in {getDaysUntilDue(request.dueDate)} days</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div className={`bg-primary h-2 rounded-full transition-all duration-500`} style={{width: `${request.progress}%`}}></div>
                      </div>
                      <span className={`text-sm font-semibold ${mode === 'dark' ? 'text-text' : 'text-primary'}`}>{request.progress}%</span>
                    </div>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <IconButton
                    icon={Eye}
                    onClick={(e) => {
                      e.stopPropagation();
                      openViewModal(request);
                    }}
                    variant="primary"
                    size="sm"
                    tooltip="View Details"
                  />
                  <IconButton
                    icon={Edit}
                    onClick={(e) => {
                      e.stopPropagation();
                      openEditModal(request);
                    }}
                    variant="secondary"
                    size="sm"
                    tooltip="Edit Request"
                  />
                  <IconButton
                    icon={Trash2}
                    onClick={(e) => {
                      e.stopPropagation();
                      openDeleteModal(request);
                    }}
                    variant="danger"
                    size="sm"
                    tooltip="Delete Request"
                  />
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Create/Edit Modal */}
      {(showCreateModal || showEditModal) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text">
                {editingRequest ? 'Edit Request' : 'Create New Request'}
              </h3>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setShowEditModal(false);
                  setEditingRequest(null);
                  resetForm();
                }}
                className="p-2 hover:bg-border rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-text-secondary" />
              </button>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">Email Address</label>
                <FormInput
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                  placeholder="Enter email address"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-text mb-2">Request Type</label>
                  <FormSelect
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                    options={[
                      { value: 'access', label: 'Data Access' },
                      { value: 'rectification', label: 'Data Rectification' },
                      { value: 'erasure', label: 'Data Erasure' },
                      { value: 'portability', label: 'Data Portability' },
                      { value: 'restriction', label: 'Processing Restriction' },
                      { value: 'objection', label: 'Processing Objection' }
                    ]}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Priority</label>
                  <FormSelect
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                    options={[
                      { value: 'low', label: 'Low' },
                      { value: 'medium', label: 'Medium' },
                      { value: 'high', label: 'High' },
                      { value: 'urgent', label: 'Urgent' }
                    ]}
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="Describe the request details..."
                  rows={4}
                  className={`w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                    mode === 'dark' ? 'bg-surface text-text' : 'bg-white text-gray-900'
                  }`}
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">Internal Notes</label>
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                  placeholder="Add internal notes..."
                  rows={3}
                  className={`w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent ${
                    mode === 'dark' ? 'bg-surface text-text' : 'bg-white text-gray-900'
                  }`}
                />
              </div>
            </div>

            <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-border">
              <Button
                variant="secondary"
                onClick={() => {
                  setShowCreateModal(false);
                  setShowEditModal(false);
                  setEditingRequest(null);
                  resetForm();
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={editingRequest ? handleUpdateRequest : handleCreateRequest}
                disabled={!formData.email || !formData.description}
              >
                <Save className="w-4 h-4 mr-2" />
                {editingRequest ? 'Update Request' : 'Create Request'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text">Request Details</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  onClick={() => openEditModal(selectedRequest)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setSelectedRequest(null);
                  }}
                  className="p-2 hover:bg-border rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-text-secondary" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Email</label>
                  <p className="text-text">{selectedRequest.email}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Type</label>
                  <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-primary/10 text-primary">
                    {selectedRequest.type.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Priority</label>
                  <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                    selectedRequest.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                    selectedRequest.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                    selectedRequest.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                  }`}>
                    {selectedRequest.priority.toUpperCase()}
                  </span>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Status</label>
                  <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${
                    selectedRequest.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                    selectedRequest.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                    selectedRequest.status === 'pending' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                    'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                  }`}>
                    {selectedRequest.status.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Submitted</label>
                  <p className="text-text">{selectedRequest.submittedAt?.toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Due Date</label>
                  <p className="text-text">{selectedRequest.dueDate?.toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Progress</label>
                  <div className="flex items-center gap-3">
                    <div className="flex-1 bg-border rounded-full h-2">
                      <div
                        className="bg-primary h-2 rounded-full transition-all duration-500"
                        style={{width: `${selectedRequest.progress}%`}}
                      />
                    </div>
                    <span className="text-sm text-text">{selectedRequest.progress}%</span>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">Description</label>
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-gray-50'} rounded-lg`}>
                  <p className="text-text">{selectedRequest.description}</p>
                </div>
              </div>

              {selectedRequest.notes && (
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-2">Internal Notes</label>
                  <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-gray-50'} rounded-lg`}>
                    <p className="text-text">{selectedRequest.notes}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedRequest && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-md w-full mx-4`}>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-text">Delete Request</h3>
                <p className="text-text-secondary">This action cannot be undone.</p>
              </div>
            </div>

            <p className="text-text mb-6">
              Are you sure you want to delete the request from <strong>{selectedRequest.email}</strong>?
            </p>

            <div className="flex items-center justify-end gap-3">
              <Button
                variant="secondary"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedRequest(null);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteRequest}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Request
              </Button>
            </div>
          </div>
        </div>
      )}

      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
    </div>
  );
};
