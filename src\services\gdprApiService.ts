// GDPR Analysis API Service
const API_BASE_URL = 'http://127.0.0.1:8000';

export interface ComplianceResult {
  gdpr_requirement: string;
  company_policy: string;
  compliance_status: string;
  notes_gaps: string;
}

export interface AnalysisResponse {
  success: boolean;
  analysis_id: string;
  policy_name: string;
  timestamp: string;
  total_requirements: number;
  compliance_score: number;
  compliance_level: string;
  status_breakdown: Record<string, number>;
  detailed_analysis: string;
  compliance_results: ComplianceResult[];
  files_generated: string[];
}

export interface HealthResponse {
  status: string;
  message: string;
  timestamp: string;
}

export class GdprApiService {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  async checkHealth(): Promise<HealthResponse> {
    const response = await fetch(`${this.baseUrl}/health`);
    if (!response.ok) {
      throw new Error('API health check failed');
    }
    return response.json();
  }

  async analyzeText(policyText: string, policyName?: string): Promise<AnalysisResponse> {
    const response = await fetch(`${this.baseUrl}/analyze/text`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        policy_text: policyText,
        policy_name: policyName || 'Unnamed Policy'
      })
    });

    if (!response.ok) {
      if (response.status === 503) {
        throw new Error('API service is not initialized. Please check that the Cohere API key is configured and restart the API server.');
      }
      const errorData = await response.json();
      throw new Error(errorData.detail || 'Analysis failed');
    }

    return response.json();
  }

  async analyzeFile(file: File, policyName?: string): Promise<AnalysisResponse> {
    const formData = new FormData();
    formData.append('file', file);
    if (policyName) {
      formData.append('policy_name', policyName);
    }

    const response = await fetch(`${this.baseUrl}/analyze/file`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      if (response.status === 503) {
        throw new Error('API service is not initialized. Please check that the Cohere API key is configured and restart the API server.');
      }
      const errorData = await response.json();
      throw new Error(errorData.detail || 'File analysis failed');
    }

    return response.json();
  }

  async getGdprRequirements(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/requirements`);
    if (!response.ok) {
      throw new Error('Failed to fetch GDPR requirements');
    }
    return response.json();
  }

  async listCompanies(): Promise<any> {
    const response = await fetch(`${this.baseUrl}/list-companies`);
    if (!response.ok) {
      throw new Error('Failed to fetch companies list');
    }
    return response.json();
  }

  async listCompanyResults(companyName: string): Promise<any> {
    const response = await fetch(`${this.baseUrl}/list-results/${encodeURIComponent(companyName)}`);
    if (!response.ok) {
      throw new Error('Failed to fetch company results');
    }
    return response.json();
  }

  async downloadFile(companyName: string, filename: string): Promise<Blob> {
    const response = await fetch(`${this.baseUrl}/download/${encodeURIComponent(companyName)}/${encodeURIComponent(filename)}`);
    if (!response.ok) {
      throw new Error('Failed to download file');
    }
    return response.blob();
  }
}

// Export a default instance
export const gdprApiService = new GdprApiService();
