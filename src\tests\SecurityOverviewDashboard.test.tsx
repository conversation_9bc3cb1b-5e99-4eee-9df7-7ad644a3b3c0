/**
 * Security Overview Dashboard Component Tests
 * Tests component rendering, data loading, error handling, and user interactions
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import '@testing-library/jest-dom';

// Mock contexts
const mockThemeContext = {
  mode: 'light',
  toggleTheme: jest.fn(),
};

jest.mock('../../context/ThemeContext', () => ({
  useTheme: () => mockThemeContext,
}));

// Mock chart components
jest.mock('react-chartjs-2', () => ({
  Line: ({ data, options }: any) => <div data-testid="line-chart" data-chart-data={JSON.stringify(data)} />,
  Doughnut: ({ data, options }: any) => <div data-testid="doughnut-chart" data-chart-data={JSON.stringify(data)} />,
}));

// Mock chart utilities
jest.mock('../../utils/chartOptimizations', () => ({
  getChartTheme: jest.fn(() => ({
    backgroundColor: '#ffffff',
    textColor: '#000000',
  })),
}));

// Mock security data service
const mockSecurityData = {
  overview: {
    securityScore: 95,
    activeThreats: 3,
    systemsMonitored: 42,
    alertsLast24h: 5,
    incidentsThisMonth: 2,
    vulnerabilitiesFound: 12,
    complianceScore: 88,
    lastUpdated: new Date()
  },
  threats: [
    {
      id: '1',
      title: 'Suspicious Network Activity',
      severity: 'high',
      status: 'investigating',
      type: 'unauthorized_access',
      detectedAt: new Date(),
      assignedTo: 'Security Team'
    }
  ],
  incidents: [
    {
      id: '1',
      title: 'Failed Login Attempts',
      severity: 'medium',
      status: 'open',
      category: 'unauthorized_access',
      reportedAt: new Date(),
      assignedTo: 'IT Team'
    }
  ],
  systems: [
    {
      id: '1',
      name: 'Web Server',
      type: 'server',
      status: 'healthy',
      location: 'Data Center 1',
      owner: 'DevOps Team',
      vulnerabilities: { critical: 0, high: 1, medium: 2, low: 3 }
    }
  ],
  alerts: [
    {
      id: '1',
      title: 'High CPU Usage',
      severity: 'medium',
      status: 'new',
      triggeredAt: new Date(),
      source: 'Monitoring System'
    }
  ],
  realTimeUpdates: {
    lastSync: new Date(),
    isLive: true,
    pendingAlerts: 2,
    systemStatus: 'operational'
  }
};

jest.mock('../../components/Security/services/SecurityDataService', () => ({
  generateSecurityOverviewData: jest.fn(() => mockSecurityData),
}));

// Import component after mocking
import SecurityOverviewDashboard from '../../components/Security/SecurityOverviewDashboardOptimized';

describe('SecurityOverviewDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockThemeContext.mode = 'light';
  });

  describe('Component Rendering', () => {
    test('renders without crashing', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });
      
      expect(screen.getByTestId('security-overview')).toBeInTheDocument();
    });

    test('displays loading state initially', async () => {
      render(<SecurityOverviewDashboard />);
      
      expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
      expect(screen.getByText(/loading security overview/i)).toBeInTheDocument();
    });

    test('displays security overview data after loading', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
        expect(screen.getByText('95')).toBeInTheDocument(); // Security score
        expect(screen.getByText('42')).toBeInTheDocument(); // Systems monitored
      });
    });

    test('displays real-time status indicator', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Live')).toBeInTheDocument();
        expect(screen.getByText('(operational)')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    test('displays error state when data loading fails', async () => {
      const mockGenerateSecurityData = require('../../components/Security/services/SecurityDataService').generateSecurityOverviewData;
      mockGenerateSecurityData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByTestId('security-overview-error')).toBeInTheDocument();
        expect(screen.getByText('Security Overview Unavailable')).toBeInTheDocument();
        expect(screen.getByText(/security monitoring service is currently unavailable/i)).toBeInTheDocument();
      });
    });

    test('displays retry functionality in error state', async () => {
      const mockGenerateSecurityData = require('../../components/Security/services/SecurityDataService').generateSecurityOverviewData;
      mockGenerateSecurityData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Retry Loading')).toBeInTheDocument();
        expect(screen.getByText('Reload Page')).toBeInTheDocument();
      });
    });

    test('retry functionality works correctly', async () => {
      const mockGenerateSecurityData = require('../../components/Security/services/SecurityDataService').generateSecurityOverviewData;
      mockGenerateSecurityData
        .mockImplementationOnce(() => {
          throw new Error('Service unavailable');
        })
        .mockImplementationOnce(() => mockSecurityData);

      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByTestId('security-overview-error')).toBeInTheDocument();
      });

      const retryButton = screen.getByText('Retry Loading');
      fireEvent.click(retryButton);

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
        expect(mockGenerateSecurityData).toHaveBeenCalledTimes(2);
      });
    });

    test('displays troubleshooting information in error state', async () => {
      const mockGenerateSecurityData = require('../../components/Security/services/SecurityDataService').generateSecurityOverviewData;
      mockGenerateSecurityData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });

      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Troubleshooting Steps:')).toBeInTheDocument();
        expect(screen.getByText(/check your internet connection/i)).toBeInTheDocument();
        expect(screen.getByText(/verify security monitoring services/i)).toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    test('switches between different tabs correctly', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      // Click on Threats tab
      const threatsTab = screen.getByText('Threats');
      fireEvent.click(threatsTab);

      await waitFor(() => {
        expect(screen.getByText('Suspicious Network Activity')).toBeInTheDocument();
      });

      // Click on Systems tab
      const systemsTab = screen.getByText('Systems');
      fireEvent.click(systemsTab);

      await waitFor(() => {
        expect(screen.getByText('Web Server')).toBeInTheDocument();
      });
    });

    test('maintains active tab state correctly', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      const threatsTab = screen.getByText('Threats');
      fireEvent.click(threatsTab);

      // Check that the threats tab is now active (has different styling)
      expect(threatsTab.closest('button')).toHaveClass('bg-primary');
    });
  });

  describe('CRUD Operations', () => {
    test('opens threat modal when Report Threat button is clicked', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      // Navigate to threats tab
      const threatsTab = screen.getByText('Threats');
      fireEvent.click(threatsTab);

      await waitFor(() => {
        const reportThreatButton = screen.getByText('Report Threat');
        fireEvent.click(reportThreatButton);
        
        expect(screen.getByText('Add Threat')).toBeInTheDocument();
      });
    });

    test('opens system modal when Add System button is clicked', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      // Navigate to systems tab
      const systemsTab = screen.getByText('Systems');
      fireEvent.click(systemsTab);

      await waitFor(() => {
        const addSystemButton = screen.getByText('Add System');
        fireEvent.click(addSystemButton);
        
        expect(screen.getByText('Add System')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    test('has proper ARIA labels and roles', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      // Check for proper heading structure
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Security Overview Dashboard');
      
      // Check for proper button roles
      const refreshButton = screen.getByText('Refresh');
      expect(refreshButton).toHaveAttribute('type', 'button');
    });

    test('supports keyboard navigation', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      const threatsTab = screen.getByText('Threats');
      threatsTab.focus();
      expect(document.activeElement).toBe(threatsTab);
    });
  });

  describe('Performance', () => {
    test('handles large datasets without performance issues', async () => {
      const largeDataset = {
        ...mockSecurityData,
        threats: Array.from({ length: 100 }, (_, i) => ({
          id: `threat-${i}`,
          title: `Threat ${i}`,
          severity: 'medium',
          status: 'investigating',
          type: 'malware',
          detectedAt: new Date(),
          assignedTo: 'Security Team'
        }))
      };

      const mockGenerateSecurityData = require('../../components/Security/services/SecurityDataService').generateSecurityOverviewData;
      mockGenerateSecurityData.mockReturnValue(largeDataset);

      const startTime = performance.now();
      
      await act(async () => {
        render(<SecurityOverviewDashboard />);
      });

      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      const endTime = performance.now();
      const renderTime = endTime - startTime;

      // Should render within reasonable time (less than 1 second)
      expect(renderTime).toBeLessThan(1000);
    });
  });
});
