// Enhanced types for policies management
export interface Policy {
  id: string;
  name: string;
  description: string;
  category: 'privacy' | 'security' | 'compliance' | 'operational' | 'hr' | 'legal';
  type: 'policy' | 'procedure' | 'guideline' | 'standard';
  status: 'draft' | 'review' | 'approved' | 'active' | 'archived' | 'expired';
  version: string;
  effectiveDate: Date;
  expiryDate?: Date;
  lastReviewDate: Date;
  nextReviewDate: Date;
  owner: string;
  ownerId: string;
  approver?: string;
  approverId?: string;
  approvalDate?: Date;
  organizationalUnits: string[]; // IDs of units this policy applies to
  regulations: string[]; // Regulatory frameworks this policy addresses
  tags: string[];
  content: {
    purpose: string;
    scope: string;
    definitions: Record<string, string>;
    requirements: PolicyRequirement[];
    procedures: PolicyProcedure[];
    responsibilities: PolicyResponsibility[];
    exceptions: string[];
    relatedPolicies: string[];
  };
  attachments: PolicyAttachment[];
  auditTrail: PolicyAuditEntry[];
  metrics: {
    complianceScore: number;
    adherenceRate: number;
    violationCount: number;
    lastAssessment?: Date;
  };
  createdAt: Date;
  updatedAt: Date;
  createdBy: string;
  updatedBy: string;
}

export interface PolicyRequirement {
  id: string;
  title: string;
  description: string;
  mandatory: boolean;
  category: string;
  controls: string[];
  evidence: string[];
  testingFrequency: 'monthly' | 'quarterly' | 'annually' | 'as_needed';
  lastTested?: Date;
  testResult?: 'pass' | 'fail' | 'partial';
}

export interface PolicyProcedure {
  id: string;
  title: string;
  description: string;
  steps: PolicyStep[];
  roles: string[];
  tools: string[];
  frequency: string;
  documentation: string[];
}

export interface PolicyStep {
  id: string;
  order: number;
  title: string;
  description: string;
  responsible: string;
  duration: string;
  dependencies: string[];
  outputs: string[];
}

export interface PolicyResponsibility {
  id: string;
  role: string;
  responsibilities: string[];
  accountabilities: string[];
  authorities: string[];
}

export interface PolicyAttachment {
  id: string;
  name: string;
  type: 'document' | 'template' | 'form' | 'checklist' | 'flowchart';
  url: string;
  size: number;
  uploadedAt: Date;
  uploadedBy: string;
}

export interface PolicyAuditEntry {
  id: string;
  timestamp: Date;
  userId: string;
  userName: string;
  action: 'created' | 'updated' | 'reviewed' | 'approved' | 'archived' | 'restored';
  changes?: Record<string, { from: any; to: any }>;
  comments?: string;
  ipAddress?: string;
}

export interface PolicyTemplate {
  id: string;
  name: string;
  description: string;
  category: Policy['category'];
  type: Policy['type'];
  template: Partial<Policy>;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  usageCount: number;
}

export interface PolicyMetrics {
  totalPolicies: number;
  byStatus: Record<string, number>;
  byCategory: Record<string, number>;
  byType: Record<string, number>;
  averageComplianceScore: number;
  policiesNeedingReview: number;
  expiredPolicies: number;
  draftPolicies: number;
  activePolicies: number;
  totalViolations: number;
  averageAdherenceRate: number;
  lastUpdated: Date;
}

export interface PolicyViolation {
  id: string;
  policyId: string;
  policyName: string;
  violationType: 'non_compliance' | 'breach' | 'deviation' | 'exception';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  detectedAt: Date;
  detectedBy: string;
  affectedUnits: string[];
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  assignee?: string;
  dueDate?: Date;
  resolutionNotes?: string;
  resolvedAt?: Date;
  resolvedBy?: string;
}

export interface PolicyAssessment {
  id: string;
  policyId: string;
  assessmentDate: Date;
  assessor: string;
  overallScore: number;
  categoryScores: Record<string, number>;
  findings: AssessmentFinding[];
  recommendations: string[];
  nextAssessmentDate: Date;
  status: 'scheduled' | 'in_progress' | 'completed' | 'approved';
}

export interface AssessmentFinding {
  id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  assignee?: string;
  dueDate?: Date;
}

export class PoliciesManagementService {
  private static policies: Policy[] = [];
  private static templates: PolicyTemplate[] = [];
  private static violations: PolicyViolation[] = [];
  private static assessments: PolicyAssessment[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    
    this.policies = this.generateMockPolicies();
    this.templates = this.generateMockTemplates();
    this.violations = this.generateMockViolations();
    this.assessments = this.generateMockAssessments();
    this.initialized = true;
  }

  private static generateMockPolicies(): Policy[] {
    return [
      {
        id: 'policy-001',
        name: 'Data Protection Policy',
        description: 'Comprehensive policy for protecting personal and sensitive data in accordance with GDPR and other privacy regulations.',
        category: 'privacy',
        type: 'policy',
        status: 'active',
        version: '2.1',
        effectiveDate: new Date('2024-01-01'),
        expiryDate: new Date('2025-12-31'),
        lastReviewDate: new Date('2024-01-15'),
        nextReviewDate: new Date('2024-07-15'),
        owner: 'Privacy Officer',
        ownerId: 'user-001',
        approver: 'Chief Legal Officer',
        approverId: 'user-002',
        approvalDate: new Date('2023-12-20'),
        organizationalUnits: ['unit-001', 'unit-002', 'unit-003'],
        regulations: ['GDPR', 'CCPA', 'PIPEDA'],
        tags: ['data protection', 'privacy', 'gdpr', 'personal data'],
        content: {
          purpose: 'To establish guidelines for the collection, processing, storage, and disposal of personal data',
          scope: 'All employees, contractors, and third parties handling personal data',
          definitions: {
            'Personal Data': 'Any information relating to an identified or identifiable natural person',
            'Processing': 'Any operation performed on personal data'
          },
          requirements: [],
          procedures: [],
          responsibilities: [],
          exceptions: ['Emergency situations where data subject consent cannot be obtained'],
          relatedPolicies: ['policy-002', 'policy-003']
        },
        attachments: [],
        auditTrail: [],
        metrics: {
          complianceScore: 94,
          adherenceRate: 89,
          violationCount: 2,
          lastAssessment: new Date('2024-01-10')
        },
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2024-01-15'),
        createdBy: 'Privacy Officer',
        updatedBy: 'Privacy Officer'
      },
      {
        id: 'policy-002',
        name: 'Information Security Policy',
        description: 'Policy governing information security practices and controls across the organization.',
        category: 'security',
        type: 'policy',
        status: 'active',
        version: '3.0',
        effectiveDate: new Date('2024-02-01'),
        lastReviewDate: new Date('2024-02-01'),
        nextReviewDate: new Date('2024-08-01'),
        owner: 'CISO',
        ownerId: 'user-003',
        approver: 'CTO',
        approverId: 'user-004',
        approvalDate: new Date('2024-01-25'),
        organizationalUnits: ['unit-004', 'unit-007'],
        regulations: ['ISO 27001', 'SOC 2', 'NIST'],
        tags: ['security', 'information security', 'iso27001', 'controls'],
        content: {
          purpose: 'To protect organizational information assets from threats and vulnerabilities',
          scope: 'All information systems, networks, and data within the organization',
          definitions: {
            'Information Asset': 'Any data, system, or resource that has value to the organization',
            'Security Incident': 'Any event that compromises the confidentiality, integrity, or availability of information'
          },
          requirements: [],
          procedures: [],
          responsibilities: [],
          exceptions: ['Emergency access procedures during system outages'],
          relatedPolicies: ['policy-001', 'policy-004']
        },
        attachments: [],
        auditTrail: [],
        metrics: {
          complianceScore: 91,
          adherenceRate: 95,
          violationCount: 1,
          lastAssessment: new Date('2024-02-05')
        },
        createdAt: new Date('2023-02-01'),
        updatedAt: new Date('2024-02-01'),
        createdBy: 'CISO',
        updatedBy: 'CISO'
      },
      {
        id: 'policy-003',
        name: 'Incident Response Procedure',
        description: 'Detailed procedures for responding to security incidents and data breaches.',
        category: 'security',
        type: 'procedure',
        status: 'review',
        version: '1.5',
        effectiveDate: new Date('2023-06-01'),
        lastReviewDate: new Date('2023-12-01'),
        nextReviewDate: new Date('2024-06-01'),
        owner: 'Security Manager',
        ownerId: 'user-005',
        organizationalUnits: ['unit-004', 'unit-001'],
        regulations: ['GDPR', 'ISO 27001'],
        tags: ['incident response', 'security', 'breach', 'procedure'],
        content: {
          purpose: 'To provide structured approach for handling security incidents',
          scope: 'All security incidents affecting organizational systems or data',
          definitions: {
            'Security Incident': 'Any event that may compromise information security',
            'Data Breach': 'Unauthorized access to or disclosure of personal data'
          },
          requirements: [],
          procedures: [],
          responsibilities: [],
          exceptions: [],
          relatedPolicies: ['policy-001', 'policy-002']
        },
        attachments: [],
        auditTrail: [],
        metrics: {
          complianceScore: 87,
          adherenceRate: 92,
          violationCount: 0,
          lastAssessment: new Date('2023-12-15')
        },
        createdAt: new Date('2023-05-01'),
        updatedAt: new Date('2023-12-01'),
        createdBy: 'Security Manager',
        updatedBy: 'Security Manager'
      }
    ];
  }

  private static generateMockTemplates(): PolicyTemplate[] {
    return [
      {
        id: 'template-001',
        name: 'Privacy Policy Template',
        description: 'Standard template for creating privacy policies',
        category: 'privacy',
        type: 'policy',
        template: {
          category: 'privacy',
          type: 'policy',
          status: 'draft',
          regulations: ['GDPR'],
          tags: ['privacy', 'data protection']
        },
        isActive: true,
        createdBy: 'Privacy Officer',
        createdAt: new Date('2023-01-01'),
        usageCount: 5
      }
    ];
  }

  private static generateMockViolations(): PolicyViolation[] {
    return [
      {
        id: 'violation-001',
        policyId: 'policy-001',
        policyName: 'Data Protection Policy',
        violationType: 'non_compliance',
        severity: 'medium',
        description: 'Unauthorized access to customer database without proper logging',
        detectedAt: new Date('2024-01-20'),
        detectedBy: 'Security Monitoring System',
        affectedUnits: ['unit-002'],
        status: 'investigating',
        assignee: 'Security Manager',
        dueDate: new Date('2024-02-20')
      }
    ];
  }

  private static generateMockAssessments(): PolicyAssessment[] {
    return [
      {
        id: 'assessment-001',
        policyId: 'policy-001',
        assessmentDate: new Date('2024-01-10'),
        assessor: 'Compliance Officer',
        overallScore: 94,
        categoryScores: {
          'implementation': 95,
          'documentation': 92,
          'training': 96,
          'monitoring': 93
        },
        findings: [],
        recommendations: ['Enhance monitoring capabilities', 'Update training materials'],
        nextAssessmentDate: new Date('2024-07-10'),
        status: 'completed'
      }
    ];
  }

  // CRUD Operations for Policies
  static async getAllPolicies(): Promise<Policy[]> {
    this.initialize();
    return [...this.policies];
  }

  static async getPolicyById(id: string): Promise<Policy | null> {
    this.initialize();
    return this.policies.find(policy => policy.id === id) || null;
  }

  static async createPolicy(policyData: Partial<Policy>): Promise<Policy> {
    this.initialize();
    const newPolicy: Policy = {
      id: `policy-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: policyData.name || '',
      description: policyData.description || '',
      category: policyData.category || 'operational',
      type: policyData.type || 'policy',
      status: 'draft',
      version: '1.0',
      effectiveDate: policyData.effectiveDate || new Date(),
      lastReviewDate: new Date(),
      nextReviewDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000), // 6 months
      owner: policyData.owner || '',
      ownerId: policyData.ownerId || '',
      organizationalUnits: policyData.organizationalUnits || [],
      regulations: policyData.regulations || [],
      tags: policyData.tags || [],
      content: {
        purpose: policyData.content?.purpose || '',
        scope: policyData.content?.scope || '',
        definitions: policyData.content?.definitions || {},
        requirements: policyData.content?.requirements || [],
        procedures: policyData.content?.procedures || [],
        responsibilities: policyData.content?.responsibilities || [],
        exceptions: policyData.content?.exceptions || [],
        relatedPolicies: policyData.content?.relatedPolicies || []
      },
      attachments: [],
      auditTrail: [{
        id: `audit-${Date.now()}`,
        timestamp: new Date(),
        userId: 'current-user',
        userName: 'Current User',
        action: 'created',
        comments: 'Policy created'
      }],
      metrics: {
        complianceScore: 0,
        adherenceRate: 0,
        violationCount: 0
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: policyData.createdBy || 'Current User',
      updatedBy: policyData.updatedBy || 'Current User'
    };

    this.policies.push(newPolicy);
    return newPolicy;
  }

  static async updatePolicy(id: string, policyData: Partial<Policy>): Promise<Policy | null> {
    this.initialize();
    const index = this.policies.findIndex(policy => policy.id === id);
    if (index === -1) return null;

    const updatedPolicy = {
      ...this.policies[index],
      ...policyData,
      updatedAt: new Date()
    };

    // Add audit trail entry
    updatedPolicy.auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'current-user',
      userName: 'Current User',
      action: 'updated',
      comments: 'Policy updated'
    });

    this.policies[index] = updatedPolicy;
    return updatedPolicy;
  }

  static async deletePolicy(id: string): Promise<boolean> {
    this.initialize();
    const index = this.policies.findIndex(policy => policy.id === id);
    if (index === -1) return false;

    this.policies.splice(index, 1);
    return true;
  }

  // Metrics and Analytics
  static async getMetrics(): Promise<PolicyMetrics> {
    this.initialize();
    
    const byStatus = this.policies.reduce((acc, policy) => {
      acc[policy.status] = (acc[policy.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byCategory = this.policies.reduce((acc, policy) => {
      acc[policy.category] = (acc[policy.category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byType = this.policies.reduce((acc, policy) => {
      acc[policy.type] = (acc[policy.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageComplianceScore = this.policies.length > 0 
      ? this.policies.reduce((sum, policy) => sum + policy.metrics.complianceScore, 0) / this.policies.length 
      : 0;

    const averageAdherenceRate = this.policies.length > 0 
      ? this.policies.reduce((sum, policy) => sum + policy.metrics.adherenceRate, 0) / this.policies.length 
      : 0;

    const totalViolations = this.policies.reduce((sum, policy) => sum + policy.metrics.violationCount, 0);

    const now = new Date();
    const policiesNeedingReview = this.policies.filter(policy => policy.nextReviewDate <= now).length;
    const expiredPolicies = this.policies.filter(policy => policy.expiryDate && policy.expiryDate <= now).length;

    return {
      totalPolicies: this.policies.length,
      byStatus,
      byCategory,
      byType,
      averageComplianceScore,
      policiesNeedingReview,
      expiredPolicies,
      draftPolicies: byStatus.draft || 0,
      activePolicies: byStatus.active || 0,
      totalViolations,
      averageAdherenceRate,
      lastUpdated: new Date()
    };
  }

  // Search and Filtering
  static async searchPolicies(query: string): Promise<Policy[]> {
    this.initialize();
    const lowercaseQuery = query.toLowerCase();
    return this.policies.filter(policy => 
      policy.name.toLowerCase().includes(lowercaseQuery) ||
      policy.description.toLowerCase().includes(lowercaseQuery) ||
      policy.category.toLowerCase().includes(lowercaseQuery) ||
      policy.owner.toLowerCase().includes(lowercaseQuery) ||
      policy.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
    );
  }

  static async filterPolicies(filters: {
    category?: string[];
    type?: string[];
    status?: string[];
    owner?: string[];
    regulations?: string[];
  }): Promise<Policy[]> {
    this.initialize();
    return this.policies.filter(policy => {
      if (filters.category && filters.category.length > 0 && !filters.category.includes(policy.category)) {
        return false;
      }
      if (filters.type && filters.type.length > 0 && !filters.type.includes(policy.type)) {
        return false;
      }
      if (filters.status && filters.status.length > 0 && !filters.status.includes(policy.status)) {
        return false;
      }
      if (filters.owner && filters.owner.length > 0 && !filters.owner.includes(policy.owner)) {
        return false;
      }
      if (filters.regulations && filters.regulations.length > 0 && 
          !filters.regulations.some(reg => policy.regulations.includes(reg))) {
        return false;
      }
      return true;
    });
  }

  // Templates
  static async getAllTemplates(): Promise<PolicyTemplate[]> {
    this.initialize();
    return [...this.templates];
  }

  // Violations
  static async getAllViolations(): Promise<PolicyViolation[]> {
    this.initialize();
    return [...this.violations];
  }

  // Assessments
  static async getAllAssessments(): Promise<PolicyAssessment[]> {
    this.initialize();
    return [...this.assessments];
  }
}
