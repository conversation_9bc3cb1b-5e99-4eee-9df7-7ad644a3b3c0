import {
  RegulatoryFramework,
  RegulatoryRequirement,
  ComplianceDeadline,
  RegulatoryComplianceMetrics,
  RegulationType,
  JurisdictionType
} from '../types/gdprTypes';

export class RegulatoryFrameworkService {
  private static frameworks: RegulatoryFramework[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.frameworks = this.generateMockFrameworks();
    this.initialized = true;
  }

  // CRUD Operations
  static async getAllFrameworks(): Promise<RegulatoryFramework[]> {
    this.initialize();
    return [...this.frameworks];
  }

  static async getFrameworkById(id: string): Promise<RegulatoryFramework | null> {
    this.initialize();
    return this.frameworks.find(framework => framework.id === id) || null;
  }

  static async createFramework(frameworkData: Partial<RegulatoryFramework>): Promise<RegulatoryFramework> {
    this.initialize();
    const newFramework: RegulatoryFramework = {
      id: `framework-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: frameworkData.name || '',
      type: frameworkData.type || 'other',
      jurisdiction: frameworkData.jurisdiction || 'other',
      description: frameworkData.description || '',
      version: frameworkData.version || '1.0',
      effectiveDate: frameworkData.effectiveDate || new Date(),
      lastUpdated: new Date(),
      status: frameworkData.status || 'draft',
      requirements: frameworkData.requirements || [],
      complianceDeadlines: frameworkData.complianceDeadlines || [],
      applicableEntities: frameworkData.applicableEntities || [],
      penalties: frameworkData.penalties || [],
      references: frameworkData.references || [],
      tags: frameworkData.tags || [],
      owner: frameworkData.owner || '',
      reviewers: frameworkData.reviewers || [],
      nextReviewDate: frameworkData.nextReviewDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
    };

    this.frameworks.push(newFramework);
    return newFramework;
  }

  static async updateFramework(id: string, updates: Partial<RegulatoryFramework>): Promise<RegulatoryFramework | null> {
    this.initialize();
    const index = this.frameworks.findIndex(framework => framework.id === id);
    if (index === -1) return null;

    updates.lastUpdated = new Date();
    this.frameworks[index] = { ...this.frameworks[index], ...updates };
    return this.frameworks[index];
  }

  static async deleteFramework(id: string): Promise<boolean> {
    this.initialize();
    const index = this.frameworks.findIndex(framework => framework.id === id);
    if (index === -1) return false;

    this.frameworks.splice(index, 1);
    return true;
  }

  // Requirements Management
  static async addRequirement(frameworkId: string, requirement: Partial<RegulatoryRequirement>): Promise<RegulatoryRequirement | null> {
    const framework = await this.getFrameworkById(frameworkId);
    if (!framework) return null;

    const newRequirement: RegulatoryRequirement = {
      id: `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: requirement.title || '',
      description: requirement.description || '',
      category: requirement.category || 'other',
      priority: requirement.priority || 'medium',
      status: requirement.status || 'not_started',
      complianceScore: requirement.complianceScore || 0,
      evidence: requirement.evidence || [],
      assignedTo: requirement.assignedTo || '',
      dueDate: requirement.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      completedDate: requirement.completedDate,
      notes: requirement.notes || '',
      subRequirements: requirement.subRequirements || [],
      controls: requirement.controls || []
    };

    framework.requirements.push(newRequirement);
    await this.updateFramework(frameworkId, { requirements: framework.requirements });
    return newRequirement;
  }

  static async updateRequirement(frameworkId: string, requirementId: string, updates: Partial<RegulatoryRequirement>): Promise<RegulatoryRequirement | null> {
    const framework = await this.getFrameworkById(frameworkId);
    if (!framework) return null;

    const requirementIndex = framework.requirements.findIndex(r => r.id === requirementId);
    if (requirementIndex === -1) return null;

    framework.requirements[requirementIndex] = { ...framework.requirements[requirementIndex], ...updates };
    await this.updateFramework(frameworkId, { requirements: framework.requirements });
    return framework.requirements[requirementIndex];
  }

  static async deleteRequirement(frameworkId: string, requirementId: string): Promise<boolean> {
    const framework = await this.getFrameworkById(frameworkId);
    if (!framework) return false;

    const requirementIndex = framework.requirements.findIndex(r => r.id === requirementId);
    if (requirementIndex === -1) return false;

    framework.requirements.splice(requirementIndex, 1);
    await this.updateFramework(frameworkId, { requirements: framework.requirements });
    return true;
  }

  // Deadlines Management
  static async addDeadline(frameworkId: string, deadline: Partial<ComplianceDeadline>): Promise<ComplianceDeadline | null> {
    const framework = await this.getFrameworkById(frameworkId);
    if (!framework) return null;

    const newDeadline: ComplianceDeadline = {
      id: `deadline-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: deadline.title || '',
      description: deadline.description || '',
      dueDate: deadline.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      status: deadline.status || 'upcoming',
      priority: deadline.priority || 'medium',
      assignedTo: deadline.assignedTo || '',
      requirements: deadline.requirements || [],
      completedDate: deadline.completedDate,
      notes: deadline.notes || ''
    };

    framework.complianceDeadlines.push(newDeadline);
    await this.updateFramework(frameworkId, { complianceDeadlines: framework.complianceDeadlines });
    return newDeadline;
  }

  // Advanced Search and Filtering
  static async searchFrameworks(query: string): Promise<RegulatoryFramework[]> {
    this.initialize();
    const searchTerm = query.toLowerCase();

    return this.frameworks.filter(framework =>
      framework.name.toLowerCase().includes(searchTerm) ||
      framework.description.toLowerCase().includes(searchTerm) ||
      framework.type.toLowerCase().includes(searchTerm) ||
      framework.jurisdiction.toLowerCase().includes(searchTerm) ||
      framework.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  static async filterFrameworks(filters: {
    type?: string[];
    jurisdiction?: string[];
    status?: string[];
    complianceScore?: { min: number; max: number };
  }): Promise<RegulatoryFramework[]> {
    this.initialize();
    let filtered = [...this.frameworks];

    if (filters.type?.length) {
      filtered = filtered.filter(framework => filters.type!.includes(framework.type));
    }

    if (filters.jurisdiction?.length) {
      filtered = filtered.filter(framework => filters.jurisdiction!.includes(framework.jurisdiction));
    }

    if (filters.status?.length) {
      filtered = filtered.filter(framework => filters.status!.includes(framework.status));
    }

    if (filters.complianceScore) {
      filtered = filtered.filter(framework => {
        const score = this.calculateFrameworkComplianceScore(framework);
        return score >= filters.complianceScore!.min && score <= filters.complianceScore!.max;
      });
    }

    return filtered;
  }

  static async getComplianceGapAnalysis(): Promise<{
    framework: string;
    totalRequirements: number;
    compliantRequirements: number;
    nonCompliantRequirements: number;
    inProgressRequirements: number;
    compliancePercentage: number;
    criticalGaps: Array<{
      requirement: string;
      priority: string;
      daysOverdue: number;
    }>;
  }[]> {
    this.initialize();
    const now = new Date();

    return this.frameworks.map(framework => {
      const totalRequirements = framework.requirements.length;
      const compliantRequirements = framework.requirements.filter(req => req.status === 'compliant').length;
      const nonCompliantRequirements = framework.requirements.filter(req => req.status === 'non_compliant').length;
      const inProgressRequirements = framework.requirements.filter(req => req.status === 'in_progress').length;
      const compliancePercentage = totalRequirements > 0 ? (compliantRequirements / totalRequirements) * 100 : 0;

      const criticalGaps = framework.requirements
        .filter(req => req.status !== 'compliant' && req.priority === 'critical')
        .map(req => ({
          requirement: req.title,
          priority: req.priority,
          daysOverdue: req.dueDate < now ? Math.ceil((now.getTime() - req.dueDate.getTime()) / (1000 * 60 * 60 * 24)) : 0
        }));

      return {
        framework: framework.name,
        totalRequirements,
        compliantRequirements,
        nonCompliantRequirements,
        inProgressRequirements,
        compliancePercentage,
        criticalGaps
      };
    });
  }

  private static calculateFrameworkComplianceScore(framework: RegulatoryFramework): number {
    if (framework.requirements.length === 0) return 0;

    const totalScore = framework.requirements.reduce((sum, req) => sum + req.complianceScore, 0);
    return totalScore / framework.requirements.length;
  }

  // Analytics and Reporting
  static async getComplianceMetrics(): Promise<RegulatoryComplianceMetrics> {
    this.initialize();

    const byJurisdiction = this.frameworks.reduce((acc, framework) => {
      acc[framework.jurisdiction] = (acc[framework.jurisdiction] || 0) + 1;
      return acc;
    }, {} as Record<JurisdictionType, number>);

    const byStatus = this.frameworks.reduce((acc, framework) => {
      acc[framework.status] = (acc[framework.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const allRequirements = this.frameworks.flatMap(f => f.requirements);
    const compliantRequirements = allRequirements.filter(r => r.status === 'compliant').length;
    const overallComplianceScore = allRequirements.length > 0 
      ? allRequirements.reduce((sum, req) => sum + req.complianceScore, 0) / allRequirements.length 
      : 0;

    const now = new Date();
    const allDeadlines = this.frameworks.flatMap(f => f.complianceDeadlines);
    const upcomingDeadlines = allDeadlines.filter(d => d.dueDate > now && d.status !== 'completed').length;
    const overdueDeadlines = allDeadlines.filter(d => d.dueDate < now && d.status !== 'completed').length;

    return {
      totalFrameworks: this.frameworks.length,
      byJurisdiction,
      byStatus,
      totalRequirements: allRequirements.length,
      compliantRequirements,
      overallComplianceScore,
      upcomingDeadlines,
      overdueDeadlines
    };
  }

  static async getUpcomingDeadlines(days: number = 30): Promise<ComplianceDeadline[]> {
    this.initialize();
    const cutoffDate = new Date(Date.now() + days * 24 * 60 * 60 * 1000);
    const now = new Date();

    return this.frameworks
      .flatMap(f => f.complianceDeadlines.map(d => ({ ...d, frameworkName: f.name })))
      .filter(d => d.dueDate >= now && d.dueDate <= cutoffDate && d.status !== 'completed')
      .sort((a, b) => a.dueDate.getTime() - b.dueDate.getTime());
  }

  // Mock Data Generation
  private static generateMockFrameworks(): RegulatoryFramework[] {
    return [
      {
        id: 'framework-gdpr-001',
        name: 'General Data Protection Regulation (GDPR)',
        type: 'gdpr',
        jurisdiction: 'eu',
        description: 'EU regulation on data protection and privacy for individuals within the European Union and European Economic Area',
        version: '2018.1',
        effectiveDate: new Date('2018-05-25'),
        lastUpdated: new Date('2024-01-15'),
        status: 'active',
        requirements: [
          {
            id: 'req-gdpr-001',
            title: 'Data Protection Impact Assessment (DPIA)',
            description: 'Conduct DPIA for high-risk processing activities',
            category: 'data_protection',
            priority: 'high',
            status: 'compliant',
            complianceScore: 95,
            evidence: ['dpia-template.pdf', 'completed-dpias.xlsx'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-03-01'),
            completedDate: new Date('2024-02-15'),
            notes: 'DPIA process implemented and documented',
            subRequirements: [],
            controls: []
          },
          {
            id: 'req-gdpr-002',
            title: 'Consent Management',
            description: 'Implement lawful basis for processing personal data',
            category: 'consent',
            priority: 'critical',
            status: 'in_progress',
            complianceScore: 75,
            evidence: ['consent-forms.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-04-01'),
            notes: 'Consent management system being implemented',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-gdpr-001',
            title: 'Annual Privacy Policy Review',
            description: 'Review and update privacy policy for GDPR compliance',
            dueDate: new Date('2024-05-25'),
            status: 'upcoming',
            priority: 'high',
            assignedTo: '<EMAIL>',
            requirements: ['req-gdpr-001', 'req-gdpr-002'],
            notes: 'Annual review required by GDPR Article 5'
          }
        ],
        applicableEntities: ['All EU subsidiaries', 'Data processing operations'],
        penalties: [
          {
            id: 'penalty-gdpr-001',
            violationType: 'Data breach notification failure',
            description: 'Failure to notify supervisory authority within 72 hours',
            minPenalty: 10000000,
            maxPenalty: 20000000,
            currency: 'EUR',
            additionalConsequences: ['Regulatory investigation', 'Reputational damage']
          }
        ],
        references: [
          {
            id: 'ref-gdpr-001',
            title: 'GDPR Official Text',
            url: 'https://eur-lex.europa.eu/eli/reg/2016/679/oj',
            type: 'official_text',
            description: 'Official GDPR regulation text'
          }
        ],
        tags: ['privacy', 'data-protection', 'eu', 'mandatory'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>', '<EMAIL>'],
        nextReviewDate: new Date('2024-12-31')
      },
      {
        id: 'framework-ccpa-001',
        name: 'California Consumer Privacy Act (CCPA)',
        type: 'ccpa',
        jurisdiction: 'california',
        description: 'California state law intended to enhance privacy rights and consumer protection for residents of California',
        version: '2020.1',
        effectiveDate: new Date('2020-01-01'),
        lastUpdated: new Date('2024-01-10'),
        status: 'active',
        requirements: [
          {
            id: 'req-ccpa-001',
            title: 'Consumer Right to Know',
            description: 'Provide consumers with the right to know what personal information is collected',
            category: 'data_protection',
            priority: 'high',
            status: 'compliant',
            complianceScore: 90,
            evidence: ['privacy-policy.pdf', 'consumer-notices.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-06-01'),
            completedDate: new Date('2024-05-15'),
            notes: 'Privacy policy updated with CCPA disclosures',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-ccpa-001',
            title: 'Annual Privacy Policy Update',
            description: 'Update privacy policy to reflect current data practices',
            dueDate: new Date('2024-12-31'),
            status: 'upcoming',
            priority: 'medium',
            assignedTo: '<EMAIL>',
            requirements: ['req-ccpa-001'],
            notes: 'Annual review required'
          }
        ],
        applicableEntities: ['California operations', 'US customer data'],
        penalties: [
          {
            id: 'penalty-ccpa-001',
            violationType: 'Failure to provide required disclosures',
            description: 'Civil penalty for non-compliance with disclosure requirements',
            minPenalty: 2500,
            maxPenalty: 7500,
            currency: 'USD',
            additionalConsequences: ['Regulatory action', 'Consumer lawsuits']
          }
        ],
        references: [
          {
            id: 'ref-ccpa-001',
            title: 'CCPA Official Text',
            url: 'https://oag.ca.gov/privacy/ccpa',
            type: 'official_text',
            description: 'Official CCPA regulation text'
          }
        ],
        tags: ['privacy', 'california', 'consumer-rights'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>'],
        nextReviewDate: new Date('2024-12-31')
      },
      {
        id: 'framework-sox-001',
        name: 'Sarbanes-Oxley Act (SOX)',
        type: 'sox',
        jurisdiction: 'us',
        description: 'Federal law that established auditing and financial regulations for public companies',
        version: '2002.1',
        effectiveDate: new Date('2002-07-30'),
        lastUpdated: new Date('2024-01-05'),
        status: 'active',
        requirements: [
          {
            id: 'req-sox-001',
            title: 'Internal Controls Assessment',
            description: 'Establish and maintain adequate internal financial controls',
            category: 'governance',
            priority: 'critical',
            status: 'compliant',
            complianceScore: 95,
            evidence: ['internal-controls-report.pdf', 'audit-findings.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-03-31'),
            completedDate: new Date('2024-03-15'),
            notes: 'Annual assessment completed successfully',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-sox-001',
            title: 'Annual SOX Compliance Certification',
            description: 'CEO and CFO certification of financial statements',
            dueDate: new Date('2024-03-31'),
            status: 'completed',
            priority: 'critical',
            assignedTo: '<EMAIL>',
            requirements: ['req-sox-001'],
            completedDate: new Date('2024-03-15'),
            notes: 'Certification completed on time'
          }
        ],
        applicableEntities: ['Public company operations', 'Financial reporting'],
        penalties: [
          {
            id: 'penalty-sox-001',
            violationType: 'False certification',
            description: 'Criminal penalties for knowingly false certification',
            minPenalty: 1000000,
            maxPenalty: 5000000,
            currency: 'USD',
            additionalConsequences: ['Criminal prosecution', 'Imprisonment up to 20 years']
          }
        ],
        references: [
          {
            id: 'ref-sox-001',
            title: 'SOX Official Text',
            url: 'https://www.sec.gov/about/laws/soa2002.pdf',
            type: 'official_text',
            description: 'Official Sarbanes-Oxley Act text'
          }
        ],
        tags: ['financial-reporting', 'governance', 'public-companies'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>', '<EMAIL>'],
        nextReviewDate: new Date('2024-12-31')
      },
      {
        id: 'framework-ccpa-001',
        name: 'California Consumer Privacy Act (CCPA)',
        type: 'privacy',
        jurisdiction: 'us_california',
        description: 'California state law intended to enhance privacy rights and consumer protection for residents of California',
        version: '2020.1',
        effectiveDate: new Date('2020-01-01'),
        lastUpdated: new Date('2024-01-10'),
        status: 'active',
        requirements: [
          {
            id: 'req-ccpa-001',
            title: 'Consumer Rights Notice',
            description: 'Provide clear notice of consumer privacy rights',
            category: 'transparency',
            priority: 'high',
            status: 'compliant',
            complianceScore: 90,
            evidence: ['privacy-notice-ca.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-02-01'),
            completedDate: new Date('2024-01-25'),
            notes: 'California-specific privacy notice implemented',
            subRequirements: [],
            controls: []
          },
          {
            id: 'req-ccpa-002',
            title: 'Data Sale Opt-Out',
            description: 'Implement mechanism for consumers to opt-out of data sales',
            category: 'consent',
            priority: 'critical',
            status: 'compliant',
            complianceScore: 95,
            evidence: ['opt-out-mechanism.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-01-15'),
            completedDate: new Date('2024-01-10'),
            notes: 'Do Not Sell My Personal Information link implemented',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-ccpa-001',
            title: 'Annual CCPA Compliance Review',
            description: 'Review CCPA compliance measures and update procedures',
            dueDate: new Date('2024-12-31'),
            status: 'pending',
            assignedTo: '<EMAIL>',
            priority: 'medium'
          }
        ],
        auditTrail: [
          {
            id: 'audit-ccpa-001',
            action: 'framework_updated',
            timestamp: new Date('2024-01-10'),
            user: '<EMAIL>',
            details: 'Updated CCPA compliance requirements',
            changes: ['Added new consumer rights notice requirement']
          }
        ],
        tags: ['privacy', 'california', 'consumer-rights'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>', '<EMAIL>'],
        nextReviewDate: new Date('2024-06-15')
      },
      {
        id: 'framework-iso27001-001',
        name: 'ISO 27001 Information Security Management',
        type: 'security',
        jurisdiction: 'international',
        description: 'International standard for information security management systems',
        version: '2013.1',
        effectiveDate: new Date('2013-10-01'),
        lastUpdated: new Date('2024-02-01'),
        status: 'active',
        requirements: [
          {
            id: 'req-iso27001-001',
            title: 'Information Security Policy',
            description: 'Establish and maintain information security policy',
            category: 'governance',
            priority: 'high',
            status: 'compliant',
            complianceScore: 95,
            evidence: ['info-sec-policy.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-01-31'),
            completedDate: new Date('2024-01-15'),
            notes: 'Information security policy updated and approved',
            subRequirements: [],
            controls: []
          },
          {
            id: 'req-iso27001-002',
            title: 'Risk Assessment Process',
            description: 'Implement systematic information security risk assessment',
            category: 'risk_management',
            priority: 'critical',
            status: 'in_progress',
            complianceScore: 80,
            evidence: ['risk-assessment-procedure.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-04-30'),
            notes: 'Risk assessment framework being implemented',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-iso27001-001',
            title: 'Annual Management Review',
            description: 'Conduct annual management review of ISMS',
            dueDate: new Date('2024-10-01'),
            status: 'pending',
            assignedTo: '<EMAIL>',
            priority: 'high'
          }
        ],
        auditTrail: [
          {
            id: 'audit-iso27001-001',
            action: 'policy_update',
            timestamp: new Date('2024-02-01'),
            user: '<EMAIL>',
            details: 'Updated information security policy',
            changes: ['Added cloud security requirements']
          }
        ],
        tags: ['security', 'iso', 'isms'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>', '<EMAIL>'],
        nextReviewDate: new Date('2024-10-01')
      },
      {
        id: 'framework-dpdp-001',
        name: 'Data Protection and Digital Privacy Act (DPDP)',
        type: 'dpdp',
        jurisdiction: 'india',
        description: 'Indian data protection law that regulates the processing of digital personal data',
        version: '2023.1',
        effectiveDate: new Date('2024-01-01'),
        lastUpdated: new Date('2024-01-15'),
        status: 'active',
        requirements: [
          {
            id: 'req-dpdp-001',
            title: 'Data Protection Impact Assessment',
            description: 'Conduct impact assessment for high-risk data processing',
            category: 'data_protection',
            priority: 'high',
            status: 'compliant',
            complianceScore: 90,
            evidence: ['dpdp-impact-assessment.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-03-31'),
            completedDate: new Date('2024-03-15'),
            notes: 'DPDP impact assessment completed and approved',
            subRequirements: [],
            controls: []
          },
          {
            id: 'req-dpdp-002',
            title: 'Consent Management',
            description: 'Implement valid consent mechanisms for data processing',
            category: 'consent',
            priority: 'critical',
            status: 'in_progress',
            complianceScore: 85,
            evidence: ['consent-framework.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-06-30'),
            notes: 'Consent management system being implemented',
            subRequirements: [],
            controls: []
          },
          {
            id: 'req-dpdp-003',
            title: 'Data Breach Notification',
            description: 'Establish procedures for data breach notification to authorities',
            category: 'breach_notification',
            priority: 'high',
            status: 'compliant',
            complianceScore: 95,
            evidence: ['breach-notification-procedure.pdf'],
            assignedTo: '<EMAIL>',
            dueDate: new Date('2024-02-28'),
            completedDate: new Date('2024-02-15'),
            notes: 'Breach notification procedures established',
            subRequirements: [],
            controls: []
          }
        ],
        complianceDeadlines: [
          {
            id: 'deadline-dpdp-001',
            title: 'Annual Privacy Policy Review',
            description: 'Review and update privacy policy for DPDP compliance',
            dueDate: new Date('2024-12-31'),
            status: 'upcoming',
            priority: 'high',
            assignedTo: '<EMAIL>',
            requirements: ['req-dpdp-001', 'req-dpdp-002'],
            notes: 'Annual review required by DPDP Act'
          }
        ],
        applicableEntities: ['Indian subsidiaries', 'Data processing operations in India'],
        penalties: [
          {
            id: 'penalty-dpdp-001',
            violationType: 'Data breach notification failure',
            description: 'Failure to notify data breach to Data Protection Board',
            minPenalty: 5000000,
            maxPenalty: 25000000,
            currency: 'INR',
            additionalConsequences: ['Regulatory investigation', 'Business restrictions']
          }
        ],
        references: [
          {
            id: 'ref-dpdp-001',
            title: 'DPDP Act 2023',
            url: 'https://www.meity.gov.in/dpdp-act-2023',
            type: 'legislation'
          }
        ],
        auditTrail: [
          {
            id: 'audit-dpdp-001',
            action: 'framework_creation',
            timestamp: new Date('2024-01-15'),
            user: '<EMAIL>',
            details: 'Created DPDP Act compliance framework',
            changes: ['Initial framework setup']
          }
        ],
        tags: ['privacy', 'data-protection', 'india', 'dpdp'],
        owner: '<EMAIL>',
        reviewers: ['<EMAIL>', '<EMAIL>'],
        nextReviewDate: new Date('2024-12-31')
      }
    ];
  }
}
