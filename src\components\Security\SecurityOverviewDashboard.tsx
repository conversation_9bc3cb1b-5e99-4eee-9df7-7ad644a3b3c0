import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import { SecurityOverviewService } from '../../services/securityOverviewService';
import {
  Shield,
  AlertTriangle,
  Search,
  Filter,
  RefreshCw,
  Download,
  Settings,
  Clock,
  Users,
  Building,
  Target,
  Activity,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  XCircle,
  AlertCircle,
  Zap,
  Lock,
  Unlock,
  Key,
  FileText,
  Bell,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Layers,
  History,
  User,
  MapPin,
  Calendar,
  Play,
  Pause,
  RotateCcw,
  ExternalLink,
  Bug,
  Wifi,
  Server,
  Database,
  Globe,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  CheckCircle2,
  Info,
  Warning,
  Crosshair,
  Network,
  HardDrive,
  Monitor,
  Smartphone
} from 'lucide-react';

// Enhanced Security Types with comprehensive CRUD functionality
interface SecurityThreat {
  id: string;
  name: string;
  type: 'malware' | 'phishing' | 'ddos' | 'intrusion' | 'data_breach' | 'insider_threat' | 'ransomware' | 'apt' | 'social_engineering';
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'investigating' | 'contained' | 'resolved' | 'false_positive';
  detectedAt: Date;
  source: string;
  targetAsset: string;
  affectedSystems: string[];
  riskScore: number; // 1-100
  investigator?: string;
  resolutionTime?: number; // minutes
  confidence: number; // 0-100
  threatActor?: string;
  attackVector: string;
  geolocation?: {
    country: string;
    city: string;
    ip: string;
  };
  mitigationActions: Array<{
    id: string;
    action: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignee: string;
    createdAt: Date;
    completedAt?: Date;
    notes?: string;
  }>;
  timeline: Array<{
    timestamp: Date;
    event: string;
    user: string;
    details?: string;
  }>;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface SecurityMetric {
  id: string;
  name: string;
  category: 'access_control' | 'network_security' | 'data_protection' | 'endpoint_security' | 'compliance';
  currentValue: number;
  targetValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'compliant' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    incidents: number;
  }>;
}

interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  category: 'malware' | 'phishing' | 'data_breach' | 'network_attack' | 'insider_threat' | 'system_compromise' | 'ransomware' | 'ddos' | 'unauthorized_access';
  reportedAt: Date;
  reportedBy: string;
  assignedTo?: string;
  resolvedAt?: Date;
  affectedAssets: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  source: 'siem' | 'edr' | 'manual' | 'automated' | 'user_report' | 'third_party';
  impactAssessment: {
    dataCompromised: boolean;
    systemsAffected: number;
    usersImpacted: number;
    financialImpact?: number;
    reputationalRisk: 'low' | 'medium' | 'high';
    businessImpact: 'minimal' | 'moderate' | 'significant' | 'severe';
    confidentialityImpact: 'none' | 'low' | 'medium' | 'high';
    integrityImpact: 'none' | 'low' | 'medium' | 'high';
    availabilityImpact: 'none' | 'low' | 'medium' | 'high';
  };
  responseActions: Array<{
    id: string;
    action: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignee: string;
    dueDate: Date;
    createdAt: Date;
    completedAt?: Date;
    notes?: string;
    priority: 'low' | 'medium' | 'high' | 'critical';
  }>;
  timeline: Array<{
    timestamp: Date;
    event: string;
    user: string;
    details?: string;
    attachments?: string[];
  }>;
  evidence: Array<{
    id: string;
    type: 'log' | 'screenshot' | 'file' | 'network_capture' | 'forensic_image';
    name: string;
    path: string;
    collectedAt: Date;
    collectedBy: string;
    hash?: string;
  }>;
  tags: string[];
  relatedIncidents: string[];
  communicationLog: Array<{
    timestamp: Date;
    type: 'internal' | 'external' | 'regulatory' | 'customer';
    recipient: string;
    message: string;
    sentBy: string;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

interface SecurityCompliance {
  id: string;
  framework: string;
  version: string;
  description: string;
  overallScore: number; // 0-100
  status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'under_review';
  lastAssessment: Date;
  nextAssessment: Date;
  assessor: string;
  controls: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
    status: 'implemented' | 'partially_implemented' | 'not_implemented' | 'not_applicable';
    score: number;
    evidence: string[];
    lastReview: Date;
    reviewer: string;
    findings: Array<{
      id: string;
      type: 'gap' | 'weakness' | 'improvement' | 'compliant';
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      remediation?: string;
      dueDate?: Date;
      assignee?: string;
      status: 'open' | 'in_progress' | 'resolved';
    }>;
  }>;
  auditTrail: Array<{
    timestamp: Date;
    action: string;
    user: string;
    details: string;
    previousScore?: number;
    newScore?: number;
  }>;
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface SecurityActivity {
  id: string;
  timestamp: Date;
  type: 'threat_detected' | 'incident_created' | 'incident_resolved' | 'compliance_updated' | 'system_alert' | 'user_action' | 'policy_change';
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  source: string;
  user?: string;
  affectedSystems: string[];
  relatedEntities: Array<{
    type: 'threat' | 'incident' | 'compliance' | 'user' | 'system';
    id: string;
    name: string;
  }>;
  status: 'active' | 'acknowledged' | 'resolved' | 'dismissed';
  tags: string[];
  metadata: Record<string, any>;
}

interface SecurityOverviewData {
  overview: {
    totalThreats: number;
    activeThreats: number;
    resolvedThreats: number;
    criticalThreats: number;
    averageResolutionTime: number; // minutes
    securityScore: number; // 1-100
    lastScan: Date;
    nextScheduledScan: Date;
    systemsMonitored: number;
    alertsLast24h: number;
  };
  threats: SecurityThreat[];
  metrics: SecurityMetric[];
  incidents: SecurityIncident[];
  compliance: SecurityCompliance[];
  activities: SecurityActivity[];
  complianceStatus: {
    iso27001: { score: number; status: string; lastAudit: Date };
    nist: { score: number; status: string; lastAudit: Date };
    gdpr: { score: number; status: string; lastAudit: Date };
    sox: { score: number; status: string; lastAudit: Date };
    pci: { score: number; status: string; lastAudit: Date };
    hipaa: { score: number; status: string; lastAudit: Date };
  };
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    activeScans: number;
    pendingInvestigations: number;
  };
}

interface SecurityOverviewDashboardProps {
  className?: string;
}

// Comprehensive mock data generator
const generateSecurityOverviewData = (): SecurityOverviewData => {
  const threats: SecurityThreat[] = [
    {
      id: 'threat-001',
      name: 'Advanced Persistent Threat Campaign',
      type: 'apt',
      severity: 'critical',
      status: 'investigating',
      detectedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      source: 'Threat Intelligence Feed',
      targetAsset: 'Financial Database',
      affectedSystems: ['Database Server', 'Web Application', 'File Server'],
      riskScore: 95,
      confidence: 87,
      investigator: 'Senior Security Analyst',
      threatActor: 'APT29 (Cozy Bear)',
      attackVector: 'Spear phishing with malicious attachment',
      geolocation: {
        country: 'Russia',
        city: 'Moscow',
        ip: '**************'
      },
      mitigationActions: [
        {
          id: 'mit-001',
          action: 'Isolate affected systems from network',
          status: 'completed',
          assignee: 'Network Security Team',
          createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          notes: 'Successfully isolated all affected systems'
        },
        {
          id: 'mit-002',
          action: 'Deploy additional monitoring on critical assets',
          status: 'in_progress',
          assignee: 'SOC Team',
          createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          notes: 'Enhanced monitoring rules deployed'
        },
        {
          id: 'mit-003',
          action: 'Coordinate with threat intelligence partners',
          status: 'pending',
          assignee: 'Threat Intelligence Analyst',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
          event: 'Threat detected by SIEM correlation rules',
          user: 'SIEM System',
          details: 'Multiple indicators matched APT29 TTPs'
        },
        {
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000),
          event: 'Incident escalated to Tier 2 analyst',
          user: 'SOC Analyst L1',
          details: 'High confidence threat actor attribution'
        },
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          event: 'Containment actions initiated',
          user: 'Senior Security Analyst',
          details: 'Network isolation and enhanced monitoring deployed'
        }
      ],
      tags: ['apt', 'nation-state', 'financial-sector', 'high-priority'],
      createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: 'threat-002',
      name: 'Ransomware Attack Attempt',
      type: 'ransomware',
      severity: 'critical',
      status: 'contained',
      detectedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      source: 'EDR System',
      targetAsset: 'File Server Cluster',
      affectedSystems: ['File Server 01', 'File Server 02', 'Backup System'],
      riskScore: 98,
      confidence: 95,
      investigator: 'Incident Response Team',
      resolutionTime: 180,
      threatActor: 'REvil Ransomware Group',
      attackVector: 'Compromised RDP credentials',
      geolocation: {
        country: 'Unknown',
        city: 'Unknown',
        ip: '**************'
      },
      mitigationActions: [
        {
          id: 'mit-003',
          action: 'Isolate affected file servers',
          status: 'completed',
          assignee: 'Network Operations',
          createdAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          notes: 'All affected servers isolated from network'
        },
        {
          id: 'mit-004',
          action: 'Initiate backup restoration process',
          status: 'completed',
          assignee: 'Backup Administrator',
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          notes: 'Clean backups restored successfully'
        },
        {
          id: 'mit-005',
          action: 'Reset all RDP credentials',
          status: 'completed',
          assignee: 'System Administrator',
          createdAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          notes: 'All RDP credentials reset and MFA enforced'
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          event: 'Ransomware behavior detected by EDR',
          user: 'EDR System',
          details: 'File encryption activity detected on multiple servers'
        },
        {
          timestamp: new Date(Date.now() - 5 * 60 * 60 * 1000),
          event: 'Emergency response team activated',
          user: 'SOC Manager',
          details: 'Critical incident declared, all hands on deck'
        },
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          event: 'Systems restored from backup',
          user: 'Incident Commander',
          details: 'All critical systems restored, business operations resumed'
        }
      ],
      tags: ['ransomware', 'critical-infrastructure', 'business-impact', 'contained'],
      createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: 'threat-003',
      name: 'Sophisticated Phishing Campaign Targeting Executives',
      type: 'phishing',
      severity: 'high',
      status: 'resolved',
      detectedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
      source: 'Email Security Gateway',
      targetAsset: 'Executive Email Accounts',
      affectedSystems: ['Email Server', 'Active Directory', 'VPN Gateway'],
      riskScore: 78,
      confidence: 92,
      investigator: 'Email Security Team',
      resolutionTime: 240,
      threatActor: 'Business Email Compromise Group',
      attackVector: 'Spear phishing with CEO impersonation',
      geolocation: {
        country: 'Nigeria',
        city: 'Lagos',
        ip: '*************'
      },
      mitigationActions: [
        {
          id: 'mit-006',
          action: 'Block malicious sender domains',
          status: 'completed',
          assignee: 'Email Security Admin',
          createdAt: new Date(Date.now() - 11 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 10 * 60 * 60 * 1000),
          notes: '15 malicious domains blocked'
        },
        {
          id: 'mit-007',
          action: 'Conduct security awareness training',
          status: 'completed',
          assignee: 'Security Training Team',
          createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          notes: 'Emergency phishing awareness session conducted'
        },
        {
          id: 'mit-008',
          action: 'Implement additional email filters',
          status: 'completed',
          assignee: 'Email Security Team',
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
          notes: 'Enhanced CEO impersonation detection rules deployed'
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
          event: 'Phishing campaign detected by email security',
          user: 'Email Security Gateway',
          details: 'Multiple suspicious emails targeting C-level executives'
        },
        {
          timestamp: new Date(Date.now() - 10 * 60 * 60 * 1000),
          event: 'Threat intelligence correlation completed',
          user: 'Threat Intelligence Analyst',
          details: 'Campaign linked to known BEC group'
        },
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          event: 'All mitigation actions completed',
          user: 'Security Team Lead',
          details: 'Campaign successfully blocked, no successful compromises'
        }
      ],
      tags: ['phishing', 'bec', 'executive-targeting', 'resolved'],
      createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: 'threat-004',
      name: 'Insider Threat - Unauthorized Data Exfiltration',
      type: 'insider_threat',
      severity: 'critical',
      status: 'investigating',
      detectedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      source: 'Data Loss Prevention System',
      targetAsset: 'Customer Database',
      affectedSystems: ['Database Server', 'API Gateway', 'File Transfer System'],
      riskScore: 92,
      confidence: 88,
      investigator: 'Internal Investigation Team',
      threatActor: 'Internal Employee',
      attackVector: 'Privileged access abuse',
      mitigationActions: [
        {
          id: 'mit-009',
          action: 'Suspend user account access',
          status: 'completed',
          assignee: 'Identity Management Team',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 45 * 60 * 1000),
          notes: 'User account suspended pending investigation'
        },
        {
          id: 'mit-010',
          action: 'Forensic analysis of user activities',
          status: 'in_progress',
          assignee: 'Digital Forensics Team',
          createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
          notes: 'Analyzing 30 days of user activity logs'
        },
        {
          id: 'mit-011',
          action: 'Legal consultation for HR action',
          status: 'pending',
          assignee: 'Legal Department',
          createdAt: new Date(Date.now() - 30 * 60 * 1000)
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
          event: 'Unusual data access pattern detected',
          user: 'DLP System',
          details: 'Employee accessed 10x normal amount of customer records'
        },
        {
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000),
          event: 'Investigation initiated',
          user: 'Security Manager',
          details: 'High-priority insider threat investigation opened'
        },
        {
          timestamp: new Date(Date.now() - 45 * 60 * 1000),
          event: 'Immediate containment actions taken',
          user: 'Incident Response Team',
          details: 'User access suspended, forensic preservation initiated'
        }
      ],
      tags: ['insider-threat', 'data-exfiltration', 'high-priority', 'under-investigation'],
      createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 15 * 60 * 1000)
    },
    {
      id: 'threat-005',
      name: 'DDoS Attack on Web Services',
      type: 'ddos',
      severity: 'high',
      status: 'resolved',
      detectedAt: new Date(Date.now() - 18 * 60 * 60 * 1000),
      source: 'Network Monitoring System',
      targetAsset: 'Web Application',
      affectedSystems: ['Load Balancer', 'Web Servers', 'CDN'],
      riskScore: 75,
      confidence: 98,
      investigator: 'Network Security Team',
      resolutionTime: 45,
      threatActor: 'Unknown Botnet',
      attackVector: 'Volumetric DDoS attack',
      geolocation: {
        country: 'Multiple',
        city: 'Global',
        ip: 'Multiple IPs'
      },
      mitigationActions: [
        {
          id: 'mit-012',
          action: 'Activate DDoS protection service',
          status: 'completed',
          assignee: 'Network Operations',
          createdAt: new Date(Date.now() - 17 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 17 * 60 * 60 * 1000),
          notes: 'CDN DDoS protection activated automatically'
        },
        {
          id: 'mit-013',
          action: 'Implement rate limiting',
          status: 'completed',
          assignee: 'DevOps Team',
          createdAt: new Date(Date.now() - 17 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 16 * 60 * 60 * 1000),
          notes: 'Aggressive rate limiting deployed'
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000),
          event: 'DDoS attack detected',
          user: 'Network Monitoring',
          details: 'Traffic spike to 50x normal levels detected'
        },
        {
          timestamp: new Date(Date.now() - 17 * 60 * 60 * 1000),
          event: 'Mitigation measures activated',
          user: 'Network Operations',
          details: 'DDoS protection and rate limiting deployed'
        },
        {
          timestamp: new Date(Date.now() - 17 * 60 * 60 * 1000),
          event: 'Attack successfully mitigated',
          user: 'Network Security Team',
          details: 'Normal traffic levels restored'
        }
      ],
      tags: ['ddos', 'network-attack', 'resolved', 'automated-mitigation'],
      createdAt: new Date(Date.now() - 18 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 17 * 60 * 60 * 1000)
    }
  ];

  const metrics: SecurityMetric[] = [
    {
      id: 'metric-001',
      name: 'Password Compliance Rate',
      category: 'access_control',
      currentValue: 94.2,
      targetValue: 98.0,
      unit: '%',
      trend: 'up',
      status: 'warning',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 90 + Math.random() * 8,
        incidents: Math.floor(Math.random() * 5)
      }))
    },
    {
      id: 'metric-002',
      name: 'Firewall Block Rate',
      category: 'network_security',
      currentValue: 99.7,
      targetValue: 99.5,
      unit: '%',
      trend: 'stable',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 5 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 98 + Math.random() * 2,
        incidents: Math.floor(Math.random() * 3)
      }))
    },
    {
      id: 'metric-003',
      name: 'Data Encryption Coverage',
      category: 'data_protection',
      currentValue: 87.5,
      targetValue: 95.0,
      unit: '%',
      trend: 'down',
      status: 'critical',
      lastUpdated: new Date(Date.now() - 10 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 85 + Math.random() * 10,
        incidents: Math.floor(Math.random() * 8)
      }))
    }
  ];

  const incidents: SecurityIncident[] = [
    {
      id: 'incident-001',
      title: 'Critical Data Breach - Customer Database Compromise',
      description: 'Unauthorized access to customer database containing PII and financial information',
      severity: 'critical',
      status: 'investigating',
      category: 'data_breach',
      reportedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
      reportedBy: 'Database Administrator',
      assignedTo: 'Incident Response Team Lead',
      resolvedAt: undefined,
      affectedAssets: ['Customer Database', 'API Gateway', 'Web Application'],
      priority: 'critical',
      source: 'manual',
      impactAssessment: {
        dataCompromised: true,
        systemsAffected: 3,
        usersImpacted: 15000,
        financialImpact: 500000,
        reputationalRisk: 'high',
        businessImpact: 'severe',
        confidentialityImpact: 'high',
        integrityImpact: 'medium',
        availabilityImpact: 'low'
      },
      responseActions: [
        {
          id: 'action-001',
          action: 'Isolate affected database servers',
          status: 'completed',
          assignee: 'Network Security Team',
          dueDate: new Date(Date.now() - 7 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 7 * 60 * 60 * 1000),
          notes: 'All affected database servers isolated from network',
          priority: 'critical'
        },
        {
          id: 'action-002',
          action: 'Notify regulatory authorities',
          status: 'in_progress',
          assignee: 'Legal Compliance Team',
          dueDate: new Date(Date.now() + 16 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          notes: 'GDPR breach notification in progress',
          priority: 'high'
        },
        {
          id: 'action-003',
          action: 'Forensic analysis of breach scope',
          status: 'in_progress',
          assignee: 'Digital Forensics Team',
          dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
          notes: 'Analyzing extent of data compromise',
          priority: 'high'
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
          event: 'Suspicious database activity detected',
          user: 'Database Monitoring System',
          details: 'Unusual data export activity detected on customer database'
        },
        {
          timestamp: new Date(Date.now() - 7 * 60 * 60 * 1000),
          event: 'Incident escalated to critical',
          user: 'Security Manager',
          details: 'Confirmed unauthorized access to sensitive customer data'
        },
        {
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          event: 'Executive leadership notified',
          user: 'Incident Commander',
          details: 'CEO and CISO briefed on breach scope and response'
        }
      ],
      evidence: [
        {
          id: 'evidence-001',
          type: 'log',
          name: 'Database Access Logs',
          path: '/evidence/db_access_logs_20240216.log',
          collectedAt: new Date(Date.now() - 7 * 60 * 60 * 1000),
          collectedBy: 'Forensics Team',
          hash: 'sha256:a1b2c3d4e5f6...'
        },
        {
          id: 'evidence-002',
          type: 'network_capture',
          name: 'Network Traffic Capture',
          path: '/evidence/network_capture_20240216.pcap',
          collectedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
          collectedBy: 'Network Security Team',
          hash: 'sha256:f6e5d4c3b2a1...'
        }
      ],
      tags: ['data-breach', 'gdpr', 'customer-data', 'critical-incident'],
      relatedIncidents: [],
      communicationLog: [
        {
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          type: 'regulatory',
          recipient: 'Data Protection Authority',
          message: 'Initial breach notification submitted',
          sentBy: 'Legal Compliance Team'
        }
      ],
      createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
    },
    {
      id: 'incident-002',
      title: 'Malware Outbreak - Endpoint Compromise',
      description: 'Multiple workstations infected with banking trojan malware',
      severity: 'high',
      status: 'contained',
      category: 'malware',
      reportedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      reportedBy: 'Endpoint Security System',
      assignedTo: 'Malware Response Team',
      resolvedAt: new Date(Date.now() - 18 * 60 * 60 * 1000),
      affectedAssets: ['Workstation-001', 'Workstation-015', 'Workstation-032', 'File Server'],
      priority: 'high',
      source: 'edr',
      impactAssessment: {
        dataCompromised: false,
        systemsAffected: 4,
        usersImpacted: 3,
        financialImpact: 25000,
        reputationalRisk: 'low',
        businessImpact: 'moderate',
        confidentialityImpact: 'low',
        integrityImpact: 'medium',
        availabilityImpact: 'medium'
      },
      responseActions: [
        {
          id: 'action-004',
          action: 'Isolate infected workstations',
          status: 'completed',
          assignee: 'IT Support Team',
          dueDate: new Date(Date.now() - 23 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 23 * 60 * 60 * 1000),
          notes: 'All infected workstations isolated and powered down',
          priority: 'critical'
        },
        {
          id: 'action-005',
          action: 'Deploy malware removal tools',
          status: 'completed',
          assignee: 'Security Operations',
          dueDate: new Date(Date.now() - 22 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 23 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 20 * 60 * 60 * 1000),
          notes: 'Malware successfully removed from all systems',
          priority: 'high'
        },
        {
          id: 'action-006',
          action: 'Update endpoint protection signatures',
          status: 'completed',
          assignee: 'Endpoint Security Team',
          dueDate: new Date(Date.now() - 21 * 60 * 60 * 1000),
          createdAt: new Date(Date.now() - 22 * 60 * 60 * 1000),
          completedAt: new Date(Date.now() - 19 * 60 * 60 * 1000),
          notes: 'All endpoint protection systems updated with new signatures',
          priority: 'medium'
        }
      ],
      timeline: [
        {
          timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
          event: 'Malware detected on multiple endpoints',
          user: 'EDR System',
          details: 'Banking trojan detected on 3 workstations'
        },
        {
          timestamp: new Date(Date.now() - 23 * 60 * 60 * 1000),
          event: 'Containment procedures initiated',
          user: 'Security Operations Center',
          details: 'Infected systems isolated from network'
        },
        {
          timestamp: new Date(Date.now() - 18 * 60 * 60 * 1000),
          event: 'Incident resolved',
          user: 'Incident Response Team',
          details: 'All systems cleaned and returned to service'
        }
      ],
      evidence: [
        {
          id: 'evidence-003',
          type: 'file',
          name: 'Malware Sample',
          path: '/evidence/malware_sample_20240215.exe',
          collectedAt: new Date(Date.now() - 23 * 60 * 60 * 1000),
          collectedBy: 'Malware Analyst',
          hash: 'sha256:1a2b3c4d5e6f...'
        }
      ],
      tags: ['malware', 'banking-trojan', 'endpoint-security', 'resolved'],
      relatedIncidents: [],
      communicationLog: [
        {
          timestamp: new Date(Date.now() - 20 * 60 * 60 * 1000),
          type: 'internal',
          recipient: 'All Staff',
          message: 'Security awareness reminder sent regarding malware prevention',
          sentBy: 'Security Team'
        }
      ],
      createdAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 18 * 60 * 60 * 1000)
    }
  ];

  const activeThreats = threats.filter(t => ['detected', 'investigating', 'contained'].includes(t.status)).length;
  const resolvedThreats = threats.filter(t => ['resolved', 'false_positive'].includes(t.status)).length;
  const criticalThreats = threats.filter(t => t.severity === 'critical').length;
  const averageResolutionTime = threats
    .filter(t => t.resolutionTime)
    .reduce((sum, t) => sum + (t.resolutionTime || 0), 0) / threats.filter(t => t.resolutionTime).length || 0;

  // Generate comprehensive compliance data
  const compliance: SecurityCompliance[] = [
    {
      id: 'comp-001',
      framework: 'ISO 27001',
      version: '2013',
      description: 'Information Security Management System',
      overallScore: 92.5,
      status: 'compliant',
      lastAssessment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      nextAssessment: new Date(Date.now() + 335 * 24 * 60 * 60 * 1000),
      assessor: 'External Auditor - CyberSec Ltd',
      controls: [
        {
          id: 'ctrl-001',
          name: 'Access Control Policy',
          description: 'Formal access control policy implemented and maintained',
          category: 'Access Control',
          status: 'implemented',
          score: 95,
          evidence: ['policy-doc-001.pdf', 'access-review-2024.xlsx'],
          lastReview: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
          reviewer: 'Security Manager',
          findings: [
            {
              id: 'find-001',
              type: 'compliant',
              description: 'Access control policy fully implemented and regularly reviewed',
              severity: 'low',
              status: 'resolved'
            }
          ]
        }
      ],
      auditTrail: [
        {
          timestamp: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          action: 'Annual assessment completed',
          user: 'External Auditor',
          details: 'ISO 27001 compliance assessment completed with 92.5% score',
          previousScore: 89.2,
          newScore: 92.5
        }
      ],
      tags: ['iso27001', 'information-security', 'annual-assessment'],
      createdAt: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    },
    {
      id: 'comp-002',
      framework: 'NIST Cybersecurity Framework',
      version: '1.1',
      description: 'Comprehensive cybersecurity risk management framework',
      overallScore: 88.7,
      status: 'partially_compliant',
      lastAssessment: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      nextAssessment: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      assessor: 'Internal Security Team',
      controls: [
        {
          id: 'ctrl-002',
          name: 'Incident Response Plan',
          description: 'Formal incident response procedures and team structure',
          category: 'Respond',
          status: 'partially_implemented',
          score: 78,
          evidence: ['ir-plan-v2.pdf', 'ir-training-records.xlsx'],
          lastReview: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
          reviewer: 'CISO',
          findings: [
            {
              id: 'find-002',
              type: 'gap',
              description: 'Incident response plan needs update for cloud environments',
              severity: 'medium',
              remediation: 'Update IR plan to include cloud-specific procedures',
              dueDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
              assignee: 'Security Architect',
              status: 'in_progress'
            }
          ]
        }
      ],
      auditTrail: [
        {
          timestamp: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          action: 'Quarterly assessment completed',
          user: 'Security Manager',
          details: 'NIST CSF assessment completed with identified gaps',
          previousScore: 85.3,
          newScore: 88.7
        }
      ],
      tags: ['nist', 'cybersecurity-framework', 'quarterly-assessment'],
      createdAt: new Date(Date.now() - 180 * 24 * 60 * 60 * 1000),
      updatedAt: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000)
    }
  ];

  // Generate comprehensive security activities
  const activities: SecurityActivity[] = [
    {
      id: 'activity-001',
      timestamp: new Date(Date.now() - 30 * 60 * 1000),
      type: 'threat_detected',
      severity: 'high',
      title: 'New APT Campaign Detected',
      description: 'Advanced persistent threat targeting financial sector detected',
      source: 'Threat Intelligence Platform',
      user: 'Threat Intelligence Analyst',
      affectedSystems: ['Financial Database', 'Web Application'],
      relatedEntities: [
        { type: 'threat', id: 'threat-001', name: 'Advanced Persistent Threat Campaign' }
      ],
      status: 'active',
      tags: ['apt', 'financial-sector', 'high-priority'],
      metadata: {
        threatActor: 'APT29',
        confidence: 87,
        riskScore: 95
      }
    },
    {
      id: 'activity-002',
      timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
      type: 'incident_resolved',
      severity: 'medium',
      title: 'Malware Incident Successfully Resolved',
      description: 'Banking trojan malware outbreak contained and resolved',
      source: 'Incident Response System',
      user: 'Incident Response Team',
      affectedSystems: ['Workstation-001', 'Workstation-015', 'Workstation-032'],
      relatedEntities: [
        { type: 'incident', id: 'incident-002', name: 'Malware Outbreak - Endpoint Compromise' }
      ],
      status: 'resolved',
      tags: ['malware', 'endpoint-security', 'resolved'],
      metadata: {
        resolutionTime: 360,
        systemsCleaned: 4,
        usersAffected: 3
      }
    },
    {
      id: 'activity-003',
      timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
      type: 'compliance_updated',
      severity: 'info',
      title: 'ISO 27001 Compliance Score Updated',
      description: 'Annual ISO 27001 assessment completed with improved score',
      source: 'Compliance Management System',
      user: 'Compliance Manager',
      affectedSystems: ['Compliance Dashboard'],
      relatedEntities: [
        { type: 'compliance', id: 'comp-001', name: 'ISO 27001' }
      ],
      status: 'acknowledged',
      tags: ['iso27001', 'compliance', 'assessment'],
      metadata: {
        previousScore: 89.2,
        newScore: 92.5,
        improvement: 3.3
      }
    }
  ];

  return {
    overview: {
      totalThreats: threats.length,
      activeThreats,
      resolvedThreats,
      criticalThreats,
      averageResolutionTime,
      securityScore: 87.3,
      lastScan: new Date(Date.now() - 30 * 60 * 1000),
      nextScheduledScan: new Date(Date.now() + 90 * 60 * 1000),
      systemsMonitored: 247,
      alertsLast24h: 23
    },
    threats,
    metrics,
    incidents,
    compliance,
    activities,
    complianceStatus: {
      iso27001: { score: 92.1, status: 'compliant', lastAudit: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) },
      nist: { score: 88.7, status: 'warning', lastAudit: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000) },
      gdpr: { score: 95.2, status: 'compliant', lastAudit: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000) },
      sox: { score: 91.8, status: 'compliant', lastAudit: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000) },
      pci: { score: 91.8, status: 'compliant', lastAudit: new Date(Date.now() - 120 * 24 * 60 * 60 * 1000) },
      hipaa: { score: 87.3, status: 'partially_compliant', lastAudit: new Date(Date.now() - 75 * 24 * 60 * 60 * 1000) }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      activeScans: Math.floor(Math.random() * 5),
      pendingInvestigations: Math.floor(Math.random() * 8)
    }
  };
};

export const SecurityOverviewDashboard: React.FC<SecurityOverviewDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<SecurityOverviewData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedThreatType, setSelectedThreatType] = useState<'all' | 'malware' | 'phishing' | 'ddos' | 'intrusion' | 'data_breach' | 'insider_threat' | 'ransomware' | 'apt' | 'social_engineering'>('all');
  const [selectedSeverity, setSelectedSeverity] = useState<'all' | 'low' | 'medium' | 'high' | 'critical'>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'detected' | 'investigating' | 'contained' | 'resolved' | 'false_positive'>('all');
  const [expandedThreat, setExpandedThreat] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'threats' | 'incidents' | 'compliance'>('overview');
  const [searchTerm, setSearchTerm] = useState('');

  // Enhanced state for CRUD operations
  const [selectedThreat, setSelectedThreat] = useState<SecurityThreat | null>(null);
  const [selectedIncident, setSelectedIncident] = useState<SecurityIncident | null>(null);
  const [selectedCompliance, setSelectedCompliance] = useState<SecurityCompliance | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('view');
  const [sortField, setSortField] = useState<string>('detectedAt');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterDateRange, setFilterDateRange] = useState<'24h' | '7d' | '30d' | '90d'>('30d');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await new Promise(resolve => setTimeout(resolve, 1200));
        const data = generateSecurityOverviewData();
        if (!data) {
          throw new Error('No data generated');
        }
        setDashboardData(data);
        setError(null);
      } catch (err) {
        console.error('Security Overview Dashboard data loading error:', err);
        setError('Failed to load security overview data');
        setDashboardData(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          overview: {
            ...prevData.overview,
            alertsLast24h: Math.max(0, prevData.overview.alertsLast24h + Math.floor(Math.random() * 3) - 1)
          },
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            activeScans: Math.floor(Math.random() * 5),
            pendingInvestigations: Math.floor(Math.random() * 8)
          }
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter threats based on criteria
  const filteredThreats = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.threats.filter(threat => {
      const matchesType = selectedThreatType === 'all' || threat.type === selectedThreatType;
      const matchesSeverity = selectedSeverity === 'all' || threat.severity === selectedSeverity;
      const matchesStatus = selectedStatus === 'all' || threat.status === selectedStatus;
      const matchesSearch = searchTerm === '' ||
        threat?.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        threat?.source?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        threat?.targetAsset?.toLowerCase()?.includes(searchTerm.toLowerCase());

      return matchesType && matchesSeverity && matchesStatus && matchesSearch;
    });
  }, [dashboardData, selectedThreatType, selectedSeverity, selectedStatus, searchTerm]);

  // Generate threat distribution chart
  const threatDistributionData = useMemo(() => {
    if (!dashboardData || !dashboardData.threats) return null;

    try {
      const statusCounts = dashboardData.threats.reduce((acc, threat) => {
        acc[threat.status] = (acc[threat.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return {
      labels: Object.keys(statusCounts).map(status => {
        const formatted = status ? status.replace('_', ' ') : 'Unknown';
        return formatted.charAt(0).toUpperCase() + formatted.slice(1);
      }),
      datasets: [{
        data: Object.values(statusCounts),
        backgroundColor: [
          'rgba(248, 113, 113, 0.8)', // detected - red
          'rgba(251, 191, 36, 0.8)',  // investigating - amber
          'rgba(59, 130, 246, 0.8)',  // contained - blue
          'rgba(52, 211, 153, 0.8)',  // resolved - green
          'rgba(156, 163, 175, 0.8)'  // false_positive - gray
        ],
        borderColor: [
          'rgb(248, 113, 113)',
          'rgb(251, 191, 36)',
          'rgb(59, 130, 246)',
          'rgb(52, 211, 153)',
          'rgb(156, 163, 175)'
        ],
        borderWidth: 2,
        hoverOffset: 8
      }]
    };
    } catch (error) {
      console.error('Error generating threat distribution chart data:', error);
      return null;
    }
  }, [dashboardData]);

  // Generate security metrics chart
  const securityMetricsData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.metrics.map(m => m.name),
      datasets: [{
        label: 'Current Value',
        data: dashboardData.metrics.map(m => m.currentValue),
        backgroundColor: dashboardData.metrics.map(m =>
          m.status === 'compliant' ? 'rgba(52, 211, 153, 0.8)' :
          m.status === 'warning' ? 'rgba(251, 191, 36, 0.8)' :
          'rgba(248, 113, 113, 0.8)'
        ),
        borderColor: dashboardData.metrics.map(m =>
          m.status === 'compliant' ? 'rgb(52, 211, 153)' :
          m.status === 'warning' ? 'rgb(251, 191, 36)' :
          'rgb(248, 113, 113)'
        ),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }, {
        label: 'Target Value',
        data: dashboardData.metrics.map(m => m.targetValue),
        backgroundColor: 'rgba(156, 163, 175, 0.3)',
        borderColor: 'rgb(156, 163, 175)',
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'medium':
        return <AlertCircle className="w-5 h-5 text-blue-500" />;
      case 'low':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      default:
        return <Shield className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSeverityColorClass = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'resolved':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'contained':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'investigating':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'detected':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'false_positive':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getThreatTypeIcon = (type: string) => {
    switch (type) {
      case 'malware':
        return <Bug className="w-4 h-4" />;
      case 'phishing':
        return <Globe className="w-4 h-4" />;
      case 'ddos':
        return <Wifi className="w-4 h-4" />;
      case 'intrusion':
        return <Unlock className="w-4 h-4" />;
      case 'data_breach':
        return <Database className="w-4 h-4" />;
      case 'insider_threat':
        return <User className="w-4 h-4" />;
      default:
        return <Shield className="w-4 h-4" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateSecurityOverviewData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleThreatClick = useCallback((threatId: string) => {
    setExpandedThreat(expandedThreat === threatId ? null : threatId);
  }, [expandedThreat]);

  // CRUD Operations for Threats
  const handleCreateThreat = useCallback(async (threatData: Omit<SecurityThreat, 'id' | 'createdAt' | 'updatedAt'>) => {
    setIsRefreshing(true);
    try {
      const newThreat: SecurityThreat = {
        ...threatData,
        id: `threat-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setDashboardData(prev => prev ? {
        ...prev,
        threats: [...prev.threats, newThreat]
      } : null);

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error creating threat:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  const handleUpdateThreat = useCallback(async (threatId: string, updates: Partial<SecurityThreat>) => {
    setIsRefreshing(true);
    try {
      setDashboardData(prev => prev ? {
        ...prev,
        threats: prev.threats.map(threat =>
          threat.id === threatId
            ? { ...threat, ...updates, updatedAt: new Date() }
            : threat
        )
      } : null);

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error updating threat:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  const handleDeleteThreat = useCallback(async (threatId: string) => {
    setIsRefreshing(true);
    try {
      setDashboardData(prev => prev ? {
        ...prev,
        threats: prev.threats.filter(threat => threat.id !== threatId)
      } : null);
    } catch (error) {
      console.error('Error deleting threat:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // CRUD Operations for Incidents
  const handleCreateIncident = useCallback(async (incidentData: Omit<SecurityIncident, 'id' | 'createdAt' | 'updatedAt'>) => {
    setIsRefreshing(true);
    try {
      const newIncident: SecurityIncident = {
        ...incidentData,
        id: `incident-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setDashboardData(prev => prev ? {
        ...prev,
        incidents: [...prev.incidents, newIncident]
      } : null);

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error creating incident:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  const handleUpdateIncident = useCallback(async (incidentId: string, updates: Partial<SecurityIncident>) => {
    setIsRefreshing(true);
    try {
      setDashboardData(prev => prev ? {
        ...prev,
        incidents: prev.incidents.map(incident =>
          incident.id === incidentId
            ? { ...incident, ...updates, updatedAt: new Date() }
            : incident
        )
      } : null);

      setIsModalOpen(false);
    } catch (error) {
      console.error('Error updating incident:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  const handleDeleteIncident = useCallback(async (incidentId: string) => {
    setIsRefreshing(true);
    try {
      setDashboardData(prev => prev ? {
        ...prev,
        incidents: prev.incidents.filter(incident => incident.id !== incidentId)
      } : null);
    } catch (error) {
      console.error('Error deleting incident:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  // Refresh data function
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));
      const newData = generateSecurityOverviewData();
      setDashboardData(newData);
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setIsRefreshing(false);
    }
  }, []);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-80" />
            <LoadingSkeleton className="h-80" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Security Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Security Overview</h1>
              <p className="text-text-secondary">
                Comprehensive security monitoring with threat detection and incident response
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • {dashboardData.realTimeUpdates.activeScans} scans active
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh security data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Security Report
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Security Score</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.securityScore.toFixed(1)}</p>
                </div>
                <Shield className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Active Threats</p>
                  <p className="text-2xl font-bold text-red-500">{dashboardData.overview.activeThreats}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Systems Monitored</p>
                  <p className="text-2xl font-bold text-blue-500">{dashboardData.overview.systemsMonitored}</p>
                </div>
                <Server className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Alerts (24h)</p>
                  <p className="text-2xl font-bold text-amber-500">{dashboardData.overview.alertsLast24h}</p>
                </div>
                <Bell className="w-8 h-8 text-amber-500" />
              </div>
            </div>
          </div>
        )}

        {/* View Tabs */}
        <div className="flex items-center gap-2 mb-6">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'overview'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('threats')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'threats'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <AlertTriangle className="w-4 h-4 inline mr-2" />
            Threats
          </button>
          <button
            onClick={() => setActiveView('incidents')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'incidents'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Activity className="w-4 h-4 inline mr-2" />
            Incidents
          </button>
          <button
            onClick={() => setActiveView('compliance')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'compliance'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <CheckCircle className="w-4 h-4 inline mr-2" />
            Compliance
          </button>
        </div>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && dashboardData && (
        <>
          {/* Enhanced Security Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div className="bg-surface rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-lg">
                  <AlertTriangle className="w-6 h-6 text-red-500" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-text">{dashboardData.overview.criticalThreats}</div>
                  <div className="text-sm text-text-secondary">Critical Threats</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-red-500" />
                <span className="text-sm text-red-500">+2 from last week</span>
              </div>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                  <Activity className="w-6 h-6 text-orange-500" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-text">{dashboardData.incidents.length}</div>
                  <div className="text-sm text-text-secondary">Active Incidents</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingDown className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500">-3 from last week</span>
              </div>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                  <Shield className="w-6 h-6 text-blue-500" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-text">{dashboardData.overview.securityScore.toFixed(1)}%</div>
                  <div className="text-sm text-text-secondary">Security Score</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500">+1.2% this month</span>
              </div>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-green-100 dark:bg-green-900/30 rounded-lg">
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-text">
                    {Object.values(dashboardData.complianceStatus).reduce((sum, framework) => sum + framework.score, 0) / Object.keys(dashboardData.complianceStatus).length}%
                  </div>
                  <div className="text-sm text-text-secondary">Avg Compliance</div>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-green-500" />
                <span className="text-sm text-green-500">+0.8% this quarter</span>
              </div>
            </div>
          </div>

          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            {/* Threat Distribution Chart */}
            <div className="bg-surface rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Threat Status Distribution
              </h3>
              {threatDistributionData && (
                <div className="h-64 flex items-center justify-center">
                  <Doughnut
                    data={threatDistributionData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>

            {/* Security Metrics Chart */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Security Metrics Performance
              </h3>
              {securityMetricsData && (
                <div className="h-64">
                  <Bar
                    data={securityMetricsData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      },
                      scales: {
                        x: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                          }
                        },
                        y: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                            callback: (value) => `${value}%`
                          },
                          min: 0,
                          max: 100
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Recent Security Activity */}
          <div className="bg-surface rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-semibold text-text flex items-center gap-2">
                <Activity className="w-5 h-5" />
                Recent Security Activity
              </h3>
              <div className="flex items-center gap-2">
                <select
                  value={filterDateRange}
                  onChange={(e) => setFilterDateRange(e.target.value as any)}
                  className="px-3 py-2 bg-background border border-border rounded-lg text-text text-sm focus:outline-none focus:ring-2 focus:ring-primary/50"
                >
                  <option value="24h">Last 24 Hours</option>
                  <option value="7d">Last 7 Days</option>
                  <option value="30d">Last 30 Days</option>
                  <option value="90d">Last 90 Days</option>
                </select>
                <button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="p-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors disabled:opacity-50"
                >
                  <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                </button>
              </div>
            </div>

            {dashboardData && (
              <div className="space-y-4">
                {dashboardData.activities.slice(0, 10).map((activity) => (
                  <div key={activity.id} className="flex items-start gap-4 p-4 bg-card rounded-lg border border-border hover:bg-card-hover transition-colors">
                    <div className={`p-2 rounded-lg ${
                      activity.severity === 'critical' ? 'bg-red-100 dark:bg-red-900/30' :
                      activity.severity === 'high' ? 'bg-orange-100 dark:bg-orange-900/30' :
                      activity.severity === 'medium' ? 'bg-yellow-100 dark:bg-yellow-900/30' :
                      activity.severity === 'low' ? 'bg-blue-100 dark:bg-blue-900/30' :
                      'bg-gray-100 dark:bg-gray-900/30'
                    }`}>
                      {activity.type === 'threat_detected' && <AlertTriangle className={`w-4 h-4 ${
                        activity.severity === 'critical' ? 'text-red-500' :
                        activity.severity === 'high' ? 'text-orange-500' :
                        activity.severity === 'medium' ? 'text-yellow-500' :
                        'text-blue-500'
                      }`} />}
                      {activity.type === 'incident_created' && <XCircle className="w-4 h-4 text-red-500" />}
                      {activity.type === 'incident_resolved' && <CheckCircle className="w-4 h-4 text-green-500" />}
                      {activity.type === 'compliance_updated' && <Shield className="w-4 h-4 text-blue-500" />}
                      {activity.type === 'system_alert' && <Bell className="w-4 h-4 text-orange-500" />}
                      {activity.type === 'user_action' && <User className="w-4 h-4 text-gray-500" />}
                      {activity.type === 'policy_change' && <FileText className="w-4 h-4 text-purple-500" />}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-text truncate">{activity.title}</h4>
                        <div className="flex items-center gap-2">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            activity.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                            activity.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                            activity.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                            activity.severity === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                          }`}>
                            {activity.severity}
                          </span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            activity.status === 'active' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                            activity.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                            activity.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                            'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                          }`}>
                            {activity.status}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-text-secondary mb-2">{activity.description}</p>

                      <div className="flex items-center gap-4 text-xs text-text-secondary">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {activity.timestamp.toLocaleString()}
                        </span>
                        <span className="flex items-center gap-1">
                          <User className="w-3 h-3" />
                          {activity.user || activity.source}
                        </span>
                        {activity.affectedSystems.length > 0 && (
                          <span className="flex items-center gap-1">
                            <Server className="w-3 h-3" />
                            {activity.affectedSystems.length} system{activity.affectedSystems.length > 1 ? 's' : ''}
                          </span>
                        )}
                      </div>

                      {activity.relatedEntities.length > 0 && (
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs text-text-secondary">Related:</span>
                          {activity.relatedEntities.slice(0, 3).map((entity, index) => (
                            <span key={index} className="px-2 py-1 bg-background rounded text-xs text-text">
                              {entity.name}
                            </span>
                          ))}
                          {activity.relatedEntities.length > 3 && (
                            <span className="text-xs text-text-secondary">+{activity.relatedEntities.length - 3} more</span>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                ))}

                {dashboardData.activities.length === 0 && (
                  <div className="text-center py-8 text-text-secondary">
                    <Activity className="w-12 h-12 mx-auto mb-4 opacity-50" />
                    <p>No recent security activity</p>
                  </div>
                )}
              </div>
            )}
          </div>
        </>
      )}

      {/* Threats View */}
      {activeView === 'threats' && (
        <div className="bg-surface rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-text">Threat Management</h2>
            <button
              onClick={() => {
                setSelectedThreat(null);
                setModalMode('create');
                setIsModalOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              Create Threat
            </button>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search threats, sources, or assets..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              />
            </div>

            <select
              value={selectedThreatType}
              onChange={(e) => setSelectedThreatType(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Types</option>
              <option value="malware">Malware</option>
              <option value="phishing">Phishing</option>
              <option value="ddos">DDoS</option>
              <option value="intrusion">Intrusion</option>
              <option value="data_breach">Data Breach</option>
              <option value="insider_threat">Insider Threat</option>
              <option value="ransomware">Ransomware</option>
              <option value="apt">APT</option>
              <option value="social_engineering">Social Engineering</option>
            </select>

            <select
              value={selectedSeverity}
              onChange={(e) => setSelectedSeverity(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Severities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Status</option>
              <option value="detected">Detected</option>
              <option value="investigating">Investigating</option>
              <option value="contained">Contained</option>
              <option value="resolved">Resolved</option>
              <option value="false_positive">False Positive</option>
            </select>
          </div>

          {/* Threats Table */}
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left py-3 px-4 font-medium text-text">Threat</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Type</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Severity</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Risk Score</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Detected</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredThreats.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center py-8">
                      <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                      <p className="text-text-secondary">No threats found matching your criteria.</p>
                    </td>
                  </tr>
                ) : (
              filteredThreats.map((threat) => (
                <tr key={threat.id} className="border-b border-border hover:bg-card/50">
                  <td className="py-3 px-4">
                    <div>
                      <div className="font-medium text-text">{threat.name}</div>
                      <div className="text-sm text-text-secondary">
                        Target: {threat.targetAsset}
                      </div>
                      {threat.threatActor && (
                        <div className="text-xs text-text-secondary">
                          Actor: {threat.threatActor}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      {getThreatTypeIcon(threat.type)}
                      <span className="text-sm text-text">{threat?.type?.replace('_', ' ') || 'Unknown'}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(threat.severity)}`}>
                      {threat.severity}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(threat.status)}`}>
                      {threat?.status?.replace('_', ' ') || 'Unknown'}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-text">{threat.riskScore}</span>
                      <div className={`w-2 h-2 rounded-full ${
                        threat.riskScore >= 90 ? 'bg-red-500' :
                        threat.riskScore >= 70 ? 'bg-orange-500' :
                        threat.riskScore >= 50 ? 'bg-yellow-500' : 'bg-green-500'
                      }`}></div>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="text-sm text-text">
                      {threat.detectedAt.toLocaleDateString()}
                    </div>
                    <div className="text-xs text-text-secondary">
                      {threat.detectedAt.toLocaleTimeString()}
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">

                      <button
                        onClick={() => {
                          setSelectedThreat(threat);
                          setModalMode('edit');
                          setIsModalOpen(true);
                        }}
                        className="p-1 text-text-secondary hover:text-text hover:bg-card rounded"
                        title="Edit Threat"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteThreat(threat.id)}
                        className="p-1 text-text-secondary hover:text-red-500 hover:bg-card rounded"
                        title="Delete Threat"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* Incidents View */}
      {activeView === 'incidents' && (
        <div className="bg-surface rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-text">Incident Management</h2>
            <button
              onClick={() => {
                setSelectedIncident(null);
                setModalMode('create');
                setIsModalOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4" />
              Create Incident
            </button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-border">
                  <th className="text-left py-3 px-4 font-medium text-text">Incident</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Category</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Severity</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Status</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Priority</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Assigned To</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Reported</th>
                  <th className="text-left py-3 px-4 font-medium text-text">Actions</th>
                </tr>
              </thead>
              <tbody>
                {dashboardData && dashboardData.incidents.length === 0 ? (
                  <tr>
                    <td colSpan={8} className="text-center py-8">
                      <Activity className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                      <p className="text-text-secondary">No security incidents reported.</p>
                    </td>
                  </tr>
                ) : (
                  dashboardData?.incidents.map((incident) => (
                    <tr key={incident.id} className="border-b border-border hover:bg-card/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-text">{incident.title}</div>
                          <div className="text-sm text-text-secondary line-clamp-2">
                            {incident.description}
                          </div>
                          <div className="text-xs text-text-secondary mt-1">
                            {incident.affectedAssets.length} system{incident.affectedAssets.length > 1 ? 's' : ''} affected
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="text-sm text-text">{incident?.category?.replace('_', ' ') || 'Unknown'}</span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(incident.severity)}`}>
                          {incident.severity}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          incident.status === 'resolved' || incident.status === 'closed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                          incident.status === 'investigating' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                          incident.status === 'contained' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' :
                          'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                        }`}>
                          {incident.status}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getSeverityColorClass(incident.priority)}`}>
                          {incident.priority}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-text">
                          {incident.assignedTo || 'Unassigned'}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm text-text">
                          {incident.reportedAt.toLocaleDateString()}
                        </div>
                        <div className="text-xs text-text-secondary">
                          by {incident.reportedBy}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">

                          <button
                            onClick={() => {
                              setSelectedIncident(incident);
                              setModalMode('edit');
                              setIsModalOpen(true);
                            }}
                            className="p-1 text-text-secondary hover:text-text hover:bg-card rounded"
                            title="Edit Incident"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleDeleteIncident(incident.id)}
                            className="p-1 text-text-secondary hover:text-red-500 hover:bg-card rounded"
                            title="Delete Incident"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}
      {/* Compliance View */}
      {activeView === 'compliance' && (
        <div className="bg-surface rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-xl font-semibold text-text">Compliance Management</h2>
            <div className="flex items-center gap-2">
              <button
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="p-2 bg-background hover:bg-card border border-border rounded-lg transition-colors"
                title="Refresh Compliance Data"
              >
                <RefreshCw className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} />
              </button>
              <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                <Download className="w-4 h-4" />
                Export Report
              </button>
            </div>
          </div>

          {/* Compliance Framework Overview */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {dashboardData && Object.entries(dashboardData.complianceStatus).map(([framework, status]) => (
              <div key={framework} className="bg-card rounded-lg p-6 border border-border hover:bg-card-hover transition-colors">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      status.status === 'compliant' ? 'bg-green-100 dark:bg-green-900/30' :
                      status.status === 'partially_compliant' ? 'bg-yellow-100 dark:bg-yellow-900/30' :
                      'bg-red-100 dark:bg-red-900/30'
                    }`}>
                      <Shield className={`w-5 h-5 ${
                        status.status === 'compliant' ? 'text-green-500' :
                        status.status === 'partially_compliant' ? 'text-yellow-500' :
                        'text-red-500'
                      }`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-text uppercase">{framework}</h3>
                      <p className="text-sm text-text-secondary">{status?.status?.replace('_', ' ') || 'Unknown'}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-text">{status.score.toFixed(1)}%</div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-text-secondary">Compliance Score</span>
                    <span className="text-text">{status.score.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-background rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        status.score >= 90 ? 'bg-green-500' :
                        status.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ width: `${status.score}%` }}
                    />
                  </div>
                </div>

                <div className="text-sm text-text-secondary">
                  Last Audit: {status.lastAudit.toLocaleDateString()}
                </div>
              </div>
            ))}
          </div>

          {/* Detailed Compliance Data */}
          {dashboardData && dashboardData.compliance && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-text">Detailed Compliance Assessments</h3>

              {dashboardData.compliance.map((compliance) => (
                <div key={compliance.id} className="bg-card rounded-lg p-6 border border-border">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h4 className="text-lg font-semibold text-text">{compliance.framework} {compliance.version}</h4>
                      <p className="text-text-secondary">{compliance.description}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-text">{compliance.overallScore.toFixed(1)}%</div>
                      <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                        compliance.status === 'compliant' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                        compliance.status === 'partially_compliant' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                        compliance.status === 'non_compliant' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                        'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      }`}>
                        {compliance?.status?.replace('_', ' ') || 'Unknown'}
                      </span>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <div>
                      <h5 className="font-medium text-text mb-2">Assessment Details</h5>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-text-secondary">Last Assessment:</span>
                          <span className="text-text">{compliance.lastAssessment.toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-text-secondary">Next Assessment:</span>
                          <span className="text-text">{compliance.nextAssessment.toLocaleDateString()}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-text-secondary">Assessor:</span>
                          <span className="text-text">{compliance.assessor}</span>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h5 className="font-medium text-text mb-2">Control Summary</h5>
                      <div className="space-y-2">
                        {compliance.controls.slice(0, 3).map((control) => (
                          <div key={control.id} className="flex items-center justify-between p-2 bg-background rounded">
                            <span className="text-sm text-text">{control.name}</span>
                            <span className={`px-2 py-1 rounded text-xs font-medium ${
                              control.status === 'implemented' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                              control.status === 'partially_implemented' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              control.status === 'not_implemented' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                              'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                            }`}>
                              {control?.status?.replace('_', ' ') || 'Unknown'}
                            </span>
                          </div>
                        ))}
                        {compliance.controls.length > 3 && (
                          <div className="text-sm text-text-secondary text-center">
                            +{compliance.controls.length - 3} more controls
                          </div>
                        )}
                      </div>
                    </div>
                  </div>

                  {compliance.tags.length > 0 && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm text-text-secondary">Tags:</span>
                      {compliance.tags.map((tag, index) => (
                        <span key={index} className="px-2 py-1 bg-background rounded text-xs text-text">
                          {tag}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SecurityOverviewDashboard;
