import { toast } from 'react-toastify';
import { ConsentLevelData } from './privacyDashboardService';

export interface GreenLevelReportData {
  executiveSummary: {
    complianceStatus: string;
    totalSubjects: number;
    complianceRate: number;
    riskLevel: 'Low' | 'Medium' | 'High';
    lastUpdated: Date;
  };
  detailedStatistics: {
    consentBreakdown: {
      dataCollection: number;
      dataProcessing: number;
      dataSharing: number;
      marketing: number;
    };
    performanceMetrics: {
      growthRate: number;
      conversionRate: number;
      retentionRate: number;
    };
    geographicDistribution: {
      region: string;
      percentage: number;
    }[];
  };
  recentActivity: {
    period: string;
    newConsents: number;
    renewals: number;
    modifications: number;
    withdrawals: number;
  };
  trends: {
    monthlyGrowth: {
      month: string;
      percentage: number;
      change: number;
    }[];
    projections: {
      nextMonth: number;
      nextQuarter: number;
      confidence: number;
    };
  };
  recommendations: {
    priority: 'High' | 'Medium' | 'Low';
    category: string;
    description: string;
    impact: string;
  }[];
}

class GreenLevelReportService {
  async generateReport(data: ConsentLevelData): Promise<GreenLevelReportData> {
    // Simulate report generation time
    await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

    const reportData: GreenLevelReportData = {
      executiveSummary: {
        complianceStatus: 'Excellent',
        totalSubjects: data.totalSubjects,
        complianceRate: data.percentage,
        riskLevel: 'Low',
        lastUpdated: data.lastUpdated
      },
      detailedStatistics: {
        consentBreakdown: data.breakdown,
        performanceMetrics: {
          growthRate: data.growthRate,
          conversionRate: 94.5,
          retentionRate: 89.2
        },
        geographicDistribution: [
          { region: 'North America', percentage: 45 },
          { region: 'Europe', percentage: 35 },
          { region: 'Asia Pacific', percentage: 15 },
          { region: 'Other', percentage: 5 }
        ]
      },
      recentActivity: {
        period: 'Last 30 days',
        newConsents: data.recentActivity.newConsents,
        renewals: data.recentActivity.renewals,
        modifications: data.recentActivity.modifications,
        withdrawals: 0
      },
      trends: {
        monthlyGrowth: [
          { month: 'Jan', percentage: 28.5, change: 2.1 },
          { month: 'Feb', percentage: 29.2, change: 0.7 },
          { month: 'Mar', percentage: 30.1, change: 0.9 },
          { month: 'Apr', percentage: 30.8, change: 0.7 },
          { month: 'May', percentage: 31.5, change: 0.7 },
          { month: 'Jun', percentage: data.percentage, change: data.percentage - 31.5 }
        ],
        projections: {
          nextMonth: data.percentage + 0.5,
          nextQuarter: data.percentage + 1.2,
          confidence: 87
        }
      },
      recommendations: [
        {
          priority: 'Medium',
          category: 'Marketing Consent',
          description: 'Improve marketing consent rate from 87% to 90%+',
          impact: 'Increase compliant marketing reach by 3-5%'
        },
        {
          priority: 'Low',
          category: 'Data Sharing',
          description: 'Maintain current data sharing consent levels',
          impact: 'Continue current compliance excellence'
        },
        {
          priority: 'Medium',
          category: 'User Experience',
          description: 'Optimize consent flow for mobile users',
          impact: 'Potential 2-3% improvement in conversion rates'
        }
      ]
    };

    return reportData;
  }

  async exportReport(reportData: GreenLevelReportData, format: 'pdf' | 'excel' | 'csv'): Promise<boolean> {
    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `green-level-report-${timestamp}.${format}`;

      // In a real implementation, this would generate and download the actual file
      console.log(`Exporting report as ${filename}`);

      // Simulate different export formats
      switch (format) {
        case 'pdf':
          toast.success(`PDF report "${filename}" downloaded successfully!`);
          break;
        case 'excel':
          toast.success(`Excel report "${filename}" downloaded successfully!`);
          break;
        case 'csv':
          toast.success(`CSV report "${filename}" downloaded successfully!`);
          break;
      }

      return true;
    } catch (error) {
      toast.error(`Failed to export ${format.toUpperCase()} report. Please try again.`);
      return false;
    }
  }

  generateReportHTML(reportData: GreenLevelReportData): string {
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Green Level Compliance Report - Executive Summary</title>
        <style>
          * { margin: 0; padding: 0; box-sizing: border-box; }

          body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #1f2937;
            background: #ffffff;
            margin: 0;
            padding: 40px;
          }

          .container { max-width: 1200px; margin: 0 auto; }

          .header {
            background: linear-gradient(135deg, #070752 0%, #1e40af 100%);
            color: white;
            padding: 40px;
            border-radius: 16px;
            margin-bottom: 40px;
            box-shadow: 0 10px 25px rgba(7, 7, 82, 0.15);
          }

          .header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 8px;
            letter-spacing: -0.025em;
          }

          .header .subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 20px;
          }

          .header .meta {
            font-size: 0.95rem;
            opacity: 0.8;
            display: flex;
            align-items: center;
            gap: 20px;
          }

          .section {
            background: white;
            border-radius: 12px;
            padding: 32px;
            margin-bottom: 32px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
          }

          .section h2 {
            font-size: 1.75rem;
            font-weight: 600;
            margin-bottom: 24px;
            color: #070752;
            border-bottom: 2px solid #22c55e;
            padding-bottom: 12px;
          }

          .section h3 {
            font-size: 1.25rem;
            font-weight: 600;
            margin: 24px 0 16px 0;
            color: #374151;
          }

          .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 24px;
            margin: 24px 0;
          }

          .metric {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 24px;
            border-radius: 12px;
            text-align: center;
            border: 1px solid #e2e8f0;
            position: relative;
            overflow: hidden;
          }

          .metric::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
          }

          .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            color: #22c55e;
            margin-bottom: 8px;
            display: block;
          }

          .metric-label {
            font-size: 0.95rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }

          .table-container {
            overflow-x: auto;
            margin: 24px 0;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
          }

          table {
            width: 100%;
            border-collapse: collapse;
            background: white;
          }

          th {
            background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
            padding: 16px;
            text-align: left;
            font-weight: 600;
            color: #374151;
            border-bottom: 2px solid #e5e7eb;
            font-size: 0.9rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
          }

          td {
            padding: 16px;
            border-bottom: 1px solid #f3f4f6;
            color: #4b5563;
          }

          tr:hover { background-color: #f9fafb; }

          .recommendation {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            padding: 24px;
            margin: 16px 0;
            border-radius: 12px;
            border-left: 6px solid #22c55e;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          }

          .recommendation h4 {
            font-weight: 600;
            color: #070752;
            margin-bottom: 8px;
            font-size: 1.1rem;
          }

          .recommendation .priority {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            margin-bottom: 12px;
          }

          .priority-high {
            background: #fef2f2;
            color: #dc2626;
            border-left-color: #ef4444;
          }

          .priority-medium {
            background: #fffbeb;
            color: #d97706;
            border-left-color: #f59e0b;
          }

          .priority-low {
            background: #f0fdf4;
            color: #16a34a;
            border-left-color: #22c55e;
          }

          .trend-positive { color: #16a34a; font-weight: 600; }
          .trend-negative { color: #dc2626; font-weight: 600; }

          .status-excellent { color: #16a34a; font-weight: 600; }
          .status-good { color: #ca8a04; font-weight: 600; }
          .status-needs-improvement { color: #dc2626; font-weight: 600; }

          .conclusion {
            background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
            border: 1px solid #bbf7d0;
            border-radius: 12px;
            padding: 32px;
            text-align: center;
            margin-top: 40px;
          }

          .conclusion h2 {
            color: #16a34a;
            margin-bottom: 16px;
            border: none;
            padding: 0;
          }

          .footer {
            text-align: center;
            margin-top: 40px;
            padding: 24px;
            color: #6b7280;
            font-size: 0.9rem;
            border-top: 1px solid #e5e7eb;
          }

          @media print {
            body { margin: 0; padding: 20px; }
            .section { break-inside: avoid; }
            .header { background: #070752 !important; }
          }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Green Level Compliance Report</h1>
          <p>Comprehensive analysis of compliant data consent status</p>
          <p><strong>Generated on:</strong> ${reportData.executiveSummary.lastUpdated.toLocaleDateString()}</p>
        </div>

        <div class="section">
          <h2>Executive Summary</h2>
          <p>Status: <strong>${reportData.executiveSummary.complianceStatus}</strong> | Risk Level: <strong>${reportData.executiveSummary.riskLevel}</strong></p>
          <div class="metric">
            <div class="metric-value">${reportData.executiveSummary.complianceRate.toFixed(1)}%</div>
            <div class="metric-label">Compliance Rate</div>
          </div>
          <div class="metric">
            <div class="metric-value">${reportData.executiveSummary.totalSubjects.toLocaleString()}</div>
            <div class="metric-label">Total Subjects</div>
          </div>
          <div class="metric">
            <div class="metric-value">${reportData.detailedStatistics.performanceMetrics.growthRate.toFixed(1)}%</div>
            <div class="metric-label">Growth Rate</div>
          </div>
        </div>

        <div class="section">
          <h2>Detailed Statistics</h2>
          <h3>Consent Breakdown</h3>
          <table>
            <tr><th>Category</th><th>Percentage</th><th>Status</th></tr>
            <tr><td>Data Collection</td><td>${reportData.detailedStatistics.consentBreakdown.dataCollection}%</td><td>Excellent</td></tr>
            <tr><td>Data Processing</td><td>${reportData.detailedStatistics.consentBreakdown.dataProcessing}%</td><td>Excellent</td></tr>
            <tr><td>Data Sharing</td><td>${reportData.detailedStatistics.consentBreakdown.dataSharing}%</td><td>Good</td></tr>
            <tr><td>Marketing</td><td>${reportData.detailedStatistics.consentBreakdown.marketing}%</td><td>Good</td></tr>
          </table>

          <h3>Performance Metrics</h3>
          <table>
            <tr><th>Metric</th><th>Value</th></tr>
            <tr><td>Conversion Rate</td><td>${reportData.detailedStatistics.performanceMetrics.conversionRate}%</td></tr>
            <tr><td>Retention Rate</td><td>${reportData.detailedStatistics.performanceMetrics.retentionRate}%</td></tr>
          </table>
        </div>

        <div class="section">
          <h2>Recent Activity (${reportData.recentActivity.period})</h2>
          <ul>
            <li><strong>New Consents:</strong> ${reportData.recentActivity.newConsents}</li>
            <li><strong>Renewals:</strong> ${reportData.recentActivity.renewals}</li>
            <li><strong>Modifications:</strong> ${reportData.recentActivity.modifications}</li>
          </ul>
        </div>

        <div class="section">
          <h2>Trends & Projections</h2>
          <h3>Monthly Growth</h3>
          <table>
            <tr><th>Month</th><th>Percentage</th><th>Change</th></tr>
            ${reportData.trends.monthlyGrowth.map(trend => `
              <tr>
                <td>${trend.month}</td>
                <td>${trend.percentage.toFixed(1)}%</td>
                <td class="${trend.change >= 0 ? 'trend-positive' : 'trend-negative'}">
                  ${trend.change >= 0 ? '+' : ''}${trend.change.toFixed(1)}%
                </td>
              </tr>
            `).join('')}
          </table>

          <h3>Projections</h3>
          <p><strong>Next Month:</strong> ${reportData.trends.projections.nextMonth.toFixed(1)}% (${reportData.trends.projections.confidence}% confidence)</p>
          <p><strong>Next Quarter:</strong> ${reportData.trends.projections.nextQuarter.toFixed(1)}%</p>
        </div>

        <div class="section">
          <h2>Recommendations</h2>
          ${reportData.recommendations.map(rec => `
            <div class="recommendation priority-${rec.priority.toLowerCase()}">
              <strong>${rec.category}</strong> (${rec.priority} Priority)<br>
              ${rec.description}<br>
              <em>Expected Impact: ${rec.impact}</em>
            </div>
          `).join('')}
        </div>

        <div class="section">
          <h2>Conclusion</h2>
          <p>The Green Level compliance status demonstrates excellent performance with a ${reportData.executiveSummary.complianceRate.toFixed(1)}% compliance rate.
          The low risk level and positive growth trends indicate strong privacy governance and user engagement.</p>
        </div>
      </body>
      </html>
    `;
  }

  async previewReport(reportData: GreenLevelReportData): Promise<void> {
    const htmlContent = this.generateReportHTML(reportData);
    const newWindow = window.open('', '_blank');
    if (newWindow) {
      newWindow.document.write(htmlContent);
      newWindow.document.close();
    } else {
      toast.error('Please allow popups to preview the report');
    }
  }
}

export const greenLevelReportService = new GreenLevelReportService();
export default greenLevelReportService;
