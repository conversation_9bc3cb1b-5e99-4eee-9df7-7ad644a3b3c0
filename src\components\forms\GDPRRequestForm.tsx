/**
 * GDPR Request Form Component
 * Comprehensive form for creating and editing GDPR requests with validation
 */

import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { X, Save, AlertCircle, Calendar, User, Mail, FileText, Tag, Clock } from 'lucide-react';
import type { GDPRRequest } from '../../services/mockDataService';

interface GDPRRequestFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: Omit<GDPRRequest, 'id' | 'submittedAt'>) => Promise<void>;
  initialData?: Partial<GDPRRequest>;
  title?: string;
}

interface FormData {
  email: string;
  type: GDPRRequest['type'];
  priority: GDPRRequest['priority'];
  description: string;
  department: string;
  dataCategories: string[];
  estimatedHours: number;
  assignedTo?: string;
  dueDate: string;
}

interface FormErrors {
  email?: string;
  type?: string;
  priority?: string;
  description?: string;
  department?: string;
  dataCategories?: string;
  estimatedHours?: string;
  dueDate?: string;
}

export const GDPRRequestForm: React.FC<GDPRRequestFormProps> = ({
  isOpen,
  onClose,
  onSave,
  initialData,
  title = 'GDPR Request'
}) => {
  const { mode } = useTheme();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const [formData, setFormData] = useState<FormData>({
    email: '',
    type: 'access',
    priority: 'medium',
    description: '',
    department: 'IT',
    dataCategories: [],
    estimatedHours: 2,
    assignedTo: '',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        email: initialData.email || '',
        type: initialData.type || 'access',
        priority: initialData.priority || 'medium',
        description: initialData.description || '',
        department: initialData.department || 'IT',
        dataCategories: initialData.dataCategories || [],
        estimatedHours: initialData.estimatedHours || 2,
        assignedTo: initialData.assignedTo || '',
        dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : 
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      });
    }
  }, [initialData]);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Email validation
    if (!formData.email) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    // Description validation
    if (!formData.description || formData.description.trim().length < 10) {
      newErrors.description = 'Description must be at least 10 characters long';
    }

    // Data categories validation
    if (formData.dataCategories.length === 0) {
      newErrors.dataCategories = 'Please select at least one data category';
    }

    // Estimated hours validation
    if (formData.estimatedHours < 1 || formData.estimatedHours > 100) {
      newErrors.estimatedHours = 'Estimated hours must be between 1 and 100';
    }

    // Due date validation
    const dueDate = new Date(formData.dueDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    if (dueDate < today) {
      newErrors.dueDate = 'Due date cannot be in the past';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSave({
        ...formData,
        dueDate: new Date(formData.dueDate),
        dataCategories: formData.dataCategories,
      });
      onClose();
    } catch (error) {
      console.error('Error saving GDPR request:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDataCategoryToggle = (category: string) => {
    setFormData(prev => ({
      ...prev,
      dataCategories: prev.dataCategories.includes(category)
        ? prev.dataCategories.filter(c => c !== category)
        : [...prev.dataCategories, category]
    }));
  };

  const availableDataCategories = [
    'Personal Info', 'Contact Details', 'Financial Data', 'Health Records', 
    'Behavioral Data', 'Location Data', 'Employment Data', 'Marketing Data'
  ];

  const departments = ['IT', 'HR', 'Marketing', 'Sales', 'Finance', 'Legal', 'Operations'];
  const officers = ['Officer 1', 'Officer 2', 'Officer 3', 'Officer 4', 'Officer 5'];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50">
      <div className={`bg-card rounded-xl shadow-2xl border border-border max-w-2xl w-full max-h-[90vh] overflow-hidden`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-text">{title}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-surface rounded-lg transition-colors duration-200"
            disabled={isSubmitting}
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          <div className="space-y-6">
            {/* Email */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                <Mail className="w-4 h-4 inline mr-2" />
                Email Address *
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                className={`w-full px-3 py-2 border rounded-lg bg-surface text-text ${
                  errors.email ? 'border-red-500' : 'border-border'
                } focus:outline-none focus:ring-2 focus:ring-primary/50`}
                placeholder="<EMAIL>"
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.email}
                </p>
              )}
            </div>

            {/* Type and Priority */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  <FileText className="w-4 h-4 inline mr-2" />
                  Request Type *
                </label>
                <select
                  value={formData.type}
                  onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as GDPRRequest['type'] }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
                  disabled={isSubmitting}
                >
                  <option value="access">Data Access</option>
                  <option value="erasure">Data Erasure</option>
                  <option value="rectification">Data Rectification</option>
                  <option value="portability">Data Portability</option>
                  <option value="restriction">Processing Restriction</option>
                  <option value="objection">Processing Objection</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  <Tag className="w-4 h-4 inline mr-2" />
                  Priority *
                </label>
                <select
                  value={formData.priority}
                  onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as GDPRRequest['priority'] }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
                  disabled={isSubmitting}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>
            </div>

            {/* Description */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Description *
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg bg-surface text-text ${
                  errors.description ? 'border-red-500' : 'border-border'
                } focus:outline-none focus:ring-2 focus:ring-primary/50`}
                placeholder="Provide detailed description of the request..."
                disabled={isSubmitting}
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.description}
                </p>
              )}
            </div>

            {/* Department and Assignment */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  Department *
                </label>
                <select
                  value={formData.department}
                  onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
                  disabled={isSubmitting}
                >
                  {departments.map(dept => (
                    <option key={dept} value={dept}>{dept}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  <User className="w-4 h-4 inline mr-2" />
                  Assigned To
                </label>
                <select
                  value={formData.assignedTo}
                  onChange={(e) => setFormData(prev => ({ ...prev, assignedTo: e.target.value }))}
                  className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
                  disabled={isSubmitting}
                >
                  <option value="">Unassigned</option>
                  {officers.map(officer => (
                    <option key={officer} value={officer}>{officer}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Data Categories */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Data Categories *
              </label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {availableDataCategories.map(category => (
                  <label key={category} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.dataCategories.includes(category)}
                      onChange={() => handleDataCategoryToggle(category)}
                      className="rounded border-border text-primary focus:ring-primary/50"
                      disabled={isSubmitting}
                    />
                    <span className="text-sm text-text">{category}</span>
                  </label>
                ))}
              </div>
              {errors.dataCategories && (
                <p className="mt-1 text-sm text-red-500 flex items-center">
                  <AlertCircle className="w-4 h-4 mr-1" />
                  {errors.dataCategories}
                </p>
              )}
            </div>

            {/* Estimated Hours and Due Date */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  <Clock className="w-4 h-4 inline mr-2" />
                  Estimated Hours *
                </label>
                <input
                  type="number"
                  min="1"
                  max="100"
                  value={formData.estimatedHours}
                  onChange={(e) => setFormData(prev => ({ ...prev, estimatedHours: parseInt(e.target.value) || 1 }))}
                  className={`w-full px-3 py-2 border rounded-lg bg-surface text-text ${
                    errors.estimatedHours ? 'border-red-500' : 'border-border'
                  } focus:outline-none focus:ring-2 focus:ring-primary/50`}
                  disabled={isSubmitting}
                />
                {errors.estimatedHours && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.estimatedHours}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-text mb-2">
                  <Calendar className="w-4 h-4 inline mr-2" />
                  Due Date *
                </label>
                <input
                  type="date"
                  value={formData.dueDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg bg-surface text-text ${
                    errors.dueDate ? 'border-red-500' : 'border-border'
                  } focus:outline-none focus:ring-2 focus:ring-primary/50`}
                  disabled={isSubmitting}
                />
                {errors.dueDate && (
                  <p className="mt-1 text-sm text-red-500 flex items-center">
                    <AlertCircle className="w-4 h-4 mr-1" />
                    {errors.dueDate}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-end gap-3 mt-8 pt-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-text-secondary hover:text-text border border-border hover:bg-surface rounded-lg transition-colors duration-200"
              disabled={isSubmitting}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex items-center gap-2 px-6 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Save className="w-4 h-4" />
              {isSubmitting ? 'Saving...' : 'Save Request'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default GDPRRequestForm;
