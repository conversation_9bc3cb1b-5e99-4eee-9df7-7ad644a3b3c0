/**
 * Performance monitoring service for compliance components
 * Tracks component performance, memory usage, and potential crash scenarios
 */

export interface PerformanceMetric {
  componentName: string;
  metricType: 'render' | 'mount' | 'unmount' | 'data_load' | 'chart_create' | 'user_interaction';
  duration: number;
  timestamp: string;
  memoryUsage?: {
    used: number;
    total: number;
  };
  additionalData?: Record<string, any>;
}

export interface ComponentHealth {
  componentName: string;
  averageRenderTime: number;
  maxRenderTime: number;
  renderCount: number;
  errorCount: number;
  lastError?: string;
  memoryTrend: 'stable' | 'increasing' | 'decreasing';
  healthScore: number; // 0-100
}

export interface PerformanceAlert {
  type: 'slow_render' | 'memory_leak' | 'frequent_errors' | 'chart_performance';
  componentName: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  timestamp: string;
  metrics: PerformanceMetric[];
}

class PerformanceMonitoringService {
  private metrics: PerformanceMetric[] = [];
  private componentHealthMap = new Map<string, ComponentHealth>();
  private alerts: PerformanceAlert[] = [];
  private maxMetricsBuffer = 1000;
  private performanceObserver?: PerformanceObserver;

  constructor() {
    this.initialize();
  }

  private initialize() {
    // Set up performance observer for navigation and resource timing
    if ('PerformanceObserver' in window) {
      try {
        this.performanceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.entryType === 'measure' && entry.name.startsWith('compliance-')) {
              this.recordMetric({
                componentName: entry.name.replace('compliance-', ''),
                metricType: 'render',
                duration: entry.duration,
                timestamp: new Date().toISOString()
              });
            }
          });
        });

        this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
      } catch (error) {
        console.warn('Performance Observer not supported:', error);
      }
    }

    // Set up periodic health checks
    setInterval(() => {
      this.performHealthCheck();
    }, 60000); // Every minute

    // Set up memory monitoring
    setInterval(() => {
      this.monitorMemoryUsage();
    }, 30000); // Every 30 seconds
  }

  public startTiming(componentName: string, operation: string): () => void {
    const startTime = performance.now();
    const measureName = `compliance-${componentName}-${operation}`;

    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;

      // Use Performance API if available
      if ('performance' in window && 'measure' in performance) {
        try {
          performance.mark(`${measureName}-start`);
          performance.mark(`${measureName}-end`);
          performance.measure(measureName, `${measureName}-start`, `${measureName}-end`);
        } catch (error) {
          // Fallback to manual recording
          this.recordMetric({
            componentName,
            metricType: operation as any,
            duration,
            timestamp: new Date().toISOString()
          });
        }
      } else {
        this.recordMetric({
          componentName,
          metricType: operation as any,
          duration,
          timestamp: new Date().toISOString()
        });
      }
    };
  }

  public recordMetric(metric: PerformanceMetric): void {
    try {
      // Add memory usage if available
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        metric.memoryUsage = {
          used: memory.usedJSHeapSize,
          total: memory.totalJSHeapSize
        };
      }

      this.metrics.push(metric);

      // Update component health
      this.updateComponentHealth(metric);

      // Check for performance issues
      this.checkForPerformanceIssues(metric);

      // Prevent buffer overflow
      if (this.metrics.length > this.maxMetricsBuffer) {
        this.metrics = this.metrics.slice(-this.maxMetricsBuffer);
      }

      // Log slow operations in development
      if (process.env.NODE_ENV === 'development' && metric.duration > 100) {
        console.warn(`Slow ${metric.metricType} in ${metric.componentName}: ${metric.duration.toFixed(2)}ms`);
      }
    } catch (error) {
      console.error('Error recording performance metric:', error);
    }
  }

  private updateComponentHealth(metric: PerformanceMetric): void {
    const existing = this.componentHealthMap.get(metric.componentName) || {
      componentName: metric.componentName,
      averageRenderTime: 0,
      maxRenderTime: 0,
      renderCount: 0,
      errorCount: 0,
      memoryTrend: 'stable' as const,
      healthScore: 100
    };

    if (metric.metricType === 'render') {
      existing.renderCount++;
      existing.maxRenderTime = Math.max(existing.maxRenderTime, metric.duration);
      existing.averageRenderTime = (existing.averageRenderTime * (existing.renderCount - 1) + metric.duration) / existing.renderCount;
    }

    // Calculate health score
    existing.healthScore = this.calculateHealthScore(existing);

    this.componentHealthMap.set(metric.componentName, existing);
  }

  private calculateHealthScore(health: ComponentHealth): number {
    let score = 100;

    // Penalize slow render times
    if (health.averageRenderTime > 50) score -= 20;
    if (health.averageRenderTime > 100) score -= 30;
    if (health.maxRenderTime > 200) score -= 20;

    // Penalize errors
    const errorRate = health.errorCount / Math.max(health.renderCount, 1);
    score -= errorRate * 50;

    return Math.max(0, Math.min(100, score));
  }

  private checkForPerformanceIssues(metric: PerformanceMetric): void {
    const alerts: PerformanceAlert[] = [];

    // Check for slow renders
    if (metric.metricType === 'render' && metric.duration > 100) {
      alerts.push({
        type: 'slow_render',
        componentName: metric.componentName,
        message: `Slow render detected: ${metric.duration.toFixed(2)}ms`,
        severity: metric.duration > 200 ? 'high' : 'medium',
        timestamp: metric.timestamp,
        metrics: [metric]
      });
    }

    // Check for chart performance issues
    if (metric.metricType === 'chart_create' && metric.duration > 500) {
      alerts.push({
        type: 'chart_performance',
        componentName: metric.componentName,
        message: `Slow chart creation: ${metric.duration.toFixed(2)}ms`,
        severity: metric.duration > 1000 ? 'high' : 'medium',
        timestamp: metric.timestamp,
        metrics: [metric]
      });
    }

    // Check for memory issues
    if (metric.memoryUsage && metric.memoryUsage.used > metric.memoryUsage.total * 0.8) {
      alerts.push({
        type: 'memory_leak',
        componentName: metric.componentName,
        message: `High memory usage: ${(metric.memoryUsage.used / 1024 / 1024).toFixed(2)}MB`,
        severity: 'high',
        timestamp: metric.timestamp,
        metrics: [metric]
      });
    }

    this.alerts.push(...alerts);

    // Keep only recent alerts
    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    this.alerts = this.alerts.filter(alert => new Date(alert.timestamp) > oneHourAgo);
  }

  private performHealthCheck(): void {
    this.componentHealthMap.forEach((health, componentName) => {
      // Check for frequent errors
      const recentMetrics = this.metrics.filter(
        m => m.componentName === componentName && 
        new Date(m.timestamp) > new Date(Date.now() - 5 * 60 * 1000) // Last 5 minutes
      );

      const errorRate = health.errorCount / Math.max(health.renderCount, 1);
      if (errorRate > 0.1) { // More than 10% error rate
        this.alerts.push({
          type: 'frequent_errors',
          componentName,
          message: `High error rate: ${(errorRate * 100).toFixed(1)}%`,
          severity: errorRate > 0.2 ? 'high' : 'medium',
          timestamp: new Date().toISOString(),
          metrics: recentMetrics
        });
      }
    });
  }

  private monitorMemoryUsage(): void {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const usagePercent = memory.usedJSHeapSize / memory.totalJSHeapSize;

      if (usagePercent > 0.9) {
        console.warn('High memory usage detected:', {
          used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(2)}MB`,
          percentage: `${(usagePercent * 100).toFixed(1)}%`
        });
      }
    }
  }

  public getComponentHealth(componentName?: string): ComponentHealth | ComponentHealth[] {
    if (componentName) {
      return this.componentHealthMap.get(componentName) || {
        componentName,
        averageRenderTime: 0,
        maxRenderTime: 0,
        renderCount: 0,
        errorCount: 0,
        memoryTrend: 'stable',
        healthScore: 100
      };
    }

    return Array.from(this.componentHealthMap.values());
  }

  public getPerformanceAlerts(): PerformanceAlert[] {
    return [...this.alerts];
  }

  public getMetrics(componentName?: string, metricType?: PerformanceMetric['metricType']): PerformanceMetric[] {
    let filtered = [...this.metrics];

    if (componentName) {
      filtered = filtered.filter(m => m.componentName === componentName);
    }

    if (metricType) {
      filtered = filtered.filter(m => m.metricType === metricType);
    }

    return filtered;
  }

  public clearMetrics(): void {
    this.metrics = [];
    this.componentHealthMap.clear();
    this.alerts = [];
  }

  public generatePerformanceReport(): {
    summary: {
      totalComponents: number;
      averageHealthScore: number;
      totalAlerts: number;
      criticalAlerts: number;
    };
    componentHealth: ComponentHealth[];
    recentAlerts: PerformanceAlert[];
  } {
    const healthScores = Array.from(this.componentHealthMap.values()).map(h => h.healthScore);
    const averageHealthScore = healthScores.length > 0 
      ? healthScores.reduce((a, b) => a + b, 0) / healthScores.length 
      : 100;

    const criticalAlerts = this.alerts.filter(a => a.severity === 'high').length;

    return {
      summary: {
        totalComponents: this.componentHealthMap.size,
        averageHealthScore: Math.round(averageHealthScore),
        totalAlerts: this.alerts.length,
        criticalAlerts
      },
      componentHealth: Array.from(this.componentHealthMap.values()),
      recentAlerts: this.alerts.slice(-10)
    };
  }
}

// Singleton instance
export const performanceMonitoringService = new PerformanceMonitoringService();

// Convenience functions for components
export const startPerformanceTimer = (componentName: string, operation: string) => {
  return performanceMonitoringService.startTiming(componentName, operation);
};

export const recordPerformanceMetric = (metric: PerformanceMetric) => {
  performanceMonitoringService.recordMetric(metric);
};

export default performanceMonitoringService;
