import React, { useState, useEffect, useMemo } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { 
  RealTimeMetrics, 
  ComplianceBreakdown, 
  MetricAlert,
  MetricsCalculationService 
} from '../../services/metricsCalculationService';
import { Department, EnhancedPolicy } from '../../types/compliance';
import {
  TrendingUp,
  TrendingDown,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  Shield,
  FileText,
  Users,
  Target,
  BarChart3,
  <PERSON><PERSON>hart,
  LineChart,
  AlertCircle,
  RefreshCw,
  Filter,
  Download
} from 'lucide-react';

interface MetricsVisualizationProps {
  departments: Department[];
  policies: EnhancedPolicy[];
  refreshInterval?: number; // in milliseconds
  className?: string;
}

interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string[];
    borderColor?: string;
    borderWidth?: number;
  }[];
}

export const MetricsVisualization: React.FC<MetricsVisualizationProps> = ({
  departments,
  policies,
  refreshInterval = 30000, // 30 seconds default
  className = ''
}) => {
  const { mode } = useTheme();
  
  const [realTimeMetrics, setRealTimeMetrics] = useState<RealTimeMetrics | null>(null);
  const [complianceBreakdown, setComplianceBreakdown] = useState<ComplianceBreakdown | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [isAutoRefresh, setIsAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date());
  const [loading, setLoading] = useState(false);

  // Calculate metrics
  const calculateMetrics = useMemo(() => {
    return () => {
      if (departments.length === 0 || policies.length === 0) return;
      
      setLoading(true);
      try {
        const options = {
          timeRange: selectedTimeRange,
          departmentFilter: selectedDepartment === 'all' ? undefined : [selectedDepartment]
        };

        const realTime = MetricsCalculationService.calculateRealTimeMetrics(
          departments, 
          policies, 
          options
        );
        
        const breakdown = MetricsCalculationService.calculateComplianceBreakdown(
          departments, 
          policies, 
          options
        );

        setRealTimeMetrics(realTime);
        setComplianceBreakdown(breakdown);
        setLastUpdated(new Date());
      } catch (error) {
        console.error('Error calculating metrics:', error);
      } finally {
        setLoading(false);
      }
    };
  }, [departments, policies, selectedTimeRange, selectedDepartment]);

  // Initial calculation and auto-refresh
  useEffect(() => {
    calculateMetrics();
  }, [calculateMetrics]);

  useEffect(() => {
    if (!isAutoRefresh) return;

    const interval = setInterval(() => {
      calculateMetrics();
    }, refreshInterval);

    return () => clearInterval(interval);
  }, [calculateMetrics, refreshInterval, isAutoRefresh]);

  // Chart data preparation
  const complianceTrendData: ChartData = useMemo(() => {
    if (!realTimeMetrics) return { labels: [], datasets: [] };

    return {
      labels: realTimeMetrics.trendsData.dates,
      datasets: [
        {
          label: 'Compliance Score',
          data: realTimeMetrics.trendsData.compliance,
          borderColor: '#3b82f6',
          borderWidth: 2
        },
        {
          label: 'Risk Level',
          data: realTimeMetrics.trendsData.risks,
          borderColor: '#ef4444',
          borderWidth: 2
        },
        {
          label: 'Assessment Completion',
          data: realTimeMetrics.trendsData.assessments,
          borderColor: '#10b981',
          borderWidth: 2
        }
      ]
    };
  }, [realTimeMetrics]);

  const departmentScoreData: ChartData = useMemo(() => {
    if (!realTimeMetrics || !complianceBreakdown) return { labels: [], datasets: [] };

    const deptData = Object.entries(complianceBreakdown.byDepartment);
    
    return {
      labels: deptData.map(([_, dept]) => dept.name),
      datasets: [{
        label: 'Compliance Score',
        data: deptData.map(([_, dept]) => dept.score),
        backgroundColor: deptData.map(([_, dept]) => 
          dept.score >= 90 ? '#10b981' :
          dept.score >= 80 ? '#f59e0b' :
          dept.score >= 70 ? '#ef4444' : '#dc2626'
        )
      }]
    };
  }, [realTimeMetrics, complianceBreakdown]);

  const riskDistributionData: ChartData = useMemo(() => {
    if (!complianceBreakdown) return { labels: [], datasets: [] };

    const riskData = complianceBreakdown.byRisk.byLevel;
    
    return {
      labels: ['Low', 'Medium', 'High', 'Critical'],
      datasets: [{
        label: 'Risk Distribution',
        data: [riskData.low, riskData.medium, riskData.high, riskData.critical],
        backgroundColor: ['#10b981', '#f59e0b', '#ef4444', '#dc2626']
      }]
    };
  }, [complianceBreakdown]);

  // Alert severity color mapping
  const getAlertColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-600 bg-red-100 border-red-200';
      case 'high': return 'text-orange-600 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-600 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-blue-600 bg-blue-100 border-blue-200';
      default: return 'text-gray-600 bg-gray-100 border-gray-200';
    }
  };

  // Risk level color mapping
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Trend icon component
  const TrendIcon = ({ trend }: { trend: 'improving' | 'declining' | 'stable' }) => {
    switch (trend) {
      case 'improving':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'declining':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  if (!realTimeMetrics || !complianceBreakdown) {
    return (
      <div className={`flex items-center justify-center h-64 ${className}`}>
        <div className="text-center">
          <RefreshCw className="w-8 h-8 animate-spin text-primary mx-auto mb-2" />
          <p className="text-text-secondary">Loading metrics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header Controls */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-text mb-2">Real-Time Compliance Metrics</h2>
          <p className="text-text-secondary">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        </div>
        
        <div className="flex items-center space-x-4 mt-4 lg:mt-0">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            <option value="week">Last Week</option>
            <option value="month">Last Month</option>
            <option value="quarter">Last Quarter</option>
            <option value="year">Last Year</option>
          </select>
          
          <select
            value={selectedDepartment}
            onChange={(e) => setSelectedDepartment(e.target.value)}
            className="px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
          >
            <option value="all">All Departments</option>
            {departments.map(dept => (
              <option key={dept.id} value={dept.id}>{dept.name}</option>
            ))}
          </select>
          
          <button
            onClick={() => setIsAutoRefresh(!isAutoRefresh)}
            className={`px-3 py-2 rounded-lg border transition-colors ${
              isAutoRefresh 
                ? 'bg-green-100 text-green-700 border-green-200' 
                : 'bg-gray-100 text-gray-700 border-gray-200'
            }`}
          >
            Auto Refresh {isAutoRefresh ? 'ON' : 'OFF'}
          </button>
          
          <button
            onClick={calculateMetrics}
            disabled={loading}
            className="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="text-sm font-medium text-text-secondary">Overall Score</h3>
                <div className="text-2xl font-bold text-text">{realTimeMetrics.overallScore}%</div>
              </div>
            </div>
            <div className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(realTimeMetrics.riskLevel)}`}>
              {realTimeMetrics.riskLevel.toUpperCase()}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <TrendIcon trend={complianceBreakdown.trends.compliance.trend} />
            <span className="text-sm text-text-secondary">
              {complianceBreakdown.trends.compliance.change > 0 ? '+' : ''}
              {complianceBreakdown.trends.compliance.change.toFixed(1)}% from last period
            </span>
          </div>
        </div>

        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-100 rounded-lg">
              <FileText className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-text-secondary">Policy Compliance</h3>
              <div className="text-2xl font-bold text-text">{realTimeMetrics.policyCompliance}%</div>
            </div>
          </div>
          <div className="text-sm text-text-secondary">
            {complianceBreakdown.byRisk.mitigated}/{complianceBreakdown.byRisk.total} policies compliant
          </div>
        </div>

        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-purple-100 rounded-lg">
              <Target className="w-6 h-6 text-purple-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-text-secondary">Assessment Rate</h3>
              <div className="text-2xl font-bold text-text">
                {complianceBreakdown.trends.assessments.completionRate.toFixed(1)}%
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <TrendIcon trend={complianceBreakdown.trends.assessments.trend} />
            <span className="text-sm text-text-secondary">
              {complianceBreakdown.trends.assessments.change > 0 ? '+' : ''}
              {complianceBreakdown.trends.assessments.change.toFixed(1)}% completion rate
            </span>
          </div>
        </div>

        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-orange-100 rounded-lg">
              <AlertTriangle className="w-6 h-6 text-orange-600" />
            </div>
            <div>
              <h3 className="text-sm font-medium text-text-secondary">Active Alerts</h3>
              <div className="text-2xl font-bold text-text">{realTimeMetrics.alerts.length}</div>
            </div>
          </div>
          <div className="text-sm text-text-secondary">
            {realTimeMetrics.alerts.filter(a => a.severity === 'critical').length} critical alerts
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Compliance Trends Chart */}
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-text">Compliance Trends</h3>
            <LineChart className="w-5 h-5 text-text-secondary" />
          </div>
          <div className="h-64 flex items-center justify-center border-2 border-dashed border-border rounded-lg">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-text-secondary mx-auto mb-2" />
              <p className="text-text-secondary">Line chart visualization</p>
              <p className="text-xs text-text-secondary mt-1">
                {complianceTrendData.datasets.length} data series over {complianceTrendData.labels.length} periods
              </p>
            </div>
          </div>
        </div>

        {/* Department Scores Chart */}
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-text">Department Scores</h3>
            <BarChart3 className="w-5 h-5 text-text-secondary" />
          </div>
          <div className="h-64 flex items-center justify-center border-2 border-dashed border-border rounded-lg">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-text-secondary mx-auto mb-2" />
              <p className="text-text-secondary">Bar chart visualization</p>
              <p className="text-xs text-text-secondary mt-1">
                {departmentScoreData.labels.length} departments
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Risk Distribution and Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Risk Distribution */}
        <div className="bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-text">Risk Distribution</h3>
            <PieChart className="w-5 h-5 text-text-secondary" />
          </div>
          <div className="space-y-4">
            {Object.entries(complianceBreakdown.byRisk.byLevel).map(([level, count]) => (
              <div key={level} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    level === 'low' ? 'bg-green-500' :
                    level === 'medium' ? 'bg-yellow-500' :
                    level === 'high' ? 'bg-orange-500' : 'bg-red-500'
                  }`} />
                  <span className="text-sm font-medium text-text capitalize">{level}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-semibold text-text">{count}</span>
                  <span className="text-xs text-text-secondary">
                    ({((count / complianceBreakdown.byRisk.total) * 100).toFixed(1)}%)
                  </span>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-6 pt-4 border-t border-border">
            <div className="grid grid-cols-2 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-green-600">{complianceBreakdown.byRisk.mitigated}</div>
                <div className="text-xs text-text-secondary">Mitigated</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">{complianceBreakdown.byRisk.open}</div>
                <div className="text-xs text-text-secondary">Open</div>
              </div>
            </div>
          </div>
        </div>

        {/* Active Alerts */}
        <div className="lg:col-span-2 bg-surface border border-border rounded-lg p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-text">Active Alerts</h3>
            <div className="flex items-center space-x-2">
              <AlertCircle className="w-5 h-5 text-text-secondary" />
              <span className="text-sm text-text-secondary">
                {realTimeMetrics.alerts.filter(a => a.actionRequired).length} require action
              </span>
            </div>
          </div>

          <div className="space-y-3 max-h-80 overflow-y-auto">
            {realTimeMetrics.alerts.length === 0 ? (
              <div className="text-center py-8">
                <CheckCircle className="w-12 h-12 text-green-500 mx-auto mb-2" />
                <p className="text-text-secondary">No active alerts</p>
                <p className="text-xs text-text-secondary mt-1">All systems are operating normally</p>
              </div>
            ) : (
              realTimeMetrics.alerts.map(alert => (
                <div
                  key={alert.id}
                  className={`p-4 rounded-lg border ${getAlertColor(alert.severity)}`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {alert.type === 'error' ? (
                          <AlertCircle className="w-4 h-4" />
                        ) : alert.type === 'warning' ? (
                          <AlertTriangle className="w-4 h-4" />
                        ) : (
                          <CheckCircle className="w-4 h-4" />
                        )}
                        <h4 className="text-sm font-semibold">{alert.title}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          alert.severity === 'critical' ? 'bg-red-200 text-red-800' :
                          alert.severity === 'high' ? 'bg-orange-200 text-orange-800' :
                          alert.severity === 'medium' ? 'bg-yellow-200 text-yellow-800' :
                          'bg-blue-200 text-blue-800'
                        }`}>
                          {alert.severity.toUpperCase()}
                        </span>
                      </div>
                      <p className="text-sm mb-2">{alert.message}</p>
                      <div className="flex items-center space-x-4 text-xs text-opacity-75">
                        <span>{alert.timestamp.toLocaleTimeString()}</span>
                        {alert.departmentId && (
                          <span>
                            Dept: {departments.find(d => d.id === alert.departmentId)?.name || 'Unknown'}
                          </span>
                        )}
                        {alert.policyId && (
                          <span>
                            Policy: {policies.find(p => p.id === alert.policyId)?.name || 'Unknown'}
                          </span>
                        )}
                      </div>
                    </div>

                    {alert.actionRequired && (
                      <button className="ml-4 px-3 py-1 bg-white bg-opacity-20 rounded text-xs font-medium hover:bg-opacity-30 transition-colors">
                        Take Action
                      </button>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </div>

      {/* Department Performance Details */}
      <div className="bg-surface border border-border rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-text">Department Performance Details</h3>
          <button className="flex items-center space-x-2 px-3 py-1 text-sm text-text-secondary hover:text-primary transition-colors">
            <Download className="w-4 h-4" />
            <span>Export Report</span>
          </button>
        </div>

        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Department</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Score</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Trend</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Assessments</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Policies</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Risk Level</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Last Updated</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(complianceBreakdown.byDepartment).map(([deptId, deptMetrics]) => {
                const dept = departments.find(d => d.id === deptId);
                if (!dept) return null;

                return (
                  <tr key={deptId} className="border-b border-border hover:bg-surface-hover">
                    <td className="py-3 px-4">
                      <div className="font-medium text-text">{deptMetrics.name}</div>
                      <div className="text-xs text-text-secondary">{dept.employeeCount} employees</div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-2">
                        <span className="font-semibold text-text">{deptMetrics.score}%</span>
                        <div className={`w-2 h-2 rounded-full ${
                          deptMetrics.score >= 90 ? 'bg-green-500' :
                          deptMetrics.score >= 80 ? 'bg-yellow-500' :
                          deptMetrics.score >= 70 ? 'bg-orange-500' : 'bg-red-500'
                        }`} />
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center space-x-1">
                        <TrendIcon trend={deptMetrics.trend} />
                        <span className="text-sm text-text-secondary capitalize">{deptMetrics.trend}</span>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <div className="text-text">{deptMetrics.assessments.completed}/{deptMetrics.assessments.total}</div>
                        <div className="text-text-secondary">
                          {deptMetrics.assessments.overdue > 0 && (
                            <span className="text-red-600">{deptMetrics.assessments.overdue} overdue</span>
                          )}
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="text-sm">
                        <div className="text-text">{deptMetrics.policies.compliant}/{deptMetrics.policies.total}</div>
                        <div className="text-text-secondary">compliant</div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(dept.riskLevel)}`}>
                        {dept.riskLevel.toUpperCase()}
                      </span>
                    </td>
                    <td className="py-3 px-4 text-sm text-text-secondary">
                      {deptMetrics.lastUpdated.toLocaleDateString()}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};
