import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { render } from '../utils/test-utils';

/**
 * This test suite specifically verifies that all the components that were originally failing
 * due to "Failed to fetch dynamically imported module" errors now work correctly.
 * 
 * Original failing components:
 * 1. Enhanced Compliance Metrics Dashboard
 * 2. Organizational Mapping Dashboard  
 * 3. Policies Management Dashboard
 * 4. Consent Management Center
 * 5. Impact Assessments (DPIA)
 */

// Import all the originally failing components
const EnhancedComplianceMetricsOptimized = React.lazy(() => import('../../components/compliance/EnhancedComplianceMetricsOptimized'));
const OrganizationalMappingDashboard = React.lazy(() => import('../../components/GDPR/OrganizationalMappingDashboard'));
const PoliciesManagementDashboard = React.lazy(() => import('../../components/GDPR/PoliciesManagementDashboard'));
const ConsentManagementDashboard = React.lazy(() => import('../../components/GDPR/ConsentManagementDashboard'));
const ImpactAssessmentDashboard = React.lazy(() => import('../../components/GDPR/ImpactAssessmentDashboard'));

// Mock all services to prevent actual API calls and ensure consistent test results
vi.mock('../../services/consentManagementService', () => ({
  ConsentManagementService: {
    initialize: vi.fn(),
    getAllConsentRecords: vi.fn().mockResolvedValue([]),
    getAllConsentCategories: vi.fn().mockResolvedValue([]),
    getConsentMetrics: vi.fn().mockResolvedValue({
      totalUsers: 100,
      consentedUsers: 85,
      consentRate: 85,
      trend: 'up',
      weeklyChange: 2.3,
      monthlyChange: 5.7,
      lastUpdated: new Date(),
      categoryBreakdown: []
    }),
    getConsentTrends: vi.fn().mockResolvedValue([])
  }
}));

vi.mock('../../services/impactAssessmentService', () => ({
  ImpactAssessmentService: {
    initialize: vi.fn(),
    getAllAssessments: vi.fn().mockResolvedValue([]),
    getAssessmentMetrics: vi.fn().mockResolvedValue({
      total: 10,
      completed: 7,
      inProgress: 2,
      overdue: 1,
      averageRiskScore: 65,
      highRiskAssessments: 3
    })
  }
}));

vi.mock('../../components/compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: () => ({
    overview: {
      totalMetrics: 25,
      compliantMetrics: 20,
      warningMetrics: 3,
      criticalMetrics: 2,
      overallScore: 85,
      trendDirection: 'up',
      lastAssessment: new Date(),
      nextAssessment: new Date()
    },
    metrics: [
      {
        id: 'metric-1',
        name: 'Data Retention Policy',
        category: 'privacy',
        status: 'compliant',
        currentValue: 95,
        targetValue: 90,
        trend: 'up',
        lastUpdated: new Date()
      }
    ],
    frameworks: [
      {
        type: 'gdpr',
        name: 'GDPR',
        overallScore: 94.2,
        departments: [],
        policies: [],
        timeline: [],
        metrics: {
          totalDepartments: 0,
          compliantDepartments: 0,
          totalPolicies: 0,
          activePolicies: 0,
          totalViolations: 0
        }
      }
    ],
    categoryBreakdown: {
      privacy: { score: 90, count: 5, trend: 'up' },
      security: { score: 88, count: 8, trend: 'stable' },
      operational: { score: 85, count: 7, trend: 'up' },
      regulatory: { score: 92, count: 5, trend: 'up' }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30000,
      pendingUpdates: 0
    }
  })
}));

// Mock chart.js to prevent canvas issues in tests
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>,
  Radar: () => <div data-testid="radar-chart">Radar Chart</div>
}));

describe('Originally Failing Components Resolution', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Component Loading Resolution', () => {
    it('Enhanced Compliance Metrics Dashboard loads successfully', async () => {
      const { container } = render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      // Should not show loading indefinitely
      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      // Should render the component successfully
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should not have any error boundaries triggered
      expect(container.querySelector('[data-testid="error-boundary"]')).toBeNull();
    });

    it('Organizational Mapping Dashboard loads successfully', async () => {
      const { container } = render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      }, { timeout: 5000 });

      expect(container.querySelector('[data-testid="error-boundary"]')).toBeNull();
    });

    it('Policies Management Dashboard loads successfully', async () => {
      const { container } = render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      }, { timeout: 5000 });

      expect(container.querySelector('[data-testid="error-boundary"]')).toBeNull();
    });

    it('Consent Management Center loads successfully', async () => {
      const { container } = render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });

      expect(container.querySelector('[data-testid="error-boundary"]')).toBeNull();
    });

    it('Impact Assessments (DPIA) Dashboard loads successfully', async () => {
      const { container } = render(
        <React.Suspense fallback={<div data-testid="loading">Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.queryByTestId('loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });

      expect(container.querySelector('[data-testid="error-boundary"]')).toBeNull();
    });
  });

  describe('Simultaneous Loading Test', () => {
    it('all originally failing components can load simultaneously without conflicts', async () => {
      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div data-testid="loading-compliance">Loading Compliance...</div>}>
            <EnhancedComplianceMetricsOptimized />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-org">Loading Organizational...</div>}>
            <OrganizationalMappingDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-policies">Loading Policies...</div>}>
            <PoliciesManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-consent">Loading Consent...</div>}>
            <ConsentManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-impact">Loading Impact...</div>}>
            <ImpactAssessmentDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Wait for all components to load
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Ensure no loading indicators remain
      expect(screen.queryByTestId('loading-compliance')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-org')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-policies')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-consent')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-impact')).not.toBeInTheDocument();
    });
  });

  describe('Error Boundary Functionality', () => {
    it('components have functional error boundaries that prevent crashes', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      // Test each component individually to ensure error boundaries work
      const components = [
        { Component: EnhancedComplianceMetricsOptimized, name: 'Enhanced Compliance Metrics' },
        { Component: OrganizationalMappingDashboard, name: 'Organizational Mapping' },
        { Component: PoliciesManagementDashboard, name: 'Policies Management' },
        { Component: ConsentManagementDashboard, name: 'Consent Management Center' },
        { Component: ImpactAssessmentDashboard, name: 'Impact Assessments' }
      ];

      for (const { Component, name } of components) {
        const { unmount } = render(
          <React.Suspense fallback={<div>Loading...</div>}>
            <Component />
          </React.Suspense>
        );

        await waitFor(() => {
          expect(screen.getByText(name)).toBeInTheDocument();
        }, { timeout: 5000 });

        // Component should unmount cleanly
        expect(() => unmount()).not.toThrow();
      }

      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Memory Management', () => {
    it('components load within acceptable time limits', async () => {
      const startTime = Date.now();

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      }, { timeout: 5000 });

      const loadTime = Date.now() - startTime;
      
      // Component should load within 5 seconds
      expect(loadTime).toBeLessThan(5000);
    });

    it('components clean up properly on unmount', async () => {
      const { unmount } = render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      });

      // Should unmount without errors or memory leaks
      expect(() => unmount()).not.toThrow();
    });
  });

  describe('Integration with Lazy Loading', () => {
    it('lazy loading works correctly for all components', async () => {
      // Verify that components are actually lazy-loaded by checking they don't exist initially
      const TestWrapper = () => {
        const [showComponent, setShowComponent] = React.useState(false);
        
        return (
          <div>
            <button onClick={() => setShowComponent(true)}>Load Component</button>
            {showComponent && (
              <React.Suspense fallback={<div data-testid="lazy-loading">Lazy Loading...</div>}>
                <EnhancedComplianceMetricsOptimized />
              </React.Suspense>
            )}
          </div>
        );
      };

      const user = require('@testing-library/user-event').default.setup();
      render(<TestWrapper />);

      // Component should not be loaded initially
      expect(screen.queryByText('Enhanced Compliance Metrics')).not.toBeInTheDocument();

      // Click to load component
      await user.click(screen.getByText('Load Component'));

      // Should show loading state briefly
      expect(screen.getByTestId('lazy-loading')).toBeInTheDocument();

      // Then load the actual component
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
        expect(screen.queryByTestId('lazy-loading')).not.toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });
});
