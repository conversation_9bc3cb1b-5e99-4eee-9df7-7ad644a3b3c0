import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { Line, Doughnut } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Shield, AlertTriangle, RefreshCw, Server, Bell, TrendingUp, XCircle,
  Plus, Edit, Save, X, AlertCircle, Clock, Wifi, WifiOff, FileText, Trash2
} from 'lucide-react';

// Import types and services
import {
  SecurityOverviewDashboardProps,
  SecurityOverviewData
} from './types/SecurityTypes';
import { generateSecurityOverviewData } from './services/SecurityDataService';

// Import UI components
import { FormInput as UIFormInput, FormSelect as UIFormSelect, FormTextarea as UIFormTextarea } from '../ui/FormInput';
import { Button as UIButton } from '../ui/Button';
import { EnhancedErrorBoundary } from '../ui/EnhancedErrorBoundary';

// Modal Component for CRUD operations
const Modal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-surface rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text">{title}</h3>
          <button
            onClick={onClose}
            className="p-2 hover:bg-card rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>
        {children}
      </div>
    </div>
  );
};

// Wrapper components to match expected interface
const FormInput: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  type?: string;
  placeholder?: string;
  required?: boolean;
}> = ({ label, value, onChange, type = 'text', placeholder, required = false }) => (
  <UIFormInput
    label={label}
    value={value}
    onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
    type={type}
    placeholder={placeholder}
    required={required}
  />
);

const FormSelect: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  options: Array<{ value: string; label: string }>;
  required?: boolean;
}> = ({ label, value, onChange, options, required = false }) => (
  <UIFormSelect
    label={label}
    value={value}
    onChange={(e: React.ChangeEvent<HTMLSelectElement>) => onChange(e.target.value)}
    options={options}
    required={required}
  />
);

const FormTextarea: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  required?: boolean;
}> = ({ label, value, onChange, placeholder, rows = 3, required = false }) => (
  <UIFormTextarea
    label={label}
    value={value}
    onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) => onChange(e.target.value)}
    placeholder={placeholder}
    rows={rows}
    required={required}
  />
);

// Main Security Overview Dashboard Component - Memoized to prevent unnecessary re-renders
const SecurityOverviewDashboard: React.FC<SecurityOverviewDashboardProps> = React.memo(({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<SecurityOverviewData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'threats' | 'incidents' | 'systems' | 'alerts'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  
  // Modal states
  const [isThreatModalOpen, setIsThreatModalOpen] = useState(false);
  const [isIncidentModalOpen, setIsIncidentModalOpen] = useState(false);
  const [isSystemModalOpen, setIsSystemModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // View modal states
  const [isThreatViewOpen, setIsThreatViewOpen] = useState(false);
  const [isIncidentViewOpen, setIsIncidentViewOpen] = useState(false);
  const [isSystemViewOpen, setIsSystemViewOpen] = useState(false);
  const [isAlertViewOpen, setIsAlertViewOpen] = useState(false);
  const [viewingItem, setViewingItem] = useState<any>(null);

  // Form state for editing
  const [formData, setFormData] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Memoize tab configuration to prevent recreation on every render
  const tabConfig = useMemo(() => [
    { id: 'overview', label: 'Overview', icon: Shield },
    { id: 'threats', label: 'Threats', icon: AlertTriangle },
    { id: 'incidents', label: 'Incidents', icon: AlertCircle },
    { id: 'systems', label: 'Systems', icon: Server },
    { id: 'alerts', label: 'Alerts', icon: Bell }
  ], []);

  // Memoize tab click handler to prevent recreation
  const handleTabClick = useCallback((tabId: string) => {
    setActiveTab(tabId as any);
  }, []);

  // Memoize chart theme to prevent recreation on every render
  const chartTheme = useMemo(() => getChartTheme(mode === 'dark'), [mode]);

  // Memoize chart options to prevent recreation and flickering
  const doughnutChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'bottom' as const,
        labels: {
          color: chartTheme.textColor,
          padding: 20
        }
      }
    }
  }), [chartTheme.textColor]);

  const lineChartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        labels: {
          color: chartTheme.textColor
        }
      }
    },
    scales: {
      x: {
        grid: {
          color: chartTheme.gridColor
        },
        ticks: {
          color: chartTheme.textSecondary
        }
      },
      y: {
        type: 'linear' as const,
        display: true,
        position: 'left' as const,
        grid: {
          color: chartTheme.gridColor
        },
        ticks: {
          color: chartTheme.textSecondary
        }
      },
      y1: {
        type: 'linear' as const,
        display: true,
        position: 'right' as const,
        grid: {
          drawOnChartArea: false,
        },
        ticks: {
          color: chartTheme.textSecondary
        }
      }
    }
  }), [chartTheme]);

  // Safe form handlers
  const handleFormChange = useCallback((field: string, value: string) => {
    try {
      setFormData((prev: any) => ({
        ...prev,
        [field]: value
      }));
      setSubmitError(null);
    } catch (error) {
      console.error('Form change error:', error);
      setSubmitError('Error updating form field');
    }
  }, []);

  const handleFormSubmit = useCallback(async (type: 'threat' | 'incident' | 'system') => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset form and close modal
      setFormData({});
      setEditingItem(null);

      // Close appropriate modal
      switch (type) {
        case 'threat':
          setIsThreatModalOpen(false);
          break;
        case 'incident':
          setIsIncidentModalOpen(false);
          break;
        case 'system':
          setIsSystemModalOpen(false);
          break;
      }

      // Show success message (you could add a toast notification here)
      console.log(`${type} saved successfully`);

    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitError('Failed to save. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [isSubmitting]);

  // Delete functions with confirmation
  const handleDeleteThreat = useCallback(async (threatId: string) => {
    if (!window.confirm('Are you sure you want to delete this threat? This action cannot be undone.')) {
      return;
    }

    try {
      setDashboardData(prev => prev ? {
        ...prev,
        threats: prev.threats.filter(threat => threat.id !== threatId)
      } : null);
    } catch (error) {
      console.error('Error deleting threat:', error);
    }
  }, []);

  const handleDeleteIncident = useCallback(async (incidentId: string) => {
    if (!window.confirm('Are you sure you want to delete this incident? This action cannot be undone.')) {
      return;
    }

    try {
      setDashboardData(prev => prev ? {
        ...prev,
        incidents: prev.incidents.filter(incident => incident.id !== incidentId)
      } : null);
    } catch (error) {
      console.error('Error deleting incident:', error);
    }
  }, []);

  const handleDeleteSystem = useCallback(async (systemId: string) => {
    if (!window.confirm('Are you sure you want to delete this system? This action cannot be undone.')) {
      return;
    }

    try {
      setDashboardData(prev => prev ? {
        ...prev,
        systems: prev.systems.filter(system => system.id !== systemId)
      } : null);
    } catch (error) {
      console.error('Error deleting system:', error);
    }
  }, []);

  const handleDeleteAlert = useCallback(async (alertId: string) => {
    if (!window.confirm('Are you sure you want to delete this alert? This action cannot be undone.')) {
      return;
    }

    try {
      setDashboardData(prev => prev ? {
        ...prev,
        alerts: prev.alerts.filter(alert => alert.id !== alertId)
      } : null);
    } catch (error) {
      console.error('Error deleting alert:', error);
    }
  }, []);

  // Optimized modal handlers to prevent re-renders
  const handleThreatModalClose = useCallback(() => {
    setFormData({});
    setEditingItem(null);
    setViewingItem(null);
    setSubmitError(null);
    setIsThreatModalOpen(false);
    setIsThreatViewOpen(false);
  }, []);

  const handleIncidentModalClose = useCallback(() => {
    setFormData({});
    setEditingItem(null);
    setViewingItem(null);
    setSubmitError(null);
    setIsIncidentModalOpen(false);
    setIsIncidentViewOpen(false);
  }, []);

  const handleSystemModalClose = useCallback(() => {
    setFormData({});
    setEditingItem(null);
    setViewingItem(null);
    setSubmitError(null);
    setIsSystemModalOpen(false);
    setIsSystemViewOpen(false);
  }, []);

  const handleAlertModalClose = useCallback(() => {
    setFormData({});
    setEditingItem(null);
    setViewingItem(null);
    setSubmitError(null);
    setIsAlertViewOpen(false);
  }, []);

  const handleModalClose = useCallback((type: 'threat' | 'incident' | 'system' | 'alert') => {
    try {
      switch (type) {
        case 'threat':
          handleThreatModalClose();
          break;
        case 'incident':
          handleIncidentModalClose();
          break;
        case 'system':
          handleSystemModalClose();
          break;
        case 'alert':
          handleAlertModalClose();
          break;
      }
    } catch (error) {
      console.error('Modal close error:', error);
    }
  }, [handleThreatModalClose, handleIncidentModalClose, handleSystemModalClose, handleAlertModalClose]);

  // Enhanced data loading with retry mechanism and better error handling
  const loadData = useCallback(async (retryCount = 0) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate network delay with exponential backoff for retries
      const delay = retryCount > 0 ? Math.min(1000 * Math.pow(2, retryCount), 5000) : 1200;
      await new Promise(resolve => setTimeout(resolve, delay));

      // Enhanced error handling for service initialization
      let data: SecurityOverviewData;
      try {
        data = generateSecurityOverviewData();
      } catch (serviceError) {
        throw new Error(`Security data service initialization failed: ${serviceError instanceof Error ? serviceError.message : 'Service unavailable'}`);
      }

      if (!data) {
        throw new Error('Security data service returned empty response. This may indicate a service configuration issue.');
      }

      // Comprehensive data structure validation
      const validationErrors: string[] = [];
      if (!data.overview) validationErrors.push('overview metrics');
      if (!data.systems) validationErrors.push('system information');
      if (!data.threats) validationErrors.push('threat data');
      if (!data.alerts) validationErrors.push('alert information');
      if (!data.incidents) validationErrors.push('incident data');

      if (validationErrors.length > 0) {
        throw new Error(`Invalid data structure - missing: ${validationErrors.join(', ')}. This indicates a data service compatibility issue.`);
      }

      // Additional data quality checks
      if (data.systems.length === 0) {
        console.warn('No systems found in security data - this may indicate a configuration issue');
      }

      setDashboardData(data);
      setError(null);
      console.log('✅ Security Overview data loaded successfully', {
        systems: data.systems.length,
        threats: data.threats.length,
        alerts: data.alerts.length,
        incidents: data.incidents.length
      });
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred while loading security data';
      console.error('🚨 Security Overview Dashboard data loading error:', {
        error: err,
        retryCount,
        timestamp: new Date().toISOString()
      });

      // Enhanced retry logic for different error types
      const isRetryableError = errorMessage.includes('service') ||
                              errorMessage.includes('network') ||
                              errorMessage.includes('timeout') ||
                              errorMessage.includes('initialization');

      if (retryCount < 3 && isRetryableError) {
        console.log(`🔄 Retrying security data load (attempt ${retryCount + 1}/3) in ${2000 * (retryCount + 1)}ms...`);
        setTimeout(() => loadData(retryCount + 1), 2000 * (retryCount + 1));
        return;
      }

      // Provide specific error messages based on error type
      let userFriendlyError = errorMessage;
      if (errorMessage.includes('service')) {
        userFriendlyError = 'Security monitoring service is currently unavailable. Please check your network connection and try again.';
      } else if (errorMessage.includes('data structure')) {
        userFriendlyError = 'Security data format is incompatible. Please contact your system administrator.';
      } else if (errorMessage.includes('initialization')) {
        userFriendlyError = 'Security monitoring system failed to initialize. Please refresh the page or contact support.';
      }

      setError(userFriendlyError);
      setDashboardData(null);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load initial data - only run once on mount
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Set up real-time updates with stable reference - separate from data loading
  useEffect(() => {
    if (!dashboardData || isLoading || error) return;

    const interval = setInterval(() => {
      try {
        setDashboardData(prev => {
          if (!prev) return null;
          
          return {
            ...prev,
            realTimeUpdates: {
              ...prev.realTimeUpdates,
              lastSync: new Date(),
              pendingAlerts: Math.floor(Math.random() * 10),
              isLive: true
            }
          };
        });
      } catch (err) {
        console.error('Real-time update error:', err);
      }
    }, 15000); // Update every 15 seconds for security dashboard

    return () => clearInterval(interval);
  }, [dashboardData?.overview?.securityScore, isLoading, error]); // Only depend on stable data presence indicators

  // Generate threat distribution chart with stable memoization
  const threatDistributionData = useMemo(() => {
    if (!dashboardData?.threats || !Array.isArray(dashboardData.threats)) return null;

    try {
      const statusCounts = dashboardData.threats.reduce((acc, threat) => {
        if (!threat) return acc;
        const status = threat?.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      return {
        labels: Object.keys(statusCounts).map(status => 
          status ? status.charAt(0).toUpperCase() + status.slice(1).replace('_', ' ') : 'Unknown'
        ),
        datasets: [{
          data: Object.values(statusCounts),
          backgroundColor: [
            'rgba(248, 113, 113, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(59, 130, 246, 0.8)',
            'rgba(52, 211, 153, 0.8)'
          ],
          borderColor: [
            'rgb(248, 113, 113)',
            'rgb(251, 191, 36)',
            'rgb(59, 130, 246)',
            'rgb(52, 211, 153)'
          ],
          borderWidth: 2,
          hoverOffset: 8
        }]
      };
    } catch (error) {
      console.error('Error generating threat distribution chart data:', error);
      return null;
    }
  }, [dashboardData?.threats?.length]); // Only recreate when threat count changes

  // Generate security trend chart with stable memoization
  const securityTrendData = useMemo(() => {
    if (!dashboardData?.metrics) return null;

    try {
      const last6Months = Array.from({ length: 6 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - (5 - i));
        return date.toLocaleDateString('en-US', { month: 'short' });
      });

      return {
        labels: last6Months,
        datasets: [{
          label: 'Security Score',
          data: last6Months.map(() => Math.floor(Math.random() * 20) + 80),
          borderColor: 'rgb(34, 197, 94)',
          backgroundColor: 'rgba(34, 197, 94, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'Threat Count',
          data: last6Months.map(() => Math.floor(Math.random() * 50) + 10),
          borderColor: 'rgb(239, 68, 68)',
          backgroundColor: 'rgba(239, 68, 68, 0.1)',
          tension: 0.4,
          fill: false,
          yAxisID: 'y1'
        }]
      };
    } catch (error) {
      console.error('Error generating security trend chart data:', error);
      return null;
    }
  }, [dashboardData?.overview?.securityScore]); // Only recreate when security score changes

  if (isLoading) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64 rounded-lg" />
            <LoadingSkeleton className="h-64 rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`} data-testid="security-overview-error">
        <div className="text-center py-8">
          <XCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-text mb-2">Security Overview Unavailable</h3>
          <p className="text-text-secondary mb-6 max-w-md mx-auto leading-relaxed">{error}</p>

          {/* Troubleshooting suggestions */}
          <div className="bg-card rounded-lg p-4 mb-6 text-left max-w-lg mx-auto">
            <h4 className="font-medium text-text mb-2 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-amber-500" />
              Troubleshooting Steps:
            </h4>
            <ul className="text-sm text-text-secondary space-y-1">
              <li>• Check your internet connection</li>
              <li>• Verify security monitoring services are running</li>
              <li>• Try refreshing the page</li>
              <li>• Contact your system administrator if the issue persists</li>
            </ul>
          </div>

          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <UIButton
              onClick={() => loadData()}
              disabled={isLoading}
              className="flex items-center gap-2"
              variant="primary"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Retrying...' : 'Retry Loading'}
            </UIButton>
            <UIButton
              variant="outline"
              onClick={() => window.location.reload()}
              className="flex items-center gap-2"
            >
              <RefreshCw className="w-4 h-4" />
              Reload Page
            </UIButton>
          </div>

          {/* Error details for debugging (only in development) */}
          {process.env.NODE_ENV === 'development' && (
            <details className="mt-6 text-left max-w-lg mx-auto">
              <summary className="cursor-pointer text-sm text-text-secondary hover:text-text">
                Show Technical Details
              </summary>
              <pre className="mt-2 p-3 bg-surface rounded text-xs text-text-secondary overflow-auto">
                {JSON.stringify({ error, timestamp: new Date().toISOString() }, null, 2)}
              </pre>
            </details>
          )}
        </div>
      </div>
    );
  }

  if (!dashboardData || !dashboardData.overview || !dashboardData.systems || !dashboardData.threats) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-amber-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">No Data Available</h3>
          <p className="text-text-secondary">Unable to load security overview data.</p>
        </div>
      </div>
    );
  }

  return (
    <EnhancedErrorBoundary
      componentName="SecurityOverviewDashboard"
      level="section"
      onError={(error, errorInfo, errorId) => {
        console.error('🚨 Security Overview Dashboard Error:', { error, errorInfo, errorId });
      }}
    >
      <div 
        key="security-overview-dashboard-main"
        className={`bg-surface rounded-lg p-6 ${className}`} 
        data-testid="security-overview"
      >
        <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Security Overview Dashboard</h1>
              <p className="text-text-secondary">Real-time security monitoring and threat intelligence</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
              <div className={`w-2 h-2 rounded-full ${
                dashboardData?.realTimeUpdates?.isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'
              }`} />
              <span className="text-xs text-text-secondary">
                {dashboardData?.realTimeUpdates?.isLive ? 'Live' : 'Offline'}
              </span>
              <span className="text-xs text-text-secondary">
                ({dashboardData?.realTimeUpdates?.systemStatus || 'Unknown'})
              </span>
            </div>
            <UIButton onClick={() => window.location.reload()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </UIButton>
          </div>
        </div>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Security Score</p>
                <p className="text-2xl font-bold text-text">{dashboardData?.overview?.securityScore || 0}</p>
                <p className="text-xs text-green-500 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +2.3% this week
                </p>
              </div>
              <Shield className="w-8 h-8 text-primary" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-red-500/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Active Threats</p>
                <p className="text-2xl font-bold text-red-500">{dashboardData?.overview?.activeThreats || 0}</p>
                <p className="text-xs text-text-secondary">Require attention</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-blue-500/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Systems Monitored</p>
                <p className="text-2xl font-bold text-blue-500">{dashboardData?.overview?.systemsMonitored || 0}</p>
                <p className="text-xs text-text-secondary">
                  {dashboardData?.systems?.filter(s => s?.monitoring?.isActive).length || 0} active
                </p>
              </div>
              <Server className="w-8 h-8 text-blue-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-amber-500/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Alerts (24h)</p>
                <p className="text-2xl font-bold text-amber-500">{dashboardData?.overview?.alertsLast24h || 0}</p>
                <p className="text-xs text-text-secondary">
                  {dashboardData?.realTimeUpdates?.pendingAlerts || 0} pending
                </p>
              </div>
              <Bell className="w-8 h-8 text-amber-500" />
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {tabConfig.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleTabClick(tab.id)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 cursor-pointer ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-text-secondary hover:text-text hover:border-border hover:bg-surface/50 rounded-t-lg'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  {tab.label}
                  {tab.id === 'threats' && dashboardData?.overview?.activeThreats > 0 && (
                    <span className="bg-red-500 text-white text-xs rounded-full px-2 py-0.5 ml-1">
                      {dashboardData.overview.activeThreats}
                    </span>
                  )}
                  {tab.id === 'alerts' && dashboardData?.realTimeUpdates?.pendingAlerts > 0 && (
                    <span className="bg-amber-500 text-white text-xs rounded-full px-2 py-0.5 ml-1">
                      {dashboardData.realTimeUpdates.pendingAlerts}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">Threat Status Distribution</h3>
                {threatDistributionData && (
                  <div className="h-64 flex items-center justify-center">
                    <Doughnut
                      key="threat-distribution-chart"
                      data={threatDistributionData}
                      options={doughnutChartOptions}
                    />
                  </div>
                )}
              </div>

              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">Security Trends</h3>
                {securityTrendData && (
                  <div className="h-64">
                    <Line
                      key="security-trends-chart"
                      data={securityTrendData}
                      options={lineChartOptions}
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Recent Activity */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">Recent Threats</h3>
                <div className="space-y-3">
                  {dashboardData.threats.slice(0, 5).map((threat) => (
                    <div key={threat.id} className="flex items-center justify-between p-3 bg-surface rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className={`w-3 h-3 rounded-full ${
                          threat.severity === 'critical' ? 'bg-red-500' :
                          threat.severity === 'high' ? 'bg-orange-500' :
                          threat.severity === 'medium' ? 'bg-yellow-500' :
                          'bg-green-500'
                        }`} />
                        <div>
                          <p className="font-medium text-text text-sm">{threat.title}</p>
                          <p className="text-xs text-text-secondary">
                            {threat?.detectedAt?.toLocaleDateString() || 'N/A'} • {threat.assignedTo}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          threat.status === 'resolved' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : threat.status === 'contained'
                            ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                            : threat.status === 'investigating'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {threat?.status?.replace('_', ' ') || 'Unknown'}
                        </span>

                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">System Status</h3>
                <div className="space-y-3">
                  {dashboardData.systems.slice(0, 5).map((system) => (
                    <div key={system.id} className="flex items-center justify-between p-3 bg-surface rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {system?.monitoring?.isActive ? (
                            <Wifi className="w-4 h-4 text-green-500" />
                          ) : (
                            <WifiOff className="w-4 h-4 text-red-500" />
                          )}
                          <Server className="w-4 h-4 text-text-secondary" />
                        </div>
                        <div>
                          <p className="font-medium text-text text-sm">{system.name}</p>
                          <p className="text-xs text-text-secondary">
                            Score: {system.securityScore}% • {system.type}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          system.status === 'secure' 
                            ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            : system.status === 'warning'
                            ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                            : system.status === 'compromised'
                            ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                            : 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {system.status}
                        </span>

                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Threats Tab */}
        {activeTab === 'threats' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Threats Tab Error"
            fallbackMessage="There was an error loading the threats tab. Please try refreshing the page."
          >
            <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <FormInput
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search threats..."
                />
                <FormSelect
                  label=""
                  value={filterStatus}
                  onChange={setFilterStatus}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'detected', label: 'Detected' },
                    { value: 'investigating', label: 'Investigating' },
                    { value: 'contained', label: 'Contained' },
                    { value: 'resolved', label: 'Resolved' }
                  ]}
                />
              </div>
              <UIButton onClick={() => setIsThreatModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Report Threat
              </UIButton>
            </div>

            <div className="bg-card rounded-lg border border-border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-surface">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Threat
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Severity
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Detected
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Assigned To
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {dashboardData.threats
                      .filter(threat =>
                        threat?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                        (filterStatus === 'all' || threat.status === filterStatus)
                      )
                      .map((threat) => (
                        <tr
                          key={threat.id}
                          className="hover:bg-surface transition-colors duration-150 cursor-pointer"
                          onClick={() => {
                            setViewingItem(threat);
                            setIsThreatViewOpen(true);
                          }}
                        >
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
                              <div>
                                <div className="text-sm font-medium text-text">{threat.title}</div>
                                <div className="text-sm text-text-secondary">{threat?.type?.replace('_', ' ') || 'Unknown'}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              threat.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                              threat.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                              threat.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            }`}>
                              {threat.severity}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              threat.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                              threat.status === 'contained' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                              threat.status === 'investigating' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                            }`}>
                              {threat?.status?.replace('_', ' ') || 'Unknown'}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                            {threat?.detectedAt?.toLocaleDateString() || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                            {threat.assignedTo}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium" onClick={(e) => e.stopPropagation()}>
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => {
                                  setEditingItem(threat);
                                  setIsThreatModalOpen(true);
                                }}
                                className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                                title="Edit Threat"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => handleDeleteThreat(threat.id)}
                                className="text-text-secondary hover:text-red-500 transition-colors cursor-pointer"
                                title="Delete Threat"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </div>
            </div>
          </ErrorBoundary>
        )}

        {/* Incidents Tab */}
        {activeTab === 'incidents' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Incidents Tab Error"
            fallbackMessage="There was an error loading the incidents tab. Please try refreshing the page."
          >
            <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <FormInput
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search incidents..."
                />
                <FormSelect
                  label=""
                  value={filterStatus}
                  onChange={setFilterStatus}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'open', label: 'Open' },
                    { value: 'investigating', label: 'Investigating' },
                    { value: 'contained', label: 'Contained' },
                    { value: 'resolved', label: 'Resolved' },
                    { value: 'closed', label: 'Closed' }
                  ]}
                />
              </div>
              <UIButton onClick={() => setIsIncidentModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Create Incident
              </UIButton>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dashboardData.incidents
                .filter(incident =>
                  incident?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                  (filterStatus === 'all' || incident.status === filterStatus)
                )
                .map((incident) => (
                  <div
                    key={incident.id}
                    className="bg-card rounded-lg p-6 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer"
                    onClick={() => {
                      setViewingItem(incident);
                      setIsIncidentViewOpen(true);
                    }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <AlertCircle className={`w-6 h-6 ${
                          incident.severity === 'critical' ? 'text-red-500' :
                          incident.severity === 'high' ? 'text-orange-500' :
                          incident.severity === 'medium' ? 'text-yellow-500' :
                          'text-green-500'
                        }`} />
                        <div>
                          <h3 className="font-semibold text-text">{incident.title}</h3>
                          <p className="text-sm text-text-secondary">{incident?.category?.replace('_', ' ') || 'Unknown'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                        <button
                          onClick={() => {
                            setEditingItem(incident);
                            setIsIncidentModalOpen(true);
                          }}
                          className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                          title="Edit Incident"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteIncident(incident.id)}
                          className="text-text-secondary hover:text-red-500 transition-colors cursor-pointer"
                          title="Delete Incident"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-sm text-text-secondary mb-4 line-clamp-2">
                      {incident.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Status</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          incident.status === 'resolved' || incident.status === 'closed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          incident.status === 'contained' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          incident.status === 'investigating' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {incident?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Severity</span>
                        <span className={`text-sm font-medium ${
                          incident.severity === 'critical' ? 'text-red-500' :
                          incident.severity === 'high' ? 'text-orange-500' :
                          incident.severity === 'medium' ? 'text-yellow-500' :
                          'text-green-500'
                        }`}>
                          {incident.severity}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Affected Users</span>
                        <span className="text-sm font-medium text-text">{incident?.affectedUsers?.toLocaleString() || '0'}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Data Compromised</span>
                        <span className={`text-sm font-medium ${incident.dataCompromised ? 'text-red-500' : 'text-green-500'}`}>
                          {incident.dataCompromised ? 'Yes' : 'No'}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Reported</span>
                        <span className="text-sm text-text">{incident?.reportedAt?.toLocaleDateString() || 'N/A'}</span>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-text-secondary">Assigned: {incident.assignedTo}</span>
                        <button className="text-primary hover:text-primary/80 text-sm">
                          View Timeline
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
            </div>
          </ErrorBoundary>
        )}

        {/* Systems Tab */}
        {activeTab === 'systems' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Systems Tab Error"
            fallbackMessage="There was an error loading the systems tab. Please try refreshing the page."
          >
            <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <FormInput
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search systems..."
                />
                <FormSelect
                  label=""
                  value={filterStatus}
                  onChange={setFilterStatus}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'secure', label: 'Secure' },
                    { value: 'warning', label: 'Warning' },
                    { value: 'compromised', label: 'Compromised' },
                    { value: 'offline', label: 'Offline' }
                  ]}
                />
              </div>
              <UIButton onClick={() => setIsSystemModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add System
              </UIButton>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dashboardData.systems
                .filter(system =>
                  system?.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                  (filterStatus === 'all' || system.status === filterStatus)
                )
                .map((system) => (
                  <div
                    key={system.id}
                    className="bg-card rounded-lg p-6 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer"
                    onClick={() => {
                      setViewingItem(system);
                      setIsSystemViewOpen(true);
                    }}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <div className="flex items-center gap-2">
                          {system?.monitoring?.isActive ? (
                            <Wifi className="w-5 h-5 text-green-500" />
                          ) : (
                            <WifiOff className="w-5 h-5 text-red-500" />
                          )}
                          <Server className="w-6 h-6 text-primary" />
                        </div>
                        <div>
                          <h3 className="font-semibold text-text">{system.name}</h3>
                          <p className="text-sm text-text-secondary capitalize">{system?.type || 'Unknown'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2" onClick={(e) => e.stopPropagation()}>
                        <button
                          onClick={() => {
                            setEditingItem(system);
                            setIsSystemModalOpen(true);
                          }}
                          className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                          title="Edit System"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteSystem(system.id)}
                          className="text-text-secondary hover:text-red-500 transition-colors cursor-pointer"
                          title="Delete System"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Status</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          system.status === 'secure' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          system.status === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          system.status === 'compromised' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {system.status}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Security Score</span>
                        <span className="text-sm font-medium text-text">{system.securityScore}%</span>
                      </div>

                      <div className="w-full bg-surface rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            system.securityScore >= 90 ? 'bg-green-500' :
                            system.securityScore >= 70 ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${system.securityScore}%` }}
                        />
                      </div>

                      <div className="grid grid-cols-2 gap-4 text-sm">
                        <div>
                          <span className="text-text-secondary">Critical:</span>
                          <span className={`ml-1 font-medium ${system.vulnerabilities.critical > 0 ? 'text-red-500' : 'text-green-500'}`}>
                            {system.vulnerabilities.critical}
                          </span>
                        </div>
                        <div>
                          <span className="text-text-secondary">High:</span>
                          <span className={`ml-1 font-medium ${system.vulnerabilities.high > 0 ? 'text-orange-500' : 'text-green-500'}`}>
                            {system.vulnerabilities.high}
                          </span>
                        </div>
                        <div>
                          <span className="text-text-secondary">Medium:</span>
                          <span className={`ml-1 font-medium ${system.vulnerabilities.medium > 0 ? 'text-yellow-500' : 'text-green-500'}`}>
                            {system.vulnerabilities.medium}
                          </span>
                        </div>
                        <div>
                          <span className="text-text-secondary">Low:</span>
                          <span className="ml-1 font-medium text-text">
                            {system.vulnerabilities.low}
                          </span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Last Scan</span>
                        <span className="text-sm text-text">{system?.lastScan?.toLocaleDateString() || 'N/A'}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Compliance</span>
                        <span className={`text-sm font-medium ${
                          system.compliance.status === 'compliant' ? 'text-green-500' :
                          system.compliance.status === 'partial' ? 'text-yellow-500' :
                          'text-red-500'
                        }`}>
                          {system?.compliance?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-text-secondary">Owner: {system.owner}</span>
                        <div className="flex items-center gap-1 text-xs text-text-secondary">
                          <Clock className="w-3 h-3" />
                          {system.monitoring.lastHeartbeat.toLocaleTimeString()}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
            </div>
          </ErrorBoundary>
        )}

        {/* Alerts Tab */}
        {activeTab === 'alerts' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Alerts Tab Error"
            fallbackMessage="There was an error loading the alerts tab. Please try refreshing the page."
          >
            <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <FormInput
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search alerts..."
                />
                <FormSelect
                  label=""
                  value={filterStatus}
                  onChange={setFilterStatus}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'new', label: 'New' },
                    { value: 'acknowledged', label: 'Acknowledged' },
                    { value: 'investigating', label: 'Investigating' },
                    { value: 'resolved', label: 'Resolved' },
                    { value: 'false_positive', label: 'False Positive' }
                  ]}
                />
              </div>
            </div>

            <div className="bg-card rounded-lg p-6 border border-border">
              <div className="space-y-4">
                {dashboardData.alerts
                  .filter(alert =>
                    alert?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                    (filterStatus === 'all' || alert.status === filterStatus)
                  )
                  .sort((a, b) => new Date(b.triggeredAt).getTime() - new Date(a.triggeredAt).getTime())
                  .map((alert) => (
                    <div
                      key={alert.id}
                      className="flex items-start gap-4 p-4 bg-surface rounded-lg hover:bg-card transition-colors duration-150 cursor-pointer"
                      onClick={() => {
                        setViewingItem(alert);
                        setIsAlertViewOpen(true);
                      }}
                    >
                      <div className={`w-3 h-3 rounded-full mt-2 ${
                        alert.severity === 'critical' ? 'bg-red-500' :
                        alert.severity === 'high' ? 'bg-orange-500' :
                        alert.severity === 'medium' ? 'bg-yellow-500' :
                        alert.severity === 'low' ? 'bg-blue-500' :
                        'bg-gray-500'
                      }`} />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-text">{alert.title}</h4>
                            <p className="text-sm text-text-secondary mt-1">{alert.description}</p>

                            <div className="flex items-center gap-4 mt-2 text-xs text-text-secondary">
                              <span className="flex items-center gap-1">
                                <Clock className="w-3 h-3" />
                                {alert?.triggeredAt?.toLocaleString() || 'N/A'}
                              </span>
                              <span className="flex items-center gap-1">
                                <Server className="w-3 h-3" />
                                {alert.source}
                              </span>
                              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                alert.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                                alert.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                                alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                                alert.severity === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                              }`}>
                                {alert.severity}
                              </span>
                              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                alert.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                alert.status === 'investigating' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                                alert.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                                alert.status === 'false_positive' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                                'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                              }`}>
                                {alert?.status ? alert.status.replace('_', ' ') : 'Unknown'}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4" onClick={(e) => e.stopPropagation()}>
                            {alert.status === 'new' && (
                              <UIButton size="sm" onClick={() => {}}>
                                Acknowledge
                              </UIButton>
                            )}
                            {/* Alert editing removed - not implemented in current version */}
                            {/* <button
                              onClick={() => {
                                setEditingItem(alert);
                                setIsAlertModalOpen(true);
                              }}
                              className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                              title="Edit Alert"
                            >
                              <Edit className="w-4 h-4" />
                            </button> */}
                            <button
                              onClick={() => handleDeleteAlert(alert.id)}
                              className="text-text-secondary hover:text-red-500 transition-colors cursor-pointer"
                              title="Delete Alert"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
              </div>
            </div>
            </div>
          </ErrorBoundary>
        )}

        {/* View Modals */}
        <Modal
          isOpen={isThreatViewOpen}
          onClose={() => {
            setIsThreatViewOpen(false);
            setViewingItem(null);
          }}
          title="Threat Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Threat Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Title:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.title || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Type:</span>
                        <span className="text-sm text-text">{viewingItem?.type?.replace('_', ' ') || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Source:</span>
                        <span className="text-sm text-text">{viewingItem?.source || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Detected:</span>
                        <span className="text-sm text-text">{viewingItem?.detectedAt?.toLocaleString() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Assigned To:</span>
                        <span className="text-sm text-text">{viewingItem.assignedTo}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Status & Severity</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Severity:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          viewingItem.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                          viewingItem.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        }`}>
                          {viewingItem?.severity?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'contained' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          viewingItem.status === 'investigating' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      {viewingItem.resolvedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Resolved:</span>
                          <span className="text-sm text-text">{viewingItem?.resolvedAt?.toLocaleString() || 'N/A'}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Impact Assessment</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Systems Affected:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.impact?.systems || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Users Affected:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.impact?.users?.toLocaleString() || '0'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Data Records:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.impact?.dataRecords?.toLocaleString() || '0'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Estimated Cost:</span>
                        <span className="text-sm font-medium text-text">${viewingItem?.impact?.estimatedCost?.toLocaleString() || '0'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Target Systems</h4>
                    <div className="bg-surface rounded-lg p-4">
                      <div className="flex flex-wrap gap-2">
                        {viewingItem?.targetSystems?.map((system: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            {system}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {viewingItem?.evidence && Array.isArray(viewingItem.evidence) && viewingItem.evidence.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Evidence</h4>
                      <div className="bg-surface rounded-lg p-4">
                        <div className="space-y-2">
                          {viewingItem?.evidence?.map((evidence: string, index: number) => (
                            <div key={index} className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-text-secondary" />
                              <span className="text-sm text-text">{evidence}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-text mb-2">Description</h4>
                <div className="bg-surface rounded-lg p-4">
                  <p className="text-sm text-text">{viewingItem.description}</p>
                </div>
              </div>

              {viewingItem?.mitigationSteps && Array.isArray(viewingItem.mitigationSteps) && viewingItem.mitigationSteps.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-text mb-2">Mitigation Steps</h4>
                  <div className="bg-surface rounded-lg p-4 max-h-48 overflow-y-auto">
                    <div className="space-y-3">
                      {viewingItem?.mitigationSteps?.map((step: any) => (
                        <div key={step.id} className="border-b border-border pb-2 last:border-b-0">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium text-text">{step.description}</div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              step.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                              step.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                              'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                            }`}>
                              {step?.status?.replace('_', ' ') || 'Unknown'}
                            </span>
                          </div>
                          <div className="text-xs text-text-secondary mt-1">
                            Assigned: {step.assignedTo} • Due: {step?.dueDate?.toLocaleDateString() || 'N/A'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <UIButton
                  variant="secondary"
                  onClick={() => {
                    setIsThreatViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </UIButton>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isIncidentViewOpen}
          onClose={() => {
            setIsIncidentViewOpen(false);
            setViewingItem(null);
          }}
          title="Incident Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Incident Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Title:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Category:</span>
                        <span className="text-sm text-text">{viewingItem?.category?.replace('_', ' ') || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Reported By:</span>
                        <span className="text-sm text-text">{viewingItem.reportedBy}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Assigned To:</span>
                        <span className="text-sm text-text">{viewingItem.assignedTo}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Reported:</span>
                        <span className="text-sm text-text">{viewingItem?.reportedAt?.toLocaleString() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Status & Impact</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Severity:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          viewingItem.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                          viewingItem.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        }`}>
                          {viewingItem?.severity?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'resolved' || viewingItem.status === 'closed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'contained' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          viewingItem.status === 'investigating' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Affected Users:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.affectedUsers?.toLocaleString() || '0'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Data Compromised:</span>
                        <span className={`text-sm font-medium ${viewingItem.dataCompromised ? 'text-red-500' : 'text-green-500'}`}>
                          {viewingItem.dataCompromised ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Timeline</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Reported:</span>
                        <span className="text-sm text-text">{viewingItem?.reportedAt?.toLocaleString() || 'N/A'}</span>
                      </div>
                      {viewingItem.acknowledgedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Acknowledged:</span>
                          <span className="text-sm text-text">{viewingItem?.acknowledgedAt?.toLocaleString() || 'N/A'}</span>
                        </div>
                      )}
                      {viewingItem.resolvedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Resolved:</span>
                          <span className="text-sm text-text">{viewingItem?.resolvedAt?.toLocaleString() || 'N/A'}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Affected Systems</h4>
                    <div className="bg-surface rounded-lg p-4">
                      <div className="flex flex-wrap gap-2">
                        {viewingItem?.affectedSystems?.map((system: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            {system}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {viewingItem?.attachments && Array.isArray(viewingItem.attachments) && viewingItem.attachments.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Attachments</h4>
                      <div className="bg-surface rounded-lg p-4">
                        <div className="space-y-2">
                          {viewingItem.attachments.map((attachment: string, index: number) => (
                            <div key={index} className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-text-secondary" />
                              <span className="text-sm text-text">{attachment}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-text mb-2">Description</h4>
                <div className="bg-surface rounded-lg p-4">
                  <p className="text-sm text-text">{viewingItem.description}</p>
                </div>
              </div>

              {viewingItem?.timeline && Array.isArray(viewingItem.timeline) && viewingItem.timeline.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-text mb-2">Activity Timeline</h4>
                  <div className="bg-surface rounded-lg p-4 max-h-48 overflow-y-auto">
                    <div className="space-y-3">
                      {viewingItem.timeline.map((activity: any, index: number) => (
                        <div key={index} className="border-b border-border pb-2 last:border-b-0">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium text-text">{activity.action}</div>
                            <div className="text-xs text-text-secondary">{activity?.timestamp?.toLocaleString() || 'N/A'}</div>
                          </div>
                          <div className="text-xs text-text-secondary mt-1">
                            By: {activity.performedBy}
                          </div>
                          {activity.notes && (
                            <div className="text-xs text-text mt-1">{activity.notes}</div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {viewingItem?.lessons && Array.isArray(viewingItem.lessons) && viewingItem.lessons.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-text mb-2">Lessons Learned</h4>
                  <div className="bg-surface rounded-lg p-4">
                    <div className="space-y-2">
                      {viewingItem.lessons.map((lesson: string, index: number) => (
                        <div key={index} className="text-sm text-text">• {lesson}</div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <UIButton
                  variant="secondary"
                  onClick={() => {
                    setIsIncidentViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </UIButton>
              </div>
            </div>
          )}
        </Modal>

        {/* Modals for CRUD operations */}
        <Modal
          isOpen={isThreatModalOpen}
          onClose={() => {
            setIsThreatModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit Threat' : 'Report Threat'}
        >
          <div className="space-y-4">
            <FormInput
              label="Threat Title"
              value={formData.title || editingItem?.title || ''}
              onChange={(value) => handleFormChange('title', value)}
              required
            />
            <FormTextarea
              label="Description"
              value={formData.description || editingItem?.description || ''}
              onChange={(value) => handleFormChange('description', value)}
              required
            />

            {submitError && (
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-700 dark:text-red-300">{submitError}</p>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <FormSelect
                label="Severity"
                value={editingItem?.severity || ''}
                onChange={() => {}}
                options={[
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                  { value: 'critical', label: 'Critical' }
                ]}
                required
              />
              <FormSelect
                label="Type"
                value={editingItem?.type || ''}
                onChange={() => {}}
                options={[
                  { value: 'malware', label: 'Malware' },
                  { value: 'phishing', label: 'Phishing' },
                  { value: 'ddos', label: 'DDoS' },
                  { value: 'unauthorized_access', label: 'Unauthorized Access' },
                  { value: 'data_breach', label: 'Data Breach' },
                  { value: 'insider_threat', label: 'Insider Threat' }
                ]}
                required
              />
            </div>
            <FormInput
              label="Assigned To"
              value={formData.assignedTo || editingItem?.assignedTo || ''}
              onChange={(value) => handleFormChange('assignedTo', value)}
              required
            />
            <div className="flex justify-end gap-2 pt-4">
              <UIButton
                variant="secondary"
                onClick={() => handleModalClose('threat')}
                disabled={isSubmitting}
              >
                Cancel
              </UIButton>
              <UIButton
                onClick={() => handleFormSubmit('threat')}
                disabled={isSubmitting}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save'}
              </UIButton>
            </div>
          </div>
        </Modal>

        <Modal
          isOpen={isSystemViewOpen}
          onClose={() => {
            setIsSystemViewOpen(false);
            setViewingItem(null);
          }}
          title="System Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid gols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">System Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Name:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Type:</span>
                        <span className="text-sm text-text capitalize">{viewingItem.type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Location:</span>
                        <span className="text-sm text-text">{viewingItem.location}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Owner:</span>
                        <span className="text-sm text-text">{viewingItem.owner}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Scan:</span>
                        <span className="text-sm text-text">{viewingItem?.lastScan?.toLocaleString() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Security Status</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'secure' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          viewingItem.status === 'compromised' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {viewingItem?.status?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Security Score:</span>
                        <span className="text-sm font-bold text-text">{viewingItem.securityScore}%</span>
                      </div>
                      <div className="w-full bg-surface rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${
                            viewingItem.securityScore >= 90 ? 'bg-green-500' :
                            viewingItem.securityScore >= 70 ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{ width: `${viewingItem.securityScore}%` }}
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Monitoring Status</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Monitoring Active:</span>
                        <span className={`text-sm font-medium ${viewingItem?.monitoring?.isActive ? 'text-green-500' : 'text-red-500'}`}>
                          {viewingItem?.monitoring?.isActive ? 'Yes' : 'No'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Heartbeat:</span>
                        <span className="text-sm text-text">{viewingItem?.monitoring?.lastHeartbeat?.toLocaleString() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Alerts Enabled:</span>
                        <span className={`text-sm font-medium ${viewingItem?.monitoring?.alertsEnabled ? 'text-green-500' : 'text-red-500'}`}>
                          {viewingItem?.monitoring?.alertsEnabled ? 'Yes' : 'No'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Vulnerability Assessment</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="grid grid-cols-2 gap-4">
                        <div className="text-center">
                          <div className={`text-lg font-bold ${(viewingItem?.vulnerabilities?.critical || 0) > 0 ? 'text-red-500' : 'text-green-500'}`}>
                            {viewingItem?.vulnerabilities?.critical || 0}
                          </div>
                          <div className="text-xs text-text-secondary">Critical</div>
                        </div>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${(viewingItem?.vulnerabilities?.high || 0) > 0 ? 'text-orange-500' : 'text-green-500'}`}>
                            {viewingItem?.vulnerabilities?.high || 0}
                          </div>
                          <div className="text-xs text-text-secondary">High</div>
                        </div>
                        <div className="text-center">
                          <div className={`text-lg font-bold ${(viewingItem?.vulnerabilities?.medium || 0) > 0 ? 'text-yellow-500' : 'text-green-500'}`}>
                            {viewingItem?.vulnerabilities?.medium || 0}
                          </div>
                          <div className="text-xs text-text-secondary">Medium</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-text">
                            {viewingItem?.vulnerabilities?.low || 0}
                          </div>
                          <div className="text-xs text-text-secondary">Low</div>
                        </div>
                      </div>
                      <div className="pt-2 border-t border-border">
                        <div className="text-center">
                          <div className="text-sm font-medium text-text">
                            Total: {Object.values(viewingItem?.vulnerabilities || {}).reduce((a: number, b: any) => a + (Number(b) || 0), 0)}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Compliance Status</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Framework:</span>
                        <span className="text-sm font-medium text-text">{viewingItem?.compliance?.framework || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem?.compliance?.status === 'compliant' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem?.compliance?.status === 'partial' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem?.compliance?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Assessment:</span>
                        <span className="text-sm text-text">{viewingItem?.compliance?.lastAssessment?.toLocaleDateString() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">System Dates</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Created:</span>
                        <span className="text-sm text-text">{viewingItem?.createdAt?.toLocaleDateString() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Updated:</span>
                        <span className="text-sm text-text">{viewingItem?.updatedAt?.toLocaleDateString() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <UIButton
                  variant="secondary"
                  onClick={() => {
                    setIsSystemViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </UIButton>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isAlertViewOpen}
          onClose={() => {
            setIsAlertViewOpen(false);
            setViewingItem(null);
          }}
          title="Alert Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Alert Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Title:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Category:</span>
                        <span className="text-sm text-text">{viewingItem?.category?.replace('_', ' ') || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Source:</span>
                        <span className="text-sm text-text">{viewingItem.source}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Triggered:</span>
                        <span className="text-sm text-text">{viewingItem?.triggeredAt?.toLocaleString() || 'N/A'}</span>
                      </div>
                      {viewingItem.assignedTo && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Assigned To:</span>
                          <span className="text-sm text-text">{viewingItem.assignedTo}</span>
                        </div>
                      )}
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Status & Severity</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Severity:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          viewingItem.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                          viewingItem.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          viewingItem.severity === 'low' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {viewingItem?.severity?.toUpperCase() || 'UNKNOWN'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'resolved' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'investigating' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          viewingItem.status === 'acknowledged' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          viewingItem.status === 'false_positive' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem?.status?.replace('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      {viewingItem.acknowledgedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Acknowledged:</span>
                          <span className="text-sm text-text">{viewingItem?.acknowledgedAt?.toLocaleString() || 'N/A'}</span>
                        </div>
                      )}
                      {viewingItem.resolvedAt && (
                        <div className="flex justify-between">
                          <span className="text-sm text-text-secondary">Resolved:</span>
                          <span className="text-sm text-text">{viewingItem?.resolvedAt?.toLocaleString() || 'N/A'}</span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Affected Systems</h4>
                    <div className="bg-surface rounded-lg p-4">
                      <div className="flex flex-wrap gap-2">
                        {viewingItem?.affectedSystems?.map((system: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            {system}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>

                  {viewingItem.metadata && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Technical Details</h4>
                      <div className="bg-surface rounded-lg p-4 space-y-2">
                        {Object.entries(viewingItem.metadata).map(([key, value]) => (
                          <div key={key} className="flex justify-between">
                            <span className="text-sm text-text-secondary capitalize">{key ? key.replace(/([A-Z])/g, ' $1') : 'Unknown'}:</span>
                            <span className="text-sm font-mono text-text">{String(value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {viewingItem?.actions && Array.isArray(viewingItem.actions) && viewingItem.actions.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Actions Taken</h4>
                      <div className="bg-surface rounded-lg p-4">
                        <div className="space-y-2">
                          {viewingItem.actions.map((action: any) => (
                            <div key={action.id} className="flex items-center justify-between">
                              <div className="flex items-center gap-2">
                                <span className="text-sm text-text capitalize">{action?.type?.replace('_', ' ') || 'Unknown'}</span>
                                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                  action.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                  action.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                                  'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                                }`}>
                                  {action.status}
                                </span>
                              </div>
                              <span className="text-xs text-text-secondary">{action?.timestamp?.toLocaleString() || 'N/A'}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-text mb-2">Description</h4>
                <div className="bg-surface rounded-lg p-4">
                  <p className="text-sm text-text">{viewingItem.description}</p>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <UIButton
                  variant="secondary"
                  onClick={() => {
                    setIsAlertViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </UIButton>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isIncidentModalOpen}
          onClose={() => {
            setIsIncidentModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit Incident' : 'Create Incident'}
        >
          <div className="space-y-4">
            <FormInput
              label="Incident Title"
              value={formData.title || editingItem?.title || ''}
              onChange={(value) => handleFormChange('title', value)}
              required
            />
            <FormTextarea
              label="Description"
              value={formData.description || editingItem?.description || ''}
              onChange={(value) => handleFormChange('description', value)}
              required
            />

            {submitError && (
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-700 dark:text-red-300">{submitError}</p>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <FormSelect
                label="Severity"
                value={editingItem?.severity || ''}
                onChange={() => {}}
                options={[
                  { value: 'low', label: 'Low' },
                  { value: 'medium', label: 'Medium' },
                  { value: 'high', label: 'High' },
                  { value: 'critical', label: 'Critical' }
                ]}
                required
              />
              <FormSelect
                label="Category"
                value={editingItem?.category || ''}
                onChange={() => {}}
                options={[
                  { value: 'security_breach', label: 'Security Breach' },
                  { value: 'policy_violation', label: 'Policy Violation' },
                  { value: 'system_compromise', label: 'System Compromise' },
                  { value: 'data_loss', label: 'Data Loss' },
                  { value: 'unauthorized_access', label: 'Unauthorized Access' }
                ]}
                required
              />
            </div>
            <FormInput
              label="Assigned To"
              value={formData.assignedTo || editingItem?.assignedTo || ''}
              onChange={(value) => handleFormChange('assignedTo', value)}
              required
            />
            <div className="flex justify-end gap-2 pt-4">
              <UIButton
                variant="secondary"
                onClick={() => handleModalClose('incident')}
                disabled={isSubmitting}
              >
                Cancel
              </UIButton>
              <UIButton
                onClick={() => handleFormSubmit('incident')}
                disabled={isSubmitting}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save'}
              </UIButton>
            </div>
          </div>
        </Modal>

        <Modal
          isOpen={isSystemModalOpen}
          onClose={() => {
            setIsSystemModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit System' : 'Add System'}
        >
          <div className="space-y-4">
            <FormInput
              label="System Name"
              value={formData.name || editingItem?.name || ''}
              onChange={(value) => handleFormChange('name', value)}
              required
            />

            {submitError && (
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-700 dark:text-red-300">{submitError}</p>
              </div>
            )}
            <div className="grid grid-cols-2 gap-4">
              <FormSelect
                label="Type"
                value={editingItem?.type || ''}
                onChange={() => {}}
                options={[
                  { value: 'endpoint', label: 'Endpoint' },
                  { value: 'network', label: 'Network' },
                  { value: 'server', label: 'Server' },
                  { value: 'database', label: 'Database' },
                  { value: 'application', label: 'Application' },
                  { value: 'cloud', label: 'Cloud' }
                ]}
                required
              />
              <FormInput
                label="Location"
                value={formData.location || editingItem?.location || ''}
                onChange={(value) => handleFormChange('location', value)}
                required
              />
            </div>
            <FormInput
              label="Owner"
              value={formData.owner || editingItem?.owner || ''}
              onChange={(value) => handleFormChange('owner', value)}
              required
            />
            <div className="flex justify-end gap-2 pt-4">
              <UIButton
                variant="secondary"
                onClick={() => handleModalClose('system')}
                disabled={isSubmitting}
              >
                Cancel
              </UIButton>
              <UIButton
                onClick={() => handleFormSubmit('system')}
                disabled={isSubmitting}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save'}
              </UIButton>
            </div>
          </div>
        </Modal>
      </div>
    </div>
    </EnhancedErrorBoundary>
  );
});

// Set display name for debugging
SecurityOverviewDashboard.displayName = 'SecurityOverviewDashboard';

export default SecurityOverviewDashboard;
