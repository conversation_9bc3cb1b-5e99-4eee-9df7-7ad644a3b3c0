import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  FileText,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Search,
  Calendar,
  Activity,
  TrendingUp,
  TrendingDown,
  Target,
  Eye,
  ChevronDown,
  ChevronRight,
  BarChart3,
  Layers,
  History,
  Users,
  Building,
  Globe,
  Scale,
  BookOpen,
  Award,
  AlertCircle,
  Zap,
  Shield,
  Lock,
  Key,
  Database,
  Server,
  Bell
} from 'lucide-react';

// Regulatory Framework Types
interface RegulatoryRequirement {
  id: string;
  name: string;
  description: string;
  framework: 'gdpr' | 'ccpa' | 'sox' | 'hipaa' | 'pci_dss' | 'iso27001' | 'nist';
  category: 'data_protection' | 'financial_reporting' | 'security' | 'privacy' | 'governance';
  priority: 'critical' | 'high' | 'medium' | 'low';
  status: 'compliant' | 'partial' | 'non_compliant' | 'not_applicable';
  complianceScore: number; // 0-100
  lastAssessed: Date;
  nextReview: Date;
  owner: string;
  evidence: Array<{
    id: string;
    type: 'document' | 'policy' | 'procedure' | 'audit' | 'certification';
    name: string;
    uploadDate: Date;
    status: 'approved' | 'pending' | 'rejected';
  }>;
  remediationActions: Array<{
    id: string;
    action: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignee: string;
    dueDate: Date;
    priority: 'critical' | 'high' | 'medium' | 'low';
  }>;
}

interface RegulatoryFramework {
  id: string;
  name: string;
  fullName: string;
  description: string;
  jurisdiction: string;
  effectiveDate: Date;
  lastUpdated: Date;
  overallScore: number; // 0-100
  status: 'compliant' | 'partial' | 'non_compliant';
  totalRequirements: number;
  compliantRequirements: number;
  partialRequirements: number;
  nonCompliantRequirements: number;
  nextAudit: Date;
  certificationStatus: 'certified' | 'pending' | 'expired' | 'not_applicable';
  requirements: RegulatoryRequirement[];
  historicalScores: Array<{
    date: string;
    score: number;
    compliantCount: number;
    totalCount: number;
  }>;
}

interface RegulatoryDashboardData {
  overview: {
    totalFrameworks: number;
    compliantFrameworks: number;
    partialFrameworks: number;
    nonCompliantFrameworks: number;
    averageComplianceScore: number;
    totalRequirements: number;
    pendingActions: number;
    upcomingAudits: number;
    expiredCertifications: number;
  };
  frameworks: RegulatoryFramework[];
  upcomingDeadlines: Array<{
    id: string;
    title: string;
    framework: string;
    type: 'audit' | 'certification' | 'review' | 'remediation';
    dueDate: Date;
    priority: 'critical' | 'high' | 'medium' | 'low';
    assignee: string;
  }>;
  recentActivity: Array<{
    id: string;
    timestamp: Date;
    user: string;
    action: string;
    framework: string;
    requirement?: string;
    details: string;
  }>;
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    pendingAssessments: number;
    recentChanges: number;
  };
}

interface RegulatoryFrameworkDashboardProps {
  className?: string;
}

// Mock data generator
const generateRegulatoryDashboardData = (): RegulatoryDashboardData => {
  const frameworks: RegulatoryFramework[] = [
    {
      id: 'gdpr',
      name: 'GDPR',
      fullName: 'General Data Protection Regulation',
      description: 'EU regulation on data protection and privacy',
      jurisdiction: 'European Union',
      effectiveDate: new Date('2018-05-25'),
      lastUpdated: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
      overallScore: 87.5,
      status: 'partial',
      totalRequirements: 45,
      compliantRequirements: 38,
      partialRequirements: 5,
      nonCompliantRequirements: 2,
      nextAudit: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
      certificationStatus: 'certified',
      requirements: [],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        score: 80 + Math.random() * 15,
        compliantCount: 35 + Math.floor(Math.random() * 8),
        totalCount: 45
      }))
    },
    {
      id: 'ccpa',
      name: 'CCPA',
      fullName: 'California Consumer Privacy Act',
      description: 'California state law on consumer privacy rights',
      jurisdiction: 'California, USA',
      effectiveDate: new Date('2020-01-01'),
      lastUpdated: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
      overallScore: 92.3,
      status: 'compliant',
      totalRequirements: 28,
      compliantRequirements: 26,
      partialRequirements: 2,
      nonCompliantRequirements: 0,
      nextAudit: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
      certificationStatus: 'certified',
      requirements: [],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        score: 85 + Math.random() * 10,
        compliantCount: 24 + Math.floor(Math.random() * 4),
        totalCount: 28
      }))
    },
    {
      id: 'sox',
      name: 'SOX',
      fullName: 'Sarbanes-Oxley Act',
      description: 'US federal law on corporate financial reporting',
      jurisdiction: 'United States',
      effectiveDate: new Date('2002-07-30'),
      lastUpdated: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
      overallScore: 78.9,
      status: 'partial',
      totalRequirements: 35,
      compliantRequirements: 25,
      partialRequirements: 7,
      nonCompliantRequirements: 3,
      nextAudit: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      certificationStatus: 'pending',
      requirements: [],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        score: 70 + Math.random() * 15,
        compliantCount: 20 + Math.floor(Math.random() * 10),
        totalCount: 35
      }))
    },
    {
      id: 'iso27001',
      name: 'ISO 27001',
      fullName: 'ISO/IEC 27001',
      description: 'International standard for information security management',
      jurisdiction: 'International',
      effectiveDate: new Date('2013-10-01'),
      lastUpdated: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
      overallScore: 94.1,
      status: 'compliant',
      totalRequirements: 114,
      compliantRequirements: 108,
      partialRequirements: 4,
      nonCompliantRequirements: 2,
      nextAudit: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
      certificationStatus: 'certified',
      requirements: [],
      historicalScores: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        score: 88 + Math.random() * 8,
        compliantCount: 100 + Math.floor(Math.random() * 12),
        totalCount: 114
      }))
    }
  ];

  const upcomingDeadlines = [
    {
      id: 'deadline-1',
      title: 'SOX Annual Audit',
      framework: 'SOX',
      type: 'audit' as const,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      priority: 'critical' as const,
      assignee: 'Audit Team'
    },
    {
      id: 'deadline-2',
      title: 'GDPR Data Mapping Review',
      framework: 'GDPR',
      type: 'review' as const,
      dueDate: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
      priority: 'high' as const,
      assignee: 'Privacy Officer'
    },
    {
      id: 'deadline-3',
      title: 'ISO 27001 Certification Renewal',
      framework: 'ISO 27001',
      type: 'certification' as const,
      dueDate: new Date(Date.now() + 120 * 24 * 60 * 60 * 1000),
      priority: 'medium' as const,
      assignee: 'Security Team'
    }
  ];

  const recentActivity = Array.from({ length: 20 }, (_, i) => ({
    id: `activity-${i + 1}`,
    timestamp: new Date(Date.now() - i * 2 * 60 * 60 * 1000),
    user: ['John Smith', 'Sarah Johnson', 'Michael Chen', 'David Wilson'][Math.floor(Math.random() * 4)],
    action: ['Updated', 'Reviewed', 'Approved', 'Assessed'][Math.floor(Math.random() * 4)],
    framework: ['GDPR', 'CCPA', 'SOX', 'ISO 27001'][Math.floor(Math.random() * 4)],
    requirement: `Requirement ${Math.floor(Math.random() * 50) + 1}`,
    details: 'Compliance assessment completed with updated documentation'
  }));

  const compliantFrameworks = frameworks.filter(f => f.status === 'compliant').length;
  const partialFrameworks = frameworks.filter(f => f.status === 'partial').length;
  const nonCompliantFrameworks = frameworks.filter(f => f.status === 'non_compliant').length;
  const averageComplianceScore = frameworks.reduce((sum, f) => sum + f.overallScore, 0) / frameworks.length;
  const totalRequirements = frameworks.reduce((sum, f) => sum + f.totalRequirements, 0);

  return {
    overview: {
      totalFrameworks: frameworks.length,
      compliantFrameworks,
      partialFrameworks,
      nonCompliantFrameworks,
      averageComplianceScore,
      totalRequirements,
      pendingActions: 12,
      upcomingAudits: 3,
      expiredCertifications: 1
    },
    frameworks,
    upcomingDeadlines,
    recentActivity,
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      pendingAssessments: Math.floor(Math.random() * 8),
      recentChanges: Math.floor(Math.random() * 5)
    }
  };
};

export const RegulatoryFrameworkDashboard: React.FC<RegulatoryFrameworkDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<RegulatoryDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFramework, setSelectedFramework] = useState<'all' | string>('all');
  const [selectedStatus, setSelectedStatus] = useState<'all' | 'compliant' | 'partial' | 'non_compliant'>('all');
  const [expandedFramework, setExpandedFramework] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'frameworks' | 'deadlines' | 'activity'>('overview');
  const [searchTerm, setSearchTerm] = useState('');

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1400));
        const data = generateRegulatoryDashboardData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load regulatory framework data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            pendingAssessments: Math.floor(Math.random() * 8),
            recentChanges: Math.floor(Math.random() * 5)
          }
        };
      });
    }, 45000); // Update every 45 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter frameworks based on criteria
  const filteredFrameworks = useMemo(() => {
    if (!dashboardData) return [];

    return dashboardData.frameworks.filter(framework => {
      const matchesFramework = selectedFramework === 'all' || framework.id === selectedFramework;
      const matchesStatus = selectedStatus === 'all' || framework.status === selectedStatus;
      const matchesSearch = searchTerm === '' ||
        framework.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        framework.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        framework.description.toLowerCase().includes(searchTerm.toLowerCase());

      return matchesFramework && matchesStatus && matchesSearch;
    });
  }, [dashboardData, selectedFramework, selectedStatus, searchTerm]);

  // Generate compliance overview chart
  const complianceOverviewData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: ['Compliant', 'Partial', 'Non-Compliant'],
      datasets: [{
        data: [
          dashboardData.overview.compliantFrameworks,
          dashboardData.overview.partialFrameworks,
          dashboardData.overview.nonCompliantFrameworks
        ],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)',
          'rgba(251, 191, 36, 0.8)',
          'rgba(248, 113, 113, 0.8)'
        ],
        borderColor: [
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
        hoverOffset: 8
      }]
    };
  }, [dashboardData]);

  // Generate framework scores chart
  const frameworkScoresData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.frameworks.map(f => f.name),
      datasets: [{
        label: 'Compliance Score',
        data: dashboardData.frameworks.map(f => f.overallScore),
        backgroundColor: dashboardData.frameworks.map(f =>
          f.status === 'compliant' ? 'rgba(52, 211, 153, 0.8)' :
          f.status === 'partial' ? 'rgba(251, 191, 36, 0.8)' :
          'rgba(248, 113, 113, 0.8)'
        ),
        borderColor: dashboardData.frameworks.map(f =>
          f.status === 'compliant' ? 'rgb(52, 211, 153)' :
          f.status === 'partial' ? 'rgb(251, 191, 36)' :
          'rgb(248, 113, 113)'
        ),
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  // Generate compliance trend chart
  const complianceTrendData = useMemo(() => {
    if (!dashboardData || filteredFrameworks.length === 0) return null;

    const last6Months = Array.from({ length: 6 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (5 - i));
      return date.toISOString().split('T')[0];
    });

    return {
      labels: last6Months.map(date => new Date(date).toLocaleDateString('en-US', { month: 'short', year: '2-digit' })),
      datasets: filteredFrameworks.map((framework, index) => ({
        label: framework.name,
        data: framework.historicalScores.slice(-6).map(h => h.score),
        borderColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)',
          'rgb(236, 72, 153)'
        ][index % 6],
        backgroundColor: [
          'rgba(79, 142, 247, 0.1)',
          'rgba(52, 211, 153, 0.1)',
          'rgba(251, 191, 36, 0.1)',
          'rgba(248, 113, 113, 0.1)',
          'rgba(139, 92, 246, 0.1)',
          'rgba(236, 72, 153, 0.1)'
        ][index % 6],
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 4,
        pointHoverRadius: 6,
      }))
    };
  }, [dashboardData, filteredFrameworks]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'partial':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'non_compliant':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'partial':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'non_compliant':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getPriorityColorClass = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'high':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'medium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getCertificationIcon = (status: string) => {
    switch (status) {
      case 'certified':
        return <Award className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-amber-500" />;
      case 'expired':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return <AlertCircle className="w-4 h-4 text-gray-500" />;
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000));
      const data = generateRegulatoryDashboardData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFrameworkClick = useCallback((frameworkId: string) => {
    setExpandedFramework(expandedFramework === frameworkId ? null : frameworkId);
  }, [expandedFramework]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-80" />
            <LoadingSkeleton className="h-80" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Regulatory Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Scale className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Regulatory Framework Tracking</h1>
              <p className="text-text-secondary">
                Multi-framework compliance monitoring with automated scoring and progress indicators
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • {dashboardData.realTimeUpdates.pendingAssessments} pending
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh regulatory data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Compliance Report
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total Frameworks</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.totalFrameworks}</p>
                </div>
                <Scale className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Avg Compliance</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.averageComplianceScore.toFixed(1)}%</p>
                </div>
                <Target className="w-8 h-8 text-blue-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Pending Actions</p>
                  <p className="text-2xl font-bold text-amber-500">{dashboardData.overview.pendingActions}</p>
                </div>
                <Clock className="w-8 h-8 text-amber-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Upcoming Audits</p>
                  <p className="text-2xl font-bold text-purple-500">{dashboardData.overview.upcomingAudits}</p>
                </div>
                <Calendar className="w-8 h-8 text-purple-500" />
              </div>
            </div>
          </div>
        )}

        {/* View Tabs */}
        <div className="flex items-center gap-2 mb-6">
          <button
            onClick={() => setActiveView('overview')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'overview'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <BarChart3 className="w-4 h-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveView('frameworks')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'frameworks'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Scale className="w-4 h-4 inline mr-2" />
            Frameworks
          </button>
          <button
            onClick={() => setActiveView('deadlines')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'deadlines'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Calendar className="w-4 h-4 inline mr-2" />
            Deadlines
          </button>
          <button
            onClick={() => setActiveView('activity')}
            className={`px-4 py-2 text-sm rounded-lg transition-colors ${
              activeView === 'activity'
                ? 'bg-primary text-white'
                : 'bg-border/50 text-text hover:bg-border'
            }`}
          >
            <Activity className="w-4 h-4 inline mr-2" />
            Activity
          </button>
        </div>
      </div>

      {/* Overview View */}
      {activeView === 'overview' && (
        <>
          {/* Charts Section */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Compliance Status Distribution */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <BarChart3 className="w-5 h-5" />
                Compliance Status Distribution
              </h3>
              {complianceOverviewData && (
                <div className="h-64 flex items-center justify-center">
                  <Doughnut
                    data={complianceOverviewData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          position: 'bottom',
                          labels: {
                            color: chartTheme.textColor,
                            padding: 20,
                            usePointStyle: true,
                          }
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>

            {/* Framework Scores */}
            <div className="bg-surface rounded-lg p-6">
              <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
                <Target className="w-5 h-5" />
                Framework Compliance Scores
              </h3>
              {frameworkScoresData && (
                <div className="h-64">
                  <Bar
                    data={frameworkScoresData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: false
                        },
                        tooltip: {
                          backgroundColor: chartTheme.tooltipBg,
                          titleColor: chartTheme.textColor,
                          bodyColor: chartTheme.textColor,
                          borderColor: chartTheme.borderColor,
                          borderWidth: 1,
                          callbacks: {
                            label: (context) => `Score: ${context.parsed.y.toFixed(1)}%`
                          }
                        }
                      },
                      scales: {
                        x: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                          }
                        },
                        y: {
                          grid: {
                            color: chartTheme.gridColor,
                          },
                          ticks: {
                            color: chartTheme.textSecondary,
                            callback: (value) => `${value}%`
                          },
                          min: 0,
                          max: 100
                        }
                      }
                    }}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Compliance Trend Chart */}
          <div className="bg-surface rounded-lg p-6">
            <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5" />
              Compliance Trends (Last 6 Months)
            </h3>
            {complianceTrendData && (
              <div className="h-80">
                <Line
                  data={complianceTrendData}
                  options={{
                    responsive: true,
                    maintainAspectRatio: false,
                    interaction: {
                      mode: 'index' as const,
                      intersect: false,
                    },
                    plugins: {
                      legend: {
                        position: 'bottom',
                        labels: {
                          color: chartTheme.textColor,
                          padding: 20,
                          usePointStyle: true,
                        }
                      },
                      tooltip: {
                        backgroundColor: chartTheme.tooltipBg,
                        titleColor: chartTheme.textColor,
                        bodyColor: chartTheme.textColor,
                        borderColor: chartTheme.borderColor,
                        borderWidth: 1,
                        callbacks: {
                          label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                        }
                      }
                    },
                    scales: {
                      x: {
                        grid: {
                          color: chartTheme.gridColor,
                        },
                        ticks: {
                          color: chartTheme.textSecondary,
                        }
                      },
                      y: {
                        grid: {
                          color: chartTheme.gridColor,
                        },
                        ticks: {
                          color: chartTheme.textSecondary,
                          callback: (value) => `${value}%`
                        },
                        min: 60,
                        max: 100
                      }
                    }
                  }}
                />
              </div>
            )}
          </div>
        </>
      )}

      {/* Frameworks View */}
      {activeView === 'frameworks' && (
        <div className="bg-surface rounded-lg p-6">
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search frameworks..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              />
            </div>

            <select
              value={selectedFramework}
              onChange={(e) => setSelectedFramework(e.target.value)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Frameworks</option>
              <option value="gdpr">GDPR</option>
              <option value="ccpa">CCPA</option>
              <option value="sox">SOX</option>
              <option value="iso27001">ISO 27001</option>
            </select>

            <select
              value={selectedStatus}
              onChange={(e) => setSelectedStatus(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Status</option>
              <option value="compliant">Compliant</option>
              <option value="partial">Partial</option>
              <option value="non_compliant">Non-Compliant</option>
            </select>
          </div>

          {/* Framework Cards */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-text">Regulatory Frameworks ({filteredFrameworks.length})</h3>

            {filteredFrameworks.length === 0 ? (
              <div className="text-center py-8">
                <Scale className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">No frameworks found matching your criteria.</p>
              </div>
            ) : (
              filteredFrameworks.map((framework) => (
                <div key={framework.id} className="bg-card rounded-lg border border-border overflow-hidden">
                  {/* Framework Header */}
                  <div
                    className="p-6 cursor-pointer hover:bg-border/30 transition-colors"
                    onClick={() => handleFrameworkClick(framework.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        {getStatusIcon(framework.status)}
                        <div>
                          <h4 className="text-xl font-semibold text-text">{framework.name}</h4>
                          <p className="text-lg text-text-secondary">{framework.fullName}</p>
                          <p className="text-sm text-text-secondary mt-1">{framework.description}</p>
                          <div className="flex items-center gap-4 text-sm text-text-secondary mt-2">
                            <span className="flex items-center gap-1">
                              <Globe className="w-3 h-3" />
                              {framework.jurisdiction}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="w-3 h-3" />
                              Effective: {framework.effectiveDate.toLocaleDateString()}
                            </span>
                            <span className="flex items-center gap-1">
                              {getCertificationIcon(framework.certificationStatus)}
                              {framework.certificationStatus.charAt(0).toUpperCase() + framework.certificationStatus.slice(1)}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-6">
                        <div className="text-center">
                          <p className="text-3xl font-bold text-text">{framework.overallScore.toFixed(1)}%</p>
                          <p className="text-sm text-text-secondary">Compliance Score</p>
                        </div>

                        <div className="flex items-center gap-2">
                          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColorClass(framework.status)}`}>
                            {framework.status.replace('_', ' ').charAt(0).toUpperCase() + framework.status.replace('_', ' ').slice(1)}
                          </span>

                          {expandedFramework === framework.id ? (
                            <ChevronDown className="w-5 h-5 text-text-secondary" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-text-secondary" />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Progress Indicators */}
                    <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-text-secondary">Compliant Requirements</span>
                          <span className="text-text font-medium">{framework.compliantRequirements}/{framework.totalRequirements}</span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2">
                          <div
                            className="h-2 bg-green-500 rounded-full transition-all duration-300"
                            style={{ width: `${(framework.compliantRequirements / framework.totalRequirements) * 100}%` }}
                          />
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-text-secondary">Partial Compliance</span>
                          <span className="text-text font-medium">{framework.partialRequirements}/{framework.totalRequirements}</span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2">
                          <div
                            className="h-2 bg-amber-500 rounded-full transition-all duration-300"
                            style={{ width: `${(framework.partialRequirements / framework.totalRequirements) * 100}%` }}
                          />
                        </div>
                      </div>

                      <div>
                        <div className="flex items-center justify-between text-sm mb-2">
                          <span className="text-text-secondary">Non-Compliant</span>
                          <span className="text-text font-medium">{framework.nonCompliantRequirements}/{framework.totalRequirements}</span>
                        </div>
                        <div className="w-full bg-border rounded-full h-2">
                          <div
                            className="h-2 bg-red-500 rounded-full transition-all duration-300"
                            style={{ width: `${(framework.nonCompliantRequirements / framework.totalRequirements) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Expanded Framework Details */}
                  {expandedFramework === framework.id && (
                    <div className="border-t border-border p-6 bg-background/50">
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        {/* Key Information */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Framework Details</h5>
                          <div className="space-y-3">
                            <div className="flex items-center justify-between p-3 bg-card rounded border border-border">
                              <span className="text-text-secondary">Last Updated</span>
                              <span className="text-text font-medium">{framework.lastUpdated.toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-card rounded border border-border">
                              <span className="text-text-secondary">Next Audit</span>
                              <span className="text-text font-medium">{framework.nextAudit.toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-card rounded border border-border">
                              <span className="text-text-secondary">Total Requirements</span>
                              <span className="text-text font-medium">{framework.totalRequirements}</span>
                            </div>
                            <div className="flex items-center justify-between p-3 bg-card rounded border border-border">
                              <span className="text-text-secondary">Certification Status</span>
                              <div className="flex items-center gap-2">
                                {getCertificationIcon(framework.certificationStatus)}
                                <span className="text-text font-medium capitalize">{framework.certificationStatus}</span>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Historical Trend */}
                        <div>
                          <h5 className="text-lg font-semibold text-text mb-3">Compliance Trend</h5>
                          <div className="h-48">
                            <Line
                              data={{
                                labels: framework.historicalScores.slice(-6).map(h => new Date(h.date).toLocaleDateString('en-US', { month: 'short' })),
                                datasets: [{
                                  label: 'Compliance Score',
                                  data: framework.historicalScores.slice(-6).map(h => h.score),
                                  borderColor: 'rgb(79, 142, 247)',
                                  backgroundColor: 'rgba(79, 142, 247, 0.1)',
                                  borderWidth: 3,
                                  fill: true,
                                  tension: 0.4,
                                }]
                              }}
                              options={{
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                  legend: {
                                    display: false
                                  },
                                  tooltip: {
                                    backgroundColor: chartTheme.tooltipBg,
                                    titleColor: chartTheme.textColor,
                                    bodyColor: chartTheme.textColor,
                                    borderColor: chartTheme.borderColor,
                                    borderWidth: 1,
                                  }
                                },
                                scales: {
                                  x: {
                                    grid: {
                                      color: chartTheme.gridColor,
                                    },
                                    ticks: {
                                      color: chartTheme.textSecondary,
                                    }
                                  },
                                  y: {
                                    grid: {
                                      color: chartTheme.gridColor,
                                    },
                                    ticks: {
                                      color: chartTheme.textSecondary,
                                      callback: (value) => `${value}%`
                                    }
                                  }
                                }
                              }}
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* Deadlines View */}
      {activeView === 'deadlines' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Calendar className="w-5 h-5" />
            Upcoming Deadlines
          </h3>

          {dashboardData && (
            <div className="space-y-4">
              {dashboardData.upcomingDeadlines.map((deadline) => (
                <div key={deadline.id} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-text">{deadline.title}</h4>
                      <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                        <span className="flex items-center gap-1">
                          <Scale className="w-3 h-3" />
                          {deadline.framework}
                        </span>
                        <span className="capitalize">{deadline.type.replace('_', ' ')}</span>
                        <span className="flex items-center gap-1">
                          <Users className="w-3 h-3" />
                          {deadline.assignee}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-3">
                      <div className="text-right">
                        <p className="text-lg font-bold text-text">{deadline.dueDate.toLocaleDateString()}</p>
                        <p className="text-sm text-text-secondary">
                          {Math.ceil((deadline.dueDate.getTime() - Date.now()) / (1000 * 60 * 60 * 24))} days
                        </p>
                      </div>

                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColorClass(deadline.priority)}`}>
                        {deadline.priority}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Activity View */}
      {activeView === 'activity' && (
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <Activity className="w-5 h-5" />
            Recent Activity
          </h3>

          {dashboardData && (
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {dashboardData.recentActivity.map((activity) => (
                <div key={activity.id} className="bg-card rounded-lg p-4 border border-border">
                  <div className="flex items-start gap-3">
                    <div className="flex-shrink-0 mt-1">
                      <Activity className="w-4 h-4 text-primary" />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <span className="font-medium text-text">{activity.user}</span>
                        <span className="text-text-secondary">•</span>
                        <span className="text-sm text-text-secondary">{activity.action}</span>
                        <span className="text-text-secondary">•</span>
                        <span className="text-sm text-text-secondary">{activity.framework}</span>
                      </div>

                      {activity.requirement && (
                        <p className="text-text font-medium mb-1">{activity.requirement}</p>
                      )}
                      <p className="text-sm text-text-secondary mb-2">{activity.details}</p>

                      <div className="flex items-center gap-4 text-xs text-text-secondary">
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {activity.timestamp.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
