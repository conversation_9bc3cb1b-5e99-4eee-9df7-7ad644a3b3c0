import { BaseApiService } from './api';
import { DPDPRule, GDPRRule, RuleEvaluationResult } from './complianceRulesService';

// API Types
export interface CreateCustomRuleRequest {
  name: string;
  description: string;
  framework: 'gdpr' | 'dpdp' | 'custom';
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  trigger: string;
  conditions: Array<{
    field: string;
    operator: string;
    value: string;
  }>;
  actions: string[];
  enabled: boolean;
  notificationSettings?: {
    email?: string[];
    slack?: string;
    webhook?: string;
  };
}

export interface UpdateRuleStatusRequest {
  enabled: boolean;
  reason: string;
  disabledBy: string;
  disabledUntil?: string;
}

export interface ComplianceEvaluationRequest {
  eventType: string;
  timestamp: string;
  source: string;
  data: Record<string, any>;
  context?: Record<string, any>;
}

export interface ComplianceEvaluationResponse {
  evaluationId: string;
  timestamp: string;
  overallCompliance: boolean;
  complianceScore: number;
  results: RuleEvaluationResult[];
  violations: RuleEvaluationResult[];
  warnings: RuleEvaluationResult[];
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low';
    category: string;
    description: string;
    action: string;
  }>;
  nextReviewDate: string;
}

export interface BatchEvaluationRequest {
  events: ComplianceEvaluationRequest[];
  options: {
    stopOnFirstViolation: boolean;
    includeRecommendations: boolean;
    detailedResults: boolean;
  };
}

export interface ComplianceHistoryItem {
  id: string;
  evaluationId: string;
  timestamp: string;
  eventType: string;
  source: string;
  overallCompliance: boolean;
  complianceScore: number;
  violationCount: number;
  warningCount: number;
  rulesEvaluated: number;
  processingTime: number;
  violations: Array<{
    ruleId: string;
    severity: string;
    message: string;
  }>;
}

export interface AuditTrailItem {
  id: string;
  timestamp: string;
  action: string;
  actor: {
    id: string;
    email: string;
    role: string;
  };
  resource: {
    type: string;
    id: string;
    name: string;
  };
  changes: Record<string, { from: any; to: any }>;
  reason: string;
  ipAddress: string;
  userAgent: string;
}

export interface ComplianceReport {
  reportId: string;
  status: 'generating' | 'completed' | 'failed';
  estimatedCompletion?: string;
  downloadUrl?: string;
  createdAt: string;
}

export interface CreateReportRequest {
  name: string;
  type: 'compliance_summary' | 'violation_report' | 'trend_analysis';
  framework: 'gdpr' | 'dpdp' | 'all';
  period: {
    start: string;
    end: string;
  };
  format: 'pdf' | 'excel' | 'csv';
  includeDetails: boolean;
  sections: string[];
  recipients?: string[];
  schedule?: {
    recurring: boolean;
    frequency: 'daily' | 'weekly' | 'monthly';
    dayOfMonth?: number;
    dayOfWeek?: number;
  };
}

export interface FrameworkSettings {
  enabled: boolean;
  settings: {
    strictMode: boolean;
    autoEvaluation: boolean;
    notificationThreshold: 'low' | 'medium' | 'high' | 'critical';
    retentionPeriod: number;
    autoRemediation: boolean;
  };
  notifications: {
    email: string[];
    slack?: string;
    webhook?: string;
  };
}

export interface ComplianceDashboard {
  overview: {
    overallComplianceScore: number;
    riskLevel: 'low' | 'medium' | 'high';
    activeViolations: number;
    pendingReviews: number;
    lastEvaluation: string;
  };
  frameworks: Record<string, {
    score: number;
    violations: number;
    trend: 'improving' | 'declining' | 'stable';
  }>;
  recentActivity: Array<{
    timestamp: string;
    type: string;
    ruleId: string;
    severity: string;
  }>;
  alerts: Array<{
    id: string;
    severity: string;
    message: string;
    count: number;
    firstOccurrence: string;
  }>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  summary?: Record<string, any>;
}

class ComplianceApiService extends BaseApiService {
  constructor() {
    super('/api/compliance');
  }

  // Rule Management
  async getGDPRRules(params: {
    page?: number;
    limit?: number;
    enabled?: boolean;
    category?: string;
    severity?: 'low' | 'medium' | 'high' | 'critical';
  } = {}): Promise<PaginatedResponse<GDPRRule>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<GDPRRule>>(`/rules/gdpr?${queryParams.toString()}`);
  }

  async getDPDPRules(params: {
    page?: number;
    limit?: number;
    enabled?: boolean;
    severity?: 'low' | 'medium' | 'high' | 'critical';
  } = {}): Promise<PaginatedResponse<DPDPRule>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<DPDPRule>>(`/rules/dpdp?${queryParams.toString()}`);
  }

  async getRule(ruleId: string): Promise<GDPRRule | DPDPRule> {
    return this.get(`/rules/${ruleId}`);
  }

  async createCustomRule(data: CreateCustomRuleRequest): Promise<any> {
    return this.post('/rules/custom', data);
  }

  async updateRule(ruleId: string, data: Partial<CreateCustomRuleRequest>): Promise<any> {
    return this.put(`/rules/${ruleId}`, data);
  }

  async updateRuleStatus(ruleId: string, data: UpdateRuleStatusRequest): Promise<any> {
    return this.patch(`/rules/${ruleId}/status`, data);
  }

  async deleteRule(ruleId: string): Promise<{ success: boolean }> {
    return this.delete(`/rules/${ruleId}`);
  }

  // Rule Evaluation
  async evaluateEvent(data: ComplianceEvaluationRequest): Promise<ComplianceEvaluationResponse> {
    return this.post('/evaluate', data);
  }

  async batchEvaluateEvents(data: BatchEvaluationRequest): Promise<ComplianceEvaluationResponse[]> {
    return this.post('/evaluate/batch', data);
  }

  async getEvaluationResult(evaluationId: string): Promise<ComplianceEvaluationResponse> {
    return this.get(`/evaluations/${evaluationId}`);
  }

  // Compliance History & Audit
  async getComplianceHistory(params: {
    page?: number;
    limit?: number;
    period?: '7d' | '30d' | '90d' | '1y';
    compliant?: boolean;
    severity?: string;
    ruleId?: string;
    source?: string;
  } = {}): Promise<PaginatedResponse<ComplianceHistoryItem>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<ComplianceHistoryItem>>(`/history?${queryParams.toString()}`);
  }

  async getAuditTrail(params: {
    page?: number;
    limit?: number;
    action?: string;
    actor?: string;
    resourceType?: string;
    dateFrom?: string;
    dateTo?: string;
  } = {}): Promise<PaginatedResponse<AuditTrailItem>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<AuditTrailItem>>(`/audit?${queryParams.toString()}`);
  }

  // Compliance Reporting
  async createReport(data: CreateReportRequest): Promise<ComplianceReport> {
    return this.post('/reports', data);
  }

  async getReport(reportId: string): Promise<ComplianceReport> {
    return this.get(`/reports/${reportId}`);
  }

  async downloadReport(reportId: string): Promise<Blob> {
    const response = await this.api.get(`/reports/${reportId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  async getReports(params: {
    page?: number;
    limit?: number;
    status?: 'generating' | 'completed' | 'failed';
    type?: string;
  } = {}): Promise<PaginatedResponse<ComplianceReport>> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value.toString());
      }
    });

    return this.get<PaginatedResponse<ComplianceReport>>(`/reports?${queryParams.toString()}`);
  }

  async deleteReport(reportId: string): Promise<{ success: boolean }> {
    return this.delete(`/reports/${reportId}`);
  }

  // Framework Configuration
  async getFrameworks(): Promise<Record<string, {
    enabled: boolean;
    version: string;
    lastUpdated: string;
    rulesCount: number;
    complianceRate: number;
    settings: any;
  }>> {
    return this.get('/frameworks');
  }

  async updateFramework(framework: 'gdpr' | 'dpdp', data: FrameworkSettings): Promise<any> {
    return this.put(`/frameworks/${framework}`, data);
  }

  // Real-time Monitoring
  async getDashboard(): Promise<ComplianceDashboard> {
    return this.get('/dashboard');
  }

  async getViolationSummary(params: {
    period?: '24h' | '7d' | '30d';
    framework?: 'gdpr' | 'dpdp';
    severity?: string;
  } = {}): Promise<{
    totalViolations: number;
    newViolations: number;
    resolvedViolations: number;
    pendingViolations: number;
    violationsByFramework: Record<string, number>;
    violationsBySeverity: Record<string, number>;
    trend: 'improving' | 'declining' | 'stable';
  }> {
    const queryParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined) {
        queryParams.append(key, value);
      }
    });

    return this.get(`/violations/summary?${queryParams.toString()}`);
  }

  // Remediation
  async createRemediationPlan(data: {
    violationIds: string[];
    priority: 'high' | 'medium' | 'low';
    assignedTo: string;
    dueDate: string;
    description: string;
  }): Promise<{
    planId: string;
    status: string;
    createdAt: string;
  }> {
    return this.post('/remediation/plans', data);
  }

  async updateRemediationPlan(planId: string, data: {
    status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
    notes?: string;
    completedBy?: string;
  }): Promise<any> {
    return this.put(`/remediation/plans/${planId}`, data);
  }
}

export const complianceApiService = new ComplianceApiService();
