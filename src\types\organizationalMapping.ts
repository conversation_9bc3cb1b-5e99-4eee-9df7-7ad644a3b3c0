// Enhanced types for organizational mapping
export interface OrganizationalUnit {
  id: string;
  name: string;
  type: 'department' | 'team' | 'business_unit' | 'subsidiary' | 'branch';
  parentId?: string;
  location: string;
  country: string;
  region: string;
  headCount: number;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  contactPerson: string;
  email: string;
  phone?: string;
  dataProcessingActivities: string[];
  regulations: string[];
  lastAssessment: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DataFlow {
  id: string;
  fromUnit: string;
  toUnit: string;
  dataType: string;
  volume: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'on-demand';
  purpose: string;
  legalBasis: string;
  safeguards: string[];
  riskScore: number;
  lastReview: Date;
  isActive: boolean;
}

export interface OrganizationalMappingMetrics {
  totalUnits: number;
  byType: Record<string, number>;
  byRiskLevel: Record<string, number>;
  byRegion: Record<string, number>;
  averageComplianceScore: number;
  totalHeadCount: number;
  totalDataFlows: number;
  highRiskUnits: number;
  lastUpdated: Date;
}

export interface UnitHierarchyNode extends OrganizationalUnit {
  children?: UnitHierarchyNode[];
  level: number;
}

export interface ComplianceAssessment {
  id: string;
  unitId: string;
  assessmentDate: Date;
  assessor: string;
  overallScore: number;
  categoryScores: {
    dataProtection: number;
    security: number;
    governance: number;
    training: number;
    documentation: number;
  };
  findings: AssessmentFinding[];
  recommendations: string[];
  nextAssessmentDate: Date;
  status: 'draft' | 'in_progress' | 'completed' | 'approved';
}

export interface AssessmentFinding {
  id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description: string;
  recommendation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  assignee?: string;
  dueDate?: Date;
  evidence?: string[];
}

export interface DataProcessingActivity {
  id: string;
  unitId: string;
  name: string;
  description: string;
  purpose: string;
  legalBasis: string;
  dataCategories: string[];
  dataSubjects: string[];
  recipients: string[];
  thirdCountryTransfers: boolean;
  retentionPeriod: string;
  securityMeasures: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  lastReviewed: Date;
  nextReview: Date;
  status: 'active' | 'inactive' | 'under_review';
}

export interface RiskAssessment {
  id: string;
  unitId: string;
  riskType: 'operational' | 'compliance' | 'security' | 'reputational';
  title: string;
  description: string;
  likelihood: number; // 1-5 scale
  impact: number; // 1-5 scale
  riskScore: number; // calculated from likelihood * impact
  mitigationMeasures: string[];
  residualRisk: number;
  owner: string;
  status: 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred';
  lastReviewed: Date;
  nextReview: Date;
}

export interface OrganizationalFilters {
  type?: string[];
  riskLevel?: string[];
  region?: string[];
  country?: string[];
  complianceScoreMin?: number;
  complianceScoreMax?: number;
  headCountMin?: number;
  headCountMax?: number;
  regulations?: string[];
  lastAssessmentBefore?: Date;
  lastAssessmentAfter?: Date;
}

export interface OrganizationalSearchOptions {
  query?: string;
  filters?: OrganizationalFilters;
  sortBy?: 'name' | 'complianceScore' | 'riskLevel' | 'lastAssessment' | 'headCount';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface OrganizationalSearchResult {
  units: OrganizationalUnit[];
  totalCount: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface DataFlowAnalysis {
  totalFlows: number;
  activeFlows: number;
  inactiveFlows: number;
  highRiskFlows: number;
  byFrequency: Record<string, number>;
  byDataType: Record<string, number>;
  byLegalBasis: Record<string, number>;
  crossBorderFlows: number;
  averageRiskScore: number;
  flowsNeedingReview: number;
}

export interface ComplianceGap {
  id: string;
  unitId: string;
  regulation: string;
  requirement: string;
  currentStatus: 'not_implemented' | 'partially_implemented' | 'implemented';
  gapDescription: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  remediationPlan: string;
  assignee: string;
  dueDate: Date;
  estimatedEffort: string;
  cost?: number;
  priority: 'low' | 'medium' | 'high' | 'critical';
}

export interface OrganizationalDashboardData {
  overview: OrganizationalMappingMetrics;
  units: OrganizationalUnit[];
  dataFlows: DataFlow[];
  recentAssessments: ComplianceAssessment[];
  upcomingReviews: Array<{
    id: string;
    unitName: string;
    type: 'assessment' | 'data_flow_review' | 'risk_review';
    dueDate: Date;
    assignee: string;
  }>;
  complianceGaps: ComplianceGap[];
  riskSummary: {
    totalRisks: number;
    byLevel: Record<string, number>;
    topRisks: RiskAssessment[];
  };
}

export interface AuditTrail {
  id: string;
  timestamp: Date;
  userId: string;
  userName: string;
  action: 'create' | 'update' | 'delete' | 'view' | 'export';
  entityType: 'unit' | 'data_flow' | 'assessment' | 'risk';
  entityId: string;
  entityName: string;
  changes?: Record<string, { from: any; to: any }>;
  ipAddress?: string;
  userAgent?: string;
  notes?: string;
}

export interface BulkOperation {
  id: string;
  type: 'update' | 'delete' | 'export' | 'assessment';
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  totalItems: number;
  processedItems: number;
  failedItems: number;
  startedAt: Date;
  completedAt?: Date;
  errors?: string[];
  results?: any[];
}

export interface OrganizationalTemplate {
  id: string;
  name: string;
  description: string;
  type: OrganizationalUnit['type'];
  defaultValues: Partial<OrganizationalUnit>;
  requiredFields: string[];
  validationRules: Record<string, any>;
  isActive: boolean;
  createdBy: string;
  createdAt: Date;
  lastUsed?: Date;
  usageCount: number;
}

export interface IntegrationConfig {
  id: string;
  name: string;
  type: 'hr_system' | 'directory_service' | 'erp' | 'crm';
  endpoint: string;
  authMethod: 'api_key' | 'oauth' | 'basic_auth';
  credentials: Record<string, string>;
  syncFrequency: 'real_time' | 'hourly' | 'daily' | 'weekly';
  fieldMappings: Record<string, string>;
  isActive: boolean;
  lastSync?: Date;
  syncStatus: 'success' | 'error' | 'pending';
  errorMessage?: string;
}

export interface NotificationRule {
  id: string;
  name: string;
  trigger: 'compliance_score_drop' | 'assessment_due' | 'risk_increase' | 'data_flow_change';
  conditions: Record<string, any>;
  recipients: string[];
  channels: ('email' | 'slack' | 'teams' | 'webhook')[];
  isActive: boolean;
  lastTriggered?: Date;
  triggerCount: number;
}
