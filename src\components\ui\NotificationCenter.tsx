/**
 * Notification Center Component
 * Displays notifications with proper styling and interactions
 */

import React, { useState, useEffect } from 'react';
import { Bell, X, Check, AlertTriangle, Shield, FileText, Clock, ExternalLink } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';
import { notificationService, Notification } from '../../services/notificationService';

interface NotificationCenterProps {
  isExpanded: boolean;
  currentUserId?: string;
}

export const NotificationCenter: React.FC<NotificationCenterProps> = ({ 
  isExpanded, 
  currentUserId = 'user-1' // Default to admin user
}) => {
  const { mode } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  useEffect(() => {
    // Load initial notifications
    const userNotifications = notificationService.getNotifications(currentUserId);
    setNotifications(userNotifications);
    setUnreadCount(notificationService.getUnreadCount(currentUserId));

    // Subscribe to new notifications
    const unsubscribe = notificationService.subscribeToNotifications((newNotification) => {
      const user = notificationService.getUsers().find(u => u.id === currentUserId);
      if (user && (
        newNotification.targetUsers.includes(currentUserId) ||
        newNotification.targetRoles.includes(user.role) ||
        newNotification.targetDepartments.includes(user.department)
      )) {
        setNotifications(prev => [newNotification, ...prev]);
        setUnreadCount(prev => prev + 1);
      }
    });

    return unsubscribe;
  }, [currentUserId]);

  const handleNotificationClick = (notification: Notification) => {
    if (!notification.readBy.includes(currentUserId)) {
      notificationService.markAsRead(notification.id, currentUserId);
      setUnreadCount(prev => Math.max(0, prev - 1));
      setNotifications(prev => 
        prev.map(n => 
          n.id === notification.id 
            ? { ...n, readBy: [...n.readBy, currentUserId] }
            : n
        )
      );
    }

    if (notification.actionUrl) {
      // In a real app, this would navigate to the URL
      console.log(`Navigate to: ${notification.actionUrl}`);
    }
  };

  const getNotificationIcon = (type: Notification['type']) => {
    const iconMap = {
      'compliance_violation': <Shield className="w-4 h-4" />,
      'gdpr_request': <FileText className="w-4 h-4" />,
      'security_alert': <AlertTriangle className="w-4 h-4" />,
      'risk_assessment': <AlertTriangle className="w-4 h-4" />,
      'policy_update': <FileText className="w-4 h-4" />,
      'system_alert': <Bell className="w-4 h-4" />,
      'deadline_reminder': <Clock className="w-4 h-4" />
    };
    return iconMap[type] || <Bell className="w-4 h-4" />;
  };

  const getSeverityColor = (severity: Notification['severity']) => {
    const colorMap = {
      'low': 'text-blue-500 bg-blue-50 dark:bg-blue-900/20',
      'medium': 'text-yellow-500 bg-yellow-50 dark:bg-yellow-900/20',
      'high': 'text-orange-500 bg-orange-50 dark:bg-orange-900/20',
      'critical': 'text-red-500 bg-red-50 dark:bg-red-900/20'
    };
    return colorMap[severity] || colorMap.medium;
  };

  const formatTimeAgo = (date: Date) => {
    const now = new Date();
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffInMinutes < 1) return 'Just now';
    if (diffInMinutes < 60) return `${diffInMinutes}m ago`;
    
    const diffInHours = Math.floor(diffInMinutes / 60);
    if (diffInHours < 24) return `${diffInHours}h ago`;
    
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  return (
    <div className="relative">
      {/* Notification Bell Button */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`relative p-2 rounded-lg transition-all duration-200 
          ${isOpen 
            ? 'bg-primary/10 text-primary' 
            : 'hover:bg-primary/5 text-text-secondary hover:text-primary'
          }
          ${!isExpanded ? 'w-full flex justify-center' : ''}`}
        aria-label="Notifications"
      >
        <Bell className="w-5 h-5" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center font-medium">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}
      </button>

      {/* Notification Dropdown */}
      {isOpen && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Notification Panel */}
          <div className={`absolute ${isExpanded ? 'left-0' : 'left-12'} top-0 z-50 w-96 max-w-sm
            bg-surface border border-border rounded-lg shadow-lg max-h-96 overflow-hidden
            ${mode === 'dark' ? 'shadow-2xl' : 'shadow-xl'}`}>
            
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b border-border">
              <h3 className="font-semibold text-text">Notifications</h3>
              <div className="flex items-center gap-2">
                {unreadCount > 0 && (
                  <span className="text-xs text-text-secondary">
                    {unreadCount} unread
                  </span>
                )}
                <button
                  onClick={() => setIsOpen(false)}
                  className="p-1 hover:bg-card rounded transition-colors"
                >
                  <X className="w-4 h-4 text-text-secondary" />
                </button>
              </div>
            </div>

            {/* Notifications List */}
            <div className="max-h-80 overflow-y-auto">
              {notifications.length === 0 ? (
                <div className="p-6 text-center text-text-secondary">
                  <Bell className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No notifications</p>
                </div>
              ) : (
                notifications.slice(0, 10).map((notification) => {
                  const isUnread = !notification.readBy.includes(currentUserId);
                  
                  return (
                    <div
                      key={notification.id}
                      onClick={() => handleNotificationClick(notification)}
                      className={`p-4 border-b border-border cursor-pointer transition-colors
                        ${isUnread ? 'bg-primary/5' : 'hover:bg-card'}
                        ${notification.actionUrl ? 'hover:bg-primary/10' : ''}`}
                    >
                      <div className="flex items-start gap-3">
                        {/* Icon */}
                        <div className={`p-2 rounded-full ${getSeverityColor(notification.severity)}`}>
                          {getNotificationIcon(notification.type)}
                        </div>

                        {/* Content */}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-start justify-between gap-2">
                            <h4 className={`font-medium text-sm ${isUnread ? 'text-text' : 'text-text-secondary'}`}>
                              {notification.title}
                            </h4>
                            {isUnread && (
                              <div className="w-2 h-2 bg-primary rounded-full flex-shrink-0 mt-1" />
                            )}
                          </div>
                          
                          <p className="text-xs text-text-secondary mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-text-tertiary">
                              {formatTimeAgo(notification.createdAt)}
                            </span>
                            
                            {notification.actionRequired && (
                              <div className="flex items-center gap-1">
                                {notification.actionUrl && (
                                  <ExternalLink className="w-3 h-3 text-primary" />
                                )}
                                <span className="text-xs text-primary font-medium">
                                  Action Required
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })
              )}
            </div>

            {/* Footer */}
            {notifications.length > 0 && (
              <div className="p-3 border-t border-border bg-card/50">
                <button
                  onClick={() => {
                    // Mark all as read
                    notifications.forEach(n => {
                      if (!n.readBy.includes(currentUserId)) {
                        notificationService.markAsRead(n.id, currentUserId);
                      }
                    });
                    setUnreadCount(0);
                    setNotifications(prev => 
                      prev.map(n => ({
                        ...n,
                        readBy: n.readBy.includes(currentUserId) ? n.readBy : [...n.readBy, currentUserId]
                      }))
                    );
                  }}
                  className="w-full text-xs text-primary hover:text-primary/80 transition-colors"
                >
                  Mark all as read
                </button>
              </div>
            )}
          </div>
        </>
      )}
    </div>
  );
};

export default NotificationCenter;
