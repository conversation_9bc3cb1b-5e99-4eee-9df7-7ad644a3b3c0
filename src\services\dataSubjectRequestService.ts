import { DataSubjectRequest as ComplianceDataSubjectRequest, WorkflowStep, Communication, AuditEntry, Attachment } from '../types/compliance';
import { DataSubjectRequest } from '../types/gdprTypes';

// Mock data generation for Data Subject Requests
export class DataSubjectRequestService {
  private static complianceRequests: ComplianceDataSubjectRequest[] = [];
  private static gdprRequests: DataSubjectRequest[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.complianceRequests = this.generateMockRequests();
    this.gdprRequests = this.generateGDPRRequests();
    this.initialized = true;
  }

  // CRUD Operations for GDPR requests
  static async getAllRequests(): Promise<DataSubjectRequest[]> {
    this.initialize();
    return [...this.gdprRequests];
  }

  static async getRequestById(id: string): Promise<DataSubjectRequest | null> {
    this.initialize();
    return this.gdprRequests.find(req => req.id === id) || null;
  }

  // GDPR-compatible create request
  static async createRequest(requestData: Partial<DataSubjectRequest>): Promise<DataSubjectRequest> {
    this.initialize();
    const newRequest: DataSubjectRequest = {
      id: `dsr-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      email: requestData.email || '',
      type: requestData.type || 'access',
      status: requestData.status || 'pending',
      priority: requestData.priority || 'medium',
      description: requestData.description || '',
      notes: requestData.notes || '',
      progress: requestData.progress || 0,
      submittedAt: requestData.submittedAt || new Date(),
      dueDate: requestData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      completedAt: requestData.completedAt,
      assignedTo: requestData.assignedTo,
      attachments: requestData.attachments || [],
      communicationLog: requestData.communicationLog || []
    };

    this.gdprRequests.push(newRequest);
    return newRequest;
  }

  static async updateRequest(id: string, updates: Partial<DataSubjectRequest>): Promise<DataSubjectRequest | null> {
    this.initialize();
    const index = this.gdprRequests.findIndex(req => req.id === id);
    if (index === -1) return null;

    this.gdprRequests[index] = { ...this.gdprRequests[index], ...updates };
    return this.gdprRequests[index];
  }

  static async deleteRequest(id: string): Promise<boolean> {
    this.initialize();
    const index = this.gdprRequests.findIndex(req => req.id === id);
    if (index === -1) return false;

    this.gdprRequests.splice(index, 1);
    return true;
  }

  // Legacy compliance request method
  static async createComplianceRequest(requestData: Partial<ComplianceDataSubjectRequest>): Promise<ComplianceDataSubjectRequest> {
    this.initialize();
    const newRequest: ComplianceDataSubjectRequest = {
      id: `dsr-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: requestData.type || 'access',
      email: requestData.email || '',
      firstName: requestData.firstName || '',
      lastName: requestData.lastName || '',
      status: 'pending',
      submittedAt: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      progress: 0,
      priority: requestData.priority || 'medium',
      assignee: requestData.assignee || 'Privacy Team',
      assigneeId: requestData.assigneeId || 'privacy-team-001',
      description: requestData.description || '',
      requestDetails: {
        dataCategories: requestData.requestDetails?.dataCategories || [],
        processingPurposes: requestData.requestDetails?.processingPurposes || [],
        legalBasis: requestData.requestDetails?.legalBasis || 'Article 6(1)(a) - Consent',
        retentionPeriod: requestData.requestDetails?.retentionPeriod,
        thirdParties: requestData.requestDetails?.thirdParties || []
      },
      workflow: this.generateWorkflow(requestData.type || 'access'),
      attachments: [],
      communications: [],
      auditTrail: [{
        id: `audit-${Date.now()}`,
        timestamp: new Date(),
        userId: 'system',
        userName: 'System',
        action: 'Request Created',
        details: `Data subject request created for ${requestData.email}`,
        newValue: 'pending'
      }]
    };

    this.complianceRequests.push(newRequest);
    return newRequest;
  }

  static async updateComplianceRequest(id: string, updates: Partial<ComplianceDataSubjectRequest>): Promise<ComplianceDataSubjectRequest | null> {
    this.initialize();
    const index = this.complianceRequests.findIndex(req => req.id === id);
    if (index === -1) return null;

    const oldRequest = { ...this.complianceRequests[index] };
    this.complianceRequests[index] = { ...this.complianceRequests[index], ...updates };

    // Add audit entry
    this.requests[index].auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'user-001',
      userName: 'Current User',
      action: 'Request Updated',
      details: 'Request details updated',
      previousValue: oldRequest,
      newValue: updates
    });

    return this.complianceRequests[index];
  }

  static async deleteComplianceRequest(id: string): Promise<boolean> {
    this.initialize();
    const index = this.complianceRequests.findIndex(req => req.id === id);
    if (index === -1) return false;

    this.complianceRequests.splice(index, 1);
    return true;
  }

  // Workflow Management (for compliance requests)
  static async advanceWorkflow(requestId: string, stepId: string, notes?: string): Promise<ComplianceDataSubjectRequest | null> {
    const request = this.complianceRequests.find(req => req.id === requestId);
    if (!request) return null;

    const step = request.workflow.steps.find(s => s.id === stepId);
    if (!step) return null;

    step.status = 'completed';
    step.completedAt = new Date();
    step.notes = notes;

    // Calculate progress
    const completedSteps = request.workflow.steps.filter(s => s.status === 'completed').length;
    request.progress = Math.round((completedSteps / request.workflow.totalSteps) * 100);

    // Check if all steps are completed
    if (completedSteps === request.workflow.totalSteps) {
      request.status = 'completed';
      request.completedAt = new Date();
    } else {
      // Start next step
      const nextStep = request.workflow.steps.find(s => s.status === 'pending');
      if (nextStep) {
        nextStep.status = 'in_progress';
        nextStep.startedAt = new Date();
      }
    }

    // Add audit entry
    request.auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'user-001',
      userName: 'Current User',
      action: 'Workflow Advanced',
      details: `Step "${step.name}" completed`,
      previousValue: 'in_progress',
      newValue: 'completed'
    });

    return await this.updateComplianceRequest(requestId, request);
  }

  // Communication Management
  static async addCommunication(requestId: string, communication: Omit<Communication, 'id'>): Promise<DataSubjectRequest | null> {
    const request = await this.getRequestById(requestId);
    if (!request) return null;

    const newCommunication: Communication = {
      id: `comm-${Date.now()}`,
      ...communication
    };

    request.communications.push(newCommunication);

    // Add audit entry
    request.auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'user-001',
      userName: 'Current User',
      action: 'Communication Added',
      details: `${communication.type} communication added`,
      newValue: newCommunication
    });

    return await this.updateRequest(requestId, request);
  }

  // Filtering and Search
  static async getFilteredRequests(filters: {
    status?: string[];
    type?: string[];
    priority?: string[];
    assignee?: string[];
    dateRange?: { start: Date; end: Date };
    searchTerm?: string;
  }): Promise<DataSubjectRequest[]> {
    this.initialize();
    let filtered = [...this.requests];

    if (filters.status?.length) {
      filtered = filtered.filter(req => filters.status!.includes(req.status));
    }

    if (filters.type?.length) {
      filtered = filtered.filter(req => filters.type!.includes(req.type));
    }

    if (filters.priority?.length) {
      filtered = filtered.filter(req => filters.priority!.includes(req.priority));
    }

    if (filters.assignee?.length) {
      filtered = filtered.filter(req => filters.assignee!.includes(req.assignee));
    }

    if (filters.dateRange) {
      filtered = filtered.filter(req => 
        req.submittedAt >= filters.dateRange!.start && 
        req.submittedAt <= filters.dateRange!.end
      );
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(req => 
        req.email.toLowerCase().includes(term) ||
        req.firstName.toLowerCase().includes(term) ||
        req.lastName.toLowerCase().includes(term) ||
        req.description.toLowerCase().includes(term)
      );
    }

    return filtered;
  }

  // Analytics and Metrics
  static async getRequestMetrics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byPriority: Record<string, number>;
    averageProcessingTime: number;
    overdueRequests: number;
    completionRate: number;
  }> {
    this.initialize();
    const requests = this.requests;

    const byStatus = requests.reduce((acc, req) => {
      acc[req.status] = (acc[req.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byType = requests.reduce((acc, req) => {
      acc[req.type] = (acc[req.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byPriority = requests.reduce((acc, req) => {
      acc[req.priority] = (acc[req.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const completedRequests = requests.filter(req => req.status === 'completed');
    const averageProcessingTime = completedRequests.length > 0 
      ? completedRequests.reduce((sum, req) => {
          if (req.completedAt) {
            return sum + (req.completedAt.getTime() - req.submittedAt.getTime());
          }
          return sum;
        }, 0) / completedRequests.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    const overdueRequests = requests.filter(req => 
      req.status !== 'completed' && new Date() > req.dueDate
    ).length;

    const completionRate = requests.length > 0 
      ? (completedRequests.length / requests.length) * 100 
      : 0;

    return {
      total: requests.length,
      byStatus,
      byType,
      byPriority,
      averageProcessingTime,
      overdueRequests,
      completionRate
    };
  }

  // Private helper methods
  private static generateWorkflow(type: string): { currentStep: number; totalSteps: number; steps: WorkflowStep[] } {
    const workflows = {
      access: [
        { name: 'Identity Verification', description: 'Verify the identity of the data subject', estimatedDuration: 2 },
        { name: 'Data Collection', description: 'Collect all personal data related to the subject', estimatedDuration: 8 },
        { name: 'Data Review', description: 'Review collected data for accuracy and completeness', estimatedDuration: 4 },
        { name: 'Response Preparation', description: 'Prepare the response document', estimatedDuration: 2 },
        { name: 'Final Review', description: 'Final review and approval', estimatedDuration: 1 },
        { name: 'Response Delivery', description: 'Deliver the response to the data subject', estimatedDuration: 1 }
      ],
      erasure: [
        { name: 'Identity Verification', description: 'Verify the identity of the data subject', estimatedDuration: 2 },
        { name: 'Legal Assessment', description: 'Assess legal grounds for erasure', estimatedDuration: 4 },
        { name: 'Data Identification', description: 'Identify all data to be erased', estimatedDuration: 6 },
        { name: 'Third Party Notification', description: 'Notify third parties if applicable', estimatedDuration: 2 },
        { name: 'Data Erasure', description: 'Execute data erasure', estimatedDuration: 4 },
        { name: 'Verification', description: 'Verify complete erasure', estimatedDuration: 2 },
        { name: 'Confirmation', description: 'Send confirmation to data subject', estimatedDuration: 1 }
      ],
      portability: [
        { name: 'Identity Verification', description: 'Verify the identity of the data subject', estimatedDuration: 2 },
        { name: 'Data Collection', description: 'Collect portable data', estimatedDuration: 6 },
        { name: 'Data Formatting', description: 'Format data in machine-readable format', estimatedDuration: 4 },
        { name: 'Quality Check', description: 'Verify data quality and completeness', estimatedDuration: 2 },
        { name: 'Secure Delivery', description: 'Securely deliver data to subject', estimatedDuration: 1 }
      ],
      rectification: [
        { name: 'Identity Verification', description: 'Verify the identity of the data subject', estimatedDuration: 2 },
        { name: 'Data Review', description: 'Review current data and requested changes', estimatedDuration: 3 },
        { name: 'Accuracy Verification', description: 'Verify accuracy of requested changes', estimatedDuration: 4 },
        { name: 'Data Update', description: 'Update data in all systems', estimatedDuration: 3 },
        { name: 'Third Party Notification', description: 'Notify third parties of changes', estimatedDuration: 2 },
        { name: 'Confirmation', description: 'Confirm changes to data subject', estimatedDuration: 1 }
      ],
      objection: [
        { name: 'Identity Verification', description: 'Verify the identity of the data subject', estimatedDuration: 2 },
        { name: 'Legal Assessment', description: 'Assess legal grounds for objection', estimatedDuration: 6 },
        { name: 'Processing Review', description: 'Review current processing activities', estimatedDuration: 4 },
        { name: 'Decision Making', description: 'Make decision on objection', estimatedDuration: 3 },
        { name: 'Implementation', description: 'Implement decision (stop processing or continue)', estimatedDuration: 2 },
        { name: 'Notification', description: 'Notify data subject of decision', estimatedDuration: 1 }
      ]
    };

    const workflowTemplate = workflows[type as keyof typeof workflows] || workflows.access;
    const steps: WorkflowStep[] = workflowTemplate.map((step, index) => ({
      id: `step-${index + 1}`,
      name: step.name,
      description: step.description,
      status: index === 0 ? 'in_progress' : 'pending',
      assignee: 'Privacy Team',
      estimatedDuration: step.estimatedDuration,
      startedAt: index === 0 ? new Date() : undefined,
      requiredActions: [],
      completedActions: []
    }));

    return {
      currentStep: 1,
      totalSteps: steps.length,
      steps
    };
  }

  private static generateMockRequests(): DataSubjectRequest[] {
    const mockRequests: DataSubjectRequest[] = [
      {
        id: 'dsr-001',
        type: 'erasure',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        status: 'in_progress',
        submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
        progress: 43,
        priority: 'urgent',
        assignee: 'Privacy Team',
        assigneeId: 'privacy-team-001',
        reviewer: 'Sarah Johnson',
        reviewerId: 'reviewer-001',
        description: 'Request for complete data erasure under GDPR Article 17 - Right to be forgotten',
        requestDetails: {
          dataCategories: ['Personal Information', 'Transaction History', 'Marketing Preferences', 'Support Tickets'],
          processingPurposes: ['Customer Service', 'Marketing', 'Analytics', 'Legal Compliance'],
          legalBasis: 'Article 6(1)(a) - Consent',
          retentionPeriod: '7 years',
          thirdParties: ['Payment Processor', 'Email Service Provider', 'Analytics Platform']
        },
        workflow: this.generateWorkflow('erasure'),
        attachments: [
          {
            id: 'att-001',
            name: 'identity_verification.pdf',
            type: 'application/pdf',
            size: 245760,
            uploadedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
            uploadedBy: 'Privacy Team',
            url: '/attachments/dsr-001/identity_verification.pdf'
          }
        ],
        communications: [
          {
            id: 'comm-001',
            type: 'email',
            direction: 'inbound',
            subject: 'Data Erasure Request',
            content: 'I would like to request the complete deletion of all my personal data from your systems.',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            from: '<EMAIL>',
            to: ['<EMAIL>']
          }
        ],
        auditTrail: [
          {
            id: 'audit-001',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
            userId: 'system',
            userName: 'System',
            action: 'Request Created',
            details: 'Data subject request <NAME_EMAIL>',
            newValue: 'pending'
          }
        ]
      },
      {
        id: 'dsr-002',
        type: 'portability',
        email: '<EMAIL>',
        firstName: 'Jane',
        lastName: 'Smith',
        status: 'in_progress',
        submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
        progress: 80,
        priority: 'medium',
        assignee: 'Data Team',
        assigneeId: 'data-team-001',
        description: 'Data portability request for customer profile and transaction history',
        requestDetails: {
          dataCategories: ['Customer Profile', 'Transaction History', 'Preferences'],
          processingPurposes: ['Service Delivery', 'Customer Support'],
          legalBasis: 'Article 6(1)(b) - Contract',
          retentionPeriod: '5 years',
          thirdParties: []
        },
        workflow: this.generateWorkflow('portability'),
        attachments: [],
        communications: [],
        auditTrail: [
          {
            id: 'audit-003',
            timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
            userId: 'system',
            userName: 'System',
            action: 'Request Created',
            details: 'Data portability request <NAME_EMAIL>',
            newValue: 'pending'
          }
        ]
      },
      {
        id: 'dsr-003',
        type: 'access',
        email: '<EMAIL>',
        firstName: 'Mike',
        lastName: 'Wilson',
        status: 'pending',
        submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 26 * 24 * 60 * 60 * 1000),
        progress: 0,
        priority: 'low',
        assignee: 'Support Team',
        assigneeId: 'support-team-001',
        description: 'Subject access request for personal data processing activities',
        requestDetails: {
          dataCategories: ['All Personal Data'],
          processingPurposes: ['All Processing Activities'],
          legalBasis: 'Article 6(1)(f) - Legitimate Interest',
          thirdParties: []
        },
        workflow: this.generateWorkflow('access'),
        attachments: [],
        communications: [],
        auditTrail: [
          {
            id: 'audit-004',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
            userId: 'system',
            userName: 'System',
            action: 'Request Created',
            details: 'Subject access request <NAME_EMAIL>',
            newValue: 'pending'
          }
        ]
      },
      {
        id: 'dsr-004',
        type: 'rectification',
        email: '<EMAIL>',
        firstName: 'Sarah',
        lastName: 'Johnson',
        status: 'completed',
        submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
        completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        progress: 100,
        priority: 'medium',
        assignee: 'Data Team',
        assigneeId: 'data-team-001',
        description: 'Request to correct inaccurate personal information',
        requestDetails: {
          dataCategories: ['Contact Information', 'Profile Data'],
          processingPurposes: ['Customer Service', 'Communication'],
          legalBasis: 'Article 6(1)(b) - Contract',
          thirdParties: []
        },
        workflow: this.generateWorkflow('rectification'),
        attachments: [],
        communications: [],
        auditTrail: [
          {
            id: 'audit-005',
            timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            userId: 'system',
            userName: 'System',
            action: 'Request Created',
            details: 'Rectification request <NAME_EMAIL>',
            newValue: 'pending'
          }
        ]
      }
    ];

    return mockRequests;
  }

  // Generate GDPR-compatible mock requests
  private static generateGDPRRequests(): DataSubjectRequest[] {
    return [
      {
        id: 'dsr-gdpr-001',
        email: '<EMAIL>',
        type: 'access',
        status: 'in_progress',
        priority: 'medium',
        description: 'Request for access to all personal data processed by the organization',
        notes: 'Customer has provided valid identification. Processing request.',
        progress: 65,
        submittedAt: new Date('2024-01-15T10:30:00Z'),
        dueDate: new Date('2024-02-14T10:30:00Z'),
        assignedTo: '<EMAIL>',
        attachments: ['id-verification.pdf'],
        communicationLog: [
          {
            id: 'comm-001',
            timestamp: new Date('2024-01-15T10:35:00Z'),
            type: 'email',
            content: 'Request received and acknowledged',
            author: 'Privacy Team'
          }
        ]
      },
      {
        id: 'dsr-gdpr-002',
        email: '<EMAIL>',
        type: 'erasure',
        status: 'pending',
        priority: 'urgent',
        description: 'Request for complete deletion of personal data under right to be forgotten',
        notes: 'Urgent request - customer closing account',
        progress: 0,
        submittedAt: new Date('2024-01-20T14:15:00Z'),
        dueDate: new Date('2024-02-19T14:15:00Z'),
        assignedTo: '<EMAIL>',
        attachments: [],
        communicationLog: []
      },
      {
        id: 'dsr-gdpr-003',
        email: '<EMAIL>',
        type: 'portability',
        status: 'completed',
        priority: 'low',
        description: 'Request for data portability to transfer to another service provider',
        notes: 'Data export completed and delivered to customer',
        progress: 100,
        submittedAt: new Date('2024-01-10T09:00:00Z'),
        dueDate: new Date('2024-02-09T09:00:00Z'),
        completedAt: new Date('2024-01-25T16:30:00Z'),
        assignedTo: '<EMAIL>',
        attachments: ['data-export.zip'],
        communicationLog: [
          {
            id: 'comm-002',
            timestamp: new Date('2024-01-25T16:30:00Z'),
            type: 'email',
            content: 'Data export completed and sent to customer',
            author: 'Technical Team'
          }
        ]
      },
      {
        id: 'dsr-gdpr-004',
        email: '<EMAIL>',
        type: 'rectification',
        status: 'in_progress',
        priority: 'high',
        description: 'Request to correct inaccurate personal information in customer profile',
        notes: 'Customer provided documentation for address correction',
        progress: 80,
        submittedAt: new Date('2024-01-18T11:45:00Z'),
        dueDate: new Date('2024-02-17T11:45:00Z'),
        assignedTo: '<EMAIL>',
        attachments: ['address-proof.pdf'],
        communicationLog: [
          {
            id: 'comm-003',
            timestamp: new Date('2024-01-18T12:00:00Z'),
            type: 'email',
            content: 'Rectification request received, processing update',
            author: 'Customer Service'
          }
        ]
      }
    ];
  }
}