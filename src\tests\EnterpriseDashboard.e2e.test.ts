/**
 * End-to-End Tests for Enterprise Dashboard
 * Tests complete user workflows and real browser interactions
 */

import { test, expect, Page } from '@playwright/test';

// Test configuration
const BASE_URL = process.env.BASE_URL || 'http://localhost:5173';
const TIMEOUT = 30000;

// Helper functions
async function waitForDashboardLoad(page: Page) {
  await page.waitForSelector('[data-testid="enterprise-dashboard"]', { timeout: TIMEOUT });
  await page.waitForLoadState('networkidle');
}

async function navigateToTab(page: Page, tabName: string) {
  await page.click(`button:has-text("${tabName}")`);
  await page.waitForTimeout(1000); // Allow for animations
}

async function checkForErrors(page: Page) {
  const errors = await page.evaluate(() => {
    return window.console.error.toString();
  });
  return errors;
}

test.describe('Enterprise Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the Enterprise Dashboard
    await page.goto(`${BASE_URL}/#/enterprise`);
    await waitForDashboardLoad(page);
  });

  test.describe('Dashboard Loading and Navigation', () => {
    test('dashboard loads completely without errors', async ({ page }) => {
      // Check that main dashboard container is present
      await expect(page.locator('[data-testid="enterprise-dashboard"]')).toBeVisible();
      
      // Check that key components are loaded
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      await expect(page.locator('[data-testid="compliance-metrics"]')).toBeVisible();
      
      // Verify no console errors
      const logs = [];
      page.on('console', msg => {
        if (msg.type() === 'error') {
          logs.push(msg.text());
        }
      });
      
      await page.waitForTimeout(2000);
      expect(logs.filter(log => !log.includes('Warning'))).toHaveLength(0);
    });

    test('tab navigation works correctly', async ({ page }) => {
      // Start on Data Protection tab (default)
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      
      // Navigate to Security Overview
      await navigateToTab(page, 'Security Overview');
      await expect(page.locator('[data-testid="security-overview"]')).toBeVisible();
      await expect(page.locator('[data-testid="gdpr-command-center"]')).not.toBeVisible();
      
      // Navigate to Site Reliability
      await navigateToTab(page, 'Site Reliability');
      await expect(page.locator('[data-testid="site-reliability"]')).toBeVisible();
      
      // Navigate back to Data Protection
      await navigateToTab(page, 'Data Protection');
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      await expect(page.locator('[data-testid="compliance-metrics"]')).toBeVisible();
    });

    test('sidebar navigation displays correctly', async ({ page }) => {
      // Check that sidebar is present
      await expect(page.locator('[data-testid="sidebar"]')).toBeVisible();
      
      // Check that navigation items have both icons and labels
      const navItems = page.locator('[data-testid="nav-item"]');
      const count = await navItems.count();
      
      for (let i = 0; i < count; i++) {
        const item = navItems.nth(i);
        await expect(item.locator('svg')).toBeVisible(); // Icon
        await expect(item.locator('span')).toBeVisible(); // Label
      }
    });
  });

  test.describe('Component Functionality', () => {
    test('GDPR Command Center functionality', async ({ page }) => {
      // Ensure we're on the Data Protection tab
      await navigateToTab(page, 'Data Protection');
      
      // Check GDPR overview loads
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      
      // Test GDPR tab navigation
      await page.click('button:has-text("Requests")');
      await expect(page.locator('text=Data Subject Requests')).toBeVisible();
      
      await page.click('button:has-text("Overview")');
      await expect(page.locator('text=GDPR Overview')).toBeVisible();
      
      // Test data loading indicators
      const loadingIndicators = page.locator('.animate-spin');
      if (await loadingIndicators.count() > 0) {
        await expect(loadingIndicators.first()).not.toBeVisible({ timeout: 10000 });
      }
    });

    test('Security Overview functionality', async ({ page }) => {
      await navigateToTab(page, 'Security Overview');
      
      // Check security metrics are displayed
      await expect(page.locator('text=Systems Monitored')).toBeVisible();
      await expect(page.locator('text=Security Score')).toBeVisible();
      
      // Check for charts
      await expect(page.locator('[data-testid="line-chart"], canvas')).toBeVisible();
      
      // Test refresh functionality
      const refreshButton = page.locator('button:has-text("Refresh")');
      if (await refreshButton.isVisible()) {
        await refreshButton.click();
        await page.waitForTimeout(1000);
      }
    });

    test('Compliance Metrics functionality', async ({ page }) => {
      await navigateToTab(page, 'Data Protection');
      
      // Check compliance metrics are displayed
      await expect(page.locator('[data-testid="compliance-metrics"]')).toBeVisible();
      
      // Test visibility controls
      const visibilityToggle = page.locator('[data-testid="departments-visibility-toggle"]');
      if (await visibilityToggle.isVisible()) {
        await visibilityToggle.click();
        await expect(page.locator('[data-testid="departments-section"]')).not.toBeVisible();
        
        await visibilityToggle.click();
        await expect(page.locator('[data-testid="departments-section"]')).toBeVisible();
      }
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('handles network failures gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      // Reload the page
      await page.reload();
      await page.waitForTimeout(3000);
      
      // Check that error states are displayed appropriately
      const errorMessages = page.locator('text=/failed to load|unavailable|error/i');
      if (await errorMessages.count() > 0) {
        await expect(errorMessages.first()).toBeVisible();
        
        // Check for retry buttons
        const retryButtons = page.locator('button:has-text("Retry")');
        if (await retryButtons.count() > 0) {
          await expect(retryButtons.first()).toBeVisible();
        }
      }
    });

    test('error boundaries prevent complete crashes', async ({ page }) => {
      // Monitor for unhandled errors
      const errors = [];
      page.on('pageerror', error => {
        errors.push(error.message);
      });
      
      // Try to trigger various interactions that might cause errors
      await navigateToTab(page, 'Security Overview');
      await page.waitForTimeout(1000);
      
      await navigateToTab(page, 'Data Protection');
      await page.waitForTimeout(1000);
      
      // The dashboard should still be functional
      await expect(page.locator('[data-testid="enterprise-dashboard"]')).toBeVisible();
      
      // Check that no critical errors occurred
      const criticalErrors = errors.filter(error => 
        !error.includes('Warning') && 
        !error.includes('ResizeObserver') &&
        !error.includes('Non-passive event listener')
      );
      expect(criticalErrors).toHaveLength(0);
    });
  });

  test.describe('Performance and Responsiveness', () => {
    test('dashboard loads within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto(`${BASE_URL}/#/enterprise`);
      await waitForDashboardLoad(page);
      
      const loadTime = Date.now() - startTime;
      expect(loadTime).toBeLessThan(5000); // Should load within 5 seconds
    });

    test('responsive design works on different screen sizes', async ({ page }) => {
      // Test desktop view
      await page.setViewportSize({ width: 1920, height: 1080 });
      await expect(page.locator('[data-testid="enterprise-dashboard"]')).toBeVisible();
      
      // Test tablet view
      await page.setViewportSize({ width: 768, height: 1024 });
      await expect(page.locator('[data-testid="enterprise-dashboard"]')).toBeVisible();
      
      // Test mobile view
      await page.setViewportSize({ width: 375, height: 667 });
      await expect(page.locator('[data-testid="enterprise-dashboard"]')).toBeVisible();
      
      // Verify navigation still works on mobile
      await navigateToTab(page, 'Security Overview');
      await expect(page.locator('[data-testid="security-overview"]')).toBeVisible();
    });

    test('charts and visualizations render correctly', async ({ page }) => {
      await navigateToTab(page, 'Data Protection');
      
      // Wait for charts to load
      await page.waitForTimeout(2000);
      
      // Check for chart elements (canvas or SVG)
      const charts = page.locator('canvas, svg, [data-testid*="chart"]');
      const chartCount = await charts.count();
      expect(chartCount).toBeGreaterThan(0);
      
      // Verify charts are visible
      for (let i = 0; i < Math.min(chartCount, 5); i++) {
        await expect(charts.nth(i)).toBeVisible();
      }
    });
  });

  test.describe('User Workflows', () => {
    test('complete dashboard exploration workflow', async ({ page }) => {
      // Start on Data Protection tab
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      
      // Explore GDPR sections
      await page.click('button:has-text("Requests")');
      await page.waitForTimeout(1000);
      
      await page.click('button:has-text("Mapping")');
      await page.waitForTimeout(1000);
      
      await page.click('button:has-text("Overview")');
      await page.waitForTimeout(1000);
      
      // Navigate to Security Overview
      await navigateToTab(page, 'Security Overview');
      await expect(page.locator('[data-testid="security-overview"]')).toBeVisible();
      
      // Navigate to Site Reliability
      await navigateToTab(page, 'Site Reliability');
      await expect(page.locator('[data-testid="site-reliability"]')).toBeVisible();
      
      // Return to Data Protection
      await navigateToTab(page, 'Data Protection');
      await expect(page.locator('[data-testid="gdpr-command-center"]')).toBeVisible();
      
      // Verify data persistence (data should still be loaded)
      await expect(page.locator('text=/\d+/')).toBeVisible(); // Some numeric data
    });

    test('accessibility navigation workflow', async ({ page }) => {
      // Test keyboard navigation
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Check focus indicators
      const focusedElement = page.locator(':focus');
      await expect(focusedElement).toBeVisible();
      
      // Test ARIA attributes
      const buttons = page.locator('button[role="button"]');
      const buttonCount = await buttons.count();
      expect(buttonCount).toBeGreaterThan(0);
      
      // Test screen reader compatibility
      const headings = page.locator('h1, h2, h3, h4, h5, h6');
      const headingCount = await headings.count();
      expect(headingCount).toBeGreaterThan(0);
    });
  });

  test.describe('Data Integrity', () => {
    test('data consistency across navigation', async ({ page }) => {
      // Load initial data
      await navigateToTab(page, 'Data Protection');
      
      // Capture some data values
      const gdprMetrics = await page.locator('[data-testid="gdpr-metrics"] .text-2xl').allTextContents();
      
      // Navigate away and back
      await navigateToTab(page, 'Security Overview');
      await navigateToTab(page, 'Data Protection');
      
      // Verify data consistency
      const gdprMetricsAfter = await page.locator('[data-testid="gdpr-metrics"] .text-2xl').allTextContents();
      expect(gdprMetricsAfter).toEqual(gdprMetrics);
    });

    test('real-time updates work correctly', async ({ page }) => {
      await navigateToTab(page, 'Data Protection');
      
      // Wait for initial load
      await page.waitForTimeout(2000);
      
      // Capture initial timestamp or data
      const initialData = await page.locator('[data-testid="last-update"]').textContent();
      
      // Wait for real-time update (15+ seconds)
      await page.waitForTimeout(16000);
      
      // Check if data has been updated
      const updatedData = await page.locator('[data-testid="last-update"]').textContent();
      
      // Data should either be updated or show loading indicators
      const hasUpdated = initialData !== updatedData;
      const hasLoadingIndicator = await page.locator('.animate-spin').isVisible();
      
      expect(hasUpdated || hasLoadingIndicator).toBeTruthy();
    });
  });
});
