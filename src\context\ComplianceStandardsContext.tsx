import React, { createContext, useContext, useState, ReactNode, useEffect, useCallback } from 'react';
import { configurationService } from '../services/api';

// Comprehensive compliance standards interface
export interface ComplianceStandards {
  gdprEnabled: boolean;
  dpdpEnabled: boolean;
  hipaaEnabled: boolean;
  sox: boolean;
  pci: boolean;
  iso27001: boolean;
  ccpa: boolean;
  nist: boolean;
  fedramp: boolean;
  soc2: boolean;
  cobit: boolean;
  itil: boolean;
  coso: boolean;
  iso20000: boolean;
  iso22301: boolean;
  gdprBcr: boolean;
  pipeda: boolean;
}

// Standard display names mapping
export const STANDARD_DISPLAY_NAMES: Record<keyof ComplianceStandards, string> = {
  gdprEnabled: 'GDPR',
  dpdpEnabled: 'DPDP',
  hipaaEnabled: 'HIPAA',
  sox: 'SOX',
  pci: 'PCI DSS',
  iso27001: 'ISO 27001',
  ccpa: 'CCPA',
  nist: 'NIST',
  fedramp: 'FedRAMP',
  soc2: 'SOC 2',
  cobit: 'COBIT',
  itil: 'ITIL',
  coso: 'COSO',
  iso20000: 'ISO 20000',
  iso22301: 'ISO 22301',
  gdprBcr: 'GDPR BCR',
  pipeda: 'PIPEDA',
};

interface ComplianceStandardsContextType {
  complianceStandards: ComplianceStandards;
  toggleStandard: (standard: keyof ComplianceStandards, enabled: boolean) => Promise<void>;
  getEnabledStandards: () => string[];
  getEnabledStandardKeys: () => (keyof ComplianceStandards)[];
  isStandardEnabled: (standard: keyof ComplianceStandards) => boolean;
  isLoading: boolean;
  error: string | null;
  refreshStandards: () => Promise<void>;
}

const ComplianceStandardsContext = createContext<ComplianceStandardsContextType | undefined>(undefined);

// Default compliance standards configuration
const defaultStandards: ComplianceStandards = {
  gdprEnabled: true,
  dpdpEnabled: false,
  hipaaEnabled: false,
  sox: false,
  pci: true,
  iso27001: false,
  ccpa: true,
  nist: false,
  fedramp: false,
  soc2: false,
  cobit: false,
  itil: false,
  coso: false,
  iso20000: false,
  iso22301: false,
  gdprBcr: false,
  pipeda: false,
};

export const ComplianceStandardsProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [complianceStandards, setComplianceStandards] = useState<ComplianceStandards>(defaultStandards);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load compliance standards from configuration service
  const loadStandards = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        setComplianceStandards({
          gdprEnabled: settings.compliance.gdprEnabled ?? defaultStandards.gdprEnabled,
          dpdpEnabled: settings.compliance.dpdpEnabled ?? defaultStandards.dpdpEnabled,
          hipaaEnabled: settings.compliance.hipaaEnabled ?? defaultStandards.hipaaEnabled,
          sox: settings.compliance.sox ?? defaultStandards.sox,
          pci: settings.compliance.pci ?? defaultStandards.pci,
          iso27001: settings.compliance.iso27001 ?? defaultStandards.iso27001,
          ccpa: settings.compliance.ccpa ?? defaultStandards.ccpa,
          nist: settings.compliance.nist ?? defaultStandards.nist,
          fedramp: settings.compliance.fedramp ?? defaultStandards.fedramp,
          soc2: settings.compliance.soc2 ?? defaultStandards.soc2,
          cobit: settings.compliance.cobit ?? defaultStandards.cobit,
          itil: settings.compliance.itil ?? defaultStandards.itil,
          coso: settings.compliance.coso ?? defaultStandards.coso,
          iso20000: settings.compliance.iso20000 ?? defaultStandards.iso20000,
          iso22301: settings.compliance.iso22301 ?? defaultStandards.iso22301,
          gdprBcr: settings.compliance.gdprBcr ?? defaultStandards.gdprBcr,
          pipeda: settings.compliance.pipeda ?? defaultStandards.pipeda,
        });
      }
    } catch (err) {
      console.warn('Could not load compliance standards, using defaults:', err);
      setError('Failed to load compliance standards');
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Initialize standards on mount
  useEffect(() => {
    loadStandards();
  }, [loadStandards]);

  // Toggle a specific compliance standard
  const toggleStandard = useCallback(async (standard: keyof ComplianceStandards, enabled: boolean) => {
    // Update local state immediately for responsive UI
    setComplianceStandards(prev => ({ ...prev, [standard]: enabled }));

    try {
      // Update configuration service
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance[standard] = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.error(`Failed to update ${standard} setting:`, err);
      // Revert local state on error
      setComplianceStandards(prev => ({ ...prev, [standard]: !enabled }));
      setError(`Failed to update ${STANDARD_DISPLAY_NAMES[standard]} setting`);
    }
  }, []);

  // Get list of enabled standards (display names)
  const getEnabledStandards = useCallback((): string[] => {
    return Object.entries(complianceStandards)
      .filter(([_, enabled]) => enabled)
      .map(([standard, _]) => STANDARD_DISPLAY_NAMES[standard as keyof ComplianceStandards]);
  }, [complianceStandards]);

  // Get list of enabled standard keys
  const getEnabledStandardKeys = useCallback((): (keyof ComplianceStandards)[] => {
    return Object.entries(complianceStandards)
      .filter(([_, enabled]) => enabled)
      .map(([standard, _]) => standard as keyof ComplianceStandards);
  }, [complianceStandards]);

  // Check if a specific standard is enabled
  const isStandardEnabled = useCallback((standard: keyof ComplianceStandards): boolean => {
    return complianceStandards[standard];
  }, [complianceStandards]);

  // Refresh standards from server
  const refreshStandards = useCallback(async () => {
    await loadStandards();
  }, [loadStandards]);

  const contextValue: ComplianceStandardsContextType = {
    complianceStandards,
    toggleStandard,
    getEnabledStandards,
    getEnabledStandardKeys,
    isStandardEnabled,
    isLoading,
    error,
    refreshStandards,
  };

  return (
    <ComplianceStandardsContext.Provider value={contextValue}>
      {children}
    </ComplianceStandardsContext.Provider>
  );
};

export const useComplianceStandards = () => {
  const context = useContext(ComplianceStandardsContext);
  if (context === undefined) {
    throw new Error('useComplianceStandards must be used within a ComplianceStandardsProvider');
  }
  return context;
};
