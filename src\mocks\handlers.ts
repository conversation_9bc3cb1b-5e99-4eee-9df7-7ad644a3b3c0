import { http, HttpResponse } from 'msw';

// Define default settings for the mock API
const defaultSettings = {
  apiConfiguration: {
    endpoint: 'https://api.compliance.example.com',
    apiKey: 'mock-api-key',
    secretKey: 'mock-secret-key',
    timeout: 30,
    environment: 'development',
    apiVersion: 'v1',
    rateLimits: {
      enabled: true,
      requestsPerMinute: 100
    }
  },
  security: {
    mfaEnabled: true,
    passwordPolicy: {
      minLength: 12,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      maxAge: 90,
      preventReuse: 5,
      preventCommonWords: true,
    },
    sessionTimeout: 30,
    ipWhitelist: [],
    failedLoginAttempts: 5,
    lockoutDuration: 15,
    sslEnabled: true,
    encryptionLevel: 'high',
    deviceTracking: false,
    geofencing: false,
    bruteForceProtection: true,
    anomalyDetection: false,
    ddosProtection: true,
  },
  notifications: {
    email: true,
    slack: false,
    webhook: '',
    frequency: 'daily',
  },
  dataRetention: {
    auditLogs: 365,
    reports: 90,
    backupEnabled: true,
    backupFrequency: 'daily',
  },
  compliance: {
    gdprEnabled: true,
    dpdpEnabled: false,
    hipaaEnabled: false,
    sox: false,
    pci: true,
    iso27001: false,
    ccpa: true,
    nist: false,
    fedramp: false,
  },
  externalServices: {
    openai: {
      apiKey: 'mock-openai-api-key',
      model: 'gpt-4',
      maxTokens: 2000
    },
    aws: {
      accessKeyId: 'mock-aws-access-key',
      secretAccessKey: 'mock-aws-secret-key',
      region: 'us-east-1'
    },
    azure: {
      tenantId: 'mock-azure-tenant-id',
      clientId: 'mock-azure-client-id',
      clientSecret: 'mock-azure-client-secret'
    }
  },
  auditSettings: {
    enableAuditLogging: true,
    logRetentionDays: 90,
    detailedLogging: true,
    sensitiveDataMasking: true
  },
  riskManagement: {
    enableRiskAssessment: true,
    riskThreshold: 75,
    automaticMitigation: false,
    notifyOnHighRisk: true
  },
  integrations: {
    jira: {
      enabled: false,
      apiKey: '',
      projectKey: ''
    }
  }
};

// Store settings in memory for the mock API
let settings = { ...defaultSettings };

export const handlers = [
  // GET /api/settings
  http.get('/api/settings', () => {
    console.log('[MSW] Returning mock settings');
    return HttpResponse.json(settings);
  }),

  // Removed all Flyer Verification API endpoints











  // PUT /api/settings
  http.put('/api/settings', async ({ request }) => {
    try {
      const updatedSettings = await request.json() as Record<string, unknown>;
      console.log('[MSW] Updating settings with:', updatedSettings);

      // Handle nested updates
      if (updatedSettings && typeof updatedSettings === 'object' && updatedSettings.compliance) {
        settings.compliance = {
          ...settings.compliance,
          ...updatedSettings.compliance
        };
      } else if (updatedSettings && typeof updatedSettings === 'object') {
        settings = { ...settings, ...updatedSettings };
      }

      return HttpResponse.json(settings);
    } catch (error) {
      console.error('[MSW] Error processing settings update:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid request data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  // POST /compliance/logs - For logging compliance results
  http.post('/compliance/logs', async ({ request }) => {
    try {
      const logData = await request.json();
      console.log('[MSW] Logging compliance result:', logData);
      return HttpResponse.json({ success: true, id: `log-${Date.now()}` });
    } catch (error) {
      console.error('[MSW] Error logging compliance result:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid log data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  // Report API endpoints
  http.post('/api/reports', async ({ request }) => {
    try {
      const reportData = await request.json() as Record<string, unknown>;
      console.log('[MSW] Creating report:', reportData);
      return HttpResponse.json({
        id: `report-${Date.now()}`,
        ...(typeof reportData === 'object' && reportData !== null ? reportData : {}),
        createdAt: new Date().toISOString()
      });
    } catch (error) {
      console.error('[MSW] Error creating report:', error);
      return new HttpResponse(JSON.stringify({ error: 'Invalid report data' }), {
        status: 400,
        headers: {
          'Content-Type': 'application/json'
        }
      });
    }
  }),

  http.delete('/api/reports/:reportId', ({ params }) => {
    const { reportId } = params;
    console.log(`[MSW] Deleting report: ${reportId}`);
    return HttpResponse.json({ success: true });
  }),

  http.get('/api/reports/:reportId/download', ({ params }) => {
    const { reportId } = params;
    console.log(`[MSW] Downloading report: ${reportId}`);

    // Return a mock PDF file (just some text in this case)
    return new HttpResponse('Mock PDF content for report', {
      status: 200,
      headers: {
        'Content-Type': 'application/pdf',
        'Content-Disposition': `attachment; filename="report-${reportId}.pdf"`
      }
    });
  })
];
