import { test, expect } from '@playwright/test';

test.describe('GDPR Dashboard E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the application
    await page.goto('/');
    
    // Wait for the application to load
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
  });

  test.describe('Dashboard Navigation', () => {
    test('should navigate between all dashboard sections', async ({ page }) => {
      // Verify main dashboard loads
      await expect(page.locator('text=GDPR Command Center')).toBeVisible();
      
      // Navigate to Data Subject Requests
      await page.click('text=Data Subject Requests');
      await expect(page.locator('text=Data Subject Request Management')).toBeVisible();
      
      // Navigate to Branch Detection
      await page.click('text=Branch Detection');
      await expect(page.locator('text=Branch Detection & Data Flow')).toBeVisible();
      
      // Navigate to Consent Management
      await page.click('text=Consent Management');
      await expect(page.locator('text=Consent Management Center')).toBeVisible();
      
      // Navigate to Impact Assessments
      await page.click('text=Impact Assessments');
      await expect(page.locator('text=Impact Assessments (DPIA)')).toBeVisible();
      
      // Return to Overview
      await page.click('text=Overview');
      await expect(page.locator('text=GDPR Command Center')).toBeVisible();
    });

    test('should maintain active tab state', async ({ page }) => {
      // Click on Data Subject Requests tab
      const requestsTab = page.locator('text=Data Subject Requests');
      await requestsTab.click();
      
      // Verify tab is active (check for active styling)
      await expect(requestsTab).toHaveClass(/bg-primary/);
      
      // Navigate to another tab
      const branchTab = page.locator('text=Branch Detection');
      await branchTab.click();
      
      // Verify new tab is active and old tab is inactive
      await expect(branchTab).toHaveClass(/bg-primary/);
      await expect(requestsTab).not.toHaveClass(/bg-primary/);
    });

    test('should load data correctly in each section', async ({ page }) => {
      // Check Overview metrics
      await expect(page.locator('[data-testid="total-requests"]')).toBeVisible();
      await expect(page.locator('[data-testid="compliance-score"]')).toBeVisible();
      
      // Check Data Subject Requests
      await page.click('text=Data Subject Requests');
      await expect(page.locator('[data-testid="requests-table"]')).toBeVisible();
      
      // Check Branch Detection
      await page.click('text=Branch Detection');
      await expect(page.locator('[data-testid="branches-table"]')).toBeVisible();
      
      // Check Consent Management
      await page.click('text=Consent Management');
      await expect(page.locator('[data-testid="consent-metrics"]')).toBeVisible();
      
      // Check Impact Assessments
      await page.click('text=Impact Assessments');
      await expect(page.locator('[data-testid="assessments-grid"]')).toBeVisible();
    });
  });

  test.describe('Data Subject Request Workflows', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('text=Data Subject Requests');
      await expect(page.locator('text=Data Subject Request Management')).toBeVisible();
    });

    test('should create a new data subject request', async ({ page }) => {
      // Click create button
      await page.click('button:has-text("Create Request")');
      
      // Verify modal opens
      await expect(page.locator('text=Create New Data Subject Request')).toBeVisible();
      
      // Fill out form
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.selectOption('[data-testid="type-select"]', 'access');
      await page.selectOption('[data-testid="priority-select"]', 'high');
      await page.fill('[data-testid="description-input"]', 'Test request for E2E testing');
      
      // Submit form
      await page.click('button:has-text("Create Request")');
      
      // Verify success notification
      await expect(page.locator('text=Request created successfully')).toBeVisible();
      
      // Verify request appears in table
      await expect(page.locator('text=<EMAIL>')).toBeVisible();
    });

    test('should edit an existing data subject request', async ({ page }) => {
      // Wait for requests to load
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Click edit button for first request
      await page.click('[data-testid="edit-request-btn"]:first-child');
      
      // Verify edit modal opens
      await expect(page.locator('text=Edit Data Subject Request')).toBeVisible();
      
      // Update status
      await page.selectOption('[data-testid="status-select"]', 'in_progress');
      
      // Add notes
      await page.fill('[data-testid="notes-input"]', 'Updated via E2E test');
      
      // Submit changes
      await page.click('button:has-text("Update Request")');
      
      // Verify success notification
      await expect(page.locator('text=Request updated successfully')).toBeVisible();
      
      // Verify changes are reflected
      await expect(page.locator('text=In Progress')).toBeVisible();
    });

    test('should delete a data subject request', async ({ page }) => {
      // Wait for requests to load
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Click delete button for first request
      await page.click('[data-testid="delete-request-btn"]:first-child');
      
      // Verify confirmation dialog
      await expect(page.locator('text=Are you sure you want to delete')).toBeVisible();
      
      // Confirm deletion
      await page.click('button:has-text("Confirm")');
      
      // Verify success notification
      await expect(page.locator('text=Request deleted successfully')).toBeVisible();
    });

    test('should validate form inputs', async ({ page }) => {
      // Click create button
      await page.click('button:has-text("Create Request")');
      
      // Try to submit empty form
      await page.click('button:has-text("Create Request")');
      
      // Verify validation errors
      await expect(page.locator('text=Email is required')).toBeVisible();
      await expect(page.locator('text=Description is required')).toBeVisible();
      
      // Fill invalid email
      await page.fill('[data-testid="email-input"]', 'invalid-email');
      await page.click('button:has-text("Create Request")');
      
      // Verify email validation
      await expect(page.locator('text=Please enter a valid email address')).toBeVisible();
    });

    test('should filter and search requests', async ({ page }) => {
      // Wait for requests to load
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Test search functionality
      await page.fill('[data-testid="search-input"]', '<EMAIL>');
      await page.waitForTimeout(500); // Wait for debounced search
      
      // Verify filtered results
      await expect(page.locator('[data-testid="requests-table"] tr')).toHaveCount(2); // Header + 1 result
      
      // Clear search
      await page.fill('[data-testid="search-input"]', '');
      
      // Test status filter
      await page.click('[data-testid="filter-button"]');
      await page.check('[data-testid="status-pending"]');
      await page.click('button:has-text("Apply Filters")');
      
      // Verify filtered results
      await expect(page.locator('text=Pending')).toBeVisible();
    });
  });

  test.describe('Branch Detection Workflows', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('text=Branch Detection');
      await expect(page.locator('text=Branch Detection & Data Flow')).toBeVisible();
    });

    test('should create a new branch', async ({ page }) => {
      // Click create branch button
      await page.click('button:has-text("Add Branch")');
      
      // Verify modal opens
      await expect(page.locator('text=Create New Branch')).toBeVisible();
      
      // Fill out form
      await page.fill('[data-testid="branch-name-input"]', 'Test Branch');
      await page.fill('[data-testid="location-input"]', 'Test Location');
      await page.selectOption('[data-testid="country-select"]', 'United States');
      await page.fill('[data-testid="employee-count-input"]', '100');
      
      // Submit form
      await page.click('button:has-text("Create Branch")');
      
      // Verify success notification
      await expect(page.locator('text=Branch created successfully')).toBeVisible();
      
      // Verify branch appears in table
      await expect(page.locator('text=Test Branch')).toBeVisible();
    });

    test('should display branch compliance scores', async ({ page }) => {
      // Wait for branches to load
      await page.waitForSelector('[data-testid="branches-table"]');
      
      // Verify compliance scores are displayed
      await expect(page.locator('[data-testid="compliance-score"]')).toBeVisible();
      
      // Verify risk levels are displayed
      await expect(page.locator('[data-testid="risk-level"]')).toBeVisible();
    });

    test('should manage data flows', async ({ page }) => {
      // Switch to data flows tab
      await page.click('text=Data Flows');
      
      // Click create data flow button
      await page.click('button:has-text("Add Data Flow")');
      
      // Verify modal opens
      await expect(page.locator('text=Create New Data Flow')).toBeVisible();
      
      // Fill out form
      await page.fill('[data-testid="flow-name-input"]', 'Test Data Flow');
      await page.selectOption('[data-testid="source-branch-select"]', 'branch-1');
      await page.selectOption('[data-testid="destination-branch-select"]', 'branch-2');
      await page.check('[data-testid="data-category-personal"]');
      
      // Submit form
      await page.click('button:has-text("Create Data Flow")');
      
      // Verify success notification
      await expect(page.locator('text=Data flow created successfully')).toBeVisible();
    });
  });

  test.describe('Consent Management Workflows', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('text=Consent Management');
      await expect(page.locator('text=Consent Management Center')).toBeVisible();
    });

    test('should display consent metrics', async ({ page }) => {
      // Verify metrics cards are displayed
      await expect(page.locator('[data-testid="total-users"]')).toBeVisible();
      await expect(page.locator('[data-testid="consent-rate"]')).toBeVisible();
      await expect(page.locator('[data-testid="withdrawal-rate"]')).toBeVisible();
      
      // Verify consent trend chart
      await expect(page.locator('[data-testid="consent-trend-chart"]')).toBeVisible();
    });

    test('should manage consent records', async ({ page }) => {
      // Switch to records tab
      await page.click('text=Records');
      
      // Wait for records to load
      await page.waitForSelector('[data-testid="consent-records-table"]');
      
      // Edit a consent record
      await page.click('[data-testid="edit-consent-btn"]:first-child');
      
      // Verify edit modal opens
      await expect(page.locator('text=Edit Consent Record')).toBeVisible();
      
      // Toggle consent status
      await page.click('[data-testid="consent-toggle"]');
      
      // Submit changes
      await page.click('button:has-text("Update Record")');
      
      // Verify success notification
      await expect(page.locator('text=Consent record updated successfully')).toBeVisible();
    });

    test('should display consent categories', async ({ page }) => {
      // Switch to categories tab
      await page.click('text=Categories');
      
      // Verify categories are displayed
      await expect(page.locator('[data-testid="consent-categories"]')).toBeVisible();
      
      // Verify category statistics
      await expect(page.locator('[data-testid="category-stats"]')).toBeVisible();
    });
  });

  test.describe('Impact Assessment Workflows', () => {
    test.beforeEach(async ({ page }) => {
      await page.click('text=Impact Assessments');
      await expect(page.locator('text=Impact Assessments (DPIA)')).toBeVisible();
    });

    test('should create a new assessment', async ({ page }) => {
      // Switch to assessments tab
      await page.click('text=Assessments');
      
      // Click create assessment button
      await page.click('button:has-text("Create Assessment")');
      
      // Verify modal opens
      await expect(page.locator('text=Create New Assessment')).toBeVisible();
      
      // Fill out form
      await page.fill('[data-testid="assessment-title-input"]', 'Test DPIA');
      await page.fill('[data-testid="assessment-description-input"]', 'Test assessment description');
      await page.selectOption('[data-testid="assessment-type-select"]', 'DPIA');
      await page.selectOption('[data-testid="assigned-to-select"]', 'user-1');
      
      // Submit form
      await page.click('button:has-text("Create Assessment")');
      
      // Verify success notification
      await expect(page.locator('text=Assessment created successfully')).toBeVisible();
      
      // Verify assessment appears in grid
      await expect(page.locator('text=Test DPIA')).toBeVisible();
    });

    test('should use assessment templates', async ({ page }) => {
      // Switch to templates tab
      await page.click('text=Templates');
      
      // Click use template button
      await page.click('[data-testid="use-template-btn"]:first-child');
      
      // Verify template form opens
      await expect(page.locator('text=Create Assessment from Template')).toBeVisible();
      
      // Template should be pre-selected
      await expect(page.locator('[data-testid="template-select"]')).toHaveValue('dpia-template');
      
      // Fill required fields
      await page.fill('[data-testid="assessment-title-input"]', 'Template-based Assessment');
      
      // Submit form
      await page.click('button:has-text("Create Assessment")');
      
      // Verify success
      await expect(page.locator('text=Assessment created successfully')).toBeVisible();
    });

    test('should display assessment analytics', async ({ page }) => {
      // Switch to analytics tab
      await page.click('text=Analytics');
      
      // Verify analytics charts are displayed
      await expect(page.locator('[data-testid="risk-trend-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="completion-rate-chart"]')).toBeVisible();
      await expect(page.locator('[data-testid="assessment-type-chart"]')).toBeVisible();
      
      // Verify KPI metrics
      await expect(page.locator('[data-testid="average-completion-time"]')).toBeVisible();
      await expect(page.locator('[data-testid="overdue-rate"]')).toBeVisible();
    });
  });

  test.describe('Theme and Responsive Design', () => {
    test('should toggle between light and dark themes', async ({ page }) => {
      // Find theme toggle button
      const themeToggle = page.locator('[data-testid="theme-toggle"]');
      
      // Verify initial theme (light)
      await expect(page.locator('body')).toHaveClass(/light/);
      
      // Toggle to dark theme
      await themeToggle.click();
      
      // Verify dark theme is applied
      await expect(page.locator('body')).toHaveClass(/dark/);
      
      // Toggle back to light theme
      await themeToggle.click();
      
      // Verify light theme is restored
      await expect(page.locator('body')).toHaveClass(/light/);
    });

    test('should be responsive on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Verify mobile navigation works
      await expect(page.locator('[data-testid="mobile-menu-toggle"]')).toBeVisible();
      
      // Open mobile menu
      await page.click('[data-testid="mobile-menu-toggle"]');
      
      // Verify navigation items are accessible
      await expect(page.locator('[data-testid="mobile-nav"]')).toBeVisible();
      
      // Navigate to a section
      await page.click('text=Data Subject Requests');
      
      // Verify content is properly displayed on mobile
      await expect(page.locator('text=Data Subject Request Management')).toBeVisible();
    });

    test('should be responsive on tablet devices', async ({ page }) => {
      // Set tablet viewport
      await page.setViewportSize({ width: 768, height: 1024 });
      
      // Verify tablet layout
      await expect(page.locator('[data-testid="dashboard-grid"]')).toBeVisible();
      
      // Navigate between sections
      await page.click('text=Branch Detection');
      
      // Verify content adapts to tablet size
      await expect(page.locator('[data-testid="branches-table"]')).toBeVisible();
    });
  });

  test.describe('Error Handling and Recovery', () => {
    test('should handle network errors gracefully', async ({ page }) => {
      // Simulate network failure
      await page.route('**/api/**', route => route.abort());
      
      // Navigate to a section that requires data
      await page.click('text=Data Subject Requests');
      
      // Verify error message is displayed
      await expect(page.locator('text=Failed to load data')).toBeVisible();
      
      // Verify retry button is available
      await expect(page.locator('button:has-text("Retry")')).toBeVisible();
      
      // Restore network and retry
      await page.unroute('**/api/**');
      await page.click('button:has-text("Retry")');
      
      // Verify data loads successfully
      await expect(page.locator('[data-testid="requests-table"]')).toBeVisible();
    });

    test('should handle component errors with error boundaries', async ({ page }) => {
      // Trigger a component error (this would need to be implemented in the app)
      await page.evaluate(() => {
        window.dispatchEvent(new CustomEvent('trigger-error'));
      });
      
      // Verify error boundary catches the error
      await expect(page.locator('text=Something went wrong')).toBeVisible();
      
      // Verify retry functionality
      await expect(page.locator('button:has-text("Try Again")')).toBeVisible();
    });
  });

  test.describe('Performance and Loading', () => {
    test('should load dashboard within acceptable time', async ({ page }) => {
      const startTime = Date.now();
      
      await page.goto('/');
      await page.waitForSelector('[data-testid="dashboard-loaded"]');
      
      const loadTime = Date.now() - startTime;
      
      // Verify load time is under 3 seconds
      expect(loadTime).toBeLessThan(3000);
    });

    test('should handle large datasets efficiently', async ({ page }) => {
      // Navigate to a section with potentially large data
      await page.click('text=Data Subject Requests');
      
      // Verify pagination or virtual scrolling is implemented
      await expect(page.locator('[data-testid="pagination"]')).toBeVisible();
      
      // Test pagination
      await page.click('[data-testid="next-page"]');
      
      // Verify page navigation works
      await expect(page.locator('[data-testid="page-indicator"]')).toContainText('2');
    });
  });
});
