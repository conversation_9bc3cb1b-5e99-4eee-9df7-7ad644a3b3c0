import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Calendar,
  Activity,
  Zap,
  Target,
  ChevronDown,
  ChevronRight,
  PieChart,
  Line<PERSON>hart,
  MousePointer,
  Layers,
  Clock,
  Users,
  Building,
  Plus,
  Edit,
  Trash2,
  Save,
  X,
  Calendar as CalendarIcon,
  FileText,
  AlertCircle,
  CheckCircle2
} from 'lucide-react';

// Enhanced Compliance Types with comprehensive CRUD functionality
interface Department {
  id: string;
  name: string;
  description: string;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  dataSubjectsCount: number;
  processingActivities: Array<{
    id: string;
    name: string;
    purpose: string;
    legalBasis: string;
    dataCategories: string[];
    retentionPeriod: string;
    status: 'active' | 'inactive' | 'under_review';
  }>;
  responsibleOfficer: string;
  lastAuditDate: Date;
  nextAuditDate: Date;
  violations: number;
  trend: 'up' | 'down' | 'stable';
  createdAt: Date;
  updatedAt: Date;
}

interface CompliancePolicy {
  id: string;
  title: string;
  description: string;
  category: 'data_protection' | 'consent_management' | 'data_retention' | 'breach_response' | 'access_control' | 'security';
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  status: 'draft' | 'under_review' | 'approved' | 'active' | 'archived';
  version: string;
  effectiveDate: Date;
  reviewDate: Date;
  owner: string;
  assignedDepartments: string[];
  adherenceRate: number;
  violations: Array<{
    id: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    reportedDate: Date;
    resolvedDate?: Date;
    status: 'open' | 'investigating' | 'resolved';
  }>;
  changeHistory: Array<{
    version: string;
    changes: string;
    changedBy: string;
    changeDate: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  type: 'policy_implementation' | 'audit_activity' | 'data_subject_request' | 'breach_incident' | 'regulatory_deadline' | 'compliance_milestone';
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  date: Date;
  status: 'completed' | 'in_progress' | 'pending' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
  department: string;
  assignedTo: string;
  relatedEntities: Array<{
    type: 'department' | 'policy' | 'audit' | 'incident';
    id: string;
    name: string;
  }>;
  outcome?: string;
  attachments: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface ComplianceFramework {
  id: string;
  name: string;
  type: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  overallScore: number;
  departments: Department[];
  policies: CompliancePolicy[];
  timeline: TimelineEvent[];
  metrics: {
    totalDepartments: number;
    compliantDepartments: number;
    totalPolicies: number;
    activePolicies: number;
    totalViolations: number;
    resolvedViolations: number;
    averageComplianceScore: number;
    trend: 'up' | 'down' | 'stable';
  };
}

interface ComplianceMetric {
  id: string;
  name: string;
  category: 'privacy' | 'security' | 'operational' | 'regulatory';
  currentValue: number;
  targetValue: number;
  trend: 'up' | 'down' | 'stable';
  status: 'compliant' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    target: number;
    violations: number;
  }>;
  drillDownData?: {
    departments: Array<{ name: string; value: number; status: string }>;
    policies: Array<{ name: string; value: number; violations: number }>;
    timeBreakdown: Array<{ period: string; value: number; incidents: number }>;
  };
}

interface ComplianceOverview {
  totalMetrics: number;
  compliantMetrics: number;
  warningMetrics: number;
  criticalMetrics: number;
  overallScore: number;
  trendDirection: 'up' | 'down' | 'stable';
  lastAssessment: Date;
  nextAssessment: Date;
}

interface EnhancedComplianceData {
  overview: ComplianceOverview;
  metrics: ComplianceMetric[];
  frameworks: ComplianceFramework[];
  categoryBreakdown: {
    privacy: { score: number; count: number; trend: string };
    security: { score: number; count: number; trend: string };
    operational: { score: number; count: number; trend: string };
    regulatory: { score: number; count: number; trend: string };
  };
  realTimeUpdates: {
    isLive: boolean;
    lastSync: Date;
    updateFrequency: number;
    pendingUpdates: number;
  };
}

interface EnhancedComplianceMetricsProps {
  className?: string;
}

// Modal Components for CRUD Operations
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/50" onClick={onClose}></div>
      <div className="relative bg-surface rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-text">{title}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-card rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>
        <div className="p-6">
          {children}
        </div>
      </div>
    </div>
  );
};

// Department Modal Component
interface DepartmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit' | 'view';
  department?: Department | null;
  onSave: (data: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdate: (id: string, data: Partial<Department>) => void;
}

const DepartmentModal: React.FC<DepartmentModalProps> = ({
  isOpen,
  onClose,
  mode,
  department,
  onSave,
  onUpdate
}) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    complianceScore: 0,
    riskLevel: 'low' as 'low' | 'medium' | 'high' | 'critical',
    dataSubjectsCount: 0,
    responsibleOfficer: '',
    violations: 0,
    trend: 'stable' as 'up' | 'down' | 'stable',
    processingActivities: [] as Array<{
      id: string;
      name: string;
      purpose: string;
      legalBasis: string;
      dataCategories: string[];
      retentionPeriod: string;
      status: 'active' | 'inactive' | 'under_review';
    }>
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (department && (mode === 'edit' || mode === 'view')) {
      setFormData({
        name: department.name,
        description: department.description,
        complianceScore: department.complianceScore,
        riskLevel: department.riskLevel,
        dataSubjectsCount: department.dataSubjectsCount,
        responsibleOfficer: department.responsibleOfficer,
        violations: department.violations,
        trend: department.trend,
        processingActivities: department.processingActivities
      });
    } else if (mode === 'create') {
      setFormData({
        name: '',
        description: '',
        complianceScore: 0,
        riskLevel: 'low',
        dataSubjectsCount: 0,
        responsibleOfficer: '',
        violations: 0,
        trend: 'stable',
        processingActivities: []
      });
    }
    setErrors({});
  }, [department, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.name.trim()) newErrors.name = 'Department name is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.responsibleOfficer.trim()) newErrors.responsibleOfficer = 'Responsible officer is required';
    if (formData.complianceScore < 0 || formData.complianceScore > 100) {
      newErrors.complianceScore = 'Compliance score must be between 0 and 100';
    }
    if (formData.dataSubjectsCount < 0) newErrors.dataSubjectsCount = 'Data subjects count cannot be negative';
    if (formData.violations < 0) newErrors.violations = 'Violations count cannot be negative';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const departmentData = {
      ...formData,
      lastAuditDate: new Date(),
      nextAuditDate: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000) // 6 months from now
    };

    if (mode === 'create') {
      onSave(departmentData);
    } else if (mode === 'edit' && department) {
      onUpdate(department.id, departmentData);
    }
  };

  const addProcessingActivity = () => {
    const newActivity = {
      id: `pa-${Date.now()}`,
      name: '',
      purpose: '',
      legalBasis: '',
      dataCategories: [],
      retentionPeriod: '',
      status: 'active' as const
    };
    setFormData(prev => ({
      ...prev,
      processingActivities: [...prev.processingActivities, newActivity]
    }));
  };

  const updateProcessingActivity = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      processingActivities: prev.processingActivities.map((activity, i) =>
        i === index ? { ...activity, [field]: value } : activity
      )
    }));
  };

  const removeProcessingActivity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      processingActivities: prev.processingActivities.filter((_, i) => i !== index)
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'create' ? 'Create' : mode === 'edit' ? 'Edit' : 'View'} Department`}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Department Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter department name"
            />
            {errors.name && <p className="text-red-500 text-sm mt-1">{errors.name}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Responsible Officer *
            </label>
            <input
              type="text"
              value={formData.responsibleOfficer}
              onChange={(e) => setFormData(prev => ({ ...prev, responsibleOfficer: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter responsible officer"
            />
            {errors.responsibleOfficer && <p className="text-red-500 text-sm mt-1">{errors.responsibleOfficer}</p>}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            disabled={mode === 'view'}
            rows={3}
            className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            placeholder="Enter department description"
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Compliance Score (%) *
            </label>
            <input
              type="number"
              min="0"
              max="100"
              step="0.1"
              value={formData.complianceScore}
              onChange={(e) => setFormData(prev => ({ ...prev, complianceScore: parseFloat(e.target.value) || 0 }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
            {errors.complianceScore && <p className="text-red-500 text-sm mt-1">{errors.complianceScore}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Risk Level *
            </label>
            <select
              value={formData.riskLevel}
              onChange={(e) => setFormData(prev => ({ ...prev, riskLevel: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Data Subjects Count *
            </label>
            <input
              type="number"
              min="0"
              value={formData.dataSubjectsCount}
              onChange={(e) => setFormData(prev => ({ ...prev, dataSubjectsCount: parseInt(e.target.value) || 0 }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
            {errors.dataSubjectsCount && <p className="text-red-500 text-sm mt-1">{errors.dataSubjectsCount}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Violations Count
            </label>
            <input
              type="number"
              min="0"
              value={formData.violations}
              onChange={(e) => setFormData(prev => ({ ...prev, violations: parseInt(e.target.value) || 0 }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
            {errors.violations && <p className="text-red-500 text-sm mt-1">{errors.violations}</p>}
          </div>
        </div>

        {/* Processing Activities Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-text">Processing Activities</h3>
            {mode !== 'view' && (
              <button
                type="button"
                onClick={addProcessingActivity}
                className="flex items-center gap-2 px-3 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Activity
              </button>
            )}
          </div>

          <div className="space-y-4">
            {formData.processingActivities.map((activity, index) => (
              <div key={activity.id} className="bg-card rounded-lg p-4 border border-border">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-text">Activity {index + 1}</h4>
                  {mode !== 'view' && (
                    <button
                      type="button"
                      onClick={() => removeProcessingActivity(index)}
                      className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Activity Name</label>
                    <input
                      type="text"
                      value={activity.name}
                      onChange={(e) => updateProcessingActivity(index, 'name', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      placeholder="Enter activity name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Legal Basis</label>
                    <input
                      type="text"
                      value={activity.legalBasis}
                      onChange={(e) => updateProcessingActivity(index, 'legalBasis', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      placeholder="Enter legal basis"
                    />
                  </div>

                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-text mb-1">Purpose</label>
                    <textarea
                      value={activity.purpose}
                      onChange={(e) => updateProcessingActivity(index, 'purpose', e.target.value)}
                      disabled={mode === 'view'}
                      rows={2}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      placeholder="Enter processing purpose"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Retention Period</label>
                    <input
                      type="text"
                      value={activity.retentionPeriod}
                      onChange={(e) => updateProcessingActivity(index, 'retentionPeriod', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      placeholder="Enter retention period"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Status</label>
                    <select
                      value={activity.status}
                      onChange={(e) => updateProcessingActivity(index, 'status', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                      <option value="under_review">Under Review</option>
                    </select>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {mode !== 'view' && (
          <div className="flex items-center justify-end gap-3 pt-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              {mode === 'create' ? 'Create' : 'Update'} Department
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

// Policy Modal Component
interface PolicyModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit' | 'view';
  policy?: CompliancePolicy | null;
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  availableDepartments: Department[];
  onSave: (data: Omit<CompliancePolicy, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdate: (id: string, data: Partial<CompliancePolicy>) => void;
}

const PolicyModal: React.FC<PolicyModalProps> = ({
  isOpen,
  onClose,
  mode,
  policy,
  framework,
  availableDepartments,
  onSave,
  onUpdate
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'data_protection' as 'data_protection' | 'consent_management' | 'data_retention' | 'breach_response' | 'access_control' | 'security',
    status: 'draft' as 'draft' | 'under_review' | 'approved' | 'active' | 'archived',
    version: '1.0',
    effectiveDate: new Date(),
    reviewDate: new Date(),
    owner: '',
    assignedDepartments: [] as string[],
    adherenceRate: 0,
    violations: [] as Array<{
      id: string;
      description: string;
      severity: 'low' | 'medium' | 'high' | 'critical';
      reportedDate: Date;
      resolvedDate?: Date;
      status: 'open' | 'investigating' | 'resolved';
    }>
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (policy && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: policy.title,
        description: policy.description,
        category: policy.category,
        status: policy.status,
        version: policy.version,
        effectiveDate: policy.effectiveDate,
        reviewDate: policy.reviewDate,
        owner: policy.owner,
        assignedDepartments: policy.assignedDepartments,
        adherenceRate: policy.adherenceRate,
        violations: policy.violations
      });
    } else if (mode === 'create') {
      setFormData({
        title: '',
        description: '',
        category: 'data_protection',
        status: 'draft',
        version: '1.0',
        effectiveDate: new Date(),
        reviewDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // 1 year from now
        owner: '',
        assignedDepartments: [],
        adherenceRate: 100,
        violations: []
      });
    }
    setErrors({});
  }, [policy, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Policy title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.owner.trim()) newErrors.owner = 'Policy owner is required';
    if (formData.adherenceRate < 0 || formData.adherenceRate > 100) {
      newErrors.adherenceRate = 'Adherence rate must be between 0 and 100';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const policyData = {
      ...formData,
      framework,
      changeHistory: policy?.changeHistory || []
    };

    if (mode === 'create') {
      onSave(policyData);
    } else if (mode === 'edit' && policy) {
      onUpdate(policy.id, policyData);
    }
  };

  const addViolation = () => {
    const newViolation = {
      id: `viol-${Date.now()}`,
      description: '',
      severity: 'medium' as const,
      reportedDate: new Date(),
      status: 'open' as const
    };
    setFormData(prev => ({
      ...prev,
      violations: [...prev.violations, newViolation]
    }));
  };

  const updateViolation = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      violations: prev.violations.map((violation, i) =>
        i === index ? { ...violation, [field]: value } : violation
      )
    }));
  };

  const removeViolation = (index: number) => {
    setFormData(prev => ({
      ...prev,
      violations: prev.violations.filter((_, i) => i !== index)
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'create' ? 'Create' : mode === 'edit' ? 'Edit' : 'View'} Policy`}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Policy Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter policy title"
            />
            {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Policy Owner *
            </label>
            <input
              type="text"
              value={formData.owner}
              onChange={(e) => setFormData(prev => ({ ...prev, owner: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter policy owner"
            />
            {errors.owner && <p className="text-red-500 text-sm mt-1">{errors.owner}</p>}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            disabled={mode === 'view'}
            rows={3}
            className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            placeholder="Enter policy description"
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Category *
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="data_protection">Data Protection</option>
              <option value="consent_management">Consent Management</option>
              <option value="data_retention">Data Retention</option>
              <option value="breach_response">Breach Response</option>
              <option value="access_control">Access Control</option>
              <option value="security">Security</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Status *
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="draft">Draft</option>
              <option value="under_review">Under Review</option>
              <option value="approved">Approved</option>
              <option value="active">Active</option>
              <option value="archived">Archived</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Version *
            </label>
            <input
              type="text"
              value={formData.version}
              onChange={(e) => setFormData(prev => ({ ...prev, version: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="e.g., 1.0"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Adherence Rate (%)
            </label>
            <input
              type="number"
              min="0"
              max="100"
              step="0.1"
              value={formData.adherenceRate}
              onChange={(e) => setFormData(prev => ({ ...prev, adherenceRate: parseFloat(e.target.value) || 0 }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
            {errors.adherenceRate && <p className="text-red-500 text-sm mt-1">{errors.adherenceRate}</p>}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Effective Date *
            </label>
            <input
              type="date"
              value={formData?.effectiveDate?.toISOString?.()?.split?.('T')?.[0] || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, effectiveDate: new Date(e.target.value) }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Review Date *
            </label>
            <input
              type="date"
              value={formData?.reviewDate?.toISOString?.()?.split?.('T')?.[0] || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, reviewDate: new Date(e.target.value) }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
          </div>
        </div>

        {/* Assigned Departments */}
        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Assigned Departments
          </label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {availableDepartments.map((dept) => (
              <label key={dept.id} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={formData?.assignedDepartments?.includes?.(dept?.id) || false}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setFormData(prev => ({
                        ...prev,
                        assignedDepartments: [...prev.assignedDepartments, dept.id]
                      }));
                    } else {
                      setFormData(prev => ({
                        ...prev,
                        assignedDepartments: prev.assignedDepartments.filter(id => id !== dept.id)
                      }));
                    }
                  }}
                  disabled={mode === 'view'}
                  className="rounded border-border text-primary focus:ring-primary focus:ring-offset-0"
                />
                <span className="text-sm text-text">{dept.name}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Violations Section */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-text">Violations</h3>
            {mode !== 'view' && (
              <button
                type="button"
                onClick={addViolation}
                className="flex items-center gap-2 px-3 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              >
                <Plus className="w-4 h-4" />
                Add Violation
              </button>
            )}
          </div>

          <div className="space-y-4">
            {formData.violations.map((violation, index) => (
              <div key={violation.id} className="bg-card rounded-lg p-4 border border-border">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-medium text-text">Violation {index + 1}</h4>
                  {mode !== 'view' && (
                    <button
                      type="button"
                      onClick={() => removeViolation(index)}
                      className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 rounded"
                    >
                      <Trash2 className="w-4 h-4" />
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="md:col-span-2">
                    <label className="block text-sm font-medium text-text mb-1">Description</label>
                    <textarea
                      value={violation.description}
                      onChange={(e) => updateViolation(index, 'description', e.target.value)}
                      disabled={mode === 'view'}
                      rows={2}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      placeholder="Enter violation description"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Severity</label>
                    <select
                      value={violation.severity}
                      onChange={(e) => updateViolation(index, 'severity', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                      <option value="critical">Critical</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Status</label>
                    <select
                      value={violation.status}
                      onChange={(e) => updateViolation(index, 'status', e.target.value)}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                    >
                      <option value="open">Open</option>
                      <option value="investigating">Investigating</option>
                      <option value="resolved">Resolved</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-1">Reported Date</label>
                    <input
                      type="date"
                      value={violation?.reportedDate?.toISOString?.()?.split?.('T')?.[0] || ''}
                      onChange={(e) => updateViolation(index, 'reportedDate', new Date(e.target.value))}
                      disabled={mode === 'view'}
                      className="w-full px-3 py-2 bg-background border border-border rounded text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                    />
                  </div>

                  {violation.status === 'resolved' && (
                    <div>
                      <label className="block text-sm font-medium text-text mb-1">Resolved Date</label>
                      <input
                        type="date"
                        value={violation?.resolvedDate?.toISOString?.()?.split?.('T')?.[0] || ''}
                        onChange={(e) => updateViolation(index, 'resolvedDate', new Date(e.target.value))}
                        disabled={mode === 'view'}
                        className="w-full px-3 py-2 bg-background border border-border rounded text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
                      />
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {mode !== 'view' && (
          <div className="flex items-center justify-end gap-3 pt-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              {mode === 'create' ? 'Create' : 'Update'} Policy
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

// Timeline Event Modal Component
interface TimelineEventModalProps {
  isOpen: boolean;
  onClose: () => void;
  mode: 'create' | 'edit' | 'view';
  event?: TimelineEvent | null;
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  availableDepartments: Department[];
  availablePolicies: CompliancePolicy[];
  onSave: (data: Omit<TimelineEvent, 'id' | 'createdAt' | 'updatedAt'>) => void;
  onUpdate: (id: string, data: Partial<TimelineEvent>) => void;
}

const TimelineEventModal: React.FC<TimelineEventModalProps> = ({
  isOpen,
  onClose,
  mode,
  event,
  framework,
  availableDepartments,
  availablePolicies,
  onSave,
  onUpdate
}) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'policy_implementation' as 'policy_implementation' | 'audit_activity' | 'data_subject_request' | 'breach_incident' | 'regulatory_deadline' | 'compliance_milestone',
    date: new Date(),
    status: 'pending' as 'completed' | 'in_progress' | 'pending' | 'overdue',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'critical',
    department: '',
    assignedTo: '',
    outcome: '',
    attachments: [] as string[],
    relatedEntities: [] as Array<{
      type: 'department' | 'policy' | 'audit' | 'incident';
      id: string;
      name: string;
    }>
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (event && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: event.title,
        description: event.description,
        type: event.type,
        date: event.date,
        status: event.status,
        priority: event.priority,
        department: event.department,
        assignedTo: event.assignedTo,
        outcome: event.outcome || '',
        attachments: event.attachments,
        relatedEntities: event.relatedEntities
      });
    } else if (mode === 'create') {
      setFormData({
        title: '',
        description: '',
        type: 'policy_implementation',
        date: new Date(),
        status: 'pending',
        priority: 'medium',
        department: '',
        assignedTo: '',
        outcome: '',
        attachments: [],
        relatedEntities: []
      });
    }
    setErrors({});
  }, [event, mode, isOpen]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) newErrors.title = 'Event title is required';
    if (!formData.description.trim()) newErrors.description = 'Description is required';
    if (!formData.department.trim()) newErrors.department = 'Department is required';
    if (!formData.assignedTo.trim()) newErrors.assignedTo = 'Assigned person is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!validateForm()) return;

    const eventData = {
      ...formData,
      framework
    };

    if (mode === 'create') {
      onSave(eventData);
    } else if (mode === 'edit' && event) {
      onUpdate(event.id, eventData);
    }
  };

  const addRelatedEntity = (type: 'department' | 'policy', id: string, name: string) => {
    const newEntity = { type, id, name };
    setFormData(prev => ({
      ...prev,
      relatedEntities: [...prev.relatedEntities.filter(e => !(e.type === type && e.id === id)), newEntity]
    }));
  };

  const removeRelatedEntity = (index: number) => {
    setFormData(prev => ({
      ...prev,
      relatedEntities: prev.relatedEntities.filter((_, i) => i !== index)
    }));
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`${mode === 'create' ? 'Create' : mode === 'edit' ? 'Edit' : 'View'} Timeline Event`}
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Event Title *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter event title"
            />
            {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Event Type *
            </label>
            <select
              value={formData.type}
              onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="policy_implementation">Policy Implementation</option>
              <option value="audit_activity">Audit Activity</option>
              <option value="data_subject_request">Data Subject Request</option>
              <option value="breach_incident">Breach Incident</option>
              <option value="regulatory_deadline">Regulatory Deadline</option>
              <option value="compliance_milestone">Compliance Milestone</option>
            </select>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Description *
          </label>
          <textarea
            value={formData.description}
            onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
            disabled={mode === 'view'}
            rows={3}
            className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            placeholder="Enter event description"
          />
          {errors.description && <p className="text-red-500 text-sm mt-1">{errors.description}</p>}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Date *
            </label>
            <input
              type="date"
              value={formData?.date?.toISOString?.()?.split?.('T')?.[0] || ''}
              onChange={(e) => setFormData(prev => ({ ...prev, date: new Date(e.target.value) }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Status *
            </label>
            <select
              value={formData.status}
              onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Priority *
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Department *
            </label>
            <select
              value={formData.department}
              onChange={(e) => setFormData(prev => ({ ...prev, department: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
            >
              <option value="">Select Department</option>
              {availableDepartments.map((dept) => (
                <option key={dept.id} value={dept.name}>{dept.name}</option>
              ))}
            </select>
            {errors.department && <p className="text-red-500 text-sm mt-1">{errors.department}</p>}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Assigned To *
            </label>
            <input
              type="text"
              value={formData.assignedTo}
              onChange={(e) => setFormData(prev => ({ ...prev, assignedTo: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter assigned person"
            />
            {errors.assignedTo && <p className="text-red-500 text-sm mt-1">{errors.assignedTo}</p>}
          </div>

          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Outcome
            </label>
            <input
              type="text"
              value={formData.outcome}
              onChange={(e) => setFormData(prev => ({ ...prev, outcome: e.target.value }))}
              disabled={mode === 'view'}
              className="w-full px-3 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50"
              placeholder="Enter outcome (if completed)"
            />
          </div>
        </div>

        {/* Related Entities */}
        <div>
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-text">Related Entities</h3>
            {mode !== 'view' && (
              <div className="flex gap-2">
                <select
                  onChange={(e) => {
                    const [type, id] = e.target.value?.split?.(':') || [];
                    if (type && id) {
                      const entity = type === 'department'
                        ? availableDepartments.find(d => d.id === id)
                        : availablePolicies.find(p => p.id === id);
                      if (entity) {
                        addRelatedEntity(type as any, id, entity.name || (entity as any).title);
                      }
                    }
                    e.target.value = '';
                  }}
                  className="px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  <option value="">Add Related Entity</option>
                  <optgroup label="Departments">
                    {availableDepartments.map((dept) => (
                      <option key={dept.id} value={`department:${dept.id}`}>{dept.name}</option>
                    ))}
                  </optgroup>
                  <optgroup label="Policies">
                    {availablePolicies.map((policy) => (
                      <option key={policy.id} value={`policy:${policy.id}`}>{policy.title}</option>
                    ))}
                  </optgroup>
                </select>
              </div>
            )}
          </div>

          <div className="space-y-2">
            {formData.relatedEntities.map((entity, index) => (
              <div key={index} className="flex items-center justify-between bg-card rounded-lg p-3 border border-border">
                <div className="flex items-center gap-3">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    entity.type === 'department' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                    entity.type === 'policy' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                    'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                  }`}>
                    {entity.type}
                  </span>
                  <span className="text-text">{entity.name}</span>
                </div>
                {mode !== 'view' && (
                  <button
                    type="button"
                    onClick={() => removeRelatedEntity(index)}
                    className="p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-900/30 rounded"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>
            ))}
          </div>
        </div>

        {mode !== 'view' && (
          <div className="flex items-center justify-end gap-3 pt-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Save className="w-4 h-4" />
              {mode === 'create' ? 'Create' : 'Update'} Event
            </button>
          </div>
        )}
      </form>
    </Modal>
  );
};

// Comprehensive mock data generators
const generateMockDepartments = (): Department[] => [
  {
    id: 'dept-001',
    name: 'Human Resources',
    description: 'Employee data management and HR operations',
    complianceScore: 94.2,
    riskLevel: 'medium',
    dataSubjectsCount: 1247,
    processingActivities: [
      {
        id: 'pa-001',
        name: 'Employee Recruitment',
        purpose: 'Hiring and onboarding new employees',
        legalBasis: 'Contract performance',
        dataCategories: ['Personal identifiers', 'Employment history', 'Education records'],
        retentionPeriod: '7 years after employment ends',
        status: 'active'
      },
      {
        id: 'pa-002',
        name: 'Payroll Processing',
        purpose: 'Monthly salary and benefits administration',
        legalBasis: 'Legal obligation',
        dataCategories: ['Financial data', 'Tax information', 'Bank details'],
        retentionPeriod: '10 years for tax purposes',
        status: 'active'
      }
    ],
    responsibleOfficer: 'Sarah Johnson, HR Director',
    lastAuditDate: new Date('2024-01-15'),
    nextAuditDate: new Date('2024-07-15'),
    violations: 2,
    trend: 'up',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-10')
  },
  {
    id: 'dept-002',
    name: 'Marketing',
    description: 'Customer acquisition and marketing campaigns',
    complianceScore: 87.6,
    riskLevel: 'high',
    dataSubjectsCount: 45632,
    processingActivities: [
      {
        id: 'pa-003',
        name: 'Email Marketing',
        purpose: 'Promotional communications and newsletters',
        legalBasis: 'Consent',
        dataCategories: ['Email addresses', 'Marketing preferences', 'Behavioral data'],
        retentionPeriod: '2 years or until consent withdrawn',
        status: 'active'
      },
      {
        id: 'pa-004',
        name: 'Customer Analytics',
        purpose: 'Understanding customer behavior and preferences',
        legalBasis: 'Legitimate interest',
        dataCategories: ['Usage data', 'Demographics', 'Purchase history'],
        retentionPeriod: '3 years from last interaction',
        status: 'under_review'
      }
    ],
    responsibleOfficer: 'Michael Chen, Marketing Director',
    lastAuditDate: new Date('2024-02-01'),
    nextAuditDate: new Date('2024-08-01'),
    violations: 5,
    trend: 'down',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-15')
  },
  {
    id: 'dept-003',
    name: 'Finance',
    description: 'Financial operations and accounting',
    complianceScore: 96.8,
    riskLevel: 'low',
    dataSubjectsCount: 892,
    processingActivities: [
      {
        id: 'pa-005',
        name: 'Invoice Processing',
        purpose: 'Customer billing and payment processing',
        legalBasis: 'Contract performance',
        dataCategories: ['Financial data', 'Payment information', 'Transaction records'],
        retentionPeriod: '7 years for accounting purposes',
        status: 'active'
      }
    ],
    responsibleOfficer: 'David Wilson, CFO',
    lastAuditDate: new Date('2024-01-20'),
    nextAuditDate: new Date('2024-07-20'),
    violations: 0,
    trend: 'stable',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-05')
  },
  {
    id: 'dept-004',
    name: 'Information Technology',
    description: 'IT infrastructure and system administration',
    complianceScore: 98.1,
    riskLevel: 'low',
    dataSubjectsCount: 156,
    processingActivities: [
      {
        id: 'pa-006',
        name: 'System Administration',
        purpose: 'Maintaining IT systems and user access',
        legalBasis: 'Legitimate interest',
        dataCategories: ['System logs', 'Access records', 'Technical data'],
        retentionPeriod: '2 years for security purposes',
        status: 'active'
      }
    ],
    responsibleOfficer: 'Lisa Rodriguez, CTO',
    lastAuditDate: new Date('2024-02-05'),
    nextAuditDate: new Date('2024-08-05'),
    violations: 1,
    trend: 'up',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-12')
  },
  {
    id: 'dept-005',
    name: 'Legal',
    description: 'Legal compliance and contract management',
    complianceScore: 99.2,
    riskLevel: 'low',
    dataSubjectsCount: 234,
    processingActivities: [
      {
        id: 'pa-007',
        name: 'Contract Management',
        purpose: 'Legal document and contract administration',
        legalBasis: 'Legal obligation',
        dataCategories: ['Contract data', 'Legal correspondence', 'Compliance records'],
        retentionPeriod: '10 years after contract expiry',
        status: 'active'
      }
    ],
    responsibleOfficer: 'Robert Taylor, General Counsel',
    lastAuditDate: new Date('2024-01-10'),
    nextAuditDate: new Date('2024-07-10'),
    violations: 0,
    trend: 'stable',
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-02-08')
  }
];

const generateMockPolicies = (): CompliancePolicy[] => [
  {
    id: 'policy-001',
    title: 'GDPR Data Protection Policy',
    description: 'Comprehensive data protection policy covering all GDPR requirements',
    category: 'data_protection',
    framework: 'gdpr',
    status: 'active',
    version: '2.1',
    effectiveDate: new Date('2024-01-01'),
    reviewDate: new Date('2024-12-31'),
    owner: 'Data Protection Officer',
    assignedDepartments: ['dept-001', 'dept-002', 'dept-003', 'dept-004', 'dept-005'],
    adherenceRate: 94.2,
    violations: [
      {
        id: 'viol-001',
        description: 'Delayed response to data subject access request',
        severity: 'medium',
        reportedDate: new Date('2024-02-10'),
        status: 'investigating'
      },
      {
        id: 'viol-002',
        description: 'Incomplete data retention documentation',
        severity: 'low',
        reportedDate: new Date('2024-01-25'),
        resolvedDate: new Date('2024-02-05'),
        status: 'resolved'
      }
    ],
    changeHistory: [
      {
        version: '2.1',
        changes: 'Updated consent management procedures',
        changedBy: 'Legal Team',
        changeDate: new Date('2024-01-01')
      },
      {
        version: '2.0',
        changes: 'Major revision for regulatory updates',
        changedBy: 'DPO',
        changeDate: new Date('2023-06-01')
      }
    ],
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 'policy-002',
    title: 'Consent Management Policy',
    description: 'Policy governing collection, management, and withdrawal of user consent',
    category: 'consent_management',
    framework: 'gdpr',
    status: 'active',
    version: '1.3',
    effectiveDate: new Date('2024-02-01'),
    reviewDate: new Date('2024-08-01'),
    owner: 'Privacy Team',
    assignedDepartments: ['dept-002', 'dept-004'],
    adherenceRate: 91.8,
    violations: [
      {
        id: 'viol-003',
        description: 'Consent banner not displaying correctly on mobile',
        severity: 'high',
        reportedDate: new Date('2024-02-12'),
        status: 'open'
      }
    ],
    changeHistory: [
      {
        version: '1.3',
        changes: 'Added mobile consent management procedures',
        changedBy: 'UX Team',
        changeDate: new Date('2024-02-01')
      }
    ],
    createdAt: new Date('2023-03-01'),
    updatedAt: new Date('2024-02-01')
  },
  {
    id: 'policy-003',
    title: 'Data Retention and Deletion Policy',
    description: 'Guidelines for data lifecycle management and secure deletion',
    category: 'data_retention',
    framework: 'gdpr',
    status: 'active',
    version: '1.5',
    effectiveDate: new Date('2023-12-01'),
    reviewDate: new Date('2024-06-01'),
    owner: 'Data Governance Team',
    assignedDepartments: ['dept-001', 'dept-002', 'dept-003', 'dept-004'],
    adherenceRate: 96.1,
    violations: [],
    changeHistory: [
      {
        version: '1.5',
        changes: 'Updated retention periods for marketing data',
        changedBy: 'Legal Counsel',
        changeDate: new Date('2023-12-01')
      }
    ],
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2023-12-01')
  },
  {
    id: 'policy-004',
    title: 'SOX Financial Controls Policy',
    description: 'Internal controls over financial reporting and compliance',
    category: 'data_protection',
    framework: 'sox',
    status: 'active',
    version: '3.0',
    effectiveDate: new Date('2024-01-01'),
    reviewDate: new Date('2024-12-31'),
    owner: 'Chief Financial Officer',
    assignedDepartments: ['dept-003', 'dept-005'],
    adherenceRate: 98.5,
    violations: [],
    changeHistory: [
      {
        version: '3.0',
        changes: 'Annual review and update for 2024',
        changedBy: 'Finance Team',
        changeDate: new Date('2024-01-01')
      }
    ],
    createdAt: new Date('2022-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    id: 'policy-005',
    title: 'ISO 27001 Information Security Policy',
    description: 'Comprehensive information security management policy',
    category: 'security',
    framework: 'iso27001',
    status: 'active',
    version: '2.2',
    effectiveDate: new Date('2024-01-15'),
    reviewDate: new Date('2024-07-15'),
    owner: 'Chief Information Security Officer',
    assignedDepartments: ['dept-004', 'dept-005'],
    adherenceRate: 97.3,
    violations: [
      {
        id: 'viol-004',
        description: 'Incomplete security awareness training records',
        severity: 'medium',
        reportedDate: new Date('2024-02-08'),
        status: 'investigating'
      }
    ],
    changeHistory: [
      {
        version: '2.2',
        changes: 'Updated incident response procedures',
        changedBy: 'Security Team',
        changeDate: new Date('2024-01-15')
      }
    ],
    createdAt: new Date('2023-01-01'),
    updatedAt: new Date('2024-01-15')
  }
];

const generateMockTimelineEvents = (): TimelineEvent[] => [
  {
    id: 'timeline-001',
    title: 'GDPR Data Protection Policy Implementation',
    description: 'Rollout of updated GDPR data protection policy across all departments',
    type: 'policy_implementation',
    framework: 'gdpr',
    date: new Date('2024-01-01'),
    status: 'completed',
    priority: 'high',
    department: 'Legal',
    assignedTo: 'Data Protection Officer',
    relatedEntities: [
      { type: 'policy', id: 'policy-001', name: 'GDPR Data Protection Policy' },
      { type: 'department', id: 'dept-005', name: 'Legal' }
    ],
    outcome: 'Successfully implemented across all departments with 94.2% compliance rate',
    attachments: ['policy-v2.1.pdf', 'implementation-guide.pdf'],
    createdAt: new Date('2023-12-01'),
    updatedAt: new Date('2024-01-15')
  },
  {
    id: 'timeline-002',
    title: 'Q1 2024 GDPR Compliance Audit',
    description: 'Quarterly compliance audit focusing on data processing activities',
    type: 'audit_activity',
    framework: 'gdpr',
    date: new Date('2024-02-15'),
    status: 'in_progress',
    priority: 'medium',
    department: 'Legal',
    assignedTo: 'External Auditor',
    relatedEntities: [
      { type: 'department', id: 'dept-001', name: 'Human Resources' },
      { type: 'department', id: 'dept-002', name: 'Marketing' }
    ],
    attachments: ['audit-checklist.pdf'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-15')
  },
  {
    id: 'timeline-003',
    title: 'Data Subject Access Request - Customer #12847',
    description: 'Processing comprehensive data access request from customer',
    type: 'data_subject_request',
    framework: 'gdpr',
    date: new Date('2024-02-10'),
    status: 'completed',
    priority: 'medium',
    department: 'Legal',
    assignedTo: 'Privacy Team',
    relatedEntities: [
      { type: 'department', id: 'dept-002', name: 'Marketing' },
      { type: 'department', id: 'dept-003', name: 'Finance' }
    ],
    outcome: 'Request fulfilled within 25 days, all requested data provided',
    attachments: ['data-export.zip', 'response-letter.pdf'],
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-02-14')
  },
  {
    id: 'timeline-004',
    title: 'Marketing Database Security Incident',
    description: 'Unauthorized access attempt detected on marketing customer database',
    type: 'breach_incident',
    framework: 'gdpr',
    date: new Date('2024-02-08'),
    status: 'completed',
    priority: 'critical',
    department: 'Marketing',
    assignedTo: 'Security Team',
    relatedEntities: [
      { type: 'department', id: 'dept-002', name: 'Marketing' },
      { type: 'department', id: 'dept-004', name: 'Information Technology' }
    ],
    outcome: 'Incident contained within 2 hours, no data compromised, security measures enhanced',
    attachments: ['incident-report.pdf', 'security-assessment.pdf'],
    createdAt: new Date('2024-02-08'),
    updatedAt: new Date('2024-02-09')
  },
  {
    id: 'timeline-005',
    title: 'SOX Quarterly Financial Controls Review',
    description: 'Quarterly review of internal controls over financial reporting',
    type: 'audit_activity',
    framework: 'sox',
    date: new Date('2024-01-31'),
    status: 'completed',
    priority: 'high',
    department: 'Finance',
    assignedTo: 'Internal Audit Team',
    relatedEntities: [
      { type: 'department', id: 'dept-003', name: 'Finance' },
      { type: 'policy', id: 'policy-004', name: 'SOX Financial Controls Policy' }
    ],
    outcome: 'All controls operating effectively, no material weaknesses identified',
    attachments: ['sox-review-q1-2024.pdf'],
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-02-05')
  },
  {
    id: 'timeline-006',
    title: 'ISO 27001 Annual Certification Audit',
    description: 'Annual surveillance audit for ISO 27001 certification maintenance',
    type: 'audit_activity',
    framework: 'iso27001',
    date: new Date('2024-03-15'),
    status: 'pending',
    priority: 'high',
    department: 'Information Technology',
    assignedTo: 'Certification Body',
    relatedEntities: [
      { type: 'department', id: 'dept-004', name: 'Information Technology' },
      { type: 'policy', id: 'policy-005', name: 'ISO 27001 Information Security Policy' }
    ],
    attachments: ['audit-schedule.pdf'],
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-15')
  },
  {
    id: 'timeline-007',
    title: 'CCPA Consumer Rights Request Processing',
    description: 'Implementation of enhanced CCPA consumer rights request processing system',
    type: 'policy_implementation',
    framework: 'ccpa',
    date: new Date('2024-01-20'),
    status: 'completed',
    priority: 'medium',
    department: 'Legal',
    assignedTo: 'Privacy Team',
    relatedEntities: [
      { type: 'department', id: 'dept-002', name: 'Marketing' },
      { type: 'department', id: 'dept-004', name: 'Information Technology' }
    ],
    outcome: 'System successfully deployed, processing time reduced by 40%',
    attachments: ['ccpa-system-guide.pdf'],
    createdAt: new Date('2024-01-05'),
    updatedAt: new Date('2024-01-25')
  }
];

// Generate comprehensive frameworks data
const generateComplianceFrameworks = (): ComplianceFramework[] => {
  const departments = generateMockDepartments();
  const policies = generateMockPolicies();
  const timeline = generateMockTimelineEvents();

  return [
    {
      id: 'framework-gdpr',
      name: 'General Data Protection Regulation (GDPR)',
      type: 'gdpr',
      overallScore: 94.2,
      departments: departments || [],
      policies: policies?.filter(p => p?.framework === 'gdpr') || [],
      timeline: timeline?.filter(t => t?.framework === 'gdpr') || [],
      metrics: {
        totalDepartments: departments?.length || 0,
        compliantDepartments: departments?.filter(d => (d?.complianceScore || 0) >= 95)?.length || 0,
        totalPolicies: policies?.filter(p => p?.framework === 'gdpr')?.length || 0,
        activePolicies: policies?.filter(p => p?.framework === 'gdpr' && p?.status === 'active')?.length || 0,
        totalViolations: policies?.filter(p => p?.framework === 'gdpr')?.reduce((sum, p) => sum + (p?.violations?.length || 0), 0) || 0,
        resolvedViolations: policies?.filter(p => p?.framework === 'gdpr')?.reduce((sum, p) => sum + (p?.violations?.filter(v => v?.status === 'resolved')?.length || 0), 0) || 0,
        averageComplianceScore: departments?.length > 0 ? departments.reduce((sum, d) => sum + (d?.complianceScore || 0), 0) / departments.length : 0,
        trend: 'up'
      }
    },
    {
      id: 'framework-sox',
      name: 'Sarbanes-Oxley Act (SOX)',
      type: 'sox',
      overallScore: 98.5,
      departments: departments?.filter(d => ['dept-003', 'dept-005'].includes(d?.id)) || [],
      policies: policies?.filter(p => p?.framework === 'sox') || [],
      timeline: timeline?.filter(t => t?.framework === 'sox') || [],
      metrics: {
        totalDepartments: 2,
        compliantDepartments: 2,
        totalPolicies: 1,
        activePolicies: 1,
        totalViolations: 0,
        resolvedViolations: 0,
        averageComplianceScore: 97.6,
        trend: 'stable'
      }
    },
    {
      id: 'framework-iso27001',
      name: 'ISO 27001 Information Security',
      type: 'iso27001',
      overallScore: 97.3,
      departments: departments?.filter(d => ['dept-004', 'dept-005'].includes(d?.id)) || [],
      policies: policies?.filter(p => p?.framework === 'iso27001') || [],
      timeline: timeline?.filter(t => t?.framework === 'iso27001') || [],
      metrics: {
        totalDepartments: 2,
        compliantDepartments: 2,
        totalPolicies: 1,
        activePolicies: 1,
        totalViolations: 1,
        resolvedViolations: 0,
        averageComplianceScore: 97.7,
        trend: 'up'
      }
    },
    {
      id: 'framework-ccpa',
      name: 'California Consumer Privacy Act (CCPA)',
      type: 'ccpa',
      overallScore: 91.4,
      departments: departments?.filter(d => ['dept-002', 'dept-004', 'dept-005'].includes(d?.id)) || [],
      policies: policies?.filter(p => p?.framework === 'ccpa') || [],
      timeline: timeline?.filter(t => t?.framework === 'ccpa') || [],
      metrics: {
        totalDepartments: 3,
        compliantDepartments: 2,
        totalPolicies: 0,
        activePolicies: 0,
        totalViolations: 0,
        resolvedViolations: 0,
        averageComplianceScore: 91.4,
        trend: 'up'
      }
    }
  ];
};

// Mock data generator
const generateEnhancedComplianceData = (): EnhancedComplianceData => {
  const frameworks = generateComplianceFrameworks();
  const metrics: ComplianceMetric[] = [
    {
      id: 'gdpr-001',
      name: 'GDPR Data Processing Compliance',
      category: 'privacy',
      currentValue: 94.2,
      targetValue: 95.0,
      trend: 'up',
      status: 'warning',
      lastUpdated: new Date(Date.now() - 15 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)?.toISOString?.()?.split?.('T')?.[0] || new Date().toISOString().split('T')[0],
        value: 90 + Math.random() * 8,
        target: 95,
        violations: Math.floor(Math.random() * 5)
      })),
      drillDownData: {
        departments: [
          { name: 'Marketing', value: 92.1, status: 'warning' },
          { name: 'Sales', value: 96.8, status: 'compliant' },
          { name: 'HR', value: 93.5, status: 'warning' },
          { name: 'IT', value: 97.2, status: 'compliant' }
        ],
        policies: [
          { name: 'Data Retention Policy', value: 95.2, violations: 2 },
          { name: 'Consent Management', value: 91.8, violations: 5 },
          { name: 'Data Transfer Policy', value: 96.1, violations: 1 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 90 + Math.random() * 8,
          incidents: Math.floor(Math.random() * 3)
        }))
      }
    },
    {
      id: 'sec-001',
      name: 'Security Framework Compliance',
      category: 'security',
      currentValue: 97.8,
      targetValue: 98.0,
      trend: 'stable',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 8 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)?.toISOString?.()?.split?.('T')?.[0] || new Date().toISOString().split('T')[0],
        value: 95 + Math.random() * 4,
        target: 98,
        violations: Math.floor(Math.random() * 3)
      })),
      drillDownData: {
        departments: [
          { name: 'Engineering', value: 98.5, status: 'compliant' },
          { name: 'Operations', value: 97.2, status: 'compliant' },
          { name: 'Support', value: 96.8, status: 'warning' }
        ],
        policies: [
          { name: 'Access Control Policy', value: 98.9, violations: 0 },
          { name: 'Encryption Standards', value: 97.1, violations: 1 },
          { name: 'Incident Response', value: 97.5, violations: 1 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 95 + Math.random() * 4,
          incidents: Math.floor(Math.random() * 2)
        }))
      }
    },
    {
      id: 'ops-001',
      name: 'Operational Compliance',
      category: 'operational',
      currentValue: 89.3,
      targetValue: 92.0,
      trend: 'down',
      status: 'critical',
      lastUpdated: new Date(Date.now() - 3 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)?.toISOString?.()?.split?.('T')?.[0] || new Date().toISOString().split('T')[0],
        value: 85 + Math.random() * 10,
        target: 92,
        violations: Math.floor(Math.random() * 8)
      })),
      drillDownData: {
        departments: [
          { name: 'Finance', value: 91.2, status: 'warning' },
          { name: 'Legal', value: 94.1, status: 'compliant' },
          { name: 'Procurement', value: 85.7, status: 'critical' },
          { name: 'Facilities', value: 87.9, status: 'critical' }
        ],
        policies: [
          { name: 'Document Management', value: 88.5, violations: 8 },
          { name: 'Approval Workflows', value: 90.1, violations: 6 },
          { name: 'Audit Procedures', value: 89.3, violations: 7 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 85 + Math.random() * 10,
          incidents: Math.floor(Math.random() * 4)
        }))
      }
    },
    {
      id: 'reg-001',
      name: 'Regulatory Compliance',
      category: 'regulatory',
      currentValue: 96.1,
      targetValue: 95.0,
      trend: 'up',
      status: 'compliant',
      lastUpdated: new Date(Date.now() - 12 * 60 * 1000),
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000)?.toISOString?.()?.split?.('T')?.[0] || new Date().toISOString().split('T')[0],
        value: 92 + Math.random() * 6,
        target: 95,
        violations: Math.floor(Math.random() * 4)
      })),
      drillDownData: {
        departments: [
          { name: 'Compliance', value: 98.2, status: 'compliant' },
          { name: 'Risk Management', value: 96.8, status: 'compliant' },
          { name: 'Audit', value: 94.3, status: 'warning' }
        ],
        policies: [
          { name: 'SOX Compliance', value: 97.1, violations: 1 },
          { name: 'Financial Reporting', value: 95.8, violations: 2 },
          { name: 'Risk Assessment', value: 95.4, violations: 3 }
        ],
        timeBreakdown: Array.from({ length: 24 }, (_, i) => ({
          period: `${i}:00`,
          value: 92 + Math.random() * 6,
          incidents: Math.floor(Math.random() * 2)
        }))
      }
    }
  ];

  const compliantMetrics = metrics?.filter(m => m?.status === 'compliant')?.length || 0;
  const warningMetrics = metrics?.filter(m => m?.status === 'warning')?.length || 0;
  const criticalMetrics = metrics?.filter(m => m?.status === 'critical')?.length || 0;
  const overallScore = metrics?.length > 0 ? metrics.reduce((sum, m) => sum + (m?.currentValue || 0), 0) / metrics.length : 0;

  return {
    overview: {
      totalMetrics: metrics?.length || 0,
      compliantMetrics,
      warningMetrics,
      criticalMetrics,
      overallScore,
      trendDirection: 'up',
      lastAssessment: new Date(Date.now() - 2 * 60 * 60 * 1000),
      nextAssessment: new Date(Date.now() + 22 * 60 * 60 * 1000)
    },
    metrics,
    frameworks,
    categoryBreakdown: {
      privacy: {
        score: (() => {
          const privacyMetrics = metrics?.filter(m => m?.category === 'privacy') || [];
          return privacyMetrics.length > 0 ? privacyMetrics.reduce((sum, m) => sum + (m?.currentValue || 0), 0) / privacyMetrics.length : 0;
        })(),
        count: metrics?.filter(m => m?.category === 'privacy')?.length || 0,
        trend: 'up'
      },
      security: {
        score: (() => {
          const securityMetrics = metrics?.filter(m => m?.category === 'security') || [];
          return securityMetrics.length > 0 ? securityMetrics.reduce((sum, m) => sum + (m?.currentValue || 0), 0) / securityMetrics.length : 0;
        })(),
        count: metrics?.filter(m => m?.category === 'security')?.length || 0,
        trend: 'stable'
      },
      operational: {
        score: (() => {
          const operationalMetrics = metrics?.filter(m => m?.category === 'operational') || [];
          return operationalMetrics.length > 0 ? operationalMetrics.reduce((sum, m) => sum + (m?.currentValue || 0), 0) / operationalMetrics.length : 0;
        })(),
        count: metrics?.filter(m => m?.category === 'operational')?.length || 0,
        trend: 'down'
      },
      regulatory: {
        score: (() => {
          const regulatoryMetrics = metrics?.filter(m => m?.category === 'regulatory') || [];
          return regulatoryMetrics.length > 0 ? regulatoryMetrics.reduce((sum, m) => sum + (m?.currentValue || 0), 0) / regulatoryMetrics.length : 0;
        })(),
        count: metrics?.filter(m => m?.category === 'regulatory')?.length || 0,
        trend: 'up'
      }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30,
      pendingUpdates: Math.floor(Math.random() * 5)
    }
  };
};

export const EnhancedComplianceMetrics: React.FC<EnhancedComplianceMetricsProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<EnhancedComplianceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'privacy' | 'security' | 'operational' | 'regulatory'>('all');
  const [selectedTimeRange, setSelectedTimeRange] = useState<'24h' | '7d' | '30d' | '90d'>('30d');
  const [expandedMetric, setExpandedMetric] = useState<string | null>(null);
  const [drillDownView, setDrillDownView] = useState<'departments' | 'policies' | 'timeline'>('departments');
  const [animatedCounters, setAnimatedCounters] = useState<Record<string, number>>({});

  // Enhanced state for comprehensive CRUD functionality
  const [selectedFramework, setSelectedFramework] = useState<'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa'>('gdpr');
  const [activeTab, setActiveTab] = useState<'departments' | 'policies' | 'timeline'>('departments');
  const [selectedDepartment, setSelectedDepartment] = useState<Department | null>(null);
  const [selectedPolicy, setSelectedPolicy] = useState<CompliancePolicy | null>(null);
  const [selectedTimelineEvent, setSelectedTimelineEvent] = useState<TimelineEvent | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<'create' | 'edit' | 'view'>('view');
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<string>('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterRiskLevel, setFilterRiskLevel] = useState<string>('all');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    type: 'department' | 'policy' | 'timeline';
    id: string;
    name: string;
  } | null>(null);

  const chartTheme = getChartTheme(mode === 'dark');

  // CRUD Operations for Departments
  const handleCreateDepartment = useCallback(async (departmentData: Omit<Department, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!dashboardData) return;

    setIsLoading(true);
    setError(null);

    try {
      const newDepartment: Department = {
        ...departmentData,
        id: `dept-${Date.now()}`,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      setDashboardData(prev => {
        if (!prev) return prev;
        const updatedFrameworks = prev?.frameworks?.map(framework => {
          if (framework?.type === selectedFramework) {
            return {
              ...framework,
              departments: [...(framework?.departments || []), newDepartment]
            };
          }
          return framework;
        }) || [];
        return { ...prev, frameworks: updatedFrameworks };
      });
      setIsModalOpen(false);
    } catch (err) {
      setError('Failed to create department. Please try again.');
      console.error('Error creating department:', err);
    } finally {
      setIsLoading(false);
    }
  }, [dashboardData, selectedFramework]);

  const handleUpdateDepartment = useCallback((departmentId: string, updates: Partial<Department>) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            departments: framework?.departments?.map(dept =>
              dept?.id === departmentId
                ? { ...dept, ...updates, updatedAt: new Date() }
                : dept
            ) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
    setIsModalOpen(false);
  }, [dashboardData, selectedFramework]);

  const handleDeleteDepartment = useCallback((departmentId: string) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            departments: framework?.departments?.filter(dept => dept?.id !== departmentId) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
  }, [dashboardData, selectedFramework]);

  // CRUD Operations for Policies
  const handleCreatePolicy = useCallback((policyData: Omit<CompliancePolicy, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!dashboardData) return;

    const newPolicy: CompliancePolicy = {
      ...policyData,
      id: `policy-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            policies: [...(framework?.policies || []), newPolicy]
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
    setIsModalOpen(false);
  }, [dashboardData, selectedFramework]);

  const handleUpdatePolicy = useCallback((policyId: string, updates: Partial<CompliancePolicy>) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            policies: framework?.policies?.map(policy =>
              policy?.id === policyId
                ? { ...policy, ...updates, updatedAt: new Date() }
                : policy
            ) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
    setIsModalOpen(false);
  }, [dashboardData, selectedFramework]);

  const handleDeletePolicy = useCallback((policyId: string) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            policies: framework?.policies?.filter(policy => policy?.id !== policyId) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
  }, [dashboardData, selectedFramework]);

  // CRUD Operations for Timeline Events
  const handleCreateTimelineEvent = useCallback((eventData: Omit<TimelineEvent, 'id' | 'createdAt' | 'updatedAt'>) => {
    if (!dashboardData) return;

    const newEvent: TimelineEvent = {
      ...eventData,
      id: `timeline-${Date.now()}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            timeline: [...(framework?.timeline || []), newEvent]
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
    setIsModalOpen(false);
  }, [dashboardData, selectedFramework]);

  const handleUpdateTimelineEvent = useCallback((eventId: string, updates: Partial<TimelineEvent>) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            timeline: framework?.timeline?.map(event =>
              event?.id === eventId
                ? { ...event, ...updates, updatedAt: new Date() }
                : event
            ) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
    setIsModalOpen(false);
  }, [dashboardData, selectedFramework]);

  const handleDeleteTimelineEvent = useCallback((eventId: string) => {
    if (!dashboardData) return;

    setDashboardData(prev => {
      if (!prev) return prev;
      const updatedFrameworks = prev?.frameworks?.map(framework => {
        if (framework?.type === selectedFramework) {
          return {
            ...framework,
            timeline: framework?.timeline?.filter(event => event?.id !== eventId) || []
          };
        }
        return framework;
      }) || [];
      return { ...prev, frameworks: updatedFrameworks };
    });
  }, [dashboardData, selectedFramework]);

  // Confirmation Dialog Handler
  const handleConfirmDelete = useCallback(() => {
    if (!deleteConfirmation) return;

    const { type, id } = deleteConfirmation;

    switch (type) {
      case 'department':
        handleDeleteDepartment(id);
        break;
      case 'policy':
        handleDeletePolicy(id);
        break;
      case 'timeline':
        handleDeleteTimelineEvent(id);
        break;
    }

    setDeleteConfirmation(null);
  }, [deleteConfirmation, handleDeleteDepartment, handleDeletePolicy, handleDeleteTimelineEvent]);

  // Filtering and Sorting Functions
  const getFilteredAndSortedDepartments = useCallback(() => {
    if (!dashboardData) return [];

    const currentFramework = dashboardData?.frameworks?.find(f => f?.type === selectedFramework);
    if (!currentFramework) return [];

    let filtered = currentFramework?.departments?.filter(dept => {
      const matchesSearch = !searchTerm ||
        dept?.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        dept?.description?.toLowerCase()?.includes(searchTerm.toLowerCase());
      const matchesRiskLevel = filterRiskLevel === 'all' || dept?.riskLevel === filterRiskLevel;
      return matchesSearch && matchesRiskLevel;
    }) || [];

    return filtered?.sort?.((a, b) => {
      const aValue = a?.[sortField as keyof Department];
      const bValue = b?.[sortField as keyof Department];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare?.(bValue) || 0
          : bValue.localeCompare?.(aValue) || 0;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    }) || [];
  }, [dashboardData, selectedFramework, searchTerm, filterRiskLevel, sortField, sortDirection]);

  const getFilteredAndSortedPolicies = useCallback(() => {
    if (!dashboardData) return [];

    const currentFramework = dashboardData?.frameworks?.find(f => f?.type === selectedFramework);
    if (!currentFramework) return [];

    let filtered = currentFramework?.policies?.filter(policy => {
      const matchesSearch = !searchTerm ||
        policy?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        policy?.description?.toLowerCase()?.includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || policy?.status === filterStatus;
      return matchesSearch && matchesStatus;
    }) || [];

    return filtered?.sort?.((a, b) => {
      const aValue = a?.[sortField as keyof CompliancePolicy];
      const bValue = b?.[sortField as keyof CompliancePolicy];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare?.(bValue) || 0
          : bValue.localeCompare?.(aValue) || 0;
      }

      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return sortDirection === 'asc' ? aValue - bValue : bValue - aValue;
      }

      return 0;
    }) || [];
  }, [dashboardData, selectedFramework, searchTerm, filterStatus, sortField, sortDirection]);

  const getFilteredAndSortedTimelineEvents = useCallback(() => {
    if (!dashboardData) return [];

    const currentFramework = dashboardData?.frameworks?.find(f => f?.type === selectedFramework);
    if (!currentFramework) return [];

    let filtered = currentFramework?.timeline?.filter(event => {
      const matchesSearch = !searchTerm ||
        event?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        event?.description?.toLowerCase()?.includes(searchTerm.toLowerCase());
      const matchesStatus = filterStatus === 'all' || event?.status === filterStatus;
      return matchesSearch && matchesStatus;
    }) || [];

    return filtered?.sort?.((a, b) => {
      if (sortField === 'date') {
        return sortDirection === 'asc'
          ? (a?.date?.getTime?.() || 0) - (b?.date?.getTime?.() || 0)
          : (b?.date?.getTime?.() || 0) - (a?.date?.getTime?.() || 0);
      }

      const aValue = a?.[sortField as keyof TimelineEvent];
      const bValue = b?.[sortField as keyof TimelineEvent];

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortDirection === 'asc'
          ? aValue.localeCompare?.(bValue) || 0
          : bValue.localeCompare?.(aValue) || 0;
      }

      return 0;
    }) || [];
  }, [dashboardData, selectedFramework, searchTerm, filterStatus, sortField, sortDirection]);

  // Simulate data loading and real-time updates
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      setError(null);
      try {
        await new Promise(resolve => setTimeout(resolve, 1200));
        const data = generateEnhancedComplianceData();
        if (!data) {
          throw new Error('No data generated');
        }
        setDashboardData(data);
        setError(null);
      } catch (err) {
        console.error('Enhanced Compliance Metrics data loading error:', err);
        setError('Failed to load compliance metrics data');
        setDashboardData(null);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Real-time updates simulation
  useEffect(() => {
    if (!dashboardData) return;

    const interval = setInterval(() => {
      setDashboardData(prevData => {
        if (!prevData) return prevData;

        return {
          ...prevData,
          metrics: prevData.metrics.map(metric => ({
            ...metric,
            currentValue: Math.max(80, Math.min(100, metric.currentValue + (Math.random() - 0.5) * 2)),
            lastUpdated: new Date()
          })),
          realTimeUpdates: {
            ...prevData.realTimeUpdates,
            lastSync: new Date(),
            pendingUpdates: Math.floor(Math.random() * 5)
          }
        };
      });
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Animated counters effect
  useEffect(() => {
    if (!dashboardData) return;

    const targets = {
      totalMetrics: dashboardData.overview.totalMetrics,
      compliantMetrics: dashboardData.overview.compliantMetrics,
      overallScore: dashboardData.overview.overallScore,
      warningMetrics: dashboardData.overview.warningMetrics,
      criticalMetrics: dashboardData.overview.criticalMetrics
    };

    const duration = 2000; // 2 seconds
    const steps = 60;
    const stepDuration = duration / steps;

    let currentStep = 0;
    const interval = setInterval(() => {
      currentStep++;
      const progress = currentStep / steps;
      const easeOutQuart = 1 - Math.pow(1 - progress, 4);

      setAnimatedCounters({
        totalMetrics: Math.round(targets.totalMetrics * easeOutQuart),
        compliantMetrics: Math.round(targets.compliantMetrics * easeOutQuart),
        overallScore: targets.overallScore * easeOutQuart,
        warningMetrics: Math.round(targets.warningMetrics * easeOutQuart),
        criticalMetrics: Math.round(targets.criticalMetrics * easeOutQuart)
      });

      if (currentStep >= steps) {
        clearInterval(interval);
        setAnimatedCounters(targets);
      }
    }, stepDuration);

    return () => clearInterval(interval);
  }, [dashboardData]);

  // Filter metrics based on category
  const filteredMetrics = useMemo(() => {
    if (!dashboardData) return [];

    if (selectedCategory === 'all') {
      return dashboardData.metrics;
    }

    return dashboardData?.metrics?.filter(metric => metric?.category === selectedCategory) || [];
  }, [dashboardData, selectedCategory]);

  // Generate overview chart data
  const overviewChartData = useMemo(() => {
    if (!dashboardData || !dashboardData.overview) return null;

    try {
      return {
        labels: ['Compliant', 'Warning', 'Critical'],
        datasets: [{
          data: [
            dashboardData.overview.compliantMetrics || 0,
            dashboardData.overview.warningMetrics || 0,
            dashboardData.overview.criticalMetrics || 0
          ],
          backgroundColor: [
            'rgba(52, 211, 153, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(248, 113, 113, 0.8)'
          ],
          borderColor: [
            'rgb(52, 211, 153)',
            'rgb(251, 191, 36)',
            'rgb(248, 113, 113)'
          ],
          borderWidth: 2,
          hoverOffset: 8
        }]
      };
    } catch (error) {
      console.error('Error generating overview chart data:', error);
      return null;
    }
  }, [dashboardData]);

  // Generate category breakdown chart
  const categoryBreakdownData = useMemo(() => {
    if (!dashboardData) return null;

    const categories = Object.entries(dashboardData.categoryBreakdown);

    return {
      labels: categories.map(([key]) => key ? (key.charAt?.(0)?.toUpperCase?.() || '') + (key.slice?.(1) || '') : 'Unknown'),
      datasets: [{
        label: 'Compliance Score',
        data: categories.map(([, value]) => value?.score || 0),
        backgroundColor: [
          'rgba(139, 92, 246, 0.8)',
          'rgba(59, 130, 246, 0.8)',
          'rgba(16, 185, 129, 0.8)',
          'rgba(245, 158, 11, 0.8)'
        ],
        borderColor: [
          'rgb(139, 92, 246)',
          'rgb(59, 130, 246)',
          'rgb(16, 185, 129)',
          'rgb(245, 158, 11)'
        ],
        borderWidth: 2,
        borderRadius: 8,
        borderSkipped: false,
      }]
    };
  }, [dashboardData]);

  // Generate trend chart data
  const trendChartData = useMemo(() => {
    if (!dashboardData || filteredMetrics.length === 0) return null;

    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date?.toISOString?.()?.split?.('T')?.[0] || new Date().toISOString().split('T')[0];
    });

    return {
      labels: last30Days.map(date => new Date(date)?.toLocaleDateString?.('en-US', { month: 'short', day: 'numeric' }) || 'N/A'),
      datasets: filteredMetrics?.map((metric, index) => ({
        label: metric?.name || 'Unknown',
        data: metric?.historicalData?.map(d => d?.value || 0) || [],
        borderColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)'
        ][index % 5],
        backgroundColor: [
          'rgba(79, 142, 247, 0.1)',
          'rgba(52, 211, 153, 0.1)',
          'rgba(251, 191, 36, 0.1)',
          'rgba(248, 113, 113, 0.1)',
          'rgba(139, 92, 246, 0.1)'
        ][index % 5],
        borderWidth: 3,
        fill: false,
        tension: 0.4,
        pointRadius: 3,
        pointHoverRadius: 6,
        pointBackgroundColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)'
        ][index % 5],
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
      }))
    };
  }, [dashboardData, filteredMetrics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'compliant':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-green-500" />;
      case 'down':
        return <TrendingDown className="w-4 h-4 text-red-500" />;
      default:
        return <Activity className="w-4 h-4 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateEnhancedComplianceData();
      setDashboardData(data);
      setError(null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMetricClick = useCallback((metricId: string) => {
    setExpandedMetric(expandedMetric === metricId ? null : metricId);
  }, [expandedMetric]);

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Compliance Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <BarChart3 className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Enhanced Compliance Metrics</h1>
              <p className="text-text-secondary">
                Interactive compliance monitoring with drill-down analytics and real-time updates
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            {/* Real-time Status Indicator */}
            {dashboardData && (
              <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
                <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-red-500'}`} />
                <span className="text-xs text-text-secondary">
                  {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
                </span>
                <span className="text-xs text-text-secondary">
                  • Last sync: {dashboardData.realTimeUpdates.lastSync.toLocaleTimeString()}
                </span>
              </div>
            )}

            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Export
            </button>
          </div>
        </div>

        {/* Overview Statistics with Animated Counters */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total Metrics</p>
                  <p className="text-2xl font-bold text-text">{animatedCounters.totalMetrics || 0}</p>
                </div>
                <Target className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Compliant</p>
                  <p className="text-2xl font-bold text-green-500">{animatedCounters.compliantMetrics || 0}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Warning</p>
                  <p className="text-2xl font-bold text-amber-500">{animatedCounters.warningMetrics || 0}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-amber-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Critical</p>
                  <p className="text-2xl font-bold text-red-500">{animatedCounters.criticalMetrics || 0}</p>
                </div>
                <XCircle className="w-8 h-8 text-red-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Overall Score</p>
                  <p className="text-2xl font-bold text-text">{(animatedCounters.overallScore || 0).toFixed(1)}%</p>
                </div>
                <BarChart3 className="w-8 h-8 text-blue-500" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Interactive Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Status Overview Chart */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <PieChart className="w-5 h-5" />
            Compliance Status Overview
          </h3>
          {overviewChartData && (
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={overviewChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => {
                          const label = context.label || '';
                          const value = context.parsed;
                          const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
                          const percentage = ((value / total) * 100).toFixed(1);
                          return `${label}: ${value} (${percentage}%)`;
                        }
                      }
                    }
                  },
                  onHover: (event, elements) => {
                    if (event.native?.target) {
                      (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    }
                  },
                  onClick: (event, elements) => {
                    if (elements.length > 0) {
                      const index = elements[0].index;
                      const categories = ['compliant', 'warning', 'critical'];
                      console.log(`Clicked on ${categories[index]} metrics`);
                      // Could implement drill-down here
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Category Breakdown Chart */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Category Performance
          </h3>
          {categoryBreakdownData && (
            <div className="h-64">
              <Bar
                data={categoryBreakdownData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => `Score: ${context.parsed.y.toFixed(1)}%`
                      }
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 0,
                      max: 100
                    }
                  },
                  onHover: (event, elements) => {
                    if (event.native?.target) {
                      (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                    }
                  },
                  onClick: (event, elements) => {
                    if (elements.length > 0) {
                      const index = elements[0].index;
                      const categories = ['privacy', 'security', 'operational', 'regulatory'];
                      setSelectedCategory(categories[index] as any);
                      console.log(`Filtering by ${categories[index]} category`);
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Trend Analysis Chart */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-text flex items-center gap-2">
            <LineChart className="w-5 h-5" />
            Compliance Trends ({selectedTimeRange})
          </h3>

          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Filter className="w-4 h-4 text-text-secondary" />
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="all">All Categories</option>
                <option value="privacy">Privacy</option>
                <option value="security">Security</option>
                <option value="operational">Operational</option>
                <option value="regulatory">Regulatory</option>
              </select>
            </div>

            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4 text-text-secondary" />
              <select
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange(e.target.value as any)}
                className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </div>
          </div>
        </div>

        {trendChartData && (
          <div className="h-80">
            <Line
              data={trendChartData}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                  mode: 'index' as const,
                  intersect: false,
                },
                plugins: {
                  legend: {
                    position: 'bottom',
                    labels: {
                      color: chartTheme.textColor,
                      padding: 20,
                      usePointStyle: true,
                    }
                  },
                  tooltip: {
                    backgroundColor: chartTheme.tooltipBg,
                    titleColor: chartTheme.textColor,
                    bodyColor: chartTheme.textColor,
                    borderColor: chartTheme.borderColor,
                    borderWidth: 1,
                    callbacks: {
                      label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`
                    }
                  }
                },
                scales: {
                  x: {
                    grid: {
                      color: chartTheme.gridColor,
                    },
                    ticks: {
                      color: chartTheme.textSecondary,
                    }
                  },
                  y: {
                    grid: {
                      color: chartTheme.gridColor,
                    },
                    ticks: {
                      color: chartTheme.textSecondary,
                      callback: (value) => `${value}%`
                    },
                    min: 80,
                    max: 100
                  }
                },
                onHover: (event, elements) => {
                  if (event.native?.target) {
                    (event.native.target as HTMLElement).style.cursor = elements.length > 0 ? 'pointer' : 'default';
                  }
                },
                onClick: (event, elements) => {
                  if (elements.length > 0) {
                    const datasetIndex = elements[0].datasetIndex;
                    const metric = filteredMetrics[datasetIndex];
                    if (metric) {
                      handleMetricClick(metric.id);
                    }
                  }
                }
              }}
            />
          </div>
        )}
      </div>

      {/* Detailed Metrics with Drill-down */}
      <div className="bg-surface rounded-lg p-6">
        <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
          <Layers className="w-5 h-5" />
          Detailed Compliance Metrics
        </h3>

        <div className="space-y-4">
          {filteredMetrics.map((metric) => (
            <div key={metric.id} className="bg-card rounded-lg border border-border overflow-hidden">
              {/* Metric Header */}
              <div
                className="p-4 cursor-pointer hover:bg-border/30 transition-colors"
                onClick={() => handleMetricClick(metric.id)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(metric.status)}
                    <div>
                      <h4 className="text-lg font-semibold text-text">{metric.name}</h4>
                      <div className="flex items-center gap-4 text-sm text-text-secondary mt-1">
                        <span className="capitalize">{metric.category}</span>
                        <span>Updated: {metric.lastUpdated.toLocaleTimeString()}</span>
                        {getTrendIcon(metric.trend)}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <p className="text-2xl font-bold text-text">{metric.currentValue.toFixed(1)}%</p>
                      <p className="text-sm text-text-secondary">Target: {metric.targetValue}%</p>
                    </div>

                    <div className="flex items-center gap-2">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(metric.status)}`}>
                        {metric?.status ? (metric.status.charAt?.(0)?.toUpperCase?.() || '') + (metric.status.slice?.(1) || '') : 'Unknown'}
                      </span>

                      {expandedMetric === metric.id ? (
                        <ChevronDown className="w-5 h-5 text-text-secondary" />
                      ) : (
                        <ChevronRight className="w-5 h-5 text-text-secondary" />
                      )}
                    </div>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-sm mb-2">
                    <span className="text-text-secondary">Progress to Target</span>
                    <span className="text-text font-medium">
                      {Math.min((metric.currentValue / metric.targetValue) * 100, 100).toFixed(1)}%
                    </span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-500 ${
                        metric.status === 'compliant' ? 'bg-green-500' :
                        metric.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'
                      }`}
                      style={{
                        width: `${Math.min((metric.currentValue / metric.targetValue) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>
              </div>

              {/* Drill-down Content */}
              {expandedMetric === metric.id && metric.drillDownData && (
                <div className="border-t border-border p-4 bg-background/50">
                  <div className="flex items-center gap-4 mb-4">
                    <button
                      onClick={() => setDrillDownView('departments')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'departments'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Building className="w-4 h-4 inline mr-2" />
                      Departments
                    </button>
                    <button
                      onClick={() => setDrillDownView('policies')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'policies'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Target className="w-4 h-4 inline mr-2" />
                      Policies
                    </button>
                    <button
                      onClick={() => setDrillDownView('timeline')}
                      className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                        drillDownView === 'timeline'
                          ? 'bg-primary text-white'
                          : 'bg-border/50 text-text hover:bg-border'
                      }`}
                    >
                      <Clock className="w-4 h-4 inline mr-2" />
                      Timeline
                    </button>
                  </div>

                  {/* Drill-down Data */}
                  <div className="space-y-3">
                    {drillDownView === 'departments' && metric?.drillDownData?.departments?.map((dept, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                        <div className="flex items-center gap-3">
                          <Building className="w-4 h-4 text-text-secondary" />
                          <span className="text-text font-medium">{dept.name}</span>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(dept.status)}`}>
                            {dept.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-text">{dept.value.toFixed(1)}%</p>
                        </div>
                      </div>
                    ))}

                    {drillDownView === 'policies' && metric?.drillDownData?.policies?.map((policy, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                        <div className="flex items-center gap-3">
                          <Target className="w-4 h-4 text-text-secondary" />
                          <span className="text-text font-medium">{policy.name}</span>
                          {policy.violations > 0 && (
                            <span className="px-2 py-1 bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400 rounded-full text-xs font-medium">
                              {policy.violations} violations
                            </span>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="text-lg font-bold text-text">{policy.value.toFixed(1)}%</p>
                        </div>
                      </div>
                    ))}

                    {drillDownView === 'timeline' && (
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                        {metric?.drillDownData?.timeBreakdown?.slice(0, 12)?.map((period, index) => (
                          <div key={index} className="p-3 bg-card rounded-lg border border-border">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm text-text-secondary">{period.period}</span>
                              <Clock className="w-3 h-3 text-text-secondary" />
                            </div>
                            <p className="text-lg font-bold text-text">{period.value.toFixed(1)}%</p>
                            {period.incidents > 0 && (
                              <p className="text-xs text-red-500">{period.incidents} incidents</p>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>

      {/* Comprehensive CRUD Interface */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-text">
            {selectedFramework ? selectedFramework.toUpperCase() : 'UNKNOWN'} {activeTab ? (activeTab.charAt?.(0)?.toUpperCase?.() || '') + (activeTab.slice?.(1) || '') : 'Unknown'} Management
          </h2>
          <div className="flex items-center gap-3">
            {/* Search */}
            <div className="relative">
              <input
                type="text"
                placeholder={`Search ${activeTab}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 bg-card border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <Filter className="w-4 h-4 text-text-secondary" />
              </div>
            </div>

            {/* Filters */}
            <select
              value={activeTab === 'departments' ? filterRiskLevel : filterStatus}
              onChange={(e) => activeTab === 'departments' ? setFilterRiskLevel(e.target.value) : setFilterStatus(e.target.value)}
              className="px-3 py-2 bg-card border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            >
              <option value="all">All {activeTab === 'departments' ? 'Risk Levels' : 'Statuses'}</option>
              {activeTab === 'departments' ? (
                <>
                  <option value="low">Low Risk</option>
                  <option value="medium">Medium Risk</option>
                  <option value="high">High Risk</option>
                  <option value="critical">Critical Risk</option>
                </>
              ) : (
                <>
                  <option value="active">Active</option>
                  <option value="draft">Draft</option>
                  <option value="under_review">Under Review</option>
                  <option value="approved">Approved</option>
                  <option value="archived">Archived</option>
                </>
              )}
            </select>

            {/* Create Button */}
            <button
              onClick={() => {
                setModalMode('create');
                setIsModalOpen(true);
              }}
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
            >
              <Users className="w-4 h-4" />
              Create {activeTab?.slice?.(0, -1) || 'Item'}
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <AlertCircle className="w-5 h-5 text-red-500" />
              <span className="text-red-700 dark:text-red-300">{error}</span>
              <button
                onClick={() => setError(null)}
                className="ml-auto p-1 text-red-500 hover:bg-red-100 dark:hover:bg-red-800/30 rounded"
              >
                <X className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}

        {/* Loading Indicator */}
        {isLoading && (
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="ml-3 text-text-secondary">Processing...</span>
          </div>
        )}

        {/* Departments Tab */}
        {activeTab === 'departments' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Departments Tab Error"
            fallbackMessage="There was an error loading the departments tab. Please try refreshing the page."
          >
          <div className="space-y-4">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left py-3 px-4 font-medium text-text">Department</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Compliance Score</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Risk Level</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Data Subjects</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Violations</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredAndSortedDepartments().map((department) => (
                    <tr key={department.id} className="border-b border-border hover:bg-card/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-text">{department.name}</div>
                          <div className="text-sm text-text-secondary">{department.description}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-text">{department.complianceScore.toFixed(1)}%</span>
                          <div className={`w-2 h-2 rounded-full ${
                            department.trend === 'up' ? 'bg-green-500' :
                            department.trend === 'down' ? 'bg-red-500' : 'bg-yellow-500'
                          }`}></div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          department.riskLevel === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          department.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                          department.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                        }`}>
                          {department.riskLevel}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-text">{department?.dataSubjectsCount?.toLocaleString() || '0'}</td>
                      <td className="py-3 px-4">
                        <span className={`font-medium ${(department?.violations || 0) > 0 ? 'text-red-500' : 'text-green-500'}`}>
                          {department?.violations || 0}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">

                          <button
                            onClick={() => {
                              setSelectedDepartment(department);
                              setModalMode('edit');
                              setIsModalOpen(true);
                            }}
                            className="p-1 text-text-secondary hover:text-text hover:bg-card rounded"
                          >
                            <Settings className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => setDeleteConfirmation({
                              isOpen: true,
                              type: 'department',
                              id: department.id,
                              name: department.name
                            })}
                            className="p-1 text-text-secondary hover:text-red-500 hover:bg-card rounded"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          </ErrorBoundary>
        )}

        {/* Policies Tab */}
        {activeTab === 'policies' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Policies Tab Error"
            fallbackMessage="There was an error loading the policies tab. Please try refreshing the page."
          >
          <div className="space-y-4">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-border">
                    <th className="text-left py-3 px-4 font-medium text-text">Policy</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Category</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Version</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Adherence Rate</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Violations</th>
                    <th className="text-left py-3 px-4 font-medium text-text">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {getFilteredAndSortedPolicies().map((policy) => (
                    <tr key={policy.id} className="border-b border-border hover:bg-card/50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-text">{policy.title}</div>
                          <div className="text-sm text-text-secondary">{policy.description}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className="px-2 py-1 bg-card rounded text-xs font-medium text-text">
                          {policy?.category?.replace?.('_', ' ') || 'Unknown'}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          policy.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          policy.status === 'approved' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          policy.status === 'under_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          policy.status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {policy?.status?.replace?.('_', ' ') || 'Unknown'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-text">{policy.version}</td>
                      <td className="py-3 px-4 text-text">{policy?.adherenceRate?.toFixed(1) || '0.0'}%</td>
                      <td className="py-3 px-4">
                        <span className={`font-medium ${(policy?.violations?.length || 0) > 0 ? 'text-red-500' : 'text-green-500'}`}>
                          {policy?.violations?.length || 0}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center gap-2">

                          <button
                            onClick={() => {
                              setSelectedPolicy(policy);
                              setModalMode('edit');
                              setIsModalOpen(true);
                            }}
                            className="p-1 text-text-secondary hover:text-text hover:bg-card rounded"
                          >
                            <Settings className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => setDeleteConfirmation({
                              isOpen: true,
                              type: 'policy',
                              id: policy.id,
                              name: policy.title
                            })}
                            className="p-1 text-text-secondary hover:text-red-500 hover:bg-card rounded"
                          >
                            <Trash2 className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
          </ErrorBoundary>
        )}

        {/* Timeline Tab */}
        {activeTab === 'timeline' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Timeline Tab Error"
            fallbackMessage="There was an error loading the timeline tab. Please try refreshing the page."
          >
          <div className="space-y-4">
            {getFilteredAndSortedTimelineEvents().map((event) => (
              <div key={event.id} className="bg-card rounded-lg p-4 border border-border">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`w-3 h-3 rounded-full ${
                        event.status === 'completed' ? 'bg-green-500' :
                        event.status === 'in_progress' ? 'bg-blue-500' :
                        event.status === 'pending' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}></div>
                      <h3 className="font-medium text-text">{event.title}</h3>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                        event.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                        event.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                        event.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                        'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      }`}>
                        {event.priority}
                      </span>
                    </div>
                    <p className="text-text-secondary mb-2">{event.description}</p>
                    <div className="flex items-center gap-4 text-sm text-text-secondary">
                      <span>{event?.date?.toLocaleDateString?.() || 'N/A'}</span>
                      <span>•</span>
                      <span>{event.department}</span>
                      <span>•</span>
                      <span>{event.assignedTo}</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">

                    <button
                      onClick={() => {
                        setSelectedTimelineEvent(event);
                        setModalMode('edit');
                        setIsModalOpen(true);
                      }}
                      className="p-1 text-text-secondary hover:text-text hover:bg-card rounded"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
          </ErrorBoundary>
        )}
      </div>

      {/* Modal Components */}
      {activeTab === 'departments' && (
        <DepartmentModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedDepartment(null);
          }}
          mode={modalMode}
          department={selectedDepartment}
          onSave={handleCreateDepartment}
          onUpdate={handleUpdateDepartment}
        />
      )}

      {activeTab === 'policies' && (
        <PolicyModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedPolicy(null);
          }}
          mode={modalMode}
          policy={selectedPolicy}
          framework={selectedFramework}
          availableDepartments={dashboardData?.frameworks?.find(f => f?.type === selectedFramework)?.departments || []}
          onSave={handleCreatePolicy}
          onUpdate={handleUpdatePolicy}
        />
      )}

      {activeTab === 'timeline' && (
        <TimelineEventModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedTimelineEvent(null);
          }}
          mode={modalMode}
          event={selectedTimelineEvent}
          framework={selectedFramework}
          availableDepartments={dashboardData?.frameworks?.find(f => f?.type === selectedFramework)?.departments || []}
          availablePolicies={dashboardData?.frameworks?.find(f => f?.type === selectedFramework)?.policies || []}
          onSave={handleCreateTimelineEvent}
          onUpdate={handleUpdateTimelineEvent}
        />
      )}

      {/* Delete Confirmation Dialog */}
      {deleteConfirmation && (
        <Modal
          isOpen={deleteConfirmation.isOpen}
          onClose={() => setDeleteConfirmation(null)}
          title="Confirm Delete"
        >
          <div className="space-y-4">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
                <AlertCircle className="w-6 h-6 text-red-500" />
              </div>
              <div>
                <h3 className="text-lg font-medium text-text">Delete {deleteConfirmation.type}</h3>
                <p className="text-text-secondary">This action cannot be undone.</p>
              </div>
            </div>

            <p className="text-text">
              Are you sure you want to delete <strong>{deleteConfirmation.name}</strong>?
            </p>

            <div className="flex items-center justify-end gap-3 pt-4 border-t border-border">
              <button
                onClick={() => setDeleteConfirmation(null)}
                className="px-4 py-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleConfirmDelete}
                className="flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                Delete
              </button>
            </div>
          </div>
        </Modal>
      )}
    </div>
  );
};

export default EnhancedComplianceMetrics;
