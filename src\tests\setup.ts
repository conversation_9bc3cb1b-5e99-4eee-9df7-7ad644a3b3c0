/**
 * Test Setup Configuration
 * Global test environment setup and mocks
 */

import '@testing-library/jest-dom';
import { jest } from '@jest/globals';

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor(callback: ResizeObserverCallback) {}
  disconnect() {}
  observe() {}
  unobserve() {}
};

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Mock scrollTo
Object.defineProperty(window, 'scrollTo', {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
) as jest.Mock;

// Mock console methods to reduce noise in tests
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args: any[]) => {
  // Only show errors that aren't React warnings or test-related
  if (
    !args[0]?.toString().includes('Warning:') &&
    !args[0]?.toString().includes('ReactDOM.render is no longer supported') &&
    !args[0]?.toString().includes('act(...)')
  ) {
    originalConsoleError(...args);
  }
};

console.warn = (...args: any[]) => {
  // Only show warnings that aren't React-related
  if (
    !args[0]?.toString().includes('componentWillReceiveProps') &&
    !args[0]?.toString().includes('componentWillMount')
  ) {
    originalConsoleWarn(...args);
  }
};

// Mock Chart.js
jest.mock('chart.js', () => ({
  Chart: {
    register: jest.fn(),
    defaults: {
      font: {},
      color: '#000',
    },
  },
  CategoryScale: jest.fn(),
  LinearScale: jest.fn(),
  PointElement: jest.fn(),
  LineElement: jest.fn(),
  BarElement: jest.fn(),
  ArcElement: jest.fn(),
  RadialLinearScale: jest.fn(),
  Title: jest.fn(),
  Tooltip: jest.fn(),
  Legend: jest.fn(),
  Filler: jest.fn(),
}));

// Mock react-chartjs-2
jest.mock('react-chartjs-2', () => ({
  Line: ({ data, options, ...props }: any) => (
    <div data-testid="line-chart" data-chart-data={JSON.stringify(data)} {...props} />
  ),
  Bar: ({ data, options, ...props }: any) => (
    <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)} {...props} />
  ),
  Doughnut: ({ data, options, ...props }: any) => (
    <div data-testid="doughnut-chart" data-chart-data={JSON.stringify(data)} {...props} />
  ),
  Radar: ({ data, options, ...props }: any) => (
    <div data-testid="radar-chart" data-chart-data={JSON.stringify(data)} {...props} />
  ),
  Pie: ({ data, options, ...props }: any) => (
    <div data-testid="pie-chart" data-chart-data={JSON.stringify(data)} {...props} />
  ),
}));

// Mock Lucide React icons
jest.mock('lucide-react', () => {
  const MockIcon = ({ className, ...props }: any) => (
    <svg className={className} {...props} data-testid="mock-icon">
      <rect width="24" height="24" />
    </svg>
  );

  return new Proxy({}, {
    get: (target, prop) => {
      if (typeof prop === 'string') {
        return MockIcon;
      }
      return target[prop as keyof typeof target];
    }
  });
});

// Mock CSS modules
jest.mock('*.module.css', () => ({}));
jest.mock('*.module.scss', () => ({}));

// Mock image imports
jest.mock('*.jpg', () => 'test-file-stub');
jest.mock('*.jpeg', () => 'test-file-stub');
jest.mock('*.png', () => 'test-file-stub');
jest.mock('*.gif', () => 'test-file-stub');
jest.mock('*.svg', () => 'test-file-stub');

// Mock environment variables
process.env.NODE_ENV = 'test';
process.env.REACT_APP_API_URL = 'http://localhost:3001';

// Global test utilities
global.testUtils = {
  // Helper to create mock data
  createMockData: (overrides = {}) => ({
    id: '1',
    name: 'Test Item',
    status: 'active',
    createdAt: new Date().toISOString(),
    ...overrides,
  }),

  // Helper to create mock user
  createMockUser: (overrides = {}) => ({
    id: '1',
    name: 'Test User',
    email: '<EMAIL>',
    role: 'admin',
    ...overrides,
  }),

  // Helper to wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // Helper to create mock API response
  createMockApiResponse: (data: any, status = 200) => ({
    ok: status >= 200 && status < 300,
    status,
    json: () => Promise.resolve(data),
    text: () => Promise.resolve(JSON.stringify(data)),
  }),
};

// Performance monitoring for tests
const performanceObserver = {
  observe: jest.fn(),
  disconnect: jest.fn(),
};

Object.defineProperty(global, 'PerformanceObserver', {
  writable: true,
  value: jest.fn(() => performanceObserver),
});

// Mock requestAnimationFrame
global.requestAnimationFrame = jest.fn(cb => setTimeout(cb, 0));
global.cancelAnimationFrame = jest.fn();

// Mock URL.createObjectURL
Object.defineProperty(URL, 'createObjectURL', {
  writable: true,
  value: jest.fn(() => 'mock-url'),
});

Object.defineProperty(URL, 'revokeObjectURL', {
  writable: true,
  value: jest.fn(),
});

// Setup test timeout
jest.setTimeout(10000);

// Global error handler for unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Cleanup after each test
afterEach(() => {
  // Clear all mocks
  jest.clearAllMocks();
  
  // Clear localStorage and sessionStorage
  localStorageMock.clear();
  sessionStorageMock.clear();
  
  // Reset fetch mock
  (global.fetch as jest.Mock).mockClear();
  
  // Clean up any timers
  jest.clearAllTimers();
});

// Global setup for all tests
beforeAll(() => {
  // Suppress specific console warnings during tests
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (
      args[0]?.includes?.('ReactDOM.render is no longer supported') ||
      args[0]?.includes?.('Warning: ReactDOM.render')
    ) {
      return;
    }
    originalWarn(...args);
  };
});

// Global cleanup
afterAll(() => {
  // Restore original console methods
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});
