# 🔑 How to Get Your Cohere API Key

Follow these steps to get your Cohere API key for the GDPR Analysis feature:

## Step 1: Create a Cohere Account

1. Go to [https://cohere.ai/](https://cohere.ai/)
2. Click "Sign Up" or "Get Started"
3. Create an account using your email address
4. Verify your email if required

## Step 2: Access the Dashboard

1. After signing up, log into your Cohere account
2. You'll be taken to the Cohere Dashboard
3. Look for "API Keys" or "Settings" in the navigation

## Step 3: Generate API Key

1. In the dashboard, find the "API Keys" section
2. Click "Create API Key" or "Generate New Key"
3. Give your key a name (e.g., "GDPR Analysis")
4. Copy the generated API key (it looks like: `co-xxxxxxxxxxxxxxxxxxxxxx`)

## Step 4: Add the Key to Your Project

1. Open the `.env` file in your project directory
2. Replace `your_cohere_api_key_here` with your actual API key:
   ```
   COHERE_API_KEY=co-your-actual-api-key-here
   ```
3. Save the file

## Step 5: Restart the API Server

1. Stop the FastAPI server (Ctrl+C in the terminal)
2. Restart it with: `python gdpr_compliance_fastapi.py`

## ⚠️ Important Notes

- **Keep your API key secure** - don't share it publicly
- **Free tier limits** - Cohere offers a free tier with limited requests
- **Rate limits** - Be aware of API rate limits for your tier

## 💰 Cohere Pricing (as of 2024)

- **Free Tier**: Usually includes some free requests per month
- **Pay-as-you-go**: Pricing based on usage
- **Check current pricing**: Visit [cohere.ai/pricing](https://cohere.ai/pricing)

## 🔄 Alternative: Use Environment Variable Directly

If you prefer not to use a .env file, you can set the environment variable directly:

### Windows (Command Prompt):
```cmd
set COHERE_API_KEY=your_cohere_api_key_here
python gdpr_compliance_fastapi.py
```

### Windows (PowerShell):
```powershell
$env:COHERE_API_KEY="your_cohere_api_key_here"
python gdpr_compliance_fastapi.py
```

### Anaconda Prompt:
```bash
set COHERE_API_KEY=your_cohere_api_key_here
python gdpr_compliance_fastapi.py
```

## ✅ Verify Setup

After adding the API key and restarting the server, you should see:
```
INFO:     Started server process [XXXX]
INFO:     Waiting for application startup.
GDPR Compliance system initialized successfully
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
```

Instead of the error message about missing API key.

## 🧪 Test the Setup

Run the test script to verify everything works:
```bash
python test_api.py
```

This should now complete successfully with your Cohere API key configured!
