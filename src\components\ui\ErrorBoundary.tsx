import React, { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw, Home, Bug, ExternalLink, ChevronDown, ChevronUp } from 'lucide-react';

// Error boundary types for different levels of error handling
export type ErrorBoundaryType = 'page' | 'section' | 'component';

// Error information interface
interface ErrorDetails {
  message: string;
  stack?: string;
  componentStack?: string;
  timestamp: Date;
  userAgent: string;
  url: string;
  userId?: string;
  sessionId?: string;
}

// Props interface for ErrorBoundary
interface ErrorBoundaryProps {
  children: ReactNode;
  type?: ErrorBoundaryType;
  fallbackTitle?: string;
  fallbackMessage?: string;
  showRetry?: boolean;
  showReportIssue?: boolean;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onRetry?: () => void;
  className?: string;
  fallback?: ReactNode; // Keep backward compatibility
}

// State interface for ErrorBoundary
interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string | null;
  retryCount: number;
  showDetails: boolean;
  isRetrying: boolean;
}

// Enhanced ErrorBoundary class component
export class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: null,
      retryCount: 0,
      showDetails: false,
      isRetrying: false
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ErrorBoundaryState> {
    // Update state so the next render will show the fallback UI
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log error details
    this.logError(error, errorInfo);

    // Update state with error info
    this.setState({
      errorInfo,
      showDetails: process.env.NODE_ENV === 'development'
    });

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  private logError = (error: Error, errorInfo: ErrorInfo) => {
    const errorDetails: ErrorDetails = {
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      // Add user/session info if available
      userId: this.getUserId(),
      sessionId: this.getSessionId()
    };

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 Error Boundary Caught Error');
      console.error('Error:', error);
      console.error('Error Info:', errorInfo);
      console.error('Error Details:', errorDetails);
      console.groupEnd();
    }

    // Send to error reporting service in production
    if (process.env.NODE_ENV === 'production') {
      this.reportError(errorDetails);
    }
  };

  private getUserId = (): string | undefined => {
    // Implement user ID retrieval logic
    return localStorage.getItem('userId') || undefined;
  };

  private getSessionId = (): string | undefined => {
    // Implement session ID retrieval logic
    return sessionStorage.getItem('sessionId') || undefined;
  };

  private reportError = async (errorDetails: ErrorDetails) => {
    try {
      // Implement error reporting to external service
      // Example: Sentry, LogRocket, or custom error reporting endpoint
      console.log('Reporting error to monitoring service:', errorDetails);
      // await fetch('/api/errors', {
      //   method: 'POST',
      //   headers: {
      //     'Content-Type': 'application/json',
      //   },
      //   body: JSON.stringify(errorDetails),
      // });
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    if (this.state.isRetrying) return;

    this.setState({ isRetrying: true });

    // Call custom retry handler if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }

    // Reset error state after a short delay
    this.retryTimeoutId = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        errorId: null,
        retryCount: this.state.retryCount + 1,
        isRetrying: false
      });
    }, 1000);
  };

  private handleReportIssue = () => {
    const { error, errorInfo, errorId } = this.state;
    const issueData = {
      errorId,
      message: error?.message,
      stack: error?.stack,
      componentStack: errorInfo?.componentStack,
      url: window.location.href,
      timestamp: new Date().toISOString()
    };

    // Open issue reporting (could be email, support ticket, etc.)
    const subject = encodeURIComponent(`Error Report: ${error?.message || 'Unknown Error'}`);
    const body = encodeURIComponent(`
Error ID: ${errorId}
Error Message: ${error?.message}
URL: ${window.location.href}
Timestamp: ${new Date().toISOString()}

Please describe what you were doing when this error occurred:
[Your description here]

Technical Details:
${JSON.stringify(issueData, null, 2)}
    `);

    console.log('Opening error report with details:', issueData);
    // window.open(`mailto:<EMAIL>?subject=${subject}&body=${body}`);
  };

  private toggleDetails = () => {
    this.setState({ showDetails: !this.state.showDetails });
  };

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  render() {
    if (this.state.hasError) {
      // Use custom fallback if provided (backward compatibility)
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return this.renderErrorFallback();
    }

    return this.props.children;
  }

  private renderErrorFallback = () => {
    const { type = 'component', fallbackTitle, fallbackMessage, showRetry = true, showReportIssue = true, className = '' } = this.props;
    const { error, errorInfo, errorId, retryCount, showDetails, isRetrying } = this.state;

    // Different styling based on error boundary type
    const containerClasses = {
      page: 'min-h-screen flex items-center justify-center bg-background p-4',
      section: 'min-h-[400px] flex items-center justify-center bg-surface rounded-lg border border-border p-6',
      component: 'min-h-[200px] flex items-center justify-center bg-card rounded-lg border border-border p-4'
    };

    const iconSizes = {
      page: 'w-16 h-16',
      section: 'w-12 h-12',
      component: 'w-8 h-8'
    };

    const titleSizes = {
      page: 'text-2xl',
      section: 'text-xl',
      component: 'text-lg'
    };

    const defaultTitles = {
      page: 'Application Error',
      section: 'Section Unavailable',
      component: 'Component Error'
    };

    const defaultMessages = {
      page: 'We encountered an unexpected error. Please try refreshing the page or contact support if the problem persists.',
      section: 'This section is temporarily unavailable due to an error. You can continue using other parts of the application.',
      component: 'This component failed to load properly. You can try refreshing or continue with other features.'
    };

    return (
      <div className={`${containerClasses[type]} ${className}`}>
        <div className="text-center max-w-md mx-auto">
          {/* Error Icon */}
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
              <AlertTriangle className={`${iconSizes[type]} text-red-500`} />
            </div>
          </div>

          {/* Error Title */}
          <h2 className={`${titleSizes[type]} font-semibold text-text mb-2`}>
            {fallbackTitle || defaultTitles[type]}
          </h2>

          {/* Error Message */}
          <p className="text-text-secondary mb-6">
            {fallbackMessage || defaultMessages[type]}
          </p>

          {/* Error ID */}
          {errorId && (
            <p className="text-xs text-text-secondary mb-4 font-mono">
              Error ID: {errorId}
            </p>
          )}

          {/* Retry Count */}
          {retryCount > 0 && (
            <p className="text-xs text-text-secondary mb-4">
              Retry attempts: {retryCount}
            </p>
          )}

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 justify-center mb-6">
            {showRetry && (
              <button
                onClick={this.handleRetry}
                disabled={isRetrying}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : 'Try Again'}
              </button>
            )}

            {type === 'page' && (
              <button
                onClick={() => window.location.href = '/'}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-background hover:bg-card border border-border text-text rounded-lg transition-colors"
              >
                <Home className="w-4 h-4" />
                Go Home
              </button>
            )}

            {showReportIssue && (
              <button
                onClick={this.handleReportIssue}
                className="flex items-center justify-center gap-2 px-4 py-2 bg-background hover:bg-card border border-border text-text rounded-lg transition-colors"
              >
                <Bug className="w-4 h-4" />
                Report Issue
              </button>
            )}
          </div>

          {/* Error Details Toggle (Development) */}
          {process.env.NODE_ENV === 'development' && error && (
            <div className="text-left">
              <button
                onClick={this.toggleDetails}
                className="flex items-center gap-2 text-sm text-text-secondary hover:text-text mb-2"
              >
                {showDetails ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
                {showDetails ? 'Hide' : 'Show'} Error Details
              </button>

              {showDetails && (
                <div className="bg-background border border-border rounded-lg p-4 text-xs font-mono text-left overflow-auto max-h-60">
                  <div className="mb-2">
                    <strong>Error:</strong> {error.message}
                  </div>
                  {error.stack && (
                    <div className="mb-2">
                      <strong>Stack Trace:</strong>
                      <pre className="whitespace-pre-wrap text-red-600 dark:text-red-400 mt-1">
                        {error.stack}
                      </pre>
                    </div>
                  )}
                  {errorInfo?.componentStack && (
                    <div>
                      <strong>Component Stack:</strong>
                      <pre className="whitespace-pre-wrap text-blue-600 dark:text-blue-400 mt-1">
                        {errorInfo.componentStack}
                      </pre>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };
}

// Higher-order component for easier usage
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<ErrorBoundaryProps, 'children'>
) => {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

export default ErrorBoundary;
