import '@testing-library/jest-dom';
import { expect, afterEach, beforeAll, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import { server } from '../mocks/server';

// Extend Vitest's expect with jest-dom matchers
expect.extend({});

// Setup MSW
beforeAll(() => {
  server.listen({ onUnhandledRequest: 'error' });
});

afterEach(() => {
  server.resetHandlers();
  cleanup();
});

afterAll(() => {
  server.close();
});

// Mock window object and its properties
Object.defineProperty(global, 'window', {
  value: {
    ...global.window,
    location: {
      reload: vi.fn(),
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
    },
    scrollTo: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
    matchMedia: (query: string) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: () => {},
      removeListener: () => {},
      addEventListener: () => {},
      removeEventListener: () => {},
      dispatchEvent: () => {},
    }),
  },
  writable: true,
});

// Mock window.matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: () => {},
    removeListener: () => {},
    addEventListener: () => {},
    removeEventListener: () => {},
    dispatchEvent: () => {},
  }),
});

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() {}
  observe() {}
  unobserve() {}
  disconnect() {}
};

// Mock Chart.js with all required exports
vi.mock('chart.js', () => ({
  Chart: {
    register: vi.fn(),
  },
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
  BarElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
  ArcElement: vi.fn(),
  Filler: vi.fn(),
  RadialLinearScale: vi.fn(),
  registerables: [],
}));

// Mock chart.js/auto
vi.mock('chart.js/auto', () => ({
  Chart: {
    register: vi.fn(),
  },
  CategoryScale: vi.fn(),
  LinearScale: vi.fn(),
  PointElement: vi.fn(),
  LineElement: vi.fn(),
  BarElement: vi.fn(),
  Title: vi.fn(),
  Tooltip: vi.fn(),
  Legend: vi.fn(),
  ArcElement: vi.fn(),
  Filler: vi.fn(),
  RadialLinearScale: vi.fn(),
  registerables: [],
}));

// Mock react-chartjs-2 with all chart types
vi.mock('react-chartjs-2', () => ({
  Line: ({ data, options }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'line-chart',
      'data-chart-data': JSON.stringify(data),
      style: { height: '400px', width: '100%' }
    });
  },
  Doughnut: ({ data, options }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'doughnut-chart',
      'data-chart-data': JSON.stringify(data),
      style: { height: '400px', width: '100%' }
    });
  },
  Bar: ({ data, options }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'bar-chart',
      'data-chart-data': JSON.stringify(data),
      style: { height: '400px', width: '100%' }
    });
  },
  Radar: ({ data, options }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'radar-chart',
      'data-chart-data': JSON.stringify(data),
      style: { height: '400px', width: '100%' }
    });
  },
  Pie: ({ data, options }: any) => {
    const React = require('react');
    return React.createElement('div', {
      'data-testid': 'pie-chart',
      'data-chart-data': JSON.stringify(data),
      style: { height: '400px', width: '100%' }
    });
  },
}));

// Mock Leaflet
vi.mock('leaflet', () => ({
  map: vi.fn(),
  tileLayer: vi.fn(),
  marker: vi.fn(),
  icon: vi.fn(),
}));

// Mock react-leaflet
vi.mock('react-leaflet', () => ({
  MapContainer: ({ children }: any) => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'map-container' }, children);
  },
  TileLayer: () => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'tile-layer' });
  },
  Marker: () => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'marker' });
  },
  Popup: ({ children }: any) => {
    const React = require('react');
    return React.createElement('div', { 'data-testid': 'popup' }, children);
  },
}));

// Mock electron (if needed)
if (typeof window !== 'undefined') {
  (window as any).electron = {
    ipcRenderer: {
      invoke: vi.fn(),
      on: vi.fn(),
      removeAllListeners: vi.fn(),
    },
  };
}

// Global test utilities
global.vi = vi;
