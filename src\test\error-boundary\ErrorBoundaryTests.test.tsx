import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import { ErrorBoundary } from '../../components/ui/ErrorBoundary';
import { GDPRCommandCenter } from '../../components/GDPR/GDPRCommandCenter';
import { BranchDetectionDashboard } from '../../components/GDPR/BranchDetectionDashboard';
import { ConsentManagementDashboard } from '../../components/GDPR/ConsentManagementDashboard';
import { ImpactAssessmentDashboard } from '../../components/GDPR/ImpactAssessmentDashboard';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Test components that throw errors
const ThrowErrorComponent: React.FC<{ shouldThrow?: boolean; errorType?: string }> = ({ 
  shouldThrow = true, 
  errorType = 'render' 
}) => {
  if (shouldThrow) {
    if (errorType === 'render') {
      throw new Error('Test render error');
    } else if (errorType === 'async') {
      React.useEffect(() => {
        throw new Error('Test async error');
      }, []);
    } else if (errorType === 'promise') {
      React.useEffect(() => {
        Promise.reject(new Error('Test promise rejection'));
      }, []);
    }
  }
  
  return <div>Component rendered successfully</div>;
};

const AsyncErrorComponent: React.FC = () => {
  const [error, setError] = React.useState<Error | null>(null);
  
  React.useEffect(() => {
    const simulateAsyncError = async () => {
      try {
        await new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Async operation failed')), 100);
        });
      } catch (err) {
        setError(err as Error);
      }
    };
    
    simulateAsyncError();
  }, []);
  
  if (error) {
    throw error;
  }
  
  return <div>Async component loading...</div>;
};

const NetworkErrorComponent: React.FC = () => {
  const [data, setData] = React.useState(null);
  const [error, setError] = React.useState<Error | null>(null);
  
  React.useEffect(() => {
    const fetchData = async () => {
      try {
        // Simulate network error
        throw new Error('Network request failed');
      } catch (err) {
        setError(err as Error);
      }
    };
    
    fetchData();
  }, []);
  
  if (error) {
    throw error;
  }
  
  return <div>Loading data...</div>;
};

describe('Error Boundary Tests', () => {
  const user = userEvent.setup();

  describe('Basic Error Boundary Functionality', () => {
    it('catches and displays render errors', () => {
      render(
        <ErrorBoundary>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/test render error/i)).toBeInTheDocument();
    });

    it('displays different error messages for different boundary types', () => {
      const { rerender } = render(
        <ErrorBoundary type="page" fallbackTitle="Page Error">
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Page Error')).toBeInTheDocument();
      
      rerender(
        <ErrorBoundary type="section" fallbackTitle="Section Error">
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Section Error')).toBeInTheDocument();
      
      rerender(
        <ErrorBoundary type="component" fallbackTitle="Component Error">
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });

    it('shows error details when requested', async () => {
      render(
        <ErrorBoundary>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      const showDetailsButton = screen.getByRole('button', { name: /show details/i });
      await user.click(showDetailsButton);
      
      expect(screen.getByText(/error details/i)).toBeInTheDocument();
      expect(screen.getByText(/test render error/i)).toBeInTheDocument();
    });

    it('hides error details when requested', async () => {
      render(
        <ErrorBoundary>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      const showDetailsButton = screen.getByRole('button', { name: /show details/i });
      await user.click(showDetailsButton);
      
      const hideDetailsButton = screen.getByRole('button', { name: /hide details/i });
      await user.click(hideDetailsButton);
      
      expect(screen.queryByText(/error details/i)).not.toBeInTheDocument();
    });
  });

  describe('Retry Mechanism', () => {
    it('provides retry functionality', async () => {
      let shouldThrow = true;
      
      const RetryTestComponent: React.FC = () => {
        if (shouldThrow) {
          throw new Error('Retry test error');
        }
        return <div>Component recovered</div>;
      };
      
      render(
        <ErrorBoundary>
          <RetryTestComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Simulate fixing the error
      shouldThrow = false;
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      expect(screen.getByText('Component recovered')).toBeInTheDocument();
    });

    it('limits retry attempts', async () => {
      let retryCount = 0;
      
      const FailingComponent: React.FC = () => {
        retryCount++;
        throw new Error(`Attempt ${retryCount} failed`);
      };
      
      render(
        <ErrorBoundary maxRetries={2}>
          <FailingComponent />
        </ErrorBoundary>
      );
      
      // First retry
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      expect(screen.getByText(/attempt 2 failed/i)).toBeInTheDocument();
      
      // Second retry
      await user.click(retryButton);
      
      expect(screen.getByText(/attempt 3 failed/i)).toBeInTheDocument();
      
      // Third retry should be disabled
      expect(retryButton).toBeDisabled();
      expect(screen.getByText(/maximum retry attempts reached/i)).toBeInTheDocument();
    });

    it('shows loading state during retry', async () => {
      let shouldThrow = true;
      
      const SlowRecoveryComponent: React.FC = () => {
        const [loading, setLoading] = React.useState(false);
        
        React.useEffect(() => {
          if (!shouldThrow) {
            setLoading(true);
            setTimeout(() => setLoading(false), 100);
          }
        }, []);
        
        if (shouldThrow) {
          throw new Error('Component error');
        }
        
        if (loading) {
          return <div>Recovering...</div>;
        }
        
        return <div>Component recovered</div>;
      };
      
      render(
        <ErrorBoundary>
          <SlowRecoveryComponent />
        </ErrorBoundary>
      );
      
      shouldThrow = false;
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      // Should show loading state briefly
      expect(screen.getByText(/retrying/i)).toBeInTheDocument();
    });
  });

  describe('Async Error Handling', () => {
    it('catches async errors in useEffect', async () => {
      render(
        <ErrorBoundary>
          <AsyncErrorComponent />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
      
      expect(screen.getByText(/async operation failed/i)).toBeInTheDocument();
    });

    it('handles network request failures', async () => {
      render(
        <ErrorBoundary>
          <NetworkErrorComponent />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
      
      expect(screen.getByText(/network request failed/i)).toBeInTheDocument();
    });

    it('provides retry mechanism for async errors', async () => {
      let shouldFail = true;
      
      const AsyncRetryComponent: React.FC = () => {
        const [data, setData] = React.useState<string | null>(null);
        const [error, setError] = React.useState<Error | null>(null);
        
        React.useEffect(() => {
          const fetchData = async () => {
            try {
              if (shouldFail) {
                throw new Error('Async fetch failed');
              }
              setData('Data loaded successfully');
            } catch (err) {
              setError(err as Error);
            }
          };
          
          fetchData();
        }, []);
        
        if (error) {
          throw error;
        }
        
        return <div>{data || 'Loading...'}</div>;
      };
      
      render(
        <ErrorBoundary>
          <AsyncRetryComponent />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
      
      // Fix the async operation
      shouldFail = false;
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      await waitFor(() => {
        expect(screen.getByText('Data loaded successfully')).toBeInTheDocument();
      });
    });
  });

  describe('Error Reporting', () => {
    it('calls error reporting callback', () => {
      const onError = vi.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.any(String)
        })
      );
    });

    it('generates unique error IDs', () => {
      const onError = vi.fn();
      
      const { rerender } = render(
        <ErrorBoundary onError={onError}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      const firstErrorId = screen.getByText(/error id:/i).textContent;
      
      rerender(
        <ErrorBoundary onError={onError}>
          <ThrowErrorComponent errorType="async" />
        </ErrorBoundary>
      );
      
      const secondErrorId = screen.getByText(/error id:/i).textContent;
      
      expect(firstErrorId).not.toBe(secondErrorId);
    });

    it('provides report issue functionality', async () => {
      const onReportIssue = vi.fn();
      
      render(
        <ErrorBoundary onReportIssue={onReportIssue}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      const reportButton = screen.getByRole('button', { name: /report issue/i });
      await user.click(reportButton);
      
      expect(onReportIssue).toHaveBeenCalledWith(
        expect.objectContaining({
          error: expect.any(Error),
          errorInfo: expect.any(Object),
          errorId: expect.any(String)
        })
      );
    });
  });

  describe('Dashboard Component Error Boundaries', () => {
    it('isolates errors in GDPR Command Center sections', async () => {
      // Mock service to throw error
      vi.doMock('../../services/gdprService', () => ({
        GDPRService: {
          getDashboardMetrics: vi.fn().mockRejectedValue(new Error('Service error')),
          getAlerts: vi.fn().mockResolvedValue([]),
          getActivities: vi.fn().mockResolvedValue([])
        }
      }));
      
      render(<GDPRCommandCenter />);
      
      // Main dashboard should still render even if one section fails
      expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
    });

    it('handles Branch Detection Dashboard errors gracefully', async () => {
      vi.doMock('../../services/branchDetectionService', () => ({
        BranchDetectionService: {
          getAllBranches: vi.fn().mockRejectedValue(new Error('Branch service error')),
          getMetrics: vi.fn().mockRejectedValue(new Error('Metrics error'))
        }
      }));
      
      render(
        <ErrorBoundary type="section">
          <BranchDetectionDashboard />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });

    it('handles Consent Management Dashboard errors gracefully', async () => {
      vi.doMock('../../services/consentManagementService', () => ({
        ConsentManagementService: {
          getAllConsentRecords: vi.fn().mockRejectedValue(new Error('Consent service error')),
          getMetrics: vi.fn().mockRejectedValue(new Error('Metrics error'))
        }
      }));
      
      render(
        <ErrorBoundary type="section">
          <ConsentManagementDashboard />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });

    it('handles Impact Assessment Dashboard errors gracefully', async () => {
      vi.doMock('../../services/impactAssessmentService', () => ({
        ImpactAssessmentService: {
          getAllAssessments: vi.fn().mockRejectedValue(new Error('Assessment service error')),
          getMetrics: vi.fn().mockRejectedValue(new Error('Metrics error'))
        }
      }));
      
      render(
        <ErrorBoundary type="section">
          <ImpactAssessmentDashboard />
        </ErrorBoundary>
      );
      
      await waitFor(() => {
        expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      });
    });
  });

  describe('Fallback UI Customization', () => {
    it('renders custom fallback component', () => {
      const CustomFallback: React.FC<{ error: Error }> = ({ error }) => (
        <div>
          <h2>Custom Error Fallback</h2>
          <p>Error: {error.message}</p>
        </div>
      );
      
      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Custom Error Fallback')).toBeInTheDocument();
      expect(screen.getByText('Error: Test render error')).toBeInTheDocument();
    });

    it('uses custom fallback title and message', () => {
      render(
        <ErrorBoundary 
          fallbackTitle="Custom Error Title"
          fallbackMessage="This is a custom error message"
        >
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Custom Error Title')).toBeInTheDocument();
      expect(screen.getByText('This is a custom error message')).toBeInTheDocument();
    });

    it('hides retry button when disabled', () => {
      render(
        <ErrorBoundary showRetry={false}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.queryByRole('button', { name: /try again/i })).not.toBeInTheDocument();
    });

    it('hides report issue button when disabled', () => {
      render(
        <ErrorBoundary showReportIssue={false}>
          <ThrowErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.queryByRole('button', { name: /report issue/i })).not.toBeInTheDocument();
    });
  });

  describe('Error Boundary Recovery', () => {
    it('recovers when error condition is resolved', async () => {
      let hasError = true;
      
      const ConditionalErrorComponent: React.FC = () => {
        if (hasError) {
          throw new Error('Conditional error');
        }
        return <div>Component is working</div>;
      };
      
      const { rerender } = render(
        <ErrorBoundary>
          <ConditionalErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Resolve error condition
      hasError = false;
      
      rerender(
        <ErrorBoundary>
          <ConditionalErrorComponent />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Component is working')).toBeInTheDocument();
    });

    it('resets error state on prop changes', async () => {
      const ErrorComponent: React.FC<{ id: string }> = ({ id }) => {
        if (id === 'error') {
          throw new Error('Component error');
        }
        return <div>Component {id}</div>;
      };
      
      const { rerender } = render(
        <ErrorBoundary>
          <ErrorComponent id="error" />
        </ErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      rerender(
        <ErrorBoundary>
          <ErrorComponent id="success" />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Component success')).toBeInTheDocument();
    });
  });
});
