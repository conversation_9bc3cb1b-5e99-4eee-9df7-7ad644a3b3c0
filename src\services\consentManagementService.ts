import { ConsentRecord, Consent<PERSON>ategory, ConsentWithdrawal, ConsentPreference, ConsentMetrics } from '../types/compliance';

export class ConsentManagementService {
  private static consentRecords: ConsentRecord[] = [];
  private static consentCategories: ConsentCategory[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.consentCategories = this.generateConsentCategories();
    this.consentRecords = this.generateMockConsentRecords();
    this.initialized = true;
  }

  // CRUD Operations for Consent Records
  static async getAllConsentRecords(): Promise<ConsentRecord[]> {
    this.initialize();
    return [...this.consentRecords];
  }

  static async getConsentRecordById(id: string): Promise<ConsentRecord | null> {
    this.initialize();
    return this.consentRecords.find(record => record.id === id) || null;
  }

  static async getConsentRecordByEmail(email: string): Promise<ConsentRecord | null> {
    this.initialize();
    return this.consentRecords.find(record => record.email === email) || null;
  }

  static async createConsentRecord(recordData: Partial<ConsentRecord>): Promise<ConsentRecord> {
    this.initialize();
    const newRecord: ConsentRecord = {
      id: `consent-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      dataSubjectId: recordData.dataSubjectId || `ds-${Date.now()}`,
      email: recordData.email || '',
      firstName: recordData.firstName || '',
      lastName: recordData.lastName || '',
      consentCategories: recordData.consentCategories || [],
      consentTimestamp: new Date(),
      consentMethod: recordData.consentMethod || 'website',
      consentVersion: recordData.consentVersion || '1.0',
      ipAddress: recordData.ipAddress,
      userAgent: recordData.userAgent,
      withdrawalHistory: [],
      preferences: [],
      status: 'active',
      lastUpdated: new Date(),
      source: recordData.source || 'website',
      auditTrail: [{
        id: `audit-${Date.now()}`,
        timestamp: new Date(),
        userId: 'system',
        userName: 'System',
        action: 'Consent Record Created',
        details: `Consent record created for ${recordData.email}`,
        newValue: 'active'
      }]
    };

    this.consentRecords.push(newRecord);
    return newRecord;
  }

  static async updateConsentRecord(id: string, updates: Partial<ConsentRecord>): Promise<ConsentRecord | null> {
    this.initialize();
    const index = this.consentRecords.findIndex(record => record.id === id);
    if (index === -1) return null;

    const oldRecord = { ...this.consentRecords[index] };
    this.consentRecords[index] = { 
      ...this.consentRecords[index], 
      ...updates,
      lastUpdated: new Date()
    };

    // Add audit entry
    this.consentRecords[index].auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'user-001',
      userName: 'Current User',
      action: 'Consent Record Updated',
      details: 'Consent record updated',
      previousValue: oldRecord,
      newValue: updates
    });

    return this.consentRecords[index];
  }

  // Consent Management Operations
  static async grantConsent(recordId: string, categoryId: string, subcategoryId?: string): Promise<ConsentRecord | null> {
    const record = await this.getConsentRecordById(recordId);
    if (!record) return null;

    const category = record.consentCategories.find(cat => cat.id === categoryId);
    if (!category) return null;

    if (subcategoryId && category.subcategories) {
      const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
      if (subcategory) {
        subcategory.consentGiven = true;
        subcategory.consentTimestamp = new Date();
        subcategory.withdrawalTimestamp = undefined;
      }
    } else {
      category.consentGiven = true;
      category.consentTimestamp = new Date();
      category.withdrawalTimestamp = undefined;
    }

    return await this.updateConsentRecord(recordId, record);
  }

  static async withdrawConsent(recordId: string, categoryId: string, reason?: string, subcategoryId?: string): Promise<ConsentRecord | null> {
    const record = await this.getConsentRecordById(recordId);
    if (!record) return null;

    const category = record.consentCategories.find(cat => cat.id === categoryId);
    if (!category) return null;

    const withdrawal: ConsentWithdrawal = {
      id: `withdrawal-${Date.now()}`,
      categoryId,
      withdrawalTimestamp: new Date(),
      withdrawalMethod: 'website',
      reason,
      processedBy: 'Current User',
      effectiveDate: new Date(),
      dataRetentionAction: 'delete'
    };

    if (subcategoryId && category.subcategories) {
      const subcategory = category.subcategories.find(sub => sub.id === subcategoryId);
      if (subcategory) {
        subcategory.consentGiven = false;
        subcategory.withdrawalTimestamp = new Date();
      }
    } else {
      category.consentGiven = false;
      category.withdrawalTimestamp = new Date();
    }

    record.withdrawalHistory.push(withdrawal);

    return await this.updateConsentRecord(recordId, record);
  }

  static async updatePreferences(recordId: string, preferences: ConsentPreference[]): Promise<ConsentRecord | null> {
    const record = await this.getConsentRecordById(recordId);
    if (!record) return null;

    record.preferences = preferences.map(pref => ({
      ...pref,
      lastUpdated: new Date()
    }));

    return await this.updateConsentRecord(recordId, record);
  }

  // Analytics and Metrics
  static async getConsentMetrics(): Promise<ConsentMetrics> {
    this.initialize();
    
    const totalUsers = this.consentRecords.length;
    const consentedUsers = this.consentRecords.filter(record => 
      record.status === 'active' && 
      record.consentCategories.some(cat => cat.consentGiven)
    ).length;
    
    const consentRate = totalUsers > 0 ? (consentedUsers / totalUsers) * 100 : 0;

    // Calculate category breakdown
    const categoryBreakdown = this.consentCategories.map(category => {
      const categoryConsents = this.consentRecords.filter(record =>
        record.consentCategories.find(cat => cat.id === category.id && cat.consentGiven)
      ).length;

      return {
        categoryId: category.id,
        categoryName: category.name,
        consentRate: totalUsers > 0 ? (categoryConsents / totalUsers) * 100 : 0,
        totalUsers,
        consentedUsers: categoryConsents
      };
    });

    return {
      totalUsers,
      consentedUsers,
      consentRate,
      trend: 'up', // This would be calculated based on historical data
      weeklyChange: 2.3, // Mock data
      monthlyChange: 5.7, // Mock data
      lastUpdated: new Date(),
      categoryBreakdown
    };
  }

  static async getConsentTrends(days: number = 30): Promise<{
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }[];
  }> {
    // Mock trend data - in real implementation, this would query historical data
    const labels = Array.from({ length: days }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (days - 1 - i));
      return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
    });

    const datasets = [
      {
        label: 'Total Consents',
        data: Array.from({ length: days }, () => Math.floor(Math.random() * 100) + 200),
        borderColor: '#3B82F6',
        backgroundColor: 'rgba(59, 130, 246, 0.1)'
      },
      {
        label: 'Withdrawals',
        data: Array.from({ length: days }, () => Math.floor(Math.random() * 20) + 5),
        borderColor: '#EF4444',
        backgroundColor: 'rgba(239, 68, 68, 0.1)'
      }
    ];

    return { labels, datasets };
  }

  // Filtering and Search
  static async getFilteredConsentRecords(filters: {
    status?: string[];
    consentMethod?: string[];
    dateRange?: { start: Date; end: Date };
    searchTerm?: string;
    hasWithdrawals?: boolean;
  }): Promise<ConsentRecord[]> {
    this.initialize();
    let filtered = [...this.consentRecords];

    if (filters.status?.length) {
      filtered = filtered.filter(record => filters.status!.includes(record.status));
    }

    if (filters.consentMethod?.length) {
      filtered = filtered.filter(record => filters.consentMethod!.includes(record.consentMethod));
    }

    if (filters.dateRange) {
      filtered = filtered.filter(record => 
        record.consentTimestamp >= filters.dateRange!.start && 
        record.consentTimestamp <= filters.dateRange!.end
      );
    }

    if (filters.searchTerm) {
      const term = filters.searchTerm.toLowerCase();
      filtered = filtered.filter(record => 
        record.email.toLowerCase().includes(term) ||
        record.firstName.toLowerCase().includes(term) ||
        record.lastName.toLowerCase().includes(term)
      );
    }

    if (filters.hasWithdrawals !== undefined) {
      filtered = filtered.filter(record => 
        filters.hasWithdrawals ? record.withdrawalHistory.length > 0 : record.withdrawalHistory.length === 0
      );
    }

    return filtered;
  }

  // Consent Categories Management
  static async getAllConsentCategories(): Promise<ConsentCategory[]> {
    this.initialize();
    return [...this.consentCategories];
  }

  static async createConsentCategory(categoryData: Partial<ConsentCategory>): Promise<ConsentCategory> {
    this.initialize();
    const newCategory: ConsentCategory = {
      id: `category-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: categoryData.name || '',
      description: categoryData.description || '',
      purpose: categoryData.purpose || '',
      legalBasis: categoryData.legalBasis || '',
      required: categoryData.required || false,
      granular: categoryData.granular || false,
      subcategories: categoryData.subcategories || [],
      dataTypes: categoryData.dataTypes || [],
      processingActivities: categoryData.processingActivities || [],
      thirdParties: categoryData.thirdParties || [],
      retentionPeriod: categoryData.retentionPeriod || { duration: 12, unit: 'months' },
      consentGiven: false,
      renewalRequired: categoryData.renewalRequired || false
    };

    this.consentCategories.push(newCategory);
    return newCategory;
  }

  // Private helper methods
  private static generateConsentCategories(): ConsentCategory[] {
    return [
      {
        id: 'cat-marketing',
        name: 'Marketing Communications',
        description: 'Email marketing, newsletters, and promotional content',
        purpose: 'Direct marketing and customer engagement',
        legalBasis: 'Article 6(1)(a) - Consent',
        required: false,
        granular: true,
        subcategories: [
          {
            id: 'sub-email-marketing',
            name: 'Email Marketing',
            description: 'Promotional emails and newsletters',
            consentGiven: false
          },
          {
            id: 'sub-sms-marketing',
            name: 'SMS Marketing',
            description: 'Text message promotions',
            consentGiven: false
          },
          {
            id: 'sub-phone-marketing',
            name: 'Phone Marketing',
            description: 'Promotional phone calls',
            consentGiven: false
          }
        ],
        dataTypes: ['Email Address', 'Phone Number', 'Name', 'Preferences'],
        processingActivities: ['Email Campaigns', 'SMS Campaigns', 'Telemarketing'],
        thirdParties: ['Email Service Provider', 'SMS Gateway', 'Marketing Platform'],
        retentionPeriod: { duration: 24, unit: 'months' },
        consentGiven: false,
        renewalRequired: true,
        renewalDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      },
      {
        id: 'cat-analytics',
        name: 'Analytics & Performance',
        description: 'Website analytics, performance tracking, and user behavior analysis',
        purpose: 'Website optimization and user experience improvement',
        legalBasis: 'Article 6(1)(f) - Legitimate Interest',
        required: false,
        granular: true,
        subcategories: [
          {
            id: 'sub-web-analytics',
            name: 'Web Analytics',
            description: 'Google Analytics and similar tools',
            consentGiven: false
          },
          {
            id: 'sub-performance',
            name: 'Performance Monitoring',
            description: 'Website performance and error tracking',
            consentGiven: false
          }
        ],
        dataTypes: ['IP Address', 'Browser Information', 'Page Views', 'Session Data'],
        processingActivities: ['Analytics Processing', 'Performance Monitoring', 'User Behavior Analysis'],
        thirdParties: ['Google Analytics', 'Performance Monitoring Service'],
        retentionPeriod: { duration: 26, unit: 'months' },
        consentGiven: false,
        renewalRequired: false
      },
      {
        id: 'cat-essential',
        name: 'Essential Functions',
        description: 'Core platform functionality and security features',
        purpose: 'Platform operation and security',
        legalBasis: 'Article 6(1)(b) - Contract',
        required: true,
        granular: false,
        dataTypes: ['Account Information', 'Authentication Data', 'Security Logs'],
        processingActivities: ['Authentication', 'Security Monitoring', 'Platform Operation'],
        thirdParties: [],
        retentionPeriod: { duration: 7, unit: 'years' },
        consentGiven: true,
        consentTimestamp: new Date(),
        renewalRequired: false
      },
      {
        id: 'cat-third-party',
        name: 'Third-party Integrations',
        description: 'Social media plugins, external analytics, and partner services',
        purpose: 'Enhanced functionality through third-party services',
        legalBasis: 'Article 6(1)(a) - Consent',
        required: false,
        granular: true,
        subcategories: [
          {
            id: 'sub-social-media',
            name: 'Social Media Plugins',
            description: 'Facebook, Twitter, LinkedIn integration',
            consentGiven: false
          },
          {
            id: 'sub-chat-support',
            name: 'Chat Support',
            description: 'Live chat and customer support tools',
            consentGiven: false
          }
        ],
        dataTypes: ['Social Media Data', 'Chat Messages', 'Support Interactions'],
        processingActivities: ['Social Media Integration', 'Customer Support', 'Live Chat'],
        thirdParties: ['Facebook', 'Twitter', 'LinkedIn', 'Chat Service Provider'],
        retentionPeriod: { duration: 12, unit: 'months' },
        consentGiven: false,
        renewalRequired: true,
        renewalDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
      }
    ];
  }

  private static generateMockConsentRecords(): ConsentRecord[] {
    const mockRecords: ConsentRecord[] = [];
    const emails = [
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>',
      '<EMAIL>'
    ];

    emails.forEach((email, index) => {
      const firstName = email.split('.')[0];
      const lastName = email.split('.')[1].split('@')[0];
      
      const record: ConsentRecord = {
        id: `consent-${index + 1}`,
        dataSubjectId: `ds-${index + 1}`,
        email,
        firstName: firstName.charAt(0).toUpperCase() + firstName.slice(1),
        lastName: lastName.charAt(0).toUpperCase() + lastName.slice(1),
        consentCategories: this.consentCategories.map(cat => ({
          ...cat,
          consentGiven: Math.random() > 0.3, // 70% consent rate
          consentTimestamp: Math.random() > 0.3 ? new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000) : undefined,
          subcategories: cat.subcategories?.map(sub => ({
            ...sub,
            consentGiven: Math.random() > 0.4, // 60% consent rate for subcategories
            consentTimestamp: Math.random() > 0.4 ? new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000) : undefined
          }))
        })),
        consentTimestamp: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000),
        consentMethod: ['website', 'email', 'phone', 'paper'][Math.floor(Math.random() * 4)] as any,
        consentVersion: '1.0',
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        withdrawalHistory: [],
        preferences: [],
        status: 'active',
        lastUpdated: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        source: 'website',
        auditTrail: [{
          id: `audit-${index + 1}`,
          timestamp: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000),
          userId: 'system',
          userName: 'System',
          action: 'Consent Record Created',
          details: `Consent record created for ${email}`,
          newValue: 'active'
        }]
      };

      // Add some withdrawal history for some records
      if (Math.random() > 0.7) {
        const categoryToWithdraw = record.consentCategories[Math.floor(Math.random() * record.consentCategories.length)];
        record.withdrawalHistory.push({
          id: `withdrawal-${index + 1}`,
          categoryId: categoryToWithdraw.id,
          withdrawalTimestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
          withdrawalMethod: 'website',
          reason: 'No longer interested',
          processedBy: 'System',
          effectiveDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
          dataRetentionAction: 'delete'
        });
      }

      mockRecords.push(record);
    });

    return mockRecords;
  }
}
