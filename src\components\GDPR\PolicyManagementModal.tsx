import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { EnhancedPolicy, Department } from '../../types/compliance';
import { EnhancedComplianceService } from '../../services/enhancedComplianceService';
import { X, Save, AlertCircle, CheckCircle, Clock, FileText } from 'lucide-react';

interface PolicyManagementModalProps {
  isOpen: boolean;
  onClose: () => void;
  policy?: EnhancedPolicy | null;
  departments: Department[];
  onSave: (policy: EnhancedPolicy) => void;
  mode: 'create' | 'edit' | 'view';
}

interface FormData {
  name: string;
  description: string;
  category: EnhancedPolicy['category'];
  priority: EnhancedPolicy['priority'];
  status: EnhancedPolicy['status'];
  owner: string;
  approver: string;
  effectiveDate: string;
  expiryDate: string;
  nextReview: string;
  applicableDepartments: string[];
  complianceRequirements: string[];
}

interface ValidationErrors {
  [key: string]: string;
}

export const PolicyManagementModal: React.FC<PolicyManagementModalProps> = ({
  isOpen,
  onClose,
  policy,
  departments,
  onSave,
  mode
}) => {
  const { mode: themeMode } = useTheme();
  
  const [formData, setFormData] = useState<FormData>({
    name: '',
    description: '',
    category: 'compliance',
    priority: 'medium',
    status: 'draft',
    owner: '',
    approver: '',
    effectiveDate: '',
    expiryDate: '',
    nextReview: '',
    applicableDepartments: [],
    complianceRequirements: []
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  // Initialize form data when policy changes
  useEffect(() => {
    if (policy) {
      setFormData({
        name: policy.name,
        description: policy.description,
        category: policy.category,
        priority: policy.priority,
        status: policy.status,
        owner: policy.owner,
        approver: policy.approver,
        effectiveDate: policy.effectiveDate.toISOString().split('T')[0],
        expiryDate: policy.expiryDate.toISOString().split('T')[0],
        nextReview: policy.nextReview.toISOString().split('T')[0],
        applicableDepartments: policy.applicableDepartments,
        complianceRequirements: policy.complianceRequirements
      });
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        description: '',
        category: 'compliance',
        priority: 'medium',
        status: 'draft',
        owner: '',
        approver: '',
        effectiveDate: '',
        expiryDate: '',
        nextReview: '',
        applicableDepartments: [],
        complianceRequirements: []
      });
    }
    setErrors({});
    setSubmitError(null);
  }, [policy, isOpen]);

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Policy name is required';
    } else if (formData.name.length < 3) {
      newErrors.name = 'Policy name must be at least 3 characters';
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Description must be at least 10 characters';
    }

    if (!formData.owner.trim()) {
      newErrors.owner = 'Policy owner is required';
    }

    if (!formData.approver.trim()) {
      newErrors.approver = 'Policy approver is required';
    }

    if (!formData.effectiveDate) {
      newErrors.effectiveDate = 'Effective date is required';
    }

    if (!formData.expiryDate) {
      newErrors.expiryDate = 'Expiry date is required';
    } else if (formData.effectiveDate && new Date(formData.expiryDate) <= new Date(formData.effectiveDate)) {
      newErrors.expiryDate = 'Expiry date must be after effective date';
    }

    if (!formData.nextReview) {
      newErrors.nextReview = 'Next review date is required';
    }

    if (formData.applicableDepartments.length === 0) {
      newErrors.applicableDepartments = 'At least one department must be selected';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (mode === 'view') return;
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      const policyData: EnhancedPolicy = {
        id: policy?.id || `policy-${Date.now()}`,
        name: formData.name,
        description: formData.description,
        version: policy?.version || '1.0',
        status: formData.status,
        category: formData.category,
        priority: formData.priority,
        owner: formData.owner,
        ownerId: policy?.ownerId || `owner-${Date.now()}`,
        approver: formData.approver,
        approverId: policy?.approverId || `approver-${Date.now()}`,
        effectiveDate: new Date(formData.effectiveDate),
        expiryDate: new Date(formData.expiryDate),
        lastReview: policy?.lastReview || new Date(),
        nextReview: new Date(formData.nextReview),
        applicableDepartments: formData.applicableDepartments,
        relatedPolicies: policy?.relatedPolicies || [],
        complianceRequirements: formData.complianceRequirements,
        attachments: policy?.attachments || [],
        metrics: policy?.metrics || {
          complianceRate: 0,
          violationCount: 0,
          lastViolation: null,
          trainingCompletion: 0,
          acknowledgmentRate: 0
        },
        auditTrail: policy?.auditTrail || []
      };

      onSave(policyData);
      onClose();
    } catch (error) {
      console.error('Error saving policy:', error);
      setSubmitError('Failed to save policy. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle input changes
  const handleInputChange = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // Handle department selection
  const handleDepartmentToggle = (departmentId: string) => {
    setFormData(prev => ({
      ...prev,
      applicableDepartments: prev.applicableDepartments.includes(departmentId)
        ? prev.applicableDepartments.filter(id => id !== departmentId)
        : [...prev.applicableDepartments, departmentId]
    }));
  };

  // Handle compliance requirements
  const handleComplianceRequirementToggle = (requirement: string) => {
    setFormData(prev => ({
      ...prev,
      complianceRequirements: prev.complianceRequirements.includes(requirement)
        ? prev.complianceRequirements.filter(req => req !== requirement)
        : [...prev.complianceRequirements, requirement]
    }));
  };

  if (!isOpen) return null;

  const isReadOnly = mode === 'view';
  const modalTitle = mode === 'create' ? 'Create New Policy' : 
                    mode === 'edit' ? 'Edit Policy' : 'View Policy';

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`bg-background border border-border rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden ${
        themeMode === 'dark' ? 'bg-gray-800' : 'bg-white'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <FileText className="w-6 h-6 text-primary" />
            <h2 className="text-xl font-semibold text-text">{modalTitle}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-surface-hover rounded-lg transition-colors"
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>

        {/* Content */}
        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {submitError && (
              <div className="flex items-center space-x-2 p-4 bg-red-50 border border-red-200 rounded-lg">
                <AlertCircle className="w-5 h-5 text-red-500" />
                <span className="text-red-700">{submitError}</span>
              </div>
            )}

            {/* Basic Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div>
                <label htmlFor="policy-name" className="block text-sm font-medium text-text mb-2">
                  Policy Name *
                </label>
                <input
                  id="policy-name"
                  type="text"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                    errors.name ? 'border-red-500' : 'border-border'
                  } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                  placeholder="Enter policy name"
                />
                {errors.name && (
                  <p className="mt-1 text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div>
                <label htmlFor="policy-owner" className="block text-sm font-medium text-text mb-2">
                  Policy Owner *
                </label>
                <input
                  id="policy-owner"
                  type="text"
                  value={formData.owner}
                  onChange={(e) => handleInputChange('owner', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                    errors.owner ? 'border-red-500' : 'border-border'
                  } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                  placeholder="Enter policy owner"
                />
                {errors.owner && (
                  <p className="mt-1 text-sm text-red-500">{errors.owner}</p>
                )}
              </div>
            </div>

            {/* Description */}
            <div>
              <label htmlFor="policy-description" className="block text-sm font-medium text-text mb-2">
                Description *
              </label>
              <textarea
                id="policy-description"
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                disabled={isReadOnly}
                rows={4}
                className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors resize-vertical ${
                  errors.description ? 'border-red-500' : 'border-border'
                } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                placeholder="Enter policy description"
              />
              {errors.description && (
                <p className="mt-1 text-sm text-red-500">{errors.description}</p>
              )}
            </div>

            {/* Category, Priority, Status */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="policy-category" className="block text-sm font-medium text-text mb-2">
                  Category
                </label>
                <select
                  id="policy-category"
                  value={formData.category}
                  onChange={(e) => handleInputChange('category', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors border-border ${
                    isReadOnly ? 'opacity-60 cursor-not-allowed' : ''
                  }`}
                >
                  <option value="privacy">Privacy</option>
                  <option value="security">Security</option>
                  <option value="data_governance">Data Governance</option>
                  <option value="compliance">Compliance</option>
                  <option value="operational">Operational</option>
                </select>
              </div>

              <div>
                <label htmlFor="policy-priority" className="block text-sm font-medium text-text mb-2">
                  Priority
                </label>
                <select
                  id="policy-priority"
                  value={formData.priority}
                  onChange={(e) => handleInputChange('priority', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors border-border ${
                    isReadOnly ? 'opacity-60 cursor-not-allowed' : ''
                  }`}
                >
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="critical">Critical</option>
                </select>
              </div>

              <div>
                <label htmlFor="policy-status" className="block text-sm font-medium text-text mb-2">
                  Status
                </label>
                <select
                  id="policy-status"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors border-border ${
                    isReadOnly ? 'opacity-60 cursor-not-allowed' : ''
                  }`}
                >
                  <option value="draft">Draft</option>
                  <option value="active">Active</option>
                  <option value="under_review">Under Review</option>
                  <option value="deprecated">Deprecated</option>
                </select>
              </div>
            </div>

            {/* Dates */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              <div>
                <label htmlFor="effective-date" className="block text-sm font-medium text-text mb-2">
                  Effective Date *
                </label>
                <input
                  id="effective-date"
                  type="date"
                  value={formData.effectiveDate}
                  onChange={(e) => handleInputChange('effectiveDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                    errors.effectiveDate ? 'border-red-500' : 'border-border'
                  } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                />
                {errors.effectiveDate && (
                  <p className="mt-1 text-sm text-red-500">{errors.effectiveDate}</p>
                )}
              </div>

              <div>
                <label htmlFor="expiry-date" className="block text-sm font-medium text-text mb-2">
                  Expiry Date *
                </label>
                <input
                  id="expiry-date"
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                    errors.expiryDate ? 'border-red-500' : 'border-border'
                  } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                />
                {errors.expiryDate && (
                  <p className="mt-1 text-sm text-red-500">{errors.expiryDate}</p>
                )}
              </div>

              <div>
                <label htmlFor="next-review" className="block text-sm font-medium text-text mb-2">
                  Next Review Date *
                </label>
                <input
                  id="next-review"
                  type="date"
                  value={formData.nextReview}
                  onChange={(e) => handleInputChange('nextReview', e.target.value)}
                  disabled={isReadOnly}
                  className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                    errors.nextReview ? 'border-red-500' : 'border-border'
                  } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                />
                {errors.nextReview && (
                  <p className="mt-1 text-sm text-red-500">{errors.nextReview}</p>
                )}
              </div>
            </div>

            {/* Approver */}
            <div>
              <label htmlFor="policy-approver" className="block text-sm font-medium text-text mb-2">
                Policy Approver *
              </label>
              <input
                id="policy-approver"
                type="text"
                value={formData.approver}
                onChange={(e) => handleInputChange('approver', e.target.value)}
                disabled={isReadOnly}
                className={`w-full px-3 py-2 border rounded-lg text-text bg-background focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary transition-colors ${
                  errors.approver ? 'border-red-500' : 'border-border'
                } ${isReadOnly ? 'opacity-60 cursor-not-allowed' : ''}`}
                placeholder="Enter policy approver"
              />
              {errors.approver && (
                <p className="mt-1 text-sm text-red-500">{errors.approver}</p>
              )}
            </div>

            {/* Applicable Departments */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Applicable Departments *
              </label>
              <div className="grid grid-cols-2 lg:grid-cols-3 gap-3 p-4 border border-border rounded-lg bg-surface">
                {departments.map(department => (
                  <label key={department.id} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.applicableDepartments.includes(department.id)}
                      onChange={() => handleDepartmentToggle(department.id)}
                      disabled={isReadOnly}
                      className="rounded border-border text-primary focus:ring-primary/50"
                    />
                    <span className="text-sm text-text">{department.name}</span>
                  </label>
                ))}
              </div>
              {errors.applicableDepartments && (
                <p className="mt-1 text-sm text-red-500">{errors.applicableDepartments}</p>
              )}
            </div>

            {/* Compliance Requirements */}
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Compliance Requirements
              </label>
              <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 p-4 border border-border rounded-lg bg-surface">
                {['GDPR', 'CCPA', 'SOX', 'HIPAA', 'PCI DSS', 'ISO 27001', 'NIST', 'SOC 2'].map(requirement => (
                  <label key={requirement} className="flex items-center space-x-2 cursor-pointer">
                    <input
                      type="checkbox"
                      checked={formData.complianceRequirements.includes(requirement)}
                      onChange={() => handleComplianceRequirementToggle(requirement)}
                      disabled={isReadOnly}
                      className="rounded border-border text-primary focus:ring-primary/50"
                    />
                    <span className="text-sm text-text">{requirement}</span>
                  </label>
                ))}
              </div>
            </div>

            {/* Policy Metrics (View Mode Only) */}
            {mode === 'view' && policy && (
              <div className="bg-surface border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-text mb-4">Policy Metrics</h3>
                <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{policy.metrics.complianceRate}%</div>
                    <div className="text-sm text-text-secondary">Compliance Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-text">{policy.metrics.trainingCompletion}%</div>
                    <div className="text-sm text-text-secondary">Training Completion</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-text">{policy.metrics.acknowledgmentRate}%</div>
                    <div className="text-sm text-text-secondary">Acknowledgment Rate</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-text">{policy.metrics.violationCount}</div>
                    <div className="text-sm text-text-secondary">Violations</div>
                  </div>
                </div>

                {policy.metrics.lastViolation && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="w-4 h-4 text-red-500" />
                      <span className="text-sm text-red-700">
                        Last violation: {policy.metrics.lastViolation.toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Audit Trail (View Mode Only) */}
            {mode === 'view' && policy && policy.auditTrail.length > 0 && (
              <div className="bg-surface border border-border rounded-lg p-4">
                <h3 className="text-lg font-semibold text-text mb-4">Audit Trail</h3>
                <div className="space-y-3 max-h-40 overflow-y-auto">
                  {policy.auditTrail.slice(0, 5).map(entry => (
                    <div key={entry.id} className="flex items-center justify-between p-3 bg-background border border-border rounded">
                      <div>
                        <div className="text-sm font-medium text-text">{entry.action}</div>
                        <div className="text-xs text-text-secondary">{entry.details}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm text-text">{entry.userName}</div>
                        <div className="text-xs text-text-secondary">
                          {entry.timestamp.toLocaleDateString()}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        {!isReadOnly && (
          <div className="flex items-center justify-end space-x-4 p-6 border-t border-border">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-text-secondary hover:text-text border border-border rounded-lg hover:bg-surface-hover transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              onClick={handleSubmit}
              disabled={isSubmitting}
              className="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Saving...</span>
                </>
              ) : (
                <>
                  <Save className="w-4 h-4" />
                  <span>{mode === 'create' ? 'Create Policy' : 'Update Policy'}</span>
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
