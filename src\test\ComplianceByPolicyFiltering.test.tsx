import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { ComplianceByPolicy } from '../components/ComplianceByPolicy';
import { ComplianceStandardsProvider } from '../context/ComplianceStandardsContext';
import { ThemeProvider } from '../context/ThemeContext';

// Mock the configuration service
vi.mock('../services/api', () => ({
  configurationService: {
    getSettings: vi.fn().mockResolvedValue({
      compliance: {
        gdprEnabled: true,
        dpdpEnabled: false,
        hipaaEnabled: false,
        sox: false,
        pci: true,
        iso27001: false,
        ccpa: true,
        nist: false,
        fedramp: false,
      }
    }),
    updateSettings: vi.fn().mockResolvedValue({}),
  }
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ThemeProvider>
      <ComplianceStandardsProvider>
        {children}
      </ComplianceStandardsProvider>
    </ThemeProvider>
  );
};

describe('ComplianceByPolicy Filtering', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render all policies when standards are enabled', async () => {
    render(
      <TestWrapper>
        <ComplianceByPolicy />
      </TestWrapper>
    );

    // Wait for component to load
    await waitFor(() => {
      expect(screen.getByText('Compliance By Policy')).toBeInTheDocument();
    });

    // Should show policies for enabled standards (GDPR, PCI DSS, CCPA)
    await waitFor(() => {
      expect(screen.getByText('GDPR Data Protection Policy')).toBeInTheDocument();
      expect(screen.getByText('PCI DSS Payment Security')).toBeInTheDocument();
      expect(screen.getByText('CCPA Consumer Privacy')).toBeInTheDocument();
      expect(screen.getByText('Cross-Compliance Data Policy')).toBeInTheDocument();
    });

    // Should not show policies for disabled standards
    expect(screen.queryByText('HIPAA Privacy Policy')).not.toBeInTheDocument();
    expect(screen.queryByText('SOX Financial Controls')).not.toBeInTheDocument();
  });

  it('should show filter indicator when standards are enabled', async () => {
    render(
      <TestWrapper>
        <ComplianceByPolicy />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText(/Filtered by:/)).toBeInTheDocument();
      expect(screen.getByText(/GDPR, PCI DSS, CCPA/)).toBeInTheDocument();
    });
  });

  it('should show framework tags on policies', async () => {
    render(
      <TestWrapper>
        <ComplianceByPolicy />
      </TestWrapper>
    );

    await waitFor(() => {
      // Check for framework tags
      const gdprTags = screen.getAllByText('GDPR');
      const pciTags = screen.getAllByText('PCI DSS');
      const ccpaTags = screen.getAllByText('CCPA');
      
      expect(gdprTags.length).toBeGreaterThan(0);
      expect(pciTags.length).toBeGreaterThan(0);
      expect(ccpaTags.length).toBeGreaterThan(0);
    });
  });

  it('should handle empty state when no policies match enabled standards', async () => {
    // Mock a scenario where no standards are enabled
    const mockConfigService = await import('../services/api');
    vi.mocked(mockConfigService.configurationService.getSettings).mockResolvedValueOnce({
      compliance: {
        gdprEnabled: false,
        dpdpEnabled: false,
        hipaaEnabled: false,
        sox: false,
        pci: false,
        iso27001: false,
        ccpa: false,
        nist: false,
        fedramp: false,
      }
    });

    render(
      <TestWrapper>
        <ComplianceByPolicy />
      </TestWrapper>
    );

    // Should show all policies when no standards are enabled
    await waitFor(() => {
      expect(screen.getByText('Compliance By Policy')).toBeInTheDocument();
    });
  });
});
