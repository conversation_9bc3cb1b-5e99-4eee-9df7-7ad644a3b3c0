import React from 'react';
import { User, Shield, FileText, ExternalLink } from 'lucide-react';
import { useTheme } from '../context/ThemeContext';
import { ErrorBoundary } from './ui/ErrorBoundary';

const PrivacyRequestCard = ({ title, description, icon }: { title: string; description: string; icon: React.ReactNode }) => {
  const { mode } = useTheme();

  return (
    <div className="bg-card rounded-xl shadow-md p-6 hover:shadow-lg transition-all border-l-4 dark:shadow-none" style={{ borderLeftColor: '#A6D933' }}>
      <div className="flex items-start mb-4">
        <div className="p-2 rounded-full mr-4" style={{ backgroundColor: 'rgba(166, 217, 51, 0.15)', color: '#A6D933' }}>{icon}</div>
        <h3 className="text-xl font-semibold text-text">{title}</h3>
      </div>
      <p className="text-text-secondary mb-4">{description}</p>
      <button className="font-medium hover:underline flex items-center" style={{ color: '#A6D933' }}>
        Submit Request <ExternalLink size={16} className="ml-1" />
      </button>
    </div>
  );
};

const ConsumerInterface = () => {
  const { mode } = useTheme();

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Consumer Interface Error</h2>
            <p className="text-text-secondary">Something went wrong loading the consumer interface. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className="flex flex-col min-h-screen bg-background">
      {/* Hero Section */}
      <div className={`bg-gradient-to-r ${mode === 'dark' ? 'from-primary/20 to-background' : 'from-[#A6D933]/10 to-white'} py-16 px-8`}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center max-w-3xl mx-auto">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">
              <span style={{ color: '#A6D933' }}>PRAE</span>
              <span className="text-text">FERRE</span>
              <span className="block text-2xl text-text-secondary mt-2">CONSUMER PRIVACY PORTAL</span>
            </h1>
            <p className="text-xl text-text-secondary mb-8">
              Take control of your personal data and privacy rights
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <button className="text-white px-6 py-3 rounded-lg transition-all" style={{ backgroundColor: '#A6D933' }}>
                Sign In
              </button>
              <button className="border border-border bg-surface px-6 py-3 rounded-lg hover:bg-surface/80 transition-all text-text">
                Create Account
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Privacy Requests Section */}
      <div className="py-16 px-8">
        <div className="max-w-7xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-text">Manage Your Privacy Rights</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <PrivacyRequestCard
              title="Access Your Data"
              description="Request a copy of all personal data we have collected about you."
              icon={<User size={24} />}
            />
            <PrivacyRequestCard
              title="Delete Your Data"
              description="Request the deletion of your personal information from our systems."
              icon={<Shield size={24} />}
            />
            <PrivacyRequestCard
              title="Correct Your Data"
              description="Submit corrections to inaccurate personal information we may have."
              icon={<FileText size={24} />}
            />
          </div>
        </div>
      </div>

      {/* FAQ Section */}
      <div className="py-16 px-8 bg-surface">
        <div className="max-w-4xl mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12 text-text">Frequently Asked Questions</h2>
          <div className="space-y-6">
            <div className="border-b border-border pb-6">
              <h3 className="text-xl font-semibold mb-2 text-text">How do I request access to my data?</h3>
              <p className="text-text-secondary">You can submit a data access request through our portal by clicking on the "Access Your Data" option above. Once submitted, we'll process your request within 30 days.</p>
            </div>
            <div className="border-b border-border pb-6">
              <h3 className="text-xl font-semibold mb-2 text-text">What happens when I request data deletion?</h3>
              <p className="text-text-secondary">When you submit a deletion request, we'll remove your personal information from our active systems within 30 days. Some information may be retained for legal or compliance purposes.</p>
            </div>
            <div className="border-b border-border pb-6">
              <h3 className="text-xl font-semibold mb-2 text-text">How is my data protected?</h3>
              <p className="text-text-secondary">We employ industry-leading security measures to protect your personal information, including encryption, access controls, and regular security audits.</p>
            </div>
          </div>
        </div>
      </div>

      {/* Contact Section */}
      <div className={`py-16 px-8 ${mode === 'dark' ? 'bg-primary/5' : 'bg-[#A6D933]/10'}`}>
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-6 text-text">Need Additional Help?</h2>
          <p className="text-lg text-text-secondary mb-8">Our privacy team is here to assist you with any questions or concerns</p>
          <button className="text-white px-8 py-4 rounded-lg text-lg font-medium transition-all" style={{ backgroundColor: '#A6D933' }}>
            Contact Privacy Team
          </button>
        </div>
      </div>
    </div>
    </ErrorBoundary>
  );
};

export default ConsumerInterface;