import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme } from '../../../test/utils/test-utils';
import { EnhancedComplianceDashboard } from '../EnhancedComplianceDashboard';
import { EnhancedComplianceService } from '../../../services/enhancedComplianceService';

// Mock the services
vi.mock('../../../services/enhancedComplianceService');

// Mock the MetricsVisualization component to avoid complex chart rendering in tests
vi.mock('../MetricsVisualization', () => ({
  MetricsVisualization: ({ departments, policies }: any) => (
    <div data-testid="metrics-visualization">
      <div data-testid="departments-count">{departments.length}</div>
      <div data-testid="policies-count">{policies.length}</div>
    </div>
  )
}));

// Mock data
const mockDepartments = [
  {
    id: 'dept-1',
    name: 'Human Resources',
    description: 'HR department',
    head: 'HR Manager',
    headId: 'head-1',
    employeeCount: 25,
    complianceScore: 85,
    riskLevel: 'medium' as const,
    policies: ['policy-1'],
    assessments: ['assessment-1'],
    lastAudit: new Date('2024-01-01'),
    nextAudit: new Date('2024-06-01'),
    status: 'active' as const,
    contactInfo: {
      email: '<EMAIL>',
      phone: '******-1234',
      location: 'Floor 1'
    },
    complianceMetrics: {
      completedAssessments: 8,
      pendingAssessments: 2,
      overdueAssessments: 1,
      policyCompliance: 85,
      trainingCompletion: 90
    }
  },
  {
    id: 'dept-2',
    name: 'Information Technology',
    description: 'IT department',
    head: 'IT Manager',
    headId: 'head-2',
    employeeCount: 15,
    complianceScore: 92,
    riskLevel: 'low' as const,
    policies: ['policy-2'],
    assessments: ['assessment-2'],
    lastAudit: new Date('2024-01-15'),
    nextAudit: new Date('2024-07-15'),
    status: 'active' as const,
    contactInfo: {
      email: '<EMAIL>',
      phone: '******-5678',
      location: 'Floor 2'
    },
    complianceMetrics: {
      completedAssessments: 10,
      pendingAssessments: 1,
      overdueAssessments: 0,
      policyCompliance: 95,
      trainingCompletion: 88
    }
  }
];

const mockPolicies = [
  {
    id: 'policy-1',
    name: 'Data Privacy Policy',
    description: 'Comprehensive data privacy policy',
    version: '1.0',
    status: 'active' as const,
    category: 'privacy' as const,
    priority: 'high' as const,
    owner: 'Privacy Officer',
    ownerId: 'owner-1',
    approver: 'Legal Team',
    approverId: 'approver-1',
    effectiveDate: new Date('2024-01-01'),
    expiryDate: new Date('2025-01-01'),
    lastReview: new Date('2024-01-01'),
    nextReview: new Date('2024-07-01'),
    applicableDepartments: ['dept-1', 'dept-2'],
    relatedPolicies: [],
    complianceRequirements: ['GDPR', 'CCPA'],
    attachments: [],
    metrics: {
      complianceRate: 85,
      violationCount: 2,
      lastViolation: new Date('2024-02-01'),
      trainingCompletion: 90,
      acknowledgmentRate: 88
    },
    auditTrail: []
  }
];

const mockMetrics = {
  overall: {
    score: 88,
    trend: 'improving' as const,
    lastUpdated: new Date()
  },
  departments: {
    'dept-1': {
      score: 85,
      assessments: 11,
      policies: 1,
      risks: 1,
      trend: 'improving' as const
    },
    'dept-2': {
      score: 92,
      assessments: 11,
      policies: 1,
      risks: 1,
      trend: 'stable' as const
    }
  },
  policies: {
    total: 1,
    active: 1,
    compliant: 1,
    nonCompliant: 0,
    pending: 0,
    overdue: 0,
    complianceRate: 85
  },
  assessments: {
    total: 22,
    completed: 18,
    inProgress: 3,
    overdue: 1,
    completionRate: 82,
    averageRiskScore: 65
  },
  risks: {
    total: 10,
    low: 6,
    medium: 3,
    high: 1,
    critical: 0,
    mitigated: 8,
    open: 2
  },
  trends: {
    period: 'month' as const,
    complianceScore: [80, 82, 85, 88],
    assessmentCompletion: [75, 78, 80, 82],
    riskReduction: [60, 65, 68, 70],
    policyCompliance: [82, 84, 85, 85],
    dates: ['2024-01', '2024-02', '2024-03', '2024-04']
  }
};

describe('EnhancedComplianceDashboard', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock service methods
    vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValue(mockDepartments);
    vi.mocked(EnhancedComplianceService.getAllPolicies).mockResolvedValue(mockPolicies);
    vi.mocked(EnhancedComplianceService.getEnhancedMetrics).mockResolvedValue(mockMetrics);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Initial Rendering', () => {
    it('renders the dashboard with all main elements', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Check for main title
      expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
      
      // Check for tab navigation
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Departments')).toBeInTheDocument();
      expect(screen.getByText('Policies')).toBeInTheDocument();
      expect(screen.getByText('Metrics')).toBeInTheDocument();

      // Check for search input
      expect(screen.getByPlaceholderText(/search departments, policies/i)).toBeInTheDocument();

      // Check for refresh button
      expect(screen.getByText('Refresh')).toBeInTheDocument();
    });

    it('loads data on mount', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      await waitFor(() => {
        expect(EnhancedComplianceService.getAllDepartments).toHaveBeenCalled();
        expect(EnhancedComplianceService.getAllPolicies).toHaveBeenCalled();
        expect(EnhancedComplianceService.getEnhancedMetrics).toHaveBeenCalled();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('switches between tabs correctly', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('Overall Score')).toBeInTheDocument();
      });

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.getByText('Information Technology')).toBeInTheDocument();
      });

      // Switch to policies tab
      await user.click(screen.getByText('Policies'));
      
      await waitFor(() => {
        expect(screen.getByText('Data Privacy Policy')).toBeInTheDocument();
      });

      // Switch to metrics tab
      await user.click(screen.getByText('Metrics'));
      
      await waitFor(() => {
        expect(screen.getByTestId('metrics-visualization')).toBeInTheDocument();
      });
    });
  });

  describe('Search Functionality', () => {
    it('filters departments by search term', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.getByText('Information Technology')).toBeInTheDocument();
      });

      // Search for HR
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      await user.type(searchInput, 'Human');

      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.queryByText('Information Technology')).not.toBeInTheDocument();
      });
    });

    it('shows empty state when no search results', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });

      // Search for non-existent department
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      await user.type(searchInput, 'NonExistent');

      await waitFor(() => {
        expect(screen.getByText('No Departments Found')).toBeInTheDocument();
        expect(screen.getByText('Clear Search')).toBeInTheDocument();
      });
    });

    it('sanitizes search input to prevent XSS', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      await user.type(searchInput, '<script>alert("xss")</script>');

      // The input should be sanitized (no script tags)
      expect(searchInput).toHaveValue('scriptalert("xss")/script');
    });
  });

  describe('Error Handling', () => {
    it('displays error state when department loading fails', async () => {
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockRejectedValue(
        new Error('Network error')
      );

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));

      await waitFor(() => {
        expect(screen.getByText('Department Loading Error')).toBeInTheDocument();
        expect(screen.getByText(/Failed to load departments: Network error/)).toBeInTheDocument();
        expect(screen.getByText('Try Again')).toBeInTheDocument();
      });
    });

    it('allows retry after error', async () => {
      // First call fails, second succeeds
      vi.mocked(EnhancedComplianceService.getAllDepartments)
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce(mockDepartments);

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));

      await waitFor(() => {
        expect(screen.getByText('Department Loading Error')).toBeInTheDocument();
      });

      // Click retry
      await user.click(screen.getByText('Try Again'));

      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });
    });
  });

  describe('Loading States', () => {
    it('shows loading state while data is being fetched', async () => {
      // Mock delayed response
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockImplementation(
        () => new Promise(resolve => setTimeout(() => resolve(mockDepartments), 100))
      );

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));

      // Should show loading state
      expect(screen.getByText('Loading department data...')).toBeInTheDocument();

      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      }, { timeout: 200 });
    });
  });

  describe('Policy Management', () => {
    it('opens create policy modal when button is clicked', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to policies tab
      await user.click(screen.getByText('Policies'));

      await waitFor(() => {
        expect(screen.getByText('New Policy')).toBeInTheDocument();
      });

      // Click new policy button
      await user.click(screen.getByText('New Policy'));

      await waitFor(() => {
        expect(screen.getByText('Create New Policy')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Check for navigation role
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Check for search input accessibility
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      expect(searchInput).toHaveAttribute('type', 'text');
      expect(searchInput).toHaveAttribute('maxLength', '100');
    });

    it('supports keyboard navigation', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Tab through navigation
      await user.tab();
      expect(screen.getByText('Overview')).toHaveFocus();

      await user.tab();
      expect(screen.getByText('Departments')).toHaveFocus();

      await user.tab();
      expect(screen.getByText('Policies')).toHaveFocus();

      await user.tab();
      expect(screen.getByText('Metrics')).toHaveFocus();
    });
  });

  describe('Theme Compatibility', () => {
    it('renders correctly in light theme', () => {
      renderWithTheme(<EnhancedComplianceDashboard />, 'light');
      
      expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
      // Component should render without errors in light theme
    });

    it('renders correctly in dark theme', () => {
      renderWithTheme(<EnhancedComplianceDashboard />, 'dark');
      
      expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
      // Component should render without errors in dark theme
    });
  });

  describe('Data Refresh', () => {
    it('refreshes all data when refresh button is clicked', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Wait for initial load
      await waitFor(() => {
        expect(EnhancedComplianceService.getAllDepartments).toHaveBeenCalledTimes(1);
      });

      // Click refresh button
      await user.click(screen.getByText('Refresh'));

      await waitFor(() => {
        expect(EnhancedComplianceService.getAllDepartments).toHaveBeenCalledTimes(2);
        expect(EnhancedComplianceService.getAllPolicies).toHaveBeenCalledTimes(2);
        expect(EnhancedComplianceService.getEnhancedMetrics).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Edge Cases', () => {
    it('handles empty data gracefully', async () => {
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValue([]);
      vi.mocked(EnhancedComplianceService.getAllPolicies).mockResolvedValue([]);

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));

      await waitFor(() => {
        expect(screen.getByText('No Departments Found')).toBeInTheDocument();
      });

      // Switch to policies tab
      await user.click(screen.getByText('Policies'));

      await waitFor(() => {
        expect(screen.getByText('No Policies Found')).toBeInTheDocument();
      });
    });

    it('handles invalid data format gracefully', async () => {
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValue(null as any);

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Switch to departments tab
      await user.click(screen.getByText('Departments'));

      await waitFor(() => {
        expect(screen.getByText('Department Loading Error')).toBeInTheDocument();
        expect(screen.getByText(/Invalid department data format received/)).toBeInTheDocument();
      });
    });
  });
});
