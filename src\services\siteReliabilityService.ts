import { toast } from 'react-toastify';

// Types for Site Reliability Guardian
export interface SLATarget {
  id: string;
  name: string;
  description: string;
  targetPercentage: number;
  currentPercentage: number;
  status: 'healthy' | 'warning' | 'critical';
  lastBreach?: Date;
  breachCount: number;
  category: 'availability' | 'performance' | 'error_rate' | 'response_time';
}

export interface UptimeMetrics {
  serviceId: string;
  serviceName: string;
  currentStatus: 'up' | 'down' | 'degraded';
  uptimePercentage: number;
  downtimeMinutes: number;
  lastIncident?: Date;
  mttr: number; // Mean Time To Recovery in minutes
  mtbf: number; // Mean Time Between Failures in hours
  availability: {
    daily: number;
    weekly: number;
    monthly: number;
    yearly: number;
  };
}

export interface PerformanceMetrics {
  timestamp: Date;
  responseTime: number;
  throughput: number;
  errorRate: number;
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLatency: number;
}

export interface AlertRule {
  id: string;
  name: string;
  description: string;
  condition: string;
  threshold: number;
  severity: 'low' | 'medium' | 'high' | 'critical';
  enabled: boolean;
  lastTriggered?: Date;
  triggerCount: number;
  escalationPolicy: string;
}

export interface ServiceIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'investigating' | 'identified' | 'monitoring' | 'resolved';
  affectedServices: string[];
  startTime: Date;
  endTime?: Date;
  duration?: number;
  rootCause?: string;
  resolution?: string;
  impactedUsers: number;
}

export interface SiteReliabilityDashboardData {
  overview: {
    overallHealth: 'healthy' | 'warning' | 'critical';
    totalServices: number;
    servicesUp: number;
    servicesDown: number;
    averageUptime: number;
    activeIncidents: number;
    resolvedIncidents: number;
    lastUpdated: Date;
  };
  slaTargets: SLATarget[];
  uptimeMetrics: UptimeMetrics[];
  performanceMetrics: PerformanceMetrics[];
  alertRules: AlertRule[];
  recentIncidents: ServiceIncident[];
  trends: {
    uptimeTrend: Array<{ date: string; uptime: number }>;
    performanceTrend: Array<{ date: string; responseTime: number; errorRate: number }>;
    incidentTrend: Array<{ date: string; incidents: number; mttr: number }>;
  };
}

// Mock data generators
const generateMockSLATargets = (): SLATarget[] => [
  {
    id: 'sla-001',
    name: 'API Availability',
    description: 'Core API service availability target',
    targetPercentage: 99.9,
    currentPercentage: 99.95 + Math.random() * 0.04,
    status: Math.random() > 0.8 ? 'warning' : 'healthy',
    breachCount: Math.floor(Math.random() * 3),
    category: 'availability'
  },
  {
    id: 'sla-002',
    name: 'Response Time',
    description: 'API response time under 200ms',
    targetPercentage: 95.0,
    currentPercentage: 94.2 + Math.random() * 2,
    status: Math.random() > 0.7 ? 'warning' : 'healthy',
    breachCount: Math.floor(Math.random() * 5),
    category: 'performance'
  },
  {
    id: 'sla-003',
    name: 'Error Rate',
    description: 'Error rate below 0.1%',
    targetPercentage: 99.9,
    currentPercentage: 99.85 + Math.random() * 0.1,
    status: Math.random() > 0.9 ? 'critical' : 'healthy',
    breachCount: Math.floor(Math.random() * 2),
    category: 'error_rate'
  },
  {
    id: 'sla-004',
    name: 'Database Performance',
    description: 'Database query response time',
    targetPercentage: 98.0,
    currentPercentage: 97.8 + Math.random() * 1.5,
    status: Math.random() > 0.6 ? 'warning' : 'healthy',
    breachCount: Math.floor(Math.random() * 4),
    category: 'performance'
  }
];

const generateMockUptimeMetrics = (): UptimeMetrics[] => [
  {
    serviceId: 'service-001',
    serviceName: 'Compliance API',
    currentStatus: Math.random() > 0.95 ? 'down' : Math.random() > 0.85 ? 'degraded' : 'up',
    uptimePercentage: 99.8 + Math.random() * 0.15,
    downtimeMinutes: Math.floor(Math.random() * 120),
    mttr: 15 + Math.random() * 30,
    mtbf: 720 + Math.random() * 480,
    availability: {
      daily: 99.9 + Math.random() * 0.1,
      weekly: 99.8 + Math.random() * 0.15,
      monthly: 99.7 + Math.random() * 0.2,
      yearly: 99.5 + Math.random() * 0.3
    }
  },
  {
    serviceId: 'service-002',
    serviceName: 'Privacy Dashboard',
    currentStatus: Math.random() > 0.98 ? 'degraded' : 'up',
    uptimePercentage: 99.95 + Math.random() * 0.04,
    downtimeMinutes: Math.floor(Math.random() * 60),
    mttr: 8 + Math.random() * 15,
    mtbf: 1440 + Math.random() * 720,
    availability: {
      daily: 99.95 + Math.random() * 0.05,
      weekly: 99.9 + Math.random() * 0.08,
      monthly: 99.85 + Math.random() * 0.1,
      yearly: 99.8 + Math.random() * 0.15
    }
  },
  {
    serviceId: 'service-003',
    serviceName: 'Authentication Service',
    currentStatus: 'up',
    uptimePercentage: 99.99,
    downtimeMinutes: Math.floor(Math.random() * 30),
    mttr: 5 + Math.random() * 10,
    mtbf: 2160 + Math.random() * 1440,
    availability: {
      daily: 99.99,
      weekly: 99.98 + Math.random() * 0.01,
      monthly: 99.95 + Math.random() * 0.03,
      yearly: 99.9 + Math.random() * 0.05
    }
  }
];

const generateMockPerformanceMetrics = (): PerformanceMetrics[] => {
  const metrics: PerformanceMetrics[] = [];
  const now = new Date();
  
  for (let i = 0; i < 24; i++) {
    const timestamp = new Date(now.getTime() - (i * 60 * 60 * 1000));
    metrics.push({
      timestamp,
      responseTime: 150 + Math.random() * 100 + (Math.sin(i / 4) * 50),
      throughput: 1000 + Math.random() * 500 + (Math.cos(i / 6) * 200),
      errorRate: 0.05 + Math.random() * 0.1,
      cpuUsage: 45 + Math.random() * 30 + (Math.sin(i / 3) * 15),
      memoryUsage: 60 + Math.random() * 25 + (Math.cos(i / 5) * 10),
      diskUsage: 75 + Math.random() * 15,
      networkLatency: 20 + Math.random() * 30
    });
  }
  
  return metrics.reverse();
};

const generateMockAlertRules = (): AlertRule[] => [
  {
    id: 'alert-001',
    name: 'High Response Time',
    description: 'Alert when API response time exceeds 500ms',
    condition: 'response_time > 500',
    threshold: 500,
    severity: 'high',
    enabled: true,
    triggerCount: Math.floor(Math.random() * 10),
    escalationPolicy: 'immediate'
  },
  {
    id: 'alert-002',
    name: 'Service Down',
    description: 'Alert when service becomes unavailable',
    condition: 'uptime < 100',
    threshold: 100,
    severity: 'critical',
    enabled: true,
    triggerCount: Math.floor(Math.random() * 3),
    escalationPolicy: 'escalate-after-5min'
  },
  {
    id: 'alert-003',
    name: 'High Error Rate',
    description: 'Alert when error rate exceeds 1%',
    condition: 'error_rate > 1',
    threshold: 1,
    severity: 'medium',
    enabled: true,
    triggerCount: Math.floor(Math.random() * 15),
    escalationPolicy: 'escalate-after-15min'
  }
];

const generateMockIncidents = (): ServiceIncident[] => [
  {
    id: 'incident-001',
    title: 'API Response Time Degradation',
    description: 'Increased response times observed across compliance endpoints',
    severity: 'medium',
    status: 'monitoring',
    affectedServices: ['Compliance API', 'Privacy Dashboard'],
    startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
    duration: 120,
    impactedUsers: 150,
    rootCause: 'Database connection pool exhaustion',
    resolution: 'Increased connection pool size and optimized queries'
  },
  {
    id: 'incident-002',
    title: 'Authentication Service Outage',
    description: 'Complete service outage affecting user login',
    severity: 'critical',
    status: 'resolved',
    affectedServices: ['Authentication Service'],
    startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
    endTime: new Date(Date.now() - 23.5 * 60 * 60 * 1000),
    duration: 30,
    impactedUsers: 500,
    rootCause: 'Redis cluster failure',
    resolution: 'Failover to backup Redis cluster and restored primary'
  }
];

// Cache management
class SiteReliabilityServiceCache {
  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();
  private readonly DEFAULT_TTL = 5 * 60 * 1000; // 5 minutes

  set(key: string, data: any, ttl: number = this.DEFAULT_TTL): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    if (!item) return null;

    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  clear(): void {
    this.cache.clear();
  }

  isDataFresh(key: string): boolean {
    return this.get(key) !== null;
  }
}

class SiteReliabilityService {
  private cache = new SiteReliabilityServiceCache();

  async getDashboardData(): Promise<SiteReliabilityDashboardData> {
    const cacheKey = 'dashboard-data';
    
    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));
    
    const slaTargets = generateMockSLATargets();
    const uptimeMetrics = generateMockUptimeMetrics();
    const performanceMetrics = generateMockPerformanceMetrics();
    const alertRules = generateMockAlertRules();
    const recentIncidents = generateMockIncidents();

    const servicesUp = uptimeMetrics.filter(m => m.currentStatus === 'up').length;
    const servicesDown = uptimeMetrics.filter(m => m.currentStatus === 'down').length;
    const averageUptime = uptimeMetrics.reduce((sum, m) => sum + m.uptimePercentage, 0) / uptimeMetrics.length;
    
    const data: SiteReliabilityDashboardData = {
      overview: {
        overallHealth: servicesDown > 0 ? 'critical' : slaTargets.some(s => s.status === 'warning') ? 'warning' : 'healthy',
        totalServices: uptimeMetrics.length,
        servicesUp,
        servicesDown,
        averageUptime,
        activeIncidents: recentIncidents.filter(i => i.status !== 'resolved').length,
        resolvedIncidents: recentIncidents.filter(i => i.status === 'resolved').length,
        lastUpdated: new Date()
      },
      slaTargets,
      uptimeMetrics,
      performanceMetrics,
      alertRules,
      recentIncidents,
      trends: {
        uptimeTrend: Array.from({ length: 30 }, (_, i) => ({
          date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          uptime: 99.5 + Math.random() * 0.4 + (Math.sin(i / 7) * 0.2)
        })),
        performanceTrend: Array.from({ length: 24 }, (_, i) => ({
          date: new Date(Date.now() - (23 - i) * 60 * 60 * 1000).toISOString(),
          responseTime: 150 + Math.random() * 100 + (Math.sin(i / 4) * 50),
          errorRate: 0.05 + Math.random() * 0.1
        })),
        incidentTrend: Array.from({ length: 7 }, (_, i) => ({
          date: new Date(Date.now() - (6 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          incidents: Math.floor(Math.random() * 5),
          mttr: 15 + Math.random() * 30
        }))
      }
    };

    this.cache.set(cacheKey, data);
    return data;
  }

  async getSLATargets(): Promise<SLATarget[]> {
    const cacheKey = 'sla-targets';

    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 300));

    const data = generateMockSLATargets();
    this.cache.set(cacheKey, data);
    return data;
  }

  async getUptimeMetrics(): Promise<UptimeMetrics[]> {
    const cacheKey = 'uptime-metrics';

    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await new Promise(resolve => setTimeout(resolve, 600 + Math.random() * 400));

    const data = generateMockUptimeMetrics();
    this.cache.set(cacheKey, data);
    return data;
  }

  async getPerformanceMetrics(timeRange: '1h' | '24h' | '7d' | '30d' = '24h'): Promise<PerformanceMetrics[]> {
    const cacheKey = `performance-metrics-${timeRange}`;

    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await new Promise(resolve => setTimeout(resolve, 700 + Math.random() * 300));

    const data = generateMockPerformanceMetrics();
    this.cache.set(cacheKey, data);
    return data;
  }

  async getAlertRules(): Promise<AlertRule[]> {
    const cacheKey = 'alert-rules';

    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await new Promise(resolve => setTimeout(resolve, 400 + Math.random() * 200));

    const data = generateMockAlertRules();
    this.cache.set(cacheKey, data);
    return data;
  }

  async getIncidents(status?: 'active' | 'resolved' | 'all'): Promise<ServiceIncident[]> {
    const cacheKey = `incidents-${status || 'all'}`;

    if (this.cache.isDataFresh(cacheKey)) {
      return this.cache.get(cacheKey);
    }

    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 300));

    let data = generateMockIncidents();

    if (status === 'active') {
      data = data.filter(incident => incident.status !== 'resolved');
    } else if (status === 'resolved') {
      data = data.filter(incident => incident.status === 'resolved');
    }

    this.cache.set(cacheKey, data);
    return data;
  }

  // Action methods
  async acknowledgeAlert(alertId: string): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 500));

      toast.success('Alert acknowledged successfully!');
      this.cache.clear(); // Clear cache to force refresh
      return true;
    } catch (error) {
      toast.error('Failed to acknowledge alert. Please try again.');
      return false;
    }
  }

  async resolveIncident(incidentId: string, resolution: string): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1500 + Math.random() * 1000));

      toast.success('Incident resolved successfully!');
      this.cache.clear(); // Clear cache to force refresh
      return true;
    } catch (error) {
      toast.error('Failed to resolve incident. Please try again.');
      return false;
    }
  }

  async updateSLATarget(targetId: string, updates: Partial<SLATarget>): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));

      toast.success('SLA target updated successfully!');
      this.cache.clear(); // Clear cache to force refresh
      return true;
    } catch (error) {
      toast.error('Failed to update SLA target. Please try again.');
      return false;
    }
  }

  async toggleAlertRule(ruleId: string, enabled: boolean): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 600 + Math.random() * 300));

      toast.success(`Alert rule ${enabled ? 'enabled' : 'disabled'} successfully!`);
      this.cache.clear(); // Clear cache to force refresh
      return true;
    } catch (error) {
      toast.error('Failed to update alert rule. Please try again.');
      return false;
    }
  }

  async exportMetrics(format: 'csv' | 'pdf' | 'excel' = 'csv', timeRange: '24h' | '7d' | '30d' = '24h'): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 1000));

      const filename = `site-reliability-metrics-${timeRange}-${new Date().toISOString().split('T')[0]}.${format}`;
      console.log(`Exporting ${filename}`);

      toast.success('Site reliability metrics exported successfully!');
      return true;
    } catch (error) {
      toast.error('Failed to export metrics. Please try again.');
      return false;
    }
  }

  async generateReport(type: 'sla' | 'uptime' | 'performance' | 'incidents' = 'sla'): Promise<boolean> {
    try {
      await new Promise(resolve => setTimeout(resolve, 3000 + Math.random() * 2000));

      toast.success(`${type.toUpperCase()} report generated successfully!`);
      return true;
    } catch (error) {
      toast.error('Failed to generate report. Please try again.');
      return false;
    }
  }

  // Real-time monitoring methods
  async refreshData(): Promise<void> {
    this.cache.clear();
    await this.getDashboardData();
  }

  // Health check method
  async performHealthCheck(): Promise<{ healthy: boolean; issues: string[] }> {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 500));

      const issues: string[] = [];
      const data = await this.getDashboardData();

      // Check for critical issues
      if (data.overview.servicesDown > 0) {
        issues.push(`${data.overview.servicesDown} service(s) are down`);
      }

      const criticalSLAs = data.slaTargets.filter(sla => sla.status === 'critical');
      if (criticalSLAs.length > 0) {
        issues.push(`${criticalSLAs.length} SLA target(s) in critical state`);
      }

      const activeIncidents = data.recentIncidents.filter(incident => incident.status !== 'resolved');
      if (activeIncidents.length > 0) {
        issues.push(`${activeIncidents.length} active incident(s)`);
      }

      return {
        healthy: issues.length === 0,
        issues
      };
    } catch (error) {
      return {
        healthy: false,
        issues: ['Health check failed - unable to retrieve system status']
      };
    }
  }
}

// Export singleton instance
const siteReliabilityService = new SiteReliabilityService();
export default siteReliabilityService;
