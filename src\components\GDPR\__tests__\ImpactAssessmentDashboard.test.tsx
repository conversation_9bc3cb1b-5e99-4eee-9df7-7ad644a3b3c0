import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test/utils/test-utils';
import { ImpactAssessmentDashboard } from '../ImpactAssessmentDashboard';
import { ImpactAssessmentService } from '../../../services/impactAssessmentService';

// Mock the service
vi.mock('../../../services/impactAssessmentService');

const mockAssessments = [
  {
    id: '1',
    title: 'Customer Data Processing DPIA',
    description: 'Assessment for customer data processing activities',
    status: 'completed',
    riskScore: 75,
    type: 'DPIA',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    assignedTo: '<PERSON>',
    dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    categories: ['personal_data', 'automated_processing'],
    mitigationMeasures: ['encryption', 'access_controls']
  },
  {
    id: '2',
    title: 'Employee Monitoring LIA',
    description: 'Legitimate Interest Assessment for employee monitoring',
    status: 'in_progress',
    riskScore: 60,
    type: 'LIA',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    assignedTo: 'Jane Smith',
    dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
    categories: ['employee_data'],
    mitigationMeasures: ['data_minimization']
  }
];

const mockMetrics = {
  total: 25,
  completed: 15,
  inProgress: 8,
  overdue: 2,
  averageRiskScore: 65,
  highRiskAssessments: 5,
  assessmentsByType: {
    DPIA: 15,
    LIA: 8,
    CHIA: 2
  },
  riskDistribution: {
    low: 10,
    medium: 10,
    high: 5
  }
};

const mockTemplates = [
  {
    id: 'dpia-template',
    name: 'Standard DPIA Template',
    type: 'DPIA',
    description: 'Standard Data Protection Impact Assessment template',
    sections: ['data_description', 'necessity_assessment', 'risk_assessment', 'mitigation_measures']
  },
  {
    id: 'lia-template',
    name: 'Legitimate Interest Assessment',
    type: 'LIA',
    description: 'Template for legitimate interest assessments',
    sections: ['purpose_test', 'necessity_test', 'balancing_test']
  }
];

describe('ImpactAssessmentDashboard', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup service mocks
    vi.mocked(ImpactAssessmentService.getAllAssessments).mockResolvedValue(mockAssessments);
    vi.mocked(ImpactAssessmentService.getMetrics).mockResolvedValue(mockMetrics);
    vi.mocked(ImpactAssessmentService.getTemplates).mockResolvedValue(mockTemplates);
    vi.mocked(ImpactAssessmentService.createAssessment).mockResolvedValue(mockAssessments[0]);
    vi.mocked(ImpactAssessmentService.updateAssessment).mockResolvedValue(mockAssessments[0]);
    vi.mocked(ImpactAssessmentService.deleteAssessment).mockResolvedValue(undefined);
  });

  describe('Component Rendering', () => {
    it('renders the dashboard with header and navigation', async () => {
      render(<ImpactAssessmentDashboard />);
      
      expect(screen.getByText('Impact Assessments (DPIA)')).toBeInTheDocument();
      expect(screen.getByText('Manage Data Protection Impact Assessments and risk evaluations')).toBeInTheDocument();
      
      // Check navigation tabs
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Assessments')).toBeInTheDocument();
      expect(screen.getByText('Templates')).toBeInTheDocument();
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    it('displays metrics cards with correct data', async () => {
      render(<ImpactAssessmentDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('25')).toBeInTheDocument(); // Total assessments
        expect(screen.getByText('15')).toBeInTheDocument(); // Completed
        expect(screen.getByText('8')).toBeInTheDocument(); // In progress
        expect(screen.getByText('2')).toBeInTheDocument(); // Overdue
        expect(screen.getByText('65')).toBeInTheDocument(); // Average risk score
      });
    });

    it('shows risk distribution chart', async () => {
      render(<ImpactAssessmentDashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
      });
    });

    it('displays assessment type breakdown', async () => {
      render(<ImpactAssessmentDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('DPIA: 15')).toBeInTheDocument();
        expect(screen.getByText('LIA: 8')).toBeInTheDocument();
        expect(screen.getByText('CHIA: 2')).toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('switches to Assessments tab and displays assessment list', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const assessmentsTab = screen.getByText('Assessments');
      await user.click(assessmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
        expect(screen.getByText('Employee Monitoring LIA')).toBeInTheDocument();
      });
    });

    it('switches to Templates tab and displays available templates', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const templatesTab = screen.getByText('Templates');
      await user.click(templatesTab);
      
      await waitFor(() => {
        expect(screen.getByText('Standard DPIA Template')).toBeInTheDocument();
        expect(screen.getByText('Legitimate Interest Assessment')).toBeInTheDocument();
      });
    });

    it('switches to Analytics tab and displays analytics', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const analyticsTab = screen.getByText('Analytics');
      await user.click(analyticsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Assessment Analytics')).toBeInTheDocument();
      });
    });
  });

  describe('Assessment Management', () => {
    beforeEach(async () => {
      render(<ImpactAssessmentDashboard />);
      const assessmentsTab = screen.getByText('Assessments');
      await user.click(assessmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });

    it('displays assessments in card format', async () => {
      expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      expect(screen.getByText('Employee Monitoring LIA')).toBeInTheDocument();
      
      // Check status indicators
      expect(screen.getByText('Completed')).toBeInTheDocument();
      expect(screen.getByText('In Progress')).toBeInTheDocument();
      
      // Check risk scores
      expect(screen.getByText('Risk Score: 75')).toBeInTheDocument();
      expect(screen.getByText('Risk Score: 60')).toBeInTheDocument();
    });

    it('shows assessment details when card is clicked', async () => {
      const assessmentCard = screen.getByText('Customer Data Processing DPIA').closest('div');
      await user.click(assessmentCard!);
      
      expect(screen.getByText('Assessment Details')).toBeInTheDocument();
      expect(screen.getByText('Assessment for customer data processing activities')).toBeInTheDocument();
    });

    it('opens edit modal when edit button is clicked', async () => {
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      expect(screen.getByText('Edit Assessment')).toBeInTheDocument();
    });

    it('shows delete confirmation when delete button is clicked', async () => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
    });

    it('creates new assessment from template', async () => {
      const createButton = screen.getByRole('button', { name: /create assessment/i });
      await user.click(createButton);
      
      expect(screen.getByText('Create New Assessment')).toBeInTheDocument();
      
      // Select template
      const templateSelect = screen.getByLabelText(/template/i);
      await user.selectOptions(templateSelect, 'dpia-template');
      
      // Fill out form
      await user.type(screen.getByLabelText(/title/i), 'New DPIA Assessment');
      await user.type(screen.getByLabelText(/description/i), 'Description for new assessment');
      
      const saveButton = screen.getByRole('button', { name: /create/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(ImpactAssessmentService.createAssessment).toHaveBeenCalled();
      });
    });

    it('updates existing assessment', async () => {
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      // Update status
      const statusSelect = screen.getByLabelText(/status/i);
      await user.selectOptions(statusSelect, 'completed');
      
      // Update risk score
      const riskScoreInput = screen.getByLabelText(/risk score/i);
      await user.clear(riskScoreInput);
      await user.type(riskScoreInput, '80');
      
      const saveButton = screen.getByRole('button', { name: /update/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(ImpactAssessmentService.updateAssessment).toHaveBeenCalled();
      });
    });

    it('deletes assessment after confirmation', async () => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(ImpactAssessmentService.deleteAssessment).toHaveBeenCalledWith('1');
      });
    });
  });

  describe('Template Management', () => {
    beforeEach(async () => {
      render(<ImpactAssessmentDashboard />);
      const templatesTab = screen.getByText('Templates');
      await user.click(templatesTab);
      
      await waitFor(() => {
        expect(screen.getByText('Standard DPIA Template')).toBeInTheDocument();
      });
    });

    it('displays available templates', async () => {
      expect(screen.getByText('Standard DPIA Template')).toBeInTheDocument();
      expect(screen.getByText('Legitimate Interest Assessment')).toBeInTheDocument();
      
      // Check descriptions
      expect(screen.getByText('Standard Data Protection Impact Assessment template')).toBeInTheDocument();
      expect(screen.getByText('Template for legitimate interest assessments')).toBeInTheDocument();
    });

    it('creates assessment from template', async () => {
      const useTemplateButtons = screen.getAllByRole('button', { name: /use template/i });
      await user.click(useTemplateButtons[0]);
      
      expect(screen.getByText('Create Assessment from Template')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Standard DPIA Template')).toBeInTheDocument();
    });

    it('previews template sections', async () => {
      const previewButtons = screen.getAllByRole('button', { name: /preview/i });
      await user.click(previewButtons[0]);
      
      expect(screen.getByText('Template Preview')).toBeInTheDocument();
      expect(screen.getByText('data_description')).toBeInTheDocument();
      expect(screen.getByText('necessity_assessment')).toBeInTheDocument();
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(async () => {
      render(<ImpactAssessmentDashboard />);
      const assessmentsTab = screen.getByText('Assessments');
      await user.click(assessmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });

    it('filters assessments by search term', async () => {
      const searchInput = screen.getByPlaceholderText(/search assessments/i);
      await user.type(searchInput, 'Customer');
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });

    it('filters by assessment status', async () => {
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      const completedFilter = screen.getByLabelText(/completed/i);
      await user.click(completedFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });

    it('filters by assessment type', async () => {
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      const dpiaFilter = screen.getByLabelText(/DPIA/i);
      await user.click(dpiaFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });

    it('filters by risk level', async () => {
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      const highRiskFilter = screen.getByLabelText(/high risk/i);
      await user.click(highRiskFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });
    });
  });

  describe('Analytics Section', () => {
    beforeEach(async () => {
      render(<ImpactAssessmentDashboard />);
      const analyticsTab = screen.getByText('Analytics');
      await user.click(analyticsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Assessment Analytics')).toBeInTheDocument();
      });
    });

    it('displays risk trend charts', async () => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('shows completion rate over time', async () => {
      expect(screen.getByText('Completion Rate Trend')).toBeInTheDocument();
    });

    it('displays assessment type distribution', async () => {
      expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
    });

    it('shows key performance indicators', async () => {
      expect(screen.getByText('Average Completion Time')).toBeInTheDocument();
      expect(screen.getByText('Overdue Rate')).toBeInTheDocument();
      expect(screen.getByText('High Risk Rate')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles service errors gracefully', async () => {
      vi.mocked(ImpactAssessmentService.getAllAssessments).mockRejectedValue(new Error('Service error'));
      
      render(<ImpactAssessmentDashboard />);
      
      expect(screen.getByText('Impact Assessments (DPIA)')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });
    });

    it('handles create assessment errors', async () => {
      vi.mocked(ImpactAssessmentService.createAssessment).mockRejectedValue(new Error('Create failed'));
      
      render(<ImpactAssessmentDashboard />);
      
      const assessmentsTab = screen.getByText('Assessments');
      await user.click(assessmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });

      const createButton = screen.getByRole('button', { name: /create assessment/i });
      await user.click(createButton);
      
      await user.type(screen.getByLabelText(/title/i), 'Test Assessment');
      await user.type(screen.getByLabelText(/description/i), 'Test description');
      
      const saveButton = screen.getByRole('button', { name: /create/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to create/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper heading structure', async () => {
      render(<ImpactAssessmentDashboard />);
      
      expect(screen.getByRole('heading', { level: 2, name: /impact assessments/i })).toBeInTheDocument();
    });

    it('supports keyboard navigation for tabs', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const overviewTab = screen.getByText('Overview');
      overviewTab.focus();
      expect(document.activeElement).toBe(overviewTab);
      
      await user.tab();
      const assessmentsTab = screen.getByText('Assessments');
      expect(document.activeElement).toBe(assessmentsTab);
    });

    it('has proper ARIA labels for interactive elements', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const createButton = screen.getByRole('button', { name: /create assessment/i });
      expect(createButton).toHaveAttribute('aria-label');
    });

    it('provides proper form labels and descriptions', async () => {
      render(<ImpactAssessmentDashboard />);
      
      const assessmentsTab = screen.getByText('Assessments');
      await user.click(assessmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Processing DPIA')).toBeInTheDocument();
      });

      const createButton = screen.getByRole('button', { name: /create assessment/i });
      await user.click(createButton);
      
      expect(screen.getByLabelText(/title/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/type/i)).toBeInTheDocument();
    });
  });
});
