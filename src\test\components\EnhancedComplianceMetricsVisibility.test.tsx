import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import EnhancedComplianceMetrics from '../../components/compliance/EnhancedComplianceMetricsOptimized';

// Mock the data service
vi.mock('../../components/compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: () => ({
    overview: {
      totalMetrics: 25,
      compliantMetrics: 20,
      warningMetrics: 3,
      criticalMetrics: 2,
      overallScore: 85,
      trendDirection: 'up',
      lastAssessment: new Date(),
      nextAssessment: new Date()
    },
    metrics: [
      {
        id: 'metric-1',
        name: 'Data Retention Policy',
        category: 'privacy',
        status: 'compliant',
        currentValue: 95,
        targetValue: 90,
        trend: 'up',
        lastUpdated: new Date()
      }
    ],
    frameworks: [
      {
        type: 'gdpr',
        name: 'GDPR',
        overallScore: 94.2,
        departments: [
          {
            id: 'dept-1',
            name: 'Human Resources',
            description: 'HR Department',
            complianceScore: 92,
            riskLevel: 'low',
            dataSubjectsCount: 150,
            violations: 0,
            trend: 'up',
            lastAuditDate: new Date(),
            nextAuditDate: new Date(),
            responsibleOfficer: 'John Doe',
            processingActivities: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        policies: [
          {
            id: 'policy-1',
            title: 'GDPR Data Protection Policy',
            description: 'Comprehensive data protection policy',
            category: 'data_protection',
            framework: 'gdpr',
            status: 'active',
            version: '2.1',
            effectiveDate: new Date(),
            reviewDate: new Date(),
            owner: 'Data Protection Officer',
            assignedDepartments: ['dept-1'],
            adherenceRate: 94.2,
            violations: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        timeline: [],
        metrics: {
          totalDepartments: 1,
          compliantDepartments: 1,
          totalPolicies: 1,
          activePolicies: 1,
          totalViolations: 0
        }
      }
    ]
  })
}));

describe('Enhanced Compliance Metrics Visibility Controls', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the component without crashing', async () => {
    render(<EnhancedComplianceMetrics />);
    
    await waitFor(() => {
      expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
    });
  });

  describe('Departments Visibility Control', () => {
    it('shows departments section by default', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });
    });

    it('toggles departments visibility when eye button is clicked', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });
      
      // Find and click the eye button
      const eyeButton = screen.getByTitle('Hide departments');
      await user.click(eyeButton);
      
      // Departments should be hidden
      await waitFor(() => {
        expect(screen.queryByText('Human Resources')).not.toBeInTheDocument();
      });
      
      // Click again to show
      const showButton = screen.getByTitle('Show departments');
      await user.click(showButton);
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });
    });

    it('maintains visibility state when switching tabs', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      // Hide departments
      const eyeButton = screen.getByTitle('Hide departments');
      await user.click(eyeButton);
      
      // Switch to overview tab
      const overviewTab = screen.getByText('Overview');
      await user.click(overviewTab);
      
      // Switch back to departments tab
      await user.click(departmentsTab);
      
      // Departments should still be hidden
      await waitFor(() => {
        expect(screen.queryByText('Human Resources')).not.toBeInTheDocument();
      });
    });
  });

  describe('Policies Visibility Control', () => {
    it('shows policies section by default', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to policies tab
      const policiesTab = screen.getByText('Policies');
      await user.click(policiesTab);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Data Protection Policy')).toBeInTheDocument();
      });
    });

    it('toggles policies visibility when eye button is clicked', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to policies tab
      const policiesTab = screen.getByText('Policies');
      await user.click(policiesTab);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Data Protection Policy')).toBeInTheDocument();
      });
      
      // Find and click the eye button
      const eyeButton = screen.getByTitle('Hide policies');
      await user.click(eyeButton);
      
      // Policies should be hidden
      await waitFor(() => {
        expect(screen.queryByText('GDPR Data Protection Policy')).not.toBeInTheDocument();
      });
    });
  });

  describe('Metrics Visibility Control', () => {
    it('shows metrics section by default', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to metrics tab
      const metricsTab = screen.getByText('Metrics');
      await user.click(metricsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Retention Policy')).toBeInTheDocument();
      });
    });

    it('toggles metrics visibility when eye button is clicked', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to metrics tab
      const metricsTab = screen.getByText('Metrics');
      await user.click(metricsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Retention Policy')).toBeInTheDocument();
      });
      
      // Find and click the eye button
      const eyeButton = screen.getByTitle('Hide metrics');
      await user.click(eyeButton);
      
      // Metrics should be hidden
      await waitFor(() => {
        expect(screen.queryByText('Data Retention Policy')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles eye button clicks without errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      // Click eye button multiple times
      const eyeButton = screen.getByTitle('Hide departments');
      await user.click(eyeButton);
      await user.click(screen.getByTitle('Show departments'));
      await user.click(screen.getByTitle('Hide departments'));
      
      // No console errors should occur
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('maintains functionality when data is missing', async () => {
      // This test would require mocking the data service to return incomplete data
      // For now, we'll just ensure the component doesn't crash
      render(<EnhancedComplianceMetrics />);
      
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('provides proper ARIA labels for eye buttons', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      const eyeButton = screen.getByLabelText('Hide departments');
      expect(eyeButton).toBeInTheDocument();
      
      await user.click(eyeButton);
      
      const showButton = screen.getByLabelText('Show departments');
      expect(showButton).toBeInTheDocument();
    });

    it('supports keyboard navigation for eye buttons', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Navigate to departments tab
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      const eyeButton = screen.getByTitle('Hide departments');
      
      // Focus and activate with keyboard
      eyeButton.focus();
      fireEvent.keyDown(eyeButton, { key: 'Enter' });
      
      await waitFor(() => {
        expect(screen.queryByText('Human Resources')).not.toBeInTheDocument();
      });
    });
  });
});
