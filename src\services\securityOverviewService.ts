// Security Overview Service - Comprehensive security monitoring and incident management
import { SecurityIncident, ThreatDetection, VulnerabilityAssessment, SecurityMetrics } from '../types/security';

export class SecurityOverviewService {
  private static initialized = false;
  private static securityIncidents: SecurityIncident[] = [];
  private static threatDetections: ThreatDetection[] = [];
  private static vulnerabilityAssessments: VulnerabilityAssessment[] = [];

  static initialize() {
    if (this.initialized) return;
    
    try {
      this.securityIncidents = this.generateMockSecurityIncidents();
      this.threatDetections = this.generateMockThreatDetections();
      this.vulnerabilityAssessments = this.generateMockVulnerabilityAssessments();
      this.initialized = true;
      console.log('✅ SecurityOverviewService initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing SecurityOverviewService:', error);
      this.securityIncidents = [];
      this.threatDetections = [];
      this.vulnerabilityAssessments = [];
      this.initialized = true;
    }
  }

  // Security Incident Management
  static async getAllSecurityIncidents(): Promise<SecurityIncident[]> {
    this.initialize();
    return [...this.securityIncidents];
  }

  static async getSecurityIncidentById(id: string): Promise<SecurityIncident | null> {
    this.initialize();
    return this.securityIncidents.find(incident => incident.id === id) || null;
  }

  static async createSecurityIncident(incidentData: Omit<SecurityIncident, 'id' | 'createdAt' | 'updatedAt'>): Promise<SecurityIncident> {
    this.initialize();
    
    const newIncident: SecurityIncident = {
      ...incidentData,
      id: `incident-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.securityIncidents.push(newIncident);
    return newIncident;
  }

  static async updateSecurityIncident(id: string, updates: Partial<SecurityIncident>): Promise<SecurityIncident | null> {
    this.initialize();
    
    const index = this.securityIncidents.findIndex(incident => incident.id === id);
    if (index === -1) return null;

    this.securityIncidents[index] = {
      ...this.securityIncidents[index],
      ...updates,
      updatedAt: new Date()
    };

    return this.securityIncidents[index];
  }

  static async deleteSecurityIncident(id: string): Promise<boolean> {
    this.initialize();
    
    const index = this.securityIncidents.findIndex(incident => incident.id === id);
    if (index === -1) return false;

    this.securityIncidents.splice(index, 1);
    return true;
  }

  // Threat Detection Management
  static async getAllThreatDetections(): Promise<ThreatDetection[]> {
    this.initialize();
    return [...this.threatDetections];
  }

  static async getActiveThreatDetections(): Promise<ThreatDetection[]> {
    this.initialize();
    return this.threatDetections.filter(threat => threat.status === 'active');
  }

  // Vulnerability Assessment Management
  static async getAllVulnerabilityAssessments(): Promise<VulnerabilityAssessment[]> {
    this.initialize();
    return [...this.vulnerabilityAssessments];
  }

  static async getCriticalVulnerabilities(): Promise<VulnerabilityAssessment[]> {
    this.initialize();
    return this.vulnerabilityAssessments.filter(vuln => vuln.severity === 'critical');
  }

  // Security Analytics and Metrics
  static async getSecurityMetrics(): Promise<SecurityMetrics> {
    this.initialize();

    const totalIncidents = this.securityIncidents.length;
    const openIncidents = this.securityIncidents.filter(incident => incident.status === 'open').length;
    const resolvedIncidents = this.securityIncidents.filter(incident => incident.status === 'resolved').length;
    const criticalIncidents = this.securityIncidents.filter(incident => incident.severity === 'critical').length;

    const totalThreats = this.threatDetections.length;
    const activeThreats = this.threatDetections.filter(threat => threat.status === 'active').length;
    const mitigatedThreats = this.threatDetections.filter(threat => threat.status === 'mitigated').length;

    const totalVulnerabilities = this.vulnerabilityAssessments.length;
    const criticalVulnerabilities = this.vulnerabilityAssessments.filter(vuln => vuln.severity === 'critical').length;
    const highVulnerabilities = this.vulnerabilityAssessments.filter(vuln => vuln.severity === 'high').length;

    // Calculate average resolution time
    const resolvedIncidentsWithTime = this.securityIncidents.filter(incident => 
      incident.status === 'resolved' && incident.resolvedAt
    );
    const avgResolutionTime = resolvedIncidentsWithTime.length > 0 
      ? resolvedIncidentsWithTime.reduce((sum, incident) => {
          const resolutionTime = incident.resolvedAt!.getTime() - incident.createdAt.getTime();
          return sum + resolutionTime;
        }, 0) / resolvedIncidentsWithTime.length / (1000 * 60 * 60) // Convert to hours
      : 0;

    // Calculate security score
    const securityScore = this.calculateSecurityScore();

    return {
      totalIncidents,
      openIncidents,
      resolvedIncidents,
      criticalIncidents,
      totalThreats,
      activeThreats,
      mitigatedThreats,
      totalVulnerabilities,
      criticalVulnerabilities,
      highVulnerabilities,
      averageResolutionTime: Math.round(avgResolutionTime * 100) / 100,
      securityScore,
      lastUpdated: new Date()
    };
  }

  private static calculateSecurityScore(): number {
    const totalIncidents = this.securityIncidents.length;
    const openIncidents = this.securityIncidents.filter(incident => incident.status === 'open').length;
    const criticalVulns = this.vulnerabilityAssessments.filter(vuln => vuln.severity === 'critical').length;
    const activeThreats = this.threatDetections.filter(threat => threat.status === 'active').length;

    // Base score of 100, deduct points for security issues
    let score = 100;
    score -= (openIncidents / Math.max(totalIncidents, 1)) * 30; // Max 30 points for incidents
    score -= Math.min(criticalVulns * 5, 40); // Max 40 points for vulnerabilities
    score -= Math.min(activeThreats * 3, 30); // Max 30 points for threats

    return Math.max(Math.round(score), 0);
  }

  // Mock Data Generation
  private static generateMockSecurityIncidents(): SecurityIncident[] {
    return [
      {
        id: 'incident-001',
        title: 'Suspicious Login Activity Detected',
        description: 'Multiple failed login attempts from unusual geographic locations',
        severity: 'medium',
        status: 'investigating',
        category: 'authentication',
        source: 'SIEM',
        assignedTo: '<EMAIL>',
        createdAt: new Date('2024-02-15T10:30:00Z'),
        updatedAt: new Date('2024-02-15T14:20:00Z'),
        affectedSystems: ['login-service', 'user-database'],
        indicators: ['unusual-geo-location', 'brute-force-pattern'],
        timeline: [
          {
            timestamp: new Date('2024-02-15T10:30:00Z'),
            action: 'Incident detected by SIEM',
            user: 'system'
          },
          {
            timestamp: new Date('2024-02-15T11:00:00Z'),
            action: 'Assigned to security team',
            user: '<EMAIL>'
          }
        ]
      },
      {
        id: 'incident-002',
        title: 'Malware Detection on Endpoint',
        description: 'Trojan malware detected on employee workstation',
        severity: 'high',
        status: 'contained',
        category: 'malware',
        source: 'EDR',
        assignedTo: '<EMAIL>',
        createdAt: new Date('2024-02-14T09:15:00Z'),
        updatedAt: new Date('2024-02-14T16:45:00Z'),
        affectedSystems: ['workstation-ws001'],
        indicators: ['trojan-signature', 'suspicious-network-traffic'],
        timeline: [
          {
            timestamp: new Date('2024-02-14T09:15:00Z'),
            action: 'Malware detected by EDR',
            user: 'system'
          },
          {
            timestamp: new Date('2024-02-14T09:30:00Z'),
            action: 'Workstation isolated from network',
            user: '<EMAIL>'
          },
          {
            timestamp: new Date('2024-02-14T16:45:00Z'),
            action: 'Malware removed, system cleaned',
            user: '<EMAIL>'
          }
        ]
      }
    ];
  }

  private static generateMockThreatDetections(): ThreatDetection[] {
    return [
      {
        id: 'threat-001',
        name: 'Advanced Persistent Threat Campaign',
        description: 'Coordinated attack targeting financial data',
        severity: 'critical',
        status: 'active',
        source: 'threat-intelligence',
        detectedAt: new Date('2024-02-16T08:00:00Z'),
        indicators: ['apt-signature', 'c2-communication'],
        affectedAssets: ['financial-database', 'payment-gateway'],
        mitigationActions: [
          'Enhanced monitoring activated',
          'Network segmentation implemented',
          'Threat hunting initiated'
        ]
      }
    ];
  }

  private static generateMockVulnerabilityAssessments(): VulnerabilityAssessment[] {
    return [
      {
        id: 'vuln-001',
        title: 'Critical SQL Injection Vulnerability',
        description: 'SQL injection vulnerability in customer portal',
        severity: 'critical',
        cvssScore: 9.8,
        status: 'open',
        discoveredAt: new Date('2024-02-10T12:00:00Z'),
        affectedSystems: ['customer-portal'],
        remediation: 'Apply security patch v2.1.3',
        dueDate: new Date('2024-02-20T23:59:59Z')
      },
      {
        id: 'vuln-002',
        title: 'Outdated SSL/TLS Configuration',
        description: 'Web server using deprecated TLS 1.0 protocol',
        severity: 'medium',
        cvssScore: 5.3,
        status: 'in_progress',
        discoveredAt: new Date('2024-02-08T14:30:00Z'),
        affectedSystems: ['web-server-01'],
        remediation: 'Update TLS configuration to use TLS 1.2+',
        dueDate: new Date('2024-03-01T23:59:59Z')
      }
    ];
  }
}
