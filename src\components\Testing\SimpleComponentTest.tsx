import React, { useState, Suspense } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Test the components with lazy loading exactly like the EnterpriseDashboard does
const EnhancedComplianceMetrics = React.lazy(() => import('../compliance/EnhancedComplianceMetrics'));
const SecurityOverviewDashboard = React.lazy(() => import('../Security/SecurityOverviewDashboard'));

export const SimpleComponentTest: React.FC = () => {
  const [activeComponent, setActiveComponent] = useState<'none' | 'compliance' | 'security'>('none');

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Simple Component Test</h1>
          <p className="text-text-secondary mb-6">
            This test loads components exactly like the Enterprise Dashboard does with lazy loading.
          </p>
          
          <div className="flex gap-4 mb-8">
            <button
              onClick={() => setActiveComponent('compliance')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeComponent === 'compliance'
                  ? 'bg-primary text-white'
                  : 'bg-surface text-text border border-border hover:bg-card'
              }`}
            >
              Load Enhanced Compliance Metrics
            </button>
            
            <button
              onClick={() => setActiveComponent('security')}
              className={`px-6 py-3 rounded-lg font-medium transition-colors ${
                activeComponent === 'security'
                  ? 'bg-primary text-white'
                  : 'bg-surface text-text border border-border hover:bg-card'
              }`}
            >
              Load Security Overview Dashboard
            </button>
            
            <button
              onClick={() => setActiveComponent('none')}
              className="px-6 py-3 rounded-lg font-medium bg-gray-500 text-white hover:bg-gray-600 transition-colors"
            >
              Clear
            </button>
          </div>
        </div>

        {activeComponent !== 'none' && (
          <div className="bg-surface rounded-lg border border-border overflow-hidden">
            <div className="p-4 border-b border-border bg-card">
              <h2 className="text-lg font-semibold text-text">
                {activeComponent === 'compliance' ? 'Enhanced Compliance Metrics' : 'Security Overview Dashboard'}
              </h2>
              <p className="text-sm text-text-secondary mt-1">
                Component loaded with lazy loading and error boundaries
              </p>
            </div>
            
            <div className="p-6">
              <ErrorBoundary
                type="section"
                fallbackTitle={`${activeComponent === 'compliance' ? 'Enhanced Compliance Metrics' : 'Security Overview Dashboard'} Error`}
                fallbackMessage="This component encountered an error while loading or rendering."
                onError={(error, errorInfo) => {
                  console.error(`${activeComponent} Component Error:`, error);
                  console.error('Error Info:', errorInfo);
                }}
              >
                <Suspense
                  fallback={
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-secondary">
                          Loading {activeComponent === 'compliance' ? 'Enhanced Compliance Metrics' : 'Security Overview Dashboard'}...
                        </p>
                      </div>
                    </div>
                  }
                >
                  {activeComponent === 'compliance' && (
                    <div className="max-h-[600px] overflow-auto">
                      <EnhancedComplianceMetrics />
                    </div>
                  )}
                  
                  {activeComponent === 'security' && (
                    <div className="max-h-[600px] overflow-auto">
                      <SecurityOverviewDashboard />
                    </div>
                  )}
                </Suspense>
              </ErrorBoundary>
            </div>
          </div>
        )}

        {activeComponent === 'none' && (
          <div className="bg-surface rounded-lg p-8 border border-border text-center">
            <h2 className="text-xl font-semibold text-text mb-4">Ready to Test</h2>
            <p className="text-text-secondary mb-6">
              Click one of the buttons above to load and test a component.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-w-2xl mx-auto">
              <div className="p-4 bg-card rounded-lg border border-border">
                <h3 className="font-semibold text-text mb-2">Enhanced Compliance Metrics</h3>
                <p className="text-sm text-text-secondary">
                  Comprehensive compliance dashboard with metrics, charts, and CRUD operations
                </p>
              </div>
              <div className="p-4 bg-card rounded-lg border border-border">
                <h3 className="font-semibold text-text mb-2">Security Overview Dashboard</h3>
                <p className="text-sm text-text-secondary">
                  Security monitoring dashboard with threat intelligence and incident management
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 bg-surface rounded-lg p-6 border border-border">
          <h2 className="text-lg font-semibold text-text mb-4">Test Instructions</h2>
          <div className="space-y-3 text-text-secondary">
            <p>• Click a button above to load the corresponding component</p>
            <p>• Watch for loading indicators and any error messages</p>
            <p>• Check the browser console for detailed error information</p>
            <p>• Components should display actual data, not fallback messages</p>
            <p>• Interactive features like charts and buttons should work properly</p>
          </div>
        </div>

        <div className="mt-6 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
            Expected Behavior
          </h3>
          <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
            <p>✅ Components should load without showing fallback error messages</p>
            <p>✅ Charts and visualizations should render properly</p>
            <p>✅ Interactive elements should be functional</p>
            <p>✅ Data should be realistic and comprehensive</p>
            <p>❌ No "component not available" messages should appear</p>
            <p>❌ No crashes or white screens should occur</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleComponentTest;
