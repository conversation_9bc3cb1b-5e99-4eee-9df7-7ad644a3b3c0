import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import App from '../../App';

// Mock all the heavy components to speed up tests
vi.mock('../../components/Dashboard/EnterpriseDashboard', () => ({
  default: () => <div data-testid="enterprise-dashboard">Enterprise Dashboard</div>
}));

vi.mock('../../components/Dashboard/Dashboard', () => ({
  default: () => <div data-testid="privacy-dashboard">Privacy Dashboard</div>
}));

vi.mock('../../components/GdprAnalysis/GdprAnalysisPage', () => ({
  default: () => <div data-testid="gdpr-analysis">GDPR Analysis</div>
}));

vi.mock('../../components/ComplianceRules/ComplianceRulesDashboard', () => ({
  default: () => <div data-testid="compliance-rules">Compliance Rules</div>
}));

vi.mock('../../components/ComplianceReports', () => ({
  default: () => <div data-testid="compliance-reports">Compliance Reports</div>
}));

vi.mock('../../components/Configurations', () => ({
  default: () => <div data-testid="configurations">Configurations</div>
}));

vi.mock('../../components/ConsumerInterface', () => ({
  default: () => <div data-testid="consumer-interface">Consumer Interface</div>
}));

vi.mock('../../components/Home', () => ({
  default: () => <div data-testid="home">Home</div>
}));

describe('Navigation Smoke Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the application without crashing', async () => {
    render(<App />);
    
    // Should render the sidebar
    expect(screen.getByText('Main Navigation')).toBeInTheDocument();
    
    // Should render the home page by default
    await waitFor(() => {
      expect(screen.getByTestId('home')).toBeInTheDocument();
    });
  });

  it('navigates to Enterprise Dashboard', async () => {
    render(<App />);
    
    const enterpriseButton = screen.getByText('Enterprise Dashboard');
    await user.click(enterpriseButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
    });
  });

  it('navigates to Privacy Dashboard', async () => {
    render(<App />);
    
    const privacyButton = screen.getByText('Privacy Dashboard');
    await user.click(privacyButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('privacy-dashboard')).toBeInTheDocument();
    });
  });

  it('navigates to GDPR Analysis', async () => {
    render(<App />);
    
    const gdprButton = screen.getByText('GDPR Analysis');
    await user.click(gdprButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('gdpr-analysis')).toBeInTheDocument();
    });
  });

  it('navigates to Compliance Rules', async () => {
    render(<App />);
    
    const rulesButton = screen.getByText('Compliance Rules');
    await user.click(rulesButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('compliance-rules')).toBeInTheDocument();
    });
  });

  it('navigates to Compliance Reports', async () => {
    render(<App />);
    
    const reportsButton = screen.getByText('Compliance Reports');
    await user.click(reportsButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('compliance-reports')).toBeInTheDocument();
    });
  });

  it('navigates to Configurations', async () => {
    render(<App />);
    
    const configButton = screen.getByText('Configurations');
    await user.click(configButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('configurations')).toBeInTheDocument();
    });
  });

  it('navigates to Consumer Interface', async () => {
    render(<App />);
    
    const consumerButton = screen.getByText('Consumer Interface');
    await user.click(consumerButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('consumer-interface')).toBeInTheDocument();
    });
  });

  it('navigates back to Home', async () => {
    render(<App />);
    
    // Navigate to another page first
    const enterpriseButton = screen.getByText('Enterprise Dashboard');
    await user.click(enterpriseButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
    });
    
    // Navigate back to home
    const homeButton = screen.getByText('Home');
    await user.click(homeButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('home')).toBeInTheDocument();
    });
  });

  it('displays all expected navigation items', () => {
    render(<App />);
    
    // Check that all navigation items are present
    expect(screen.getByText('Home')).toBeInTheDocument();
    expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Privacy Dashboard')).toBeInTheDocument();
    expect(screen.getByText('GDPR Analysis')).toBeInTheDocument();
    expect(screen.getByText('Compliance Rules')).toBeInTheDocument();
    expect(screen.getByText('Compliance Reports')).toBeInTheDocument();
    expect(screen.getByText('Configurations')).toBeInTheDocument();
    expect(screen.getByText('Consumer Interface')).toBeInTheDocument();
  });

  it('highlights active navigation item', async () => {
    render(<App />);
    
    const enterpriseButton = screen.getByText('Enterprise Dashboard');
    await user.click(enterpriseButton);
    
    // Check that the enterprise button has active styling
    // This would depend on the specific CSS classes used
    const activeButton = screen.getByText('Enterprise Dashboard').closest('a');
    expect(activeButton).toHaveClass('text-primary');
  });
});
