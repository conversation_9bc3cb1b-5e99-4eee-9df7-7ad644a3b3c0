import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { PoliciesManagementService, Policy, PolicyMetrics } from '../../services/policiesManagementService';
import { Modal, ConfirmationModal } from '../ui/Modal';
import {
  FileText,
  Shield,
  Users,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Clock,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  Eye,
  Save,
  RefreshCw,
  Download,
  Upload,
  GitBranch,
  UserCheck,
  X,
  Settings,
  Archive,
  Star,
  Tag
} from 'lucide-react';

// Types are now imported from the service

interface PoliciesManagementDashboardProps {
  className?: string;
}

export const PoliciesManagementDashboard: React.FC<PoliciesManagementDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  // State management
  const [policies, setPolicies] = useState<Policy[]>([]);
  const [filteredPolicies, setFilteredPolicies] = useState<Policy[]>([]);
  const [metrics, setMetrics] = useState<PolicyMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: [] as string[],
    category: [] as string[],
    type: [] as string[]
  });
  const [activeView, setActiveView] = useState<'overview' | 'policies' | 'templates' | 'approvals'>('overview');

  // CRUD Modal States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<Policy | null>(null);
  const [editingPolicy, setEditingPolicy] = useState<Policy | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    category: 'privacy' as const,
    type: 'policy' as const,
    owner: '',
    effectiveDate: '',
    nextReviewDate: '',
    expiryDate: '',
    tags: [] as string[],
    regulations: [] as string[],
    applicableRoles: [] as string[],
    isTemplate: false,
    templateCategory: ''
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [policies, searchTerm, selectedFilters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the actual service instead of mock data
      const [policiesData, metricsData] = await Promise.all([
        PoliciesManagementService.getAllPolicies(),
        PoliciesManagementService.getMetrics()
      ]);

      setPolicies(policiesData);
      setMetrics(metricsData);

      notifications.addNotification({
        type: 'success',
        title: 'Data Loaded',
        message: 'Policies data loaded successfully.'
      });
    } catch (error) {
      console.error('Error loading policies data:', error);
      setError('Failed to load policies data. Please try again.');
      notifications.addNotification({
        type: 'error',
        title: 'Error Loading Data',
        message: 'Failed to load policies data. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };


  const applyFilters = () => {
    let filtered = policies;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(policy =>
        policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.owner.toLowerCase().includes(searchTerm.toLowerCase()) ||
        policy.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      );
    }

    // Apply status filter
    if (selectedFilters.status.length > 0) {
      filtered = filtered.filter(policy => selectedFilters.status.includes(policy.status));
    }

    // Apply category filter
    if (selectedFilters.category.length > 0) {
      filtered = filtered.filter(policy => selectedFilters.category.includes(policy.category));
    }

    // Apply type filter
    if (selectedFilters.type.length > 0) {
      filtered = filtered.filter(policy => selectedFilters.type.includes(policy.type));
    }

    setFilteredPolicies(filtered);
  };

  // CRUD Operations
  const handleCreatePolicy = async () => {
    try {
      const policyData = {
        ...formData,
        effectiveDate: formData.effectiveDate ? new Date(formData.effectiveDate) : new Date(),
        nextReviewDate: formData.nextReviewDate ? new Date(formData.nextReviewDate) : new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
        expiryDate: formData.expiryDate ? new Date(formData.expiryDate) : undefined,
        createdBy: 'Current User' // Replace with actual user
      };

      const newPolicy = await PoliciesManagementService.createPolicy(policyData);

      setPolicies(prev => [newPolicy, ...prev]);
      setShowCreateModal(false);
      resetForm();

      // Refresh metrics
      const updatedMetrics = await PoliciesManagementService.getMetrics();
      setMetrics(updatedMetrics);

      notifications.addNotification({
        type: 'success',
        title: 'Policy Created',
        message: 'Policy has been created successfully.'
      });
    } catch (error) {
      console.error('Error creating policy:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Creating Policy',
        message: 'Failed to create policy. Please try again.'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      category: 'privacy',
      type: 'policy',
      owner: '',
      effectiveDate: '',
      nextReviewDate: '',
      expiryDate: '',
      tags: [],
      regulations: [],
      applicableRoles: [],
      isTemplate: false,
      templateCategory: ''
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'published': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'approved': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'review': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'draft': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
      case 'archived': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'privacy': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'security': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'compliance': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'operational': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'hr': return 'text-purple-600 bg-purple-100 dark:bg-purple-900/30';
      case 'legal': return 'text-indigo-600 bg-indigo-100 dark:bg-indigo-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'privacy': return Shield;
      case 'security': return FileText;
      case 'compliance': return CheckCircle;
      case 'operational': return Settings;
      case 'hr': return Users;
      default: return FileText;
    }
  };

  if (loading) {
    return (
      <div className={`${className} space-y-6`}>
        <div className="animate-pulse">
          <div className="h-8 bg-border rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-24 bg-border rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-border rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Policies Management Error</h2>
            <p className="text-text-secondary">Something went wrong loading the policies management dashboard. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <FileText className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Policies Management</h2>
            <p className="text-text-secondary">Manage organizational policies, templates, and approval workflows</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="secondary" onClick={loadData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="secondary">
            <Upload className="w-4 h-4 mr-2" />
            Import
          </Button>
          <Button variant="primary" onClick={openCreateModal}>
            <Plus className="w-4 h-4 mr-2" />
            New Policy
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-border">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: FileText },
            { id: 'policies', label: 'Policies', icon: Shield },
            { id: 'templates', label: 'Templates', icon: Star },
            { id: 'approvals', label: 'Approvals', icon: UserCheck }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 ${
                  activeView === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-text-secondary hover:text-text hover:border-border'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeView === 'overview' && metrics && (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                  <FileText className="w-6 h-6 text-primary" />
                </div>
                <span className="text-xs text-green-500 font-medium">+12%</span>
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalPolicies}</div>
              <div className="text-text-secondary text-sm">Total Policies</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-yellow-500/10'} rounded-lg`}>
                  <Clock className="w-6 h-6 text-yellow-500" />
                </div>
                <span className="text-xs text-yellow-500 font-medium">Urgent</span>
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.policiesNeedingReview}</div>
              <div className="text-text-secondary text-sm">Pending Reviews</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-orange-500/10'} rounded-lg`}>
                  <AlertTriangle className="w-6 h-6 text-orange-500" />
                </div>
                <span className="text-xs text-orange-500 font-medium">Review</span>
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.expiredPolicies}</div>
              <div className="text-text-secondary text-sm">Expired Policies</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-green-500/10'} rounded-lg`}>
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
                <span className="text-xs text-green-500 font-medium">Good</span>
              </div>
              <div className="text-3xl font-bold text-text mb-2">{Math.round(metrics.averageComplianceScore)}%</div>
              <div className="text-text-secondary text-sm">Avg Compliance Score</div>
            </div>
          </div>

          {/* Charts and Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Policy Status Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getStatusColor(status)} text-xs font-bold rounded-full uppercase`}>
                        {status}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${metrics.totalPolicies ? ((count as number / metrics.totalPolicies) * 100) : 0}%`}}
                        />
                      </div>
                      <span className="text-sm font-medium text-text w-8">{count as number}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Policy Categories</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byCategory).map(([category, count]) => {
                  const Icon = getCategoryIcon(category);
                  return (
                    <div key={category} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                          <Icon className="w-4 h-4 text-primary" />
                        </div>
                        <span className="text-text capitalize">{category}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-24 bg-border rounded-full h-2">
                          <div
                            className="bg-secondary h-2 rounded-full transition-all duration-500"
                            style={{width: `${metrics.totalPolicies ? ((count as number / metrics.totalPolicies) * 100) : 0}%`}}
                          />
                        </div>
                        <span className="text-sm font-medium text-text w-8">{count as number}</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Policies Tab */}
      {activeView === 'policies' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary w-4 h-4" />
                <FormInput
                  type="text"
                  placeholder="Search policies..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
              <Button variant="secondary">
                <Download className="w-4 h-4 mr-2" />
                Export
              </Button>
            </div>
          </div>

          {/* Policies List */}
          <div className="space-y-4">
            {filteredPolicies.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text mb-2">No policies found</h3>
                <p className="text-text-secondary">Try adjusting your search or filters</p>
              </div>
            ) : (
              filteredPolicies.map((policy) => {
                const CategoryIcon = getCategoryIcon(policy.category);
                return (
                  <div
                    key={policy.id}
                    className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-primary/30 hover:shadow-lg'} rounded-xl transition-all duration-300 cursor-pointer`}
                    onClick={() => setSelectedPolicy(policy)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-xl`}>
                          <CategoryIcon className="w-6 h-6 text-primary" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-text">{policy.name}</h3>
                            <span className={`px-3 py-1 ${getStatusColor(policy.status)} text-xs font-bold rounded-full uppercase`}>
                              {policy.status}
                            </span>
                            <span className={`px-3 py-1 ${getCategoryColor(policy.category)} text-xs font-bold rounded-full uppercase`}>
                              {policy.category}
                            </span>
                            <span className="px-3 py-1 bg-border text-text text-xs font-medium rounded-full">
                              v{policy.version}
                            </span>
                          </div>

                          <p className="text-text-secondary text-sm mb-3 line-clamp-2">{policy.description}</p>

                          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Users className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">Owner: {policy.owner}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">Effective: {policy.effectiveDate.toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">Review: {policy.reviewDate.toLocaleDateString()}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Tag className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">{policy.tags.length} tags</span>
                            </div>
                          </div>

                          {policy.tags.length > 0 && (
                            <div className="mt-3 flex flex-wrap gap-2">
                              {policy.tags.slice(0, 3).map((tag, index) => (
                                <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                                  {tag}
                                </span>
                              ))}
                              {policy.tags.length > 3 && (
                                <span className="px-2 py-1 bg-border text-text-secondary text-xs rounded-full">
                                  +{policy.tags.length - 3} more
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <IconButton
                          icon={Eye}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedPolicy(policy);
                            setShowViewModal(true);
                          }}
                          variant="primary"
                          size="sm"
                          tooltip="View Details"
                        />
                        <IconButton
                          icon={Edit}
                          onClick={(e) => {
                            e.stopPropagation();
                            setEditingPolicy(policy);
                            setShowEditModal(true);
                          }}
                          variant="secondary"
                          size="sm"
                          tooltip="Edit Policy"
                        />
                        <IconButton
                          icon={Trash2}
                          onClick={(e) => {
                            e.stopPropagation();
                            setSelectedPolicy(policy);
                            setShowDeleteModal(true);
                          }}
                          variant="danger"
                          size="sm"
                          tooltip="Delete Policy"
                        />
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeView === 'templates' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <Star className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Policy Templates</h3>
            <p className="text-text-secondary">Template management coming soon</p>
          </div>
        </div>
      )}

      {/* Approvals Tab */}
      {activeView === 'approvals' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <UserCheck className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Approval Workflows</h3>
            <p className="text-text-secondary">Approval management coming soon</p>
          </div>
        </div>
      )}

      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
      </div>
    </ErrorBoundary>
  );
};

export default PoliciesManagementDashboard;
