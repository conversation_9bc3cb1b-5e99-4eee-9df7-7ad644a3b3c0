import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Chart } from 'chart.js/auto';
import { useCompliance } from '../context/ComplianceContext';
import { useTheme } from '../context/ThemeContext';
import { getChartColors, getChartTheme } from '../utils/chartOptimizations';
import { ComplianceErrorBoundary } from './compliance/ComplianceErrorBoundary';
import { BarChart3, AlertTriangle, TrendingUp } from 'lucide-react';

// Type guard for monthly data validation
const isValidMonthlyData = (data: any): boolean => {
  return (
    data &&
    typeof data === 'object' &&
    Array.isArray(data.high) &&
    Array.isArray(data.medium) &&
    Array.isArray(data.low) &&
    data.high.length === 12 &&
    data.medium.length === 12 &&
    data.low.length === 12 &&
    data.high.every((val: any) => typeof val === 'number' && !isNaN(val) && val >= 0 && val <= 100) &&
    data.medium.every((val: any) => typeof val === 'number' && !isNaN(val) && val >= 0 && val <= 100) &&
    data.low.every((val: any) => typeof val === 'number' && !isNaN(val) && val >= 0 && val <= 100)
  );
};

// Validate and sanitize monthly data
const validateMonthlyData = (data: any) => {
  if (!isValidMonthlyData(data)) {
    console.warn('PolicyDistributionChart: Invalid monthly data, using fallback');
    return {
      high: [72, 73, 74, 73, 74, 73, 74, 73, 74, 73, 74, 73],
      medium: [18, 17, 16, 17, 16, 17, 16, 17, 16, 17, 16, 17],
      low: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10]
    };
  }
  return data;
};

const PolicyDistributionChartContent: React.FC = () => {
  const { metrics, fetchData, isLoading } = useCompliance();
  const { mode } = useTheme();
  const isDark = mode === 'dark';
  const [error, setError] = useState<string | null>(null);
  const [chartError, setChartError] = useState<string | null>(null);

  // Safe color and theme access with fallbacks
  const colors = React.useMemo(() => {
    try {
      return getChartColors(isDark);
    } catch (err) {
      console.error('Error getting chart colors:', err);
      return {
        compliant: '#10b981',
        pending: '#f59e0b',
        nonCompliant: '#ef4444'
      };
    }
  }, [isDark]);

  const theme = React.useMemo(() => {
    try {
      return getChartTheme(isDark);
    } catch (err) {
      console.error('Error getting chart theme:', err);
      return {
        textColor: isDark ? '#e5e7eb' : '#374151',
        gridColor: isDark ? 'rgba(255, 255, 255, 0.1)' : '#f1f5f9'
      };
    }
  }, [isDark]);

  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);

  // Initialize monthly data with validation
  const [monthlyData, setMonthlyData] = useState(() => {
    try {
      const initialData = {
        high: [72, 73, 74, 73, 74, 73, 74, 73, 74, 73, 74, 73],
        medium: [18, 17, 16, 17, 16, 17, 16, 17, 16, 17, 16, 17],
        low: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10]
      };
      return validateMonthlyData(initialData);
    } catch (err) {
      console.error('Error initializing monthly data:', err);
      setError('Failed to initialize chart data');
      return {
        high: [72, 73, 74, 73, 74, 73, 74, 73, 74, 73, 74, 73],
        medium: [18, 17, 16, 17, 16, 17, 16, 17, 16, 17, 16, 17],
        low: [10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10, 10]
      };
    }
  });

  // Data update effect with comprehensive error handling
  useEffect(() => {
    const updateData = () => {
      try {
        setMonthlyData(prev => {
          if (!isValidMonthlyData(prev)) {
            console.warn('Invalid previous data, using fallback');
            return validateMonthlyData(null);
          }

          const newData = {
            high: [...prev.high],
            medium: [...prev.medium],
            low: [...prev.low]
          };

          // April is index 3
          const aprilIndex = 3;
          const monthVariation = (Math.random() - 0.5) * 3;

          // Base values for April with bounds checking
          const baseHigh = Math.max(45, Math.min(65, 55 + (Math.random() - 0.5) * 10));
          const baseMedium = Math.max(15, Math.min(25, 20 + (Math.random() - 0.5) * 5));
          const baseLow = Math.max(20, Math.min(30, 25 + (Math.random() - 0.5) * 5));

          // Update only April values with bounds checking
          newData.high[aprilIndex] = Math.min(60, Math.max(50, baseHigh + monthVariation));
          newData.medium[aprilIndex] = Math.min(25, Math.max(15, baseMedium + monthVariation));
          newData.low[aprilIndex] = Math.min(30, Math.max(20, baseLow + monthVariation));

          // Normalize April to ensure total is 100%
          const total = newData.high[aprilIndex] + newData.medium[aprilIndex] + newData.low[aprilIndex];
          if (total > 0) {
            const factor = 100 / total;
            newData.high[aprilIndex] = Math.max(0, Math.min(100, newData.high[aprilIndex] * factor));
            newData.medium[aprilIndex] = Math.max(0, Math.min(100, newData.medium[aprilIndex] * factor));
            newData.low[aprilIndex] = Math.max(0, Math.min(100, newData.low[aprilIndex] * factor));
          }

          // Validate the updated data
          const validatedData = validateMonthlyData(newData);
          return validatedData;
        });
      } catch (err) {
        console.error('Error updating monthly data:', err);
        setError('Failed to update chart data');
      }
    };

    const interval = setInterval(updateData, 10000);
    return () => {
      try {
        clearInterval(interval);
      } catch (err) {
        console.error('Error clearing interval:', err);
      }
    };
  }, []);

  // Chart creation with comprehensive error handling
  useEffect(() => {
    if (!chartRef.current || isLoading || error || chartError) {
      return;
    }

    try {
      // Validate monthly data before creating chart
      if (!isValidMonthlyData(monthlyData)) {
        throw new Error('Invalid monthly data for chart creation');
      }

      // Cleanup existing chart instance
      if (chartInstance.current) {
        chartInstance.current.destroy();
        chartInstance.current = null;
      }

      const ctx = chartRef.current.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get 2D context from canvas');
      }

      // Validate colors object
      if (!colors || !colors.compliant || !colors.pending || !colors.nonCompliant) {
        throw new Error('Invalid chart colors configuration');
      }

      chartInstance.current = new Chart(ctx, {
        type: 'bar',
        data: {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          datasets: [
            {
              label: 'High Compliance',
              data: monthlyData.high,
              backgroundColor: colors.compliant + 'CC', // Add transparency
              borderColor: colors.compliant,
              borderWidth: isDark ? 1 : 0,
              barPercentage: 0.6,
              categoryPercentage: 0.8,
              stack: 'stack0'
            },
            {
              label: 'Medium Compliance',
              data: monthlyData.medium,
              backgroundColor: colors.pending + 'CC', // Add transparency
              borderColor: colors.pending,
              borderWidth: isDark ? 1 : 0,
              barPercentage: 0.6,
              categoryPercentage: 0.8,
              stack: 'stack0'
            },
            {
              label: 'Low Compliance',
              data: monthlyData.low,
              backgroundColor: colors.nonCompliant + 'CC', // Add transparency
              borderColor: colors.nonCompliant,
              borderWidth: isDark ? 1 : 0,
              barPercentage: 0.6,
              categoryPercentage: 0.8,
              stack: 'stack0'
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'top' as const,
              align: 'start' as const,
              labels: {
                usePointStyle: true,
                pointStyle: 'circle' as const,
                padding: 20,
                boxWidth: 8,
                boxHeight: 8,
                color: theme?.textColor || '#64748b',
                font: {
                  size: 12,
                  family: "'Inter', sans-serif"
                }
              }
            },
            tooltip: {
              mode: 'index' as const,
              intersect: false,
              backgroundColor: theme?.tooltipBg || (isDark ? '#1f2937' : '#ffffff'),
              titleColor: theme?.textColor || (isDark ? '#f9fafb' : '#111827'),
              bodyColor: theme?.textSecondary || (isDark ? '#e5e7eb' : '#374151'),
              borderColor: theme?.tooltipBorder || (isDark ? '#374151' : '#e5e7eb'),
              borderWidth: 1,
              padding: {
                x: 12,
                y: 8
              },
              bodyFont: {
                size: 12,
                family: "'Inter', sans-serif"
              },
              titleFont: {
                size: 13,
                family: "'Inter', sans-serif",
                weight: '600' as const
              },
              cornerRadius: 8,
              callbacks: {
                label: (context: any) => {
                  try {
                    return `${context.dataset.label}: ${context.parsed.y.toFixed(1)}%`;
                  } catch (err) {
                    console.error('Error in tooltip callback:', err);
                    return 'Data unavailable';
                  }
                }
              }
            },
            displayColors: true,
            boxWidth: 8,
            boxHeight: 8,
            usePointStyle: true
          },
        scales: {
          y: {
            stacked: true,
            beginAtZero: true,
            max: 120,
            grid: {
              color: theme.gridColor,
              display: false,
              lineWidth: 1
            },
            ticks: {
              stepSize: 20,
              padding: 10,
              color: theme.textSecondary,
              font: {
                size: 12,
                family: "'Inter', sans-serif"
              }
            },
            border: {
              display: false
            }
          },
          x: {
            stacked: true,
            grid: {
              display: false // Remove duplicate display property and drawBorder
            },
            ticks: {
              padding: 10,
              color: theme.textSecondary,
              font: {
                size: 12,
                family: "'Inter', sans-serif"
              }
            },
            border: {
              display: false
            }
          }
        }
      }
    });

      console.log('Policy distribution chart created successfully');
      setChartError(null);
    } catch (err) {
      console.error('Error creating policy distribution chart:', err);
      setChartError(err instanceof Error ? err.message : 'Failed to create chart');
    }

    // Cleanup function
    return () => {
      try {
        if (chartInstance.current) {
          chartInstance.current.destroy();
          chartInstance.current = null;
        }
      } catch (err) {
        console.error('Error cleaning up chart:', err);
      }
    };
  }, [monthlyData, colors, theme, isDark, isLoading, error, chartError]);

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
        <div className="animate-pulse">
          <div className="mb-6">
            <div className="h-6 bg-surface rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-surface rounded w-1/2"></div>
          </div>
          <div className="h-[350px] bg-surface rounded"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error || chartError) {
    const errorMessage = error || chartError;
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">Chart Unavailable</h3>
          <p className="text-text-secondary mb-4">{errorMessage}</p>
          <button
            onClick={() => {
              setError(null);
              setChartError(null);
              // Retry chart creation
              if (chartRef.current) {
                const event = new Event('resize');
                window.dispatchEvent(event);
              }
            }}
            className="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="w-5 h-5 text-primary" />
        <div>
          <h3 className="text-xl font-semibold text-text">Compliance Distribution</h3>
          <p className="text-sm text-text-secondary mt-1">Monthly compliance level breakdown</p>
        </div>
      </div>

      <div className="h-[350px] w-full relative">
        <canvas
          ref={chartRef}
          aria-label="Policy distribution chart showing monthly compliance levels"
          role="img"
        />

        {/* Loading overlay for chart updates */}
        {chartInstance.current === null && !error && !chartError && (
          <div className="absolute inset-0 flex items-center justify-center bg-card/80">
            <div className="text-center">
              <TrendingUp className="w-8 h-8 text-primary mx-auto mb-2 animate-pulse" />
              <p className="text-text-secondary text-sm">Updating chart...</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// Main component with error boundary
const PolicyDistributionChart: React.FC = () => {
  return (
    <ComplianceErrorBoundary
      componentName="PolicyDistributionChart"
      fallbackType="chart"
    >
      <PolicyDistributionChartContent />
    </ComplianceErrorBoundary>
  );
};

export default PolicyDistributionChart;