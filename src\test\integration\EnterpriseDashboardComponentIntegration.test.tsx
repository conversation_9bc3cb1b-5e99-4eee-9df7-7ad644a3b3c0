import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import EnterpriseDashboard from '../../components/Dashboard/EnterpriseDashboard';

// Mock all the services to prevent actual API calls
vi.mock('../../services/riskAssessmentService', () => ({
  RiskAssessmentService: {
    initialize: vi.fn(),
    getRiskMetrics: vi.fn().mockResolvedValue({
      totalRisks: 25,
      highRisks: 5,
      mediumRisks: 12,
      lowRisks: 8,
      overallRiskScore: 65
    })
  }
}));

vi.mock('../../services/regulatoryFrameworkService', () => ({
  RegulatoryFrameworkService: {
    initialize: vi.fn(),
    getFrameworkMetrics: vi.fn().mockResolvedValue({
      totalFrameworks: 8,
      activeFrameworks: 6,
      complianceScore: 92.5
    })
  }
}));

vi.mock('../../services/branchDetectionService', () => ({
  BranchDetectionService: {
    initialize: vi.fn(),
    getBranchMetrics: vi.fn().mockResolvedValue({
      totalBranches: 15,
      compliantBranches: 12,
      complianceScore: 80
    })
  }
}));

vi.mock('../../services/dataSubjectRequestService', () => ({
  DataSubjectRequestService: {
    initialize: vi.fn(),
    getRequestMetrics: vi.fn().mockResolvedValue({
      total: 45,
      pending: 8,
      completed: 35,
      overdue: 2
    })
  }
}));

vi.mock('../../services/consentManagementService', () => ({
  ConsentManagementService: {
    initialize: vi.fn(),
    getConsentMetrics: vi.fn().mockResolvedValue({
      totalUsers: 1000,
      consentedUsers: 850,
      consentRate: 85,
      categoryBreakdown: []
    })
  }
}));

vi.mock('../../services/impactAssessmentService', () => ({
  ImpactAssessmentService: {
    initialize: vi.fn(),
    getAssessmentMetrics: vi.fn().mockResolvedValue({
      total: 20,
      completed: 15,
      inProgress: 3,
      overdue: 2
    })
  }
}));

vi.mock('../../services/gdprService', () => ({
  GDPRService: {
    initialize: vi.fn(),
    getDashboardMetrics: vi.fn().mockResolvedValue({
      dataSubjectRequests: { total: 45, pending: 8, completed: 35, overdue: 2 },
      branchCompliance: { totalBranches: 15, compliantBranches: 12, complianceScore: 80 },
      consentManagement: { totalUsers: 1000, consentedUsers: 850, consentRate: 85 },
      impactAssessments: { total: 20, completed: 15, inProgress: 3, overdue: 2 }
    })
  }
}));

// Mock all the lazy-loaded components
vi.mock('../../components/compliance/EnhancedComplianceMetricsOptimized', () => ({
  default: () => <div data-testid="enhanced-compliance-metrics">Enhanced Compliance Metrics</div>
}));

vi.mock('../../components/GDPR/OrganizationalMappingDashboard', () => ({
  default: () => <div data-testid="organizational-mapping">Organizational Mapping Dashboard</div>
}));

vi.mock('../../components/GDPR/PoliciesManagementDashboard', () => ({
  default: () => <div data-testid="policies-management">Policies Management Dashboard</div>
}));

vi.mock('../../components/GDPR/ConsentManagementDashboard', () => ({
  default: () => <div data-testid="consent-management">Consent Management Dashboard</div>
}));

vi.mock('../../components/GDPR/ImpactAssessmentDashboard', () => ({
  default: () => <div data-testid="impact-assessment">Impact Assessment Dashboard</div>
}));

vi.mock('../../components/GDPR/GDPRCommandCenter', () => ({
  default: () => <div data-testid="gdpr-command-center">GDPR Command Center</div>
}));

vi.mock('../../components/Security/SecurityOverviewDashboardOptimized', () => ({
  default: () => <div data-testid="security-overview">Security Overview Dashboard</div>
}));

vi.mock('../../components/Risk/RiskAssessmentDashboard', () => ({
  default: () => <div data-testid="risk-assessment">Risk Assessment Dashboard</div>
}));

vi.mock('../../components/compliance/RegulatoryFrameworkDashboard', () => ({
  default: () => <div data-testid="regulatory-framework">Regulatory Framework Dashboard</div>
}));

vi.mock('../../components/SiteReliability/SiteReliabilityGuardian', () => ({
  default: () => <div data-testid="site-reliability">Site Reliability Guardian</div>
}));

vi.mock('../../components/SLO/ServiceLevelObjectivesDashboard', () => ({
  default: () => <div data-testid="slo-dashboard">SLO Dashboard</div>
}));

vi.mock('../../components/SLO/ServiceLevelObjectivesClassic', () => ({
  default: () => <div data-testid="slo-classic">SLO Classic</div>
}));

// Mock chart.js
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>
}));

describe('Enterprise Dashboard Component Integration', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders the Enterprise Dashboard without errors', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('displays the GDPR Command Center by default', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to Enhanced Compliance Metrics', async () => {
    render(<EnterpriseDashboard />);

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    // Click on Compliance Metrics tab
    const complianceTab = screen.getByText('Compliance Metrics');
    await user.click(complianceTab);

    await waitFor(() => {
      expect(screen.getByTestId('enhanced-compliance-metrics')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to Risk Assessment Dashboard', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const riskTab = screen.getByText('Risk Assessment');
    await user.click(riskTab);

    await waitFor(() => {
      expect(screen.getByTestId('risk-assessment')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to Security Overview Dashboard', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const securityTab = screen.getByText('Security Overview');
    await user.click(securityTab);

    await waitFor(() => {
      expect(screen.getByTestId('security-overview')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to Regulatory Framework Dashboard', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const regulatoryTab = screen.getByText('Regulatory Framework');
    await user.click(regulatoryTab);

    await waitFor(() => {
      expect(screen.getByTestId('regulatory-framework')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to Site Reliability Guardian', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const siteReliabilityTab = screen.getByText('Site Reliability');
    await user.click(siteReliabilityTab);

    await waitFor(() => {
      expect(screen.getByTestId('site-reliability')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to SLO Dashboard', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const sloTab = screen.getByText('SLO Dashboard');
    await user.click(sloTab);

    await waitFor(() => {
      expect(screen.getByTestId('slo-dashboard')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('can navigate to SLO Classic', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    const sloClassicTab = screen.getByText('SLO Classic');
    await user.click(sloClassicTab);

    await waitFor(() => {
      expect(screen.getByTestId('slo-classic')).toBeInTheDocument();
    }, { timeout: 5000 });
  });

  it('handles rapid tab switching without errors', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    // Rapidly switch between tabs
    const complianceTab = screen.getByText('Compliance Metrics');
    const riskTab = screen.getByText('Risk Assessment');
    const securityTab = screen.getByText('Security Overview');

    await user.click(complianceTab);
    await user.click(riskTab);
    await user.click(securityTab);
    await user.click(complianceTab);

    // Should not have any console errors
    expect(consoleSpy).not.toHaveBeenCalled();

    consoleSpy.mockRestore();
  });

  it('maintains state when switching tabs', async () => {
    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    });

    // Switch to compliance metrics
    const complianceTab = screen.getByText('Compliance Metrics');
    await user.click(complianceTab);

    await waitFor(() => {
      expect(screen.getByTestId('enhanced-compliance-metrics')).toBeInTheDocument();
    });

    // Switch to another tab and back
    const riskTab = screen.getByText('Risk Assessment');
    await user.click(riskTab);

    await waitFor(() => {
      expect(screen.getByTestId('risk-assessment')).toBeInTheDocument();
    });

    // Switch back to compliance metrics
    await user.click(complianceTab);

    await waitFor(() => {
      expect(screen.getByTestId('enhanced-compliance-metrics')).toBeInTheDocument();
    });
  });

  it('handles component loading errors gracefully', async () => {
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

    render(<EnterpriseDashboard />);

    await waitFor(() => {
      expect(screen.getByText('Enterprise Dashboard')).toBeInTheDocument();
    }, { timeout: 10000 });

    // Should not crash even if some components have issues
    expect(consoleSpy).not.toHaveBeenCalledWith(expect.stringMatching(/Error/));

    consoleSpy.mockRestore();
  });
});
