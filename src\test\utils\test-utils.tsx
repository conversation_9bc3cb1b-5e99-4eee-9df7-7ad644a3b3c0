import React, { ReactElement } from 'react';
import { render, RenderOptions, screen, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '../../context/ThemeContext';
import { AuthProvider } from '../../context/AuthContext';
import { ComplianceProvider } from '../../context/ComplianceContext';
import { ComplianceRulesProvider } from '../../context/ComplianceRulesContext';

// Mock localStorage for tests
const mockLocalStorage = {
  getItem: vi.fn(() => 'light'),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

// Replace localStorage in tests
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
  writable: true,
});

// Custom render function that includes all providers
const AllTheProviders = ({ children }: { children: React.ReactNode }) => {
  return (
    <BrowserRouter>
      <ThemeProvider>
        <AuthProvider>
          <ComplianceProvider>
            <ComplianceRulesProvider>
              {children}
            </ComplianceRulesProvider>
          </ComplianceProvider>
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

const customRender = (
  ui: ReactElement,
  options?: Omit<RenderOptions, 'wrapper'>
) => render(ui, { wrapper: AllTheProviders, ...options });

// Re-export everything
export * from '@testing-library/react';
export { customRender as render };

// Test utilities for common scenarios
export const renderWithTheme = (
  ui: ReactElement,
  theme: 'light' | 'dark' = 'light'
) => {
  // Set localStorage mock to return the desired theme
  mockLocalStorage.getItem.mockReturnValue(theme);

  const ThemeWrapper = ({ children }: { children: React.ReactNode }) => (
    <ThemeProvider>
      {children}
    </ThemeProvider>
  );

  return render(ui, { wrapper: ThemeWrapper });
};

export const renderWithRouter = (
  ui: ReactElement,
  initialEntries: string[] = ['/']
) => {
  const RouterWrapper = ({ children }: { children: React.ReactNode }) => (
    <BrowserRouter>
      {children}
    </BrowserRouter>
  );
  
  return render(ui, { wrapper: RouterWrapper });
};

// Mock data generators
export const createMockDataSubjectRequest = (overrides = {}) => ({
  id: 'test-request-1',
  email: '<EMAIL>',
  type: 'access',
  status: 'pending',
  priority: 'medium',
  description: 'Test request description',
  notes: 'Test notes',
  progress: 0,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString(),
  ...overrides
});

export const createMockBranch = (overrides = {}) => ({
  id: 'test-branch-1',
  name: 'Test Branch',
  location: 'Test Location',
  country: 'Test Country',
  complianceScore: 85,
  riskLevel: 'medium',
  dataFlows: [],
  ...overrides
});

export const createMockConsentRecord = (overrides = {}) => ({
  id: 'test-consent-1',
  userId: 'test-user-1',
  email: '<EMAIL>',
  consentGiven: true,
  consentDate: new Date().toISOString(),
  categories: ['marketing', 'analytics'],
  ...overrides
});

export const createMockImpactAssessment = (overrides = {}) => ({
  id: 'test-assessment-1',
  title: 'Test Assessment',
  description: 'Test assessment description',
  status: 'draft',
  riskScore: 75,
  type: 'DPIA',
  createdAt: new Date().toISOString(),
  ...overrides
});

// Common test assertions
export const expectElementToBeVisible = (element: HTMLElement) => {
  expect(element).toBeInTheDocument();
  expect(element).toBeVisible();
};

export const expectElementToHaveText = (element: HTMLElement, text: string) => {
  expect(element).toBeInTheDocument();
  expect(element).toHaveTextContent(text);
};

// Async utilities
export const waitForLoadingToFinish = async () => {
  await waitFor(() => {
    expect(screen.queryByText(/loading/i)).not.toBeInTheDocument();
  });
};

export const waitForErrorToDisappear = async () => {
  await waitFor(() => {
    expect(screen.queryByText(/error/i)).not.toBeInTheDocument();
  });
};
