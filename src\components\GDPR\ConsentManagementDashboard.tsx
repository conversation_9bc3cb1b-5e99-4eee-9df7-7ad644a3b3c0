import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { ConsentRecord, ConsentCategory, ConsentMetrics } from '../../types/compliance';
import { ConsentManagementService } from '../../services/consentManagementService';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button, IconButton } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { Line } from 'react-chartjs-2';
import {
  CheckCircle,
  XCircle,
  Users,
  TrendingUp,
  TrendingDown,
  Shield,
  Settings,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Calendar,
  Mail,
  Phone,
  Globe,
  AlertTriangle
} from 'lucide-react';

interface ConsentManagementDashboardProps {
  className?: string;
}

export const ConsentManagementDashboard: React.FC<ConsentManagementDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  // State management
  const [consentRecords, setConsentRecords] = useState<ConsentRecord[]>([]);
  const [consentCategories, setConsentCategories] = useState<ConsentCategory[]>([]);
  const [filteredRecords, setFilteredRecords] = useState<ConsentRecord[]>([]);
  const [metrics, setMetrics] = useState<ConsentMetrics | null>(null);
  const [trendData, setTrendData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'records' | 'categories' | 'analytics'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    status: [] as string[],
    consentMethod: [] as string[],
    category: [] as string[]
  });

  // CRUD Modal States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ConsentRecord | null>(null);
  const [editingRecord, setEditingRecord] = useState<ConsentRecord | null>(null);

  // Bulk Operations
  const [selectedRecords, setSelectedRecords] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [bulkAction, setBulkAction] = useState<'withdraw' | 'delete' | 'export'>('withdraw');

  // Form data
  const [formData, setFormData] = useState({
    dataSubjectId: '',
    email: '',
    firstName: '',
    lastName: '',
    categoryId: '',
    granted: true,
    method: 'website' as const,
    purpose: '',
    legalBasis: '',
    notes: ''
  });

  useEffect(() => {
    loadData();

    // Set up real-time updates
    const interval = setInterval(loadData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    applyFilters();
  }, [consentRecords, searchTerm, selectedFilters]);

  const applyFilters = () => {
    let filtered = consentRecords;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(record =>
        record.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.dataSubjectId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        record.lastName?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (selectedFilters.status.length > 0) {
      filtered = filtered.filter(record =>
        selectedFilters.status.includes(record.status)
      );
    }

    // Apply method filter
    if (selectedFilters.consentMethod.length > 0) {
      filtered = filtered.filter(record => selectedFilters.consentMethod.includes(record.consentMethod));
    }

    // Apply category filter
    if (selectedFilters.category.length > 0) {
      filtered = filtered.filter(record =>
        record.consentCategories.some(category => selectedFilters.category.includes(category.id))
      );
    }

    setFilteredRecords(filtered);
  };

  // Advanced search functionality
  const handleAdvancedSearch = async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      setFilteredRecords(consentRecords);
      return;
    }

    try {
      // Use the service's search functionality if available
      const searchResults = await ConsentManagementService.searchConsentRecords(searchQuery);
      setFilteredRecords(searchResults);
    } catch (error) {
      console.error('Search error:', error);
      // Fallback to local filtering
      applyFilters();
    }
  };

  // Export functionality
  const handleExport = async (format: 'json' | 'csv' = 'json') => {
    try {
      const dataToExport = selectedRecords.length > 0
        ? consentRecords.filter(record => selectedRecords.includes(record.id))
        : filteredRecords;

      if (format === 'json') {
        const dataStr = JSON.stringify(dataToExport, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `consent-records-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
      } else if (format === 'csv') {
        const headers = ['ID', 'Email', 'First Name', 'Last Name', 'Status', 'Consent Method', 'Created Date'];
        const csvContent = [
          headers.join(','),
          ...dataToExport.map(record => [
            record.id,
            record.email,
            record.firstName,
            record.lastName,
            record.status,
            record.consentMethod,
            record.consentTimestamp.toISOString().split('T')[0]
          ].join(','))
        ].join('\n');

        const dataBlob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `consent-records-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        URL.revokeObjectURL(url);
      }

      notifications.addNotification({
        type: 'success',
        title: 'Export Complete',
        message: `Successfully exported ${dataToExport.length} records as ${format.toUpperCase()}.`
      });
    } catch (error) {
      console.error('Export error:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Export Failed',
        message: 'Failed to export data. Please try again.'
      });
    }
  };

  // Enhanced CRUD Operations using service layer
  const handleCreateConsent = async () => {
    try {
      setLoading(true);

      // Find the selected category
      const selectedCategory = consentCategories.find(cat => cat.id === formData.categoryId);
      if (!selectedCategory) {
        throw new Error('Please select a valid consent category');
      }

      // Create consent record using service
      const newRecord = await ConsentManagementService.createConsentRecord({
        dataSubjectId: formData.dataSubjectId,
        email: formData.email,
        firstName: formData.firstName || '',
        lastName: formData.lastName || '',
        consentCategories: [selectedCategory],
        consentMethod: formData.method,
        consentVersion: '1.0',
        ipAddress: '***********', // In real app, get from request
        userAgent: navigator.userAgent,
        source: 'dashboard'
      });

      // Refresh data to get updated list
      await loadData();

      setShowCreateModal(false);
      resetForm();
      notifications.addNotification({
        type: 'success',
        title: 'Consent Created',
        message: `Consent record created successfully for ${formData.email}.`
      });
    } catch (error) {
      console.error('Error creating consent:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Creating Consent',
        message: 'Failed to create consent record. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateConsent = async () => {
    if (!editingRecord) return;

    try {
      setLoading(true);

      // Find the selected category
      const selectedCategory = consentCategories.find(cat => cat.id === formData.categoryId);
      if (!selectedCategory) {
        throw new Error('Please select a valid consent category');
      }

      // Update consent record using service
      const updatedRecord = await ConsentManagementService.updateConsentRecord(editingRecord.id, {
        email: formData.email,
        firstName: formData.firstName || editingRecord.firstName,
        lastName: formData.lastName || editingRecord.lastName,
        consentCategories: [selectedCategory],
        consentMethod: formData.method,
        status: formData.granted ? 'active' : 'withdrawn'
      });

      if (updatedRecord) {
        // Refresh data to get updated list
        await loadData();

        setShowEditModal(false);
        setEditingRecord(null);
        resetForm();
        notifications.addNotification({
          type: 'success',
          title: 'Consent Updated',
          message: `Consent record updated successfully for ${formData.email}.`
        });
      } else {
        throw new Error('Failed to update consent record');
      }
    } catch (error) {
      console.error('Error updating consent:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Updating Consent',
        message: 'Failed to update consent record. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleWithdrawConsent = async () => {
    if (!selectedRecord) return;

    try {
      setLoading(true);

      // Withdraw consent using service
      const categoryId = selectedRecord.consentCategories[0]?.id || 'general';
      const success = await ConsentManagementService.withdrawConsent(
        selectedRecord.id,
        categoryId,
        'Withdrawn by administrator'
      );

      if (success) {
        // Refresh data to get updated list
        await loadData();

        setShowWithdrawModal(false);
        setSelectedRecord(null);
        notifications.addNotification({
          type: 'success',
          title: 'Consent Withdrawn',
          message: `Consent withdrawn successfully for ${selectedRecord.email}.`
        });
      } else {
        throw new Error('Failed to withdraw consent');
      }
    } catch (error) {
      console.error('Error withdrawing consent:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Withdrawing Consent',
        message: 'Failed to withdraw consent. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteConsent = async () => {
    if (!selectedRecord) return;

    try {
      setConsentRecords(prev => prev.filter(record => record.id !== selectedRecord.id));
      setShowDeleteModal(false);
      setSelectedRecord(null);
      notifications.addNotification({
        type: 'success',
        title: 'Consent Deleted',
        message: 'Consent record has been deleted successfully.'
      });
    } catch (error) {
      console.error('Error deleting consent:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Deleting Consent',
        message: 'Failed to delete consent record. Please try again.'
      });
    }
  };

  // Bulk Operations
  const handleSelectRecord = (recordId: string) => {
    setSelectedRecords(prev =>
      prev.includes(recordId)
        ? prev.filter(id => id !== recordId)
        : [...prev, recordId]
    );
  };

  const handleSelectAll = () => {
    if (selectedRecords.length === filteredRecords.length) {
      setSelectedRecords([]);
    } else {
      setSelectedRecords(filteredRecords.map(record => record.id));
    }
  };

  const handleBulkAction = async () => {
    if (selectedRecords.length === 0) return;

    try {
      setLoading(true);

      switch (bulkAction) {
        case 'withdraw':
          for (const recordId of selectedRecords) {
            const record = consentRecords.find(r => r.id === recordId);
            if (record) {
              const categoryId = record.consentCategories[0]?.id || 'general';
              await ConsentManagementService.withdrawConsent(recordId, categoryId, 'Bulk withdrawal');
            }
          }
          notifications.addNotification({
            type: 'success',
            title: 'Bulk Withdrawal Complete',
            message: `Successfully withdrew consent for ${selectedRecords.length} records.`
          });
          break;

        case 'delete':
          for (const recordId of selectedRecords) {
            await ConsentManagementService.deleteConsentRecord(recordId);
          }
          notifications.addNotification({
            type: 'success',
            title: 'Bulk Delete Complete',
            message: `Successfully deleted ${selectedRecords.length} records.`
          });
          break;

        case 'export':
          const exportData = consentRecords.filter(record => selectedRecords.includes(record.id));
          const dataStr = JSON.stringify(exportData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `consent-records-${new Date().toISOString().split('T')[0]}.json`;
          link.click();
          URL.revokeObjectURL(url);
          notifications.addNotification({
            type: 'success',
            title: 'Export Complete',
            message: `Successfully exported ${selectedRecords.length} records.`
          });
          break;
      }

      // Refresh data and clear selection
      await loadData();
      setSelectedRecords([]);
      setShowBulkActions(false);

    } catch (error) {
      console.error('Bulk action error:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Bulk Action Failed',
        message: 'Failed to complete bulk action. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setFormData({
      dataSubjectId: '',
      email: '',
      firstName: '',
      lastName: '',
      categoryId: '',
      granted: true,
      method: 'website',
      purpose: '',
      legalBasis: '',
      notes: ''
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (record: ConsentRecord) => {
    setEditingRecord(record);
    setFormData({
      dataSubjectId: record.dataSubjectId,
      email: record.email || '',
      firstName: record.firstName || '',
      lastName: record.lastName || '',
      categoryId: record.consentCategories[0]?.id || '',
      granted: record.status === 'active',
      method: record.consentMethod,
      purpose: record.consentCategories[0]?.purpose || '',
      legalBasis: record.consentCategories[0]?.legalBasis || '',
      notes: ''
    });
    setShowEditModal(true);
  };

  const openViewModal = (record: ConsentRecord) => {
    setSelectedRecord(record);
    setShowViewModal(true);
  };

  const openDeleteModal = (record: ConsentRecord) => {
    setSelectedRecord(record);
    setShowDeleteModal(true);
  };

  const openWithdrawModal = (record: ConsentRecord) => {
    setSelectedRecord(record);
    setShowWithdrawModal(true);
  };

  const loadData = async () => {
    try {
      setLoading(true);
      const [recordsData, categoriesData, metricsData, trendsData] = await Promise.all([
        ConsentManagementService.getAllConsentRecords(),
        ConsentManagementService.getAllConsentCategories(),
        ConsentManagementService.getConsentMetrics(),
        ConsentManagementService.getConsentTrends(30)
      ]);
      
      setConsentRecords(recordsData);
      setConsentCategories(categoriesData);
      setMetrics(metricsData);
      setTrendData(trendsData);
    } catch (error) {
      console.error('Error loading consent data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleConsentToggle = async (recordId: string, categoryId: string, granted: boolean) => {
    try {
      if (granted) {
        await ConsentManagementService.grantConsent(recordId, categoryId);
      } else {
        await ConsentManagementService.withdrawConsent(recordId, categoryId, 'User preference');
      }
      await loadData(); // Refresh data
    } catch (error) {
      console.error('Error updating consent:', error);
    }
  };

  const getConsentMethodIcon = (method: string) => {
    switch (method) {
      case 'website': return <Globe className="w-4 h-4" />;
      case 'email': return <Mail className="w-4 h-4" />;
      case 'phone': return <Phone className="w-4 h-4" />;
      case 'paper': return <Edit className="w-4 h-4" />;
      default: return <Globe className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-text-secondary">Loading consent data...</span>
      </div>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Consent Management Error</h2>
            <p className="text-text-secondary">Something went wrong loading the consent management dashboard. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Consent Management Center</h2>
            <p className="text-text-secondary">Manage user consents and privacy preferences</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button
            onClick={loadData}
            className={`p-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}
          >
            <RefreshCw className="w-4 h-4" />
          </button>
          <button className={`flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105'}`}>
            <Plus className="w-4 h-4" />
            New Category
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-surface rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview', icon: TrendingUp },
          { id: 'records', label: 'Consent Records', icon: Users },
          { id: 'categories', label: 'Categories', icon: Settings },
          { id: 'analytics', label: 'Analytics', icon: TrendingUp }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-primary text-white'
                : 'text-text-secondary hover:text-text hover:bg-card'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && metrics && (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                  <Users className="w-6 h-6 text-primary" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalUsers.toLocaleString()}</div>
              <div className="text-text-secondary text-sm">Total Users</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-green-500/10'} rounded-lg`}>
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.consentedUsers.toLocaleString()}</div>
              <div className="text-text-secondary text-sm">Consented Users</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                  <TrendingUp className="w-6 h-6 text-secondary" />
                </div>
                {metrics.trend === 'up' ? (
                  <TrendingUp className="w-5 h-5 text-green-500" />
                ) : (
                  <TrendingDown className="w-5 h-5 text-red-500" />
                )}
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.consentRate.toFixed(1)}%</div>
              <div className="text-text-secondary text-sm">Consent Rate</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-accent-purple/10'} rounded-lg`}>
                  <Calendar className="w-6 h-6 text-accent-purple" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">+{metrics.weeklyChange.toFixed(1)}%</div>
              <div className="text-text-secondary text-sm">Weekly Change</div>
            </div>
          </div>

          {/* Category Breakdown */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Consent by Category</h3>
              <div className="space-y-4">
                {metrics.categoryBreakdown.map((category, index) => {
                  const colors = ['primary', 'secondary', 'accent-purple', 'green-500'];
                  const color = colors[index % colors.length];
                  
                  return (
                    <div key={category.categoryId} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-text">{category.categoryName}</span>
                        <span className={`text-sm font-bold text-${color}`}>{category.consentRate.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-border rounded-full h-2">
                        <div 
                          className={`bg-${color} h-2 rounded-full transition-all duration-1000`}
                          style={{width: `${category.consentRate}%`}}
                        ></div>
                      </div>
                      <div className="flex items-center justify-between text-xs text-text-secondary">
                        <span>{category.consentedUsers.toLocaleString()} consented</span>
                        <span>{category.totalUsers.toLocaleString()} total</span>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Consent Trends Chart */}
            {trendData && (
              <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
                <h3 className="text-lg font-semibold text-text mb-4">Consent Trends (30 Days)</h3>
                <div style={{ height: '300px' }}>
                  <Line
                    data={trendData}
                    options={{
                      responsive: true,
                      maintainAspectRatio: false,
                      plugins: {
                        legend: {
                          display: true,
                          position: 'top' as const,
                        },
                      },
                      scales: {
                        y: {
                          beginAtZero: true,
                          grid: {
                            color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
                          },
                          ticks: {
                            color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
                          },
                        },
                        x: {
                          grid: {
                            color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
                          },
                          ticks: {
                            color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Consent Records Tab */}
      {activeTab === 'records' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search by email, name..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 bg-surface border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary`}
              />
            </div>
            <button className={`flex items-center gap-2 px-4 py-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
              <Filter className="w-4 h-4" />
              Filters
            </button>
            <button className={`p-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
              <Download className="w-4 h-4" />
            </button>
          </div>

          {/* Records List */}
          <div className="space-y-4">
            {consentRecords
              .filter(record => 
                !searchTerm || 
                record.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                record.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                record.lastName.toLowerCase().includes(searchTerm.toLowerCase())
              )
              .slice(0, 10) // Show first 10 for demo
              .map((record) => (
                <div
                  key={record.id}
                  className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border hover:border-primary/30 transition-all duration-300`}
                >
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-full flex items-center justify-center`}>
                        <Users className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-text">
                          {record.firstName} {record.lastName}
                        </h4>
                        <p className="text-text-secondary">{record.email}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        {getConsentMethodIcon(record.consentMethod)}
                        <span className="text-sm text-text-secondary capitalize">
                          {record.consentMethod}
                        </span>
                      </div>
                      <span className="text-sm text-text-secondary">
                        {formatDate(record.consentTimestamp)}
                      </span>
                      <button className={`p-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
                        <Eye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>

                  {/* Consent Categories */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    {record.consentCategories.map((category) => (
                      <div
                        key={category.id}
                        className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-card'} rounded-lg border border-border`}
                      >
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-text">{category.name}</span>
                          <button
                            onClick={() => handleConsentToggle(record.id, category.id, !category.consentGiven)}
                            className={`p-1 rounded transition-colors ${
                              category.consentGiven
                                ? 'text-green-500 hover:text-green-600'
                                : 'text-gray-400 hover:text-gray-500'
                            }`}
                          >
                            {category.consentGiven ? (
                              <CheckCircle className="w-5 h-5" />
                            ) : (
                              <XCircle className="w-5 h-5" />
                            )}
                          </button>
                        </div>
                        <div className="text-xs text-text-secondary">
                          {category.consentGiven ? (
                            <span className="text-green-600">
                              Granted {category.consentTimestamp ? formatDate(category.consentTimestamp) : ''}
                            </span>
                          ) : (
                            <span className="text-gray-500">Not granted</span>
                          )}
                        </div>
                        {category.withdrawalTimestamp && (
                          <div className="text-xs text-red-600 mt-1">
                            Withdrawn {formatDate(category.withdrawalTimestamp)}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>

                  {/* Withdrawal History */}
                  {record.withdrawalHistory.length > 0 && (
                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="w-4 h-4 text-orange-500" />
                        <span className="text-sm font-medium text-text">Recent Withdrawals</span>
                      </div>
                      <div className="space-y-1">
                        {record.withdrawalHistory.slice(0, 2).map((withdrawal) => (
                          <div key={withdrawal.id} className="text-xs text-text-secondary">
                            Withdrew consent for category on {formatDate(withdrawal.withdrawalTimestamp)}
                            {withdrawal.reason && ` - ${withdrawal.reason}`}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Categories Tab */}
      {activeTab === 'categories' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {consentCategories.map((category) => (
              <div
                key={category.id}
                className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}
              >
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                      <Settings className="w-5 h-5 text-primary" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-text">{category.name}</h3>
                      <p className="text-text-secondary text-sm">{category.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {category.required && (
                      <span className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 text-xs font-bold rounded-full">
                        Required
                      </span>
                    )}
                    <button className={`p-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
                      <Edit className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <span className="text-sm font-medium text-text">Purpose:</span>
                    <p className="text-sm text-text-secondary">{category.purpose}</p>
                  </div>
                  
                  <div>
                    <span className="text-sm font-medium text-text">Legal Basis:</span>
                    <p className="text-sm text-text-secondary">{category.legalBasis}</p>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-text">Data Types:</span>
                    <div className="flex flex-wrap gap-1 mt-1">
                      {category.dataTypes.map((type, index) => (
                        <span
                          key={index}
                          className={`px-2 py-1 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} text-text border border-border rounded text-xs`}
                        >
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>

                  <div>
                    <span className="text-sm font-medium text-text">Retention Period:</span>
                    <p className="text-sm text-text-secondary">
                      {category.retentionPeriod.duration} {category.retentionPeriod.unit}
                    </p>
                  </div>

                  {category.subcategories && category.subcategories.length > 0 && (
                    <div>
                      <span className="text-sm font-medium text-text">Subcategories:</span>
                      <div className="mt-2 space-y-2">
                        {category.subcategories.map((sub) => (
                          <div key={sub.id} className="flex items-center justify-between p-2 bg-surface rounded border border-border">
                            <div>
                              <span className="text-sm font-medium text-text">{sub.name}</span>
                              <p className="text-xs text-text-secondary">{sub.description}</p>
                            </div>
                            <div className={`w-3 h-3 rounded-full ${sub.consentGiven ? 'bg-green-500' : 'bg-gray-300'}`}></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Analytics Tab */}
      {activeTab === 'analytics' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <TrendingUp className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Advanced Analytics</h3>
            <p className="text-text-secondary">Detailed consent analytics and reporting coming soon</p>
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
};

export default ConsentManagementDashboard;
