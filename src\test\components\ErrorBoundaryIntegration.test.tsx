import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { render } from '../utils/test-utils';

// Import all the components that now have error boundaries
const EnhancedComplianceMetricsOptimized = React.lazy(() => import('../../components/compliance/EnhancedComplianceMetricsOptimized'));
const OrganizationalMappingDashboard = React.lazy(() => import('../../components/GDPR/OrganizationalMappingDashboard'));
const PoliciesManagementDashboard = React.lazy(() => import('../../components/GDPR/PoliciesManagementDashboard'));
const ConsentManagementDashboard = React.lazy(() => import('../../components/GDPR/ConsentManagementDashboard'));
const ImpactAssessmentDashboard = React.lazy(() => import('../../components/GDPR/ImpactAssessmentDashboard'));

// Component that throws an error during render
const ErrorThrowingComponent = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error for error boundary');
  }
  return <div>No error</div>;
};

// Mock all services to prevent actual API calls
vi.mock('../../services/consentManagementService', () => ({
  ConsentManagementService: {
    initialize: vi.fn(),
    getAllConsentRecords: vi.fn().mockResolvedValue([]),
    getAllConsentCategories: vi.fn().mockResolvedValue([]),
    getConsentMetrics: vi.fn().mockResolvedValue({
      totalUsers: 100,
      consentedUsers: 85,
      consentRate: 85,
      trend: 'up',
      weeklyChange: 2.3,
      monthlyChange: 5.7,
      lastUpdated: new Date(),
      categoryBreakdown: []
    }),
    getConsentTrends: vi.fn().mockResolvedValue([])
  }
}));

vi.mock('../../services/impactAssessmentService', () => ({
  ImpactAssessmentService: {
    initialize: vi.fn(),
    getAllAssessments: vi.fn().mockResolvedValue([]),
    getAssessmentMetrics: vi.fn().mockResolvedValue({
      total: 10,
      completed: 7,
      inProgress: 2,
      overdue: 1,
      averageRiskScore: 65,
      highRiskAssessments: 3
    })
  }
}));

vi.mock('../../components/compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: () => ({
    overview: {
      totalMetrics: 25,
      compliantMetrics: 20,
      warningMetrics: 3,
      criticalMetrics: 2,
      overallScore: 85,
      trendDirection: 'up',
      lastAssessment: new Date(),
      nextAssessment: new Date()
    },
    metrics: [],
    frameworks: [],
    categoryBreakdown: {
      privacy: { score: 90, count: 5, trend: 'up' },
      security: { score: 88, count: 8, trend: 'stable' },
      operational: { score: 85, count: 7, trend: 'up' },
      regulatory: { score: 92, count: 5, trend: 'up' }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30000,
      pendingUpdates: 0
    }
  })
}));

// Mock chart.js to prevent canvas issues in tests
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>,
  Radar: () => <div data-testid="radar-chart">Radar Chart</div>
}));

describe('Error Boundary Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Suppress console.error for error boundary tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Enhanced Compliance Metrics Error Handling', () => {
    it('catches and displays error boundary fallback', async () => {
      // Mock the service to throw an error
      vi.mocked(require('../../components/compliance/services/ComplianceDataService').generateEnhancedComplianceData)
        .mockImplementation(() => {
          throw new Error('Service error');
        });

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      // Should show error boundary fallback or handle error gracefully
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Organizational Mapping Dashboard Error Handling', () => {
    it('displays error boundary fallback when component fails', async () => {
      // Create a wrapper that will throw an error
      const ErrorWrapper = () => {
        throw new Error('Component error');
      };

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ErrorWrapper />
        </React.Suspense>
      );

      // Should not crash the entire application
      expect(screen.getByText('Loading...')).toBeInTheDocument();
    });

    it('renders successfully with error boundaries in place', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Policies Management Dashboard Error Handling', () => {
    it('renders successfully with error boundaries in place', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Consent Management Dashboard Error Handling', () => {
    it('handles service errors gracefully', async () => {
      // Mock service to reject
      vi.mocked(require('../../services/consentManagementService').ConsentManagementService.getAllConsentRecords)
        .mockRejectedValue(new Error('Service error'));

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    it('renders successfully with error boundaries in place', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Impact Assessment Dashboard Error Handling', () => {
    it('handles service errors gracefully', async () => {
      // Mock service to reject
      vi.mocked(require('../../services/impactAssessmentService').ImpactAssessmentService.getAllAssessments)
        .mockRejectedValue(new Error('Service error'));

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });
    });

    it('renders successfully with error boundaries in place', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Error Boundary Isolation', () => {
    it('isolates errors to individual components', async () => {
      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div>Loading Consent...</div>}>
            <ConsentManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Impact...</div>}>
            <ImpactAssessmentDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Both components should load independently
      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 10000 });
    });

    it('prevents error propagation between components', async () => {
      // Mock one service to fail
      vi.mocked(require('../../services/consentManagementService').ConsentManagementService.getAllConsentRecords)
        .mockRejectedValue(new Error('Service error'));

      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div>Loading Consent...</div>}>
            <ConsentManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Impact...</div>}>
            <ImpactAssessmentDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Impact Assessment should still load even if Consent Management fails
      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Recovery and Resilience', () => {
    it('components recover from transient errors', async () => {
      let callCount = 0;
      vi.mocked(require('../../services/consentManagementService').ConsentManagementService.getAllConsentRecords)
        .mockImplementation(() => {
          callCount++;
          if (callCount === 1) {
            return Promise.reject(new Error('Transient error'));
          }
          return Promise.resolve([]);
        });

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });
});
