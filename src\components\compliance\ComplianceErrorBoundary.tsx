import React, { Component, ErrorInfo, ReactNode } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshC<PERSON>, FileText, BarChart3, TrendingUp } from 'lucide-react';
import { reportError } from '../../services/errorReportingService';
import { performanceMonitoringService } from '../../services/performanceMonitoringService';

interface Props {
  children: ReactNode;
  componentName: string;
  fallbackType?: 'chart' | 'data' | 'policy' | 'violations' | 'analytics';
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onRetry?: () => void;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
}

export class ComplianceErrorBoundary extends Component<Props, State> {
  private maxRetries = 3;

  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Report error using the error reporting service
    reportError(error, this.props.componentName, {
      category: this.props.fallbackType === 'chart' ? 'chart' : 'ui',
      severity: 'high',
      additionalContext: {
        fallbackType: this.props.fallbackType,
        errorInfo: {
          componentStack: errorInfo.componentStack?.split('\n').slice(0, 5).join('\n')
        }
      }
    });

    // Update component health in performance monitoring
    const health = performanceMonitoringService.getComponentHealth(this.props.componentName) as any;
    if (health) {
      health.errorCount = (health.errorCount || 0) + 1;
      health.lastError = error.message;
    }

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    this.setState({
      error,
      errorInfo
    });
  }

  handleRetry = () => {
    if (this.state.retryCount < this.maxRetries) {
      this.setState(prevState => ({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: prevState.retryCount + 1
      }));

      // Call custom retry handler if provided
      if (this.props.onRetry) {
        this.props.onRetry();
      }
    }
  };

  handleReload = () => {
    window.location.reload();
  };

  getFallbackIcon = () => {
    switch (this.props.fallbackType) {
      case 'chart':
        return BarChart3;
      case 'analytics':
        return TrendingUp;
      case 'policy':
        return FileText;
      case 'violations':
        return AlertTriangle;
      default:
        return AlertTriangle;
    }
  };

  getFallbackTitle = () => {
    switch (this.props.fallbackType) {
      case 'chart':
        return 'Chart Unavailable';
      case 'analytics':
        return 'Analytics Unavailable';
      case 'policy':
        return 'Policy Data Unavailable';
      case 'violations':
        return 'Violations Data Unavailable';
      default:
        return 'Component Unavailable';
    }
  };

  getFallbackMessage = () => {
    switch (this.props.fallbackType) {
      case 'chart':
        return 'The compliance chart could not be loaded. This may be due to data formatting issues or network connectivity.';
      case 'analytics':
        return 'Analytics data could not be processed. The system may be temporarily unavailable.';
      case 'policy':
        return 'Policy compliance data could not be retrieved. Please check your connection and try again.';
      case 'violations':
        return 'Violations summary could not be loaded. The data may be temporarily unavailable.';
      default:
        return 'This compliance component encountered an error and could not be displayed.';
    }
  };

  render() {
    if (this.state.hasError) {
      const Icon = this.getFallbackIcon();
      const canRetry = this.state.retryCount < this.maxRetries;

      return (
        <div className="bg-card rounded-lg p-6 border border-border">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-red-50 dark:bg-red-900/20 rounded-full">
                <Icon className="w-8 h-8 text-red-500" />
              </div>
            </div>
            
            <h3 className="text-lg font-semibold text-text mb-2">
              {this.getFallbackTitle()}
            </h3>
            
            <p className="text-text-secondary mb-6 max-w-md mx-auto">
              {this.getFallbackMessage()}
            </p>

            {/* Error Details (Development Only) */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mb-6 text-left">
                <summary className="cursor-pointer text-sm text-text-secondary hover:text-text">
                  Technical Details (Development)
                </summary>
                <div className="mt-2 p-3 bg-surface rounded text-xs font-mono text-text-secondary">
                  <div className="mb-2">
                    <strong>Error:</strong> {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div>
                      <strong>Stack:</strong>
                      <pre className="mt-1 whitespace-pre-wrap">
                        {this.state.error.stack.split('\n').slice(0, 5).join('\n')}
                      </pre>
                    </div>
                  )}
                </div>
              </details>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-center gap-3">
              {canRetry && (
                <button
                  onClick={this.handleRetry}
                  className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors duration-200"
                >
                  <RefreshCw className="w-4 h-4" />
                  Retry ({this.maxRetries - this.state.retryCount} left)
                </button>
              )}
              
              <button
                onClick={this.handleReload}
                className="flex items-center gap-2 px-4 py-2 bg-card hover:bg-surface text-text border border-border rounded-lg transition-colors duration-200"
              >
                <RefreshCw className="w-4 h-4" />
                Reload Page
              </button>
            </div>

            {/* Retry Count Warning */}
            {this.state.retryCount >= this.maxRetries && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  Maximum retry attempts reached. Please reload the page or contact support if the issue persists.
                </p>
              </div>
            )}

            {/* Support Information */}
            <div className="mt-6 pt-4 border-t border-border">
              <p className="text-xs text-text-secondary">
                Error ID: {Date.now().toString(36)}-{Math.random().toString(36).substr(2, 9)}
              </p>
              <p className="text-xs text-text-secondary mt-1">
                Component: {this.props.componentName}
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ComplianceErrorBoundary;
