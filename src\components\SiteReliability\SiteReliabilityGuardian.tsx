import React, { useState } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useSiteReliability } from '../../hooks/useSiteReliability';
import { SLAComplianceMonitoring } from './SLAComplianceMonitoring';
import { UptimeTrackingDashboard } from './UptimeTrackingDashboard';
import { PerformanceMetricsCollection } from './PerformanceMetricsCollection';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import {
  Shield,
  Activity,
  Server,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  RefreshCw,
  Bell,
  Settings,
  BarChart3,
  Zap
} from 'lucide-react';

interface SiteReliabilityGuardianProps {
  className?: string;
}

export const SiteReliabilityGuardian: React.FC<SiteReliabilityGuardianProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const { dashboardData, isLoading, error, refresh, performHealthCheck } = useSiteReliability();
  
  const [activeTab, setActiveTab] = useState<'overview' | 'sla' | 'uptime' | 'performance' | 'alerts'>('overview');
  const [healthCheckResult, setHealthCheckResult] = useState<{ healthy: boolean; issues: string[] } | null>(null);
  const [isPerformingHealthCheck, setIsPerformingHealthCheck] = useState(false);

  const handleHealthCheck = async () => {
    setIsPerformingHealthCheck(true);
    try {
      const result = await performHealthCheck();
      setHealthCheckResult(result);
    } finally {
      setIsPerformingHealthCheck(false);
    }
  };

  const getOverallHealthStatus = () => {
    if (!dashboardData) return 'unknown';
    return dashboardData.overview.overallHealth;
  };

  const getHealthStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-6 h-6 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-6 h-6 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-6 h-6 text-red-500" />;
      default:
        return <Clock className="w-6 h-6 text-gray-500" />;
    }
  };

  const getHealthStatusColorClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-800';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-800';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-800';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400 border-gray-200 dark:border-gray-800';
    }
  };

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Site Reliability Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Site Reliability Guardian</h1>
              <p className="text-text-secondary">
                Comprehensive monitoring and management of service reliability, performance, and availability
              </p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={handleHealthCheck}
              disabled={isPerformingHealthCheck}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors"
            >
              <Zap className={`w-4 h-4 ${isPerformingHealthCheck ? 'animate-pulse' : ''}`} />
              {isPerformingHealthCheck ? 'Checking...' : 'Health Check'}
            </button>
            
            <button
              onClick={refresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh all data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>
          </div>
        </div>

        {/* Overall Health Status */}
        {dashboardData && (
          <div className={`p-4 rounded-lg border-2 ${getHealthStatusColorClass(getOverallHealthStatus())}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                {getHealthStatusIcon(getOverallHealthStatus())}
                <div>
                  <h3 className="text-lg font-semibold">
                    System Status: {getOverallHealthStatus().charAt(0).toUpperCase() + getOverallHealthStatus().slice(1)}
                  </h3>
                  <p className="text-sm opacity-80">
                    {dashboardData.overview.servicesUp} of {dashboardData.overview.totalServices} services operational
                    {dashboardData.overview.activeIncidents > 0 && ` • ${dashboardData.overview.activeIncidents} active incidents`}
                  </p>
                </div>
              </div>
              
              <div className="text-right">
                <p className="text-2xl font-bold">{dashboardData.overview.averageUptime.toFixed(2)}%</p>
                <p className="text-sm opacity-80">Average Uptime</p>
              </div>
            </div>
          </div>
        )}

        {/* Health Check Results */}
        {healthCheckResult && (
          <div className="mt-4 p-4 bg-card rounded-lg border border-border">
            <div className="flex items-center gap-2 mb-2">
              {healthCheckResult.healthy ? (
                <CheckCircle className="w-5 h-5 text-green-500" />
              ) : (
                <AlertTriangle className="w-5 h-5 text-amber-500" />
              )}
              <h4 className="font-semibold text-text">
                Health Check: {healthCheckResult.healthy ? 'All Systems Operational' : 'Issues Detected'}
              </h4>
            </div>
            {healthCheckResult.issues.length > 0 && (
              <ul className="space-y-1 text-sm text-text-secondary">
                {healthCheckResult.issues.map((issue, index) => (
                  <li key={index} className="flex items-center gap-2">
                    <AlertTriangle className="w-3 h-3 text-amber-500" />
                    {issue}
                  </li>
                ))}
              </ul>
            )}
          </div>
        )}

        {/* Quick Stats */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mt-6">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total Services</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.totalServices}</p>
                </div>
                <Server className="w-8 h-8 text-primary" />
              </div>
            </div>
            
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Services Up</p>
                  <p className="text-2xl font-bold text-green-500">{dashboardData.overview.servicesUp}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>
            
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Active Incidents</p>
                  <p className="text-2xl font-bold text-red-500">{dashboardData.overview.activeIncidents}</p>
                </div>
                <AlertTriangle className="w-8 h-8 text-red-500" />
              </div>
            </div>
            
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Avg Uptime</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.averageUptime.toFixed(1)}%</p>
                </div>
                <TrendingUp className="w-8 h-8 text-green-500" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <div className="bg-surface rounded-lg p-1">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'sla', label: 'SLA Monitoring', icon: Shield },
            { id: 'uptime', label: 'Uptime Tracking', icon: Activity },
            { id: 'performance', label: 'Performance', icon: TrendingUp },
            { id: 'alerts', label: 'Alert Management', icon: Bell }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-primary text-white'
                  : 'text-text-secondary hover:text-text hover:bg-border/50'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[600px]">
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Overview content - could show summary of all components */}
            <div className="bg-surface rounded-lg p-6">
              <h2 className="text-xl font-semibold text-text mb-4">System Overview</h2>
              <p className="text-text-secondary mb-6">
                The Site Reliability Guardian provides comprehensive monitoring across all critical system components.
                Use the tabs above to access detailed views for SLA monitoring, uptime tracking, performance metrics, and alert management.
              </p>

              {dashboardData && (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-text">Recent Activity</h3>
                    <div className="space-y-3">
                      {dashboardData.recentIncidents.slice(0, 3).map((incident) => (
                        <div key={incident.id} className="flex items-start gap-3 p-3 bg-card rounded-lg border border-border">
                          <div className={`p-1 rounded-full ${
                            incident.severity === 'critical' ? 'bg-red-100 dark:bg-red-900/20' :
                            incident.severity === 'high' ? 'bg-amber-100 dark:bg-amber-900/20' :
                            'bg-blue-100 dark:bg-blue-900/20'
                          }`}>
                            <AlertTriangle className={`w-3 h-3 ${
                              incident.severity === 'critical' ? 'text-red-500' :
                              incident.severity === 'high' ? 'text-amber-500' :
                              'text-blue-500'
                            }`} />
                          </div>
                          <div className="flex-1">
                            <p className="text-sm font-medium text-text">{incident.title}</p>
                            <p className="text-xs text-text-secondary">
                              {new Date(incident.startTime).toLocaleString()} • {incident.status}
                            </p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-text">SLA Status Summary</h3>
                    <div className="space-y-3">
                      {dashboardData.slaTargets.slice(0, 3).map((sla) => (
                        <div key={sla.id} className="flex items-center justify-between p-3 bg-card rounded-lg border border-border">
                          <div className="flex items-center gap-3">
                            {sla.status === 'healthy' ? (
                              <CheckCircle className="w-4 h-4 text-green-500" />
                            ) : sla.status === 'warning' ? (
                              <AlertTriangle className="w-4 h-4 text-amber-500" />
                            ) : (
                              <XCircle className="w-4 h-4 text-red-500" />
                            )}
                            <div>
                              <p className="text-sm font-medium text-text">{sla.name}</p>
                              <p className="text-xs text-text-secondary">{sla.category}</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-semibold text-text">{sla.currentPercentage.toFixed(2)}%</p>
                            <p className="text-xs text-text-secondary">Target: {sla.targetPercentage}%</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'sla' && <SLAComplianceMonitoring />}
        {activeTab === 'uptime' && <UptimeTrackingDashboard />}
        {activeTab === 'performance' && <PerformanceMetricsCollection />}

        {activeTab === 'alerts' && (
          <div className="bg-surface rounded-lg p-6">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Bell className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold text-text">Alert Management System</h2>
                <p className="text-sm text-text-secondary">
                  Configure and manage automated alerts, escalation policies, and notification workflows
                </p>
              </div>
            </div>

            <div className="text-center py-12">
              <Settings className="w-16 h-16 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text mb-2">Alert Management Coming Soon</h3>
              <p className="text-text-secondary mb-4">
                Advanced alert management features are currently under development.
              </p>
              <p className="text-sm text-text-secondary">
                This will include automated alerting, escalation workflows, notification management,
                and integration with existing monitoring systems.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SiteReliabilityGuardian;
