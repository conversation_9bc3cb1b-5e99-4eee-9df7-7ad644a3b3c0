import React, { useState, useMemo } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useUptimeMetrics } from '../../hooks/useSiteReliability';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Activity,
  Server,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  TrendingUp,
  TrendingDown,
  RefreshCw,
  Download,
  Calendar,
  Timer
} from 'lucide-react';

interface UptimeTrackingDashboardProps {
  className?: string;
}

export const UptimeTrackingDashboard: React.FC<UptimeTrackingDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const { uptimeMetrics, isLoading, error, refresh } = useUptimeMetrics();
  
  const [selectedTimeRange, setSelectedTimeRange] = useState<'daily' | 'weekly' | 'monthly' | 'yearly'>('weekly');
  const [selectedService, setSelectedService] = useState<string>('all');

  const chartTheme = getChartTheme(mode === 'dark');

  // Filter services based on selection
  const filteredServices = useMemo(() => {
    if (!uptimeMetrics) return [];
    
    if (selectedService === 'all') {
      return uptimeMetrics;
    }
    
    return uptimeMetrics.filter(service => service.serviceId === selectedService);
  }, [uptimeMetrics, selectedService]);

  // Calculate overall statistics
  const overallStats = useMemo(() => {
    if (!uptimeMetrics || uptimeMetrics.length === 0) {
      return {
        totalServices: 0,
        servicesUp: 0,
        servicesDown: 0,
        servicesDegraded: 0,
        averageUptime: 0,
        totalDowntime: 0,
        averageMTTR: 0,
        averageMTBF: 0
      };
    }

    const totalServices = uptimeMetrics.length;
    const servicesUp = uptimeMetrics.filter(s => s.currentStatus === 'up').length;
    const servicesDown = uptimeMetrics.filter(s => s.currentStatus === 'down').length;
    const servicesDegraded = uptimeMetrics.filter(s => s.currentStatus === 'degraded').length;
    const averageUptime = uptimeMetrics.reduce((sum, s) => sum + s.uptimePercentage, 0) / totalServices;
    const totalDowntime = uptimeMetrics.reduce((sum, s) => sum + s.downtimeMinutes, 0);
    const averageMTTR = uptimeMetrics.reduce((sum, s) => sum + s.mttr, 0) / totalServices;
    const averageMTBF = uptimeMetrics.reduce((sum, s) => sum + s.mtbf, 0) / totalServices;

    return {
      totalServices,
      servicesUp,
      servicesDown,
      servicesDegraded,
      averageUptime,
      totalDowntime,
      averageMTTR,
      averageMTBF
    };
  }, [uptimeMetrics]);

  // Generate uptime trend chart data
  const uptimeTrendData = useMemo(() => {
    if (!uptimeMetrics) return null;

    const last30Days = Array.from({ length: 30 }, (_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - (29 - i));
      return date.toISOString().split('T')[0];
    });

    return {
      labels: last30Days.map(date => new Date(date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })),
      datasets: uptimeMetrics.map((service, index) => ({
        label: service.serviceName,
        data: last30Days.map(() => 99.5 + Math.random() * 0.4 + (Math.sin(Math.random() * 10) * 0.2)),
        borderColor: [
          'rgb(79, 142, 247)',
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)',
          'rgb(139, 92, 246)'
        ][index % 5],
        backgroundColor: [
          'rgba(79, 142, 247, 0.1)',
          'rgba(52, 211, 153, 0.1)',
          'rgba(251, 191, 36, 0.1)',
          'rgba(248, 113, 113, 0.1)',
          'rgba(139, 92, 246, 0.1)'
        ][index % 5],
        borderWidth: 2,
        fill: false,
        tension: 0.4,
        pointRadius: 2,
        pointHoverRadius: 4,
      }))
    };
  }, [uptimeMetrics]);

  // Generate availability comparison chart
  const availabilityComparisonData = useMemo(() => {
    if (!uptimeMetrics) return null;

    return {
      labels: uptimeMetrics.map(service => service.serviceName),
      datasets: [
        {
          label: 'Daily',
          data: uptimeMetrics.map(service => service.availability.daily),
          backgroundColor: 'rgba(79, 142, 247, 0.8)',
          borderColor: 'rgb(79, 142, 247)',
          borderWidth: 1,
        },
        {
          label: 'Weekly',
          data: uptimeMetrics.map(service => service.availability.weekly),
          backgroundColor: 'rgba(52, 211, 153, 0.8)',
          borderColor: 'rgb(52, 211, 153)',
          borderWidth: 1,
        },
        {
          label: 'Monthly',
          data: uptimeMetrics.map(service => service.availability.monthly),
          backgroundColor: 'rgba(251, 191, 36, 0.8)',
          borderColor: 'rgb(251, 191, 36)',
          borderWidth: 1,
        }
      ]
    };
  }, [uptimeMetrics]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'up':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'down':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'degraded':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'up':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'down':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      case 'degraded':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const formatDuration = (minutes: number) => {
    if (minutes < 60) {
      return `${Math.round(minutes)}m`;
    } else if (minutes < 1440) {
      return `${Math.round(minutes / 60)}h ${Math.round(minutes % 60)}m`;
    } else {
      return `${Math.round(minutes / 1440)}d ${Math.round((minutes % 1440) / 60)}h`;
    }
  };

  if (isLoading && !uptimeMetrics) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Uptime Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Activity className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-text">Uptime Tracking Dashboard</h2>
            <p className="text-sm text-text-secondary">
              Real-time monitoring of service availability and performance metrics
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={refresh}
            disabled={isLoading}
            className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
            title="Refresh data"
          >
            <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          
          <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
            <Download className="w-4 h-4" />
            Export
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Total Services</p>
              <p className="text-2xl font-bold text-text">{overallStats.totalServices}</p>
            </div>
            <Server className="w-8 h-8 text-primary" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Services Up</p>
              <p className="text-2xl font-bold text-green-500">{overallStats.servicesUp}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Average Uptime</p>
              <p className="text-2xl font-bold text-text">{overallStats.averageUptime.toFixed(2)}%</p>
            </div>
            <TrendingUp className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Avg MTTR</p>
              <p className="text-2xl font-bold text-text">{formatDuration(overallStats.averageMTTR)}</p>
            </div>
            <Timer className="w-8 h-8 text-amber-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Uptime Trend Chart */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">30-Day Uptime Trend</h3>
          {uptimeTrendData && (
            <div className="h-64">
              <Line
                data={uptimeTrendData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`
                      }
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 98,
                      max: 100
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Availability Comparison */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">Availability Comparison</h3>
          {availabilityComparisonData && (
            <div className="h-64">
              <Bar
                data={availabilityComparisonData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                      callbacks: {
                        label: (context) => `${context.dataset.label}: ${context.parsed.y.toFixed(2)}%`
                      }
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 95,
                      max: 100
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Service Filters */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex items-center gap-2">
          <Calendar className="w-4 h-4 text-text-secondary" />
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
          </select>
        </div>

        <div className="flex items-center gap-2">
          <Server className="w-4 h-4 text-text-secondary" />
          <select
            value={selectedService}
            onChange={(e) => setSelectedService(e.target.value)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="all">All Services</option>
            {uptimeMetrics?.map((service) => (
              <option key={service.serviceId} value={service.serviceId}>
                {service.serviceName}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Service Details */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text">Service Details</h3>

        {filteredServices.length === 0 ? (
          <div className="text-center py-8">
            <Server className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">No services found.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {filteredServices.map((service) => (
              <div key={service.serviceId} className="bg-card rounded-lg p-6 border border-border hover:shadow-md transition-all duration-200">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(service.currentStatus)}
                    <div>
                      <h4 className="text-lg font-semibold text-text">{service.serviceName}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(service.currentStatus)}`}>
                        {service.currentStatus.charAt(0).toUpperCase() + service.currentStatus.slice(1)}
                      </span>
                    </div>
                  </div>

                  <div className="text-right">
                    <p className="text-2xl font-bold text-text">{service.uptimePercentage.toFixed(2)}%</p>
                    <p className="text-sm text-text-secondary">Current Uptime</p>
                  </div>
                </div>

                {/* Metrics Grid */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div className="text-center">
                    <p className="text-xs text-text-secondary">Downtime</p>
                    <p className="text-sm font-semibold text-text">{formatDuration(service.downtimeMinutes)}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-text-secondary">MTTR</p>
                    <p className="text-sm font-semibold text-text">{formatDuration(service.mttr)}</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-text-secondary">MTBF</p>
                    <p className="text-sm font-semibold text-text">{service.mtbf.toFixed(1)}h</p>
                  </div>
                  <div className="text-center">
                    <p className="text-xs text-text-secondary">Last Incident</p>
                    <p className="text-sm font-semibold text-text">
                      {service.lastIncident ? new Date(service.lastIncident).toLocaleDateString() : 'None'}
                    </p>
                  </div>
                </div>

                {/* Availability Breakdown */}
                <div className="border-t border-border pt-4">
                  <h5 className="text-sm font-semibold text-text mb-3">Availability Breakdown</h5>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-xs text-text-secondary">Daily</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold text-text">{service.availability.daily.toFixed(2)}%</p>
                        {service.availability.daily >= 99.9 ? (
                          <TrendingUp className="w-3 h-3 text-green-500" />
                        ) : (
                          <TrendingDown className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-text-secondary">Weekly</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold text-text">{service.availability.weekly.toFixed(2)}%</p>
                        {service.availability.weekly >= 99.5 ? (
                          <TrendingUp className="w-3 h-3 text-green-500" />
                        ) : (
                          <TrendingDown className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-text-secondary">Monthly</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold text-text">{service.availability.monthly.toFixed(2)}%</p>
                        {service.availability.monthly >= 99.0 ? (
                          <TrendingUp className="w-3 h-3 text-green-500" />
                        ) : (
                          <TrendingDown className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                    <div>
                      <p className="text-xs text-text-secondary">Yearly</p>
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-semibold text-text">{service.availability.yearly.toFixed(2)}%</p>
                        {service.availability.yearly >= 98.0 ? (
                          <TrendingUp className="w-3 h-3 text-green-500" />
                        ) : (
                          <TrendingDown className="w-3 h-3 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
