import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, User<PERSON>he<PERSON>, Shield, CheckCircle } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useTheme } from '../../context/ThemeContext';
import DocumentScanner from './DocumentScanner';
import VerificationResults from './VerificationResults';
import TravelRestrictions from './TravelRestrictions';

type VerificationSection = 'scanner' | 'results' | 'restrictions';

interface VerificationStats {
  totalVerifications: number;
  successfulVerifications: number;
  failedVerifications: number;
  pendingVerifications: number;
  accuracyRate: number;
}

const FlyerVerificationDashboard: React.FC = () => {
  const navigate = useNavigate();
  // const { mode } = useTheme(); // Commented out as not currently used
  const [activeSection, setActiveSection] = useState<VerificationSection>('scanner');
  const [stats, setStats] = useState<VerificationStats>({
    totalVerifications: 1247,
    successfulVerifications: 1198,
    failedVerifications: 23,
    pendingVerifications: 26,
    accuracyRate: 96.1
  });

  // Simulate real-time stats updates
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        ...prev,
        totalVerifications: prev.totalVerifications + Math.floor(Math.random() * 3),
        successfulVerifications: prev.successfulVerifications + Math.floor(Math.random() * 2),
        pendingVerifications: Math.max(0, prev.pendingVerifications + Math.floor(Math.random() * 3) - 1)
      }));
    }, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const sidebarItems = [
    {
      id: 'scanner' as VerificationSection,
      name: 'Document Scanner',
      description: 'Advanced OCR',
      icon: UserCheck,
      implemented: true,
      status: 'active'
    },
    {
      id: 'results' as VerificationSection,
      name: 'Verification Results',
      description: 'Real-time Status & History',
      icon: CheckCircle,
      implemented: true,
      status: 'active'
    },
    {
      id: 'restrictions' as VerificationSection,
      name: 'Travel Restrictions',
      description: 'Daily Policy Updates',
      icon: Shield,
      implemented: true,
      status: 'active'
    }
  ];

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'scanner':
        return <DocumentScanner />;
      case 'results':
        return <VerificationResults />;
      case 'restrictions':
        return <TravelRestrictions />;
      default:
        return <DocumentScanner />;
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="bg-card border-b border-border px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => navigate('/')}
              className="p-2 hover:bg-surface rounded-lg transition-colors duration-200"
            >
              <ArrowLeft className="w-5 h-5 text-text-secondary" />
            </button>
            <div>
              <h1 className="text-2xl font-bold text-text">Passenger Document Verification</h1>
              <p className="text-text-secondary">passenger documentation verification</p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-success rounded-full animate-pulse"></div>
            <span className="text-sm text-success font-medium">System Active</span>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Sidebar */}
        <div className="w-80 bg-card border-r border-border min-h-screen">
          {/* Stats Overview */}
          <div className="p-6 border-b border-border">
            <h3 className="text-lg font-semibold text-text mb-4">Today's Statistics</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-surface rounded-lg p-3">
                <div className="text-2xl font-bold text-success">{stats.successfulVerifications}</div>
                <div className="text-xs text-text-secondary">Verified</div>
              </div>
              <div className="bg-surface rounded-lg p-3">
                <div className="text-2xl font-bold text-error">{stats.failedVerifications}</div>
                <div className="text-xs text-text-secondary">Failed</div>
              </div>
              <div className="bg-surface rounded-lg p-3">
                <div className="text-2xl font-bold text-warning">{stats.pendingVerifications}</div>
                <div className="text-xs text-text-secondary">Pending</div>
              </div>
              <div className="bg-surface rounded-lg p-3">
                <div className="text-2xl font-bold text-primary">{stats.accuracyRate}%</div>
                <div className="text-xs text-text-secondary">Accuracy</div>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="p-4">
            <div className="space-y-2">
              {sidebarItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveSection(item.id)}
                    className={`w-full text-left p-4 rounded-lg transition-all duration-200 ${
                      isActive
                        ? 'bg-primary/10 text-primary border border-primary/20'
                        : 'hover:bg-surface text-text-secondary hover:text-text'
                    }`}
                  >
                    <div className="flex items-start space-x-3">
                      <div className={`p-2 rounded-lg ${
                        isActive ? 'bg-primary/20' : 'bg-surface'
                      }`}>
                        <Icon className={`w-5 h-5 ${
                          isActive ? 'text-primary' : 'text-text-secondary'
                        }`} />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className={`font-medium text-sm ${
                          isActive ? 'text-primary' : 'text-text'
                        }`}>
                          {item.name}
                        </div>
                        <div className="text-xs text-text-secondary mt-1">
                          {item.description}
                        </div>
                        {item.status === 'active' && (
                          <div className="flex items-center space-x-1 mt-2">
                            <div className="w-2 h-2 bg-success rounded-full"></div>
                            <span className="text-xs text-success">Active</span>
                          </div>
                        )}
                      </div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-6">
          {renderActiveSection()}
        </div>
      </div>
    </div>
  );
};

export default FlyerVerificationDashboard;
