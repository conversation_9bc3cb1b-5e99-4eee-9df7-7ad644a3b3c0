import React, { useState, useEffect } from 'react';
import { Modal } from '../UI/Modal';
import { FormInput, FormSelect, FormTextarea } from '../UI/FormInput';
import { Button } from '../UI/Button';
import { createValidator, validateRequired } from '../../utils/formValidation';
import { Branch, DataFlow } from '../../types/gdprTypes';
import { useTheme } from '../../context/ThemeContext';
import { X, Plus } from 'lucide-react';

interface BranchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (branch: Partial<Branch>) => Promise<void>;
  branch?: Branch | null;
  mode: 'create' | 'edit';
}

const riskLevelOptions = [
  { value: 'low', label: 'Low Risk' },
  { value: 'medium', label: 'Medium Risk' },
  { value: 'high', label: 'High Risk' },
  { value: 'critical', label: 'Critical Risk' }
];

const regionOptions = [
  { value: 'europe', label: 'Europe' },
  { value: 'north_america', label: 'North America' },
  { value: 'south_america', label: 'South America' },
  { value: 'asia_pacific', label: 'Asia Pacific' },
  { value: 'middle_east', label: 'Middle East' },
  { value: 'africa', label: 'Africa' }
];

export const BranchModal: React.FC<BranchModalProps> = ({
  isOpen,
  onClose,
  onSave,
  branch,
  mode
}) => {
  const { mode: themeMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const [regulations, setRegulations] = useState<string[]>([]);
  const [newRegulation, setNewRegulation] = useState('');
  
  const [formData, setFormData] = useState({
    name: '',
    location: '',
    country: '',
    region: 'europe',
    complianceScore: 100,
    riskLevel: 'low' as 'low' | 'medium' | 'high' | 'critical',
    contactPerson: ''
  });

  const validator = createValidator({
    name: { ...validateRequired() },
    location: { ...validateRequired() },
    country: { ...validateRequired() },
    region: { ...validateRequired() },
    contactPerson: { ...validateRequired() }
  });

  useEffect(() => {
    if (branch && mode === 'edit') {
      setFormData({
        name: branch.name,
        location: branch.location,
        country: branch.country,
        region: branch.region,
        complianceScore: branch.complianceScore,
        riskLevel: branch.riskLevel,
        contactPerson: branch.contactPerson
      });
      setRegulations(branch.regulations || []);
    } else {
      // Reset form for create mode
      setFormData({
        name: '',
        location: '',
        country: '',
        region: 'europe',
        complianceScore: 100,
        riskLevel: 'low',
        contactPerson: ''
      });
      setRegulations([]);
    }
    setErrors({});
  }, [branch, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const handleAddRegulation = () => {
    if (newRegulation.trim()) {
      setRegulations(prev => [...prev, newRegulation.trim()]);
      setNewRegulation('');
    }
  };

  const handleRemoveRegulation = (index: number) => {
    setRegulations(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validator.validate(formData);
    if (!validation.isValid) {
      const fieldErrors: any = {};
      validation.errors.forEach(error => {
        fieldErrors[error.field] = error.message;
      });
      setErrors(fieldErrors);
      return;
    }

    setLoading(true);
    try {
      const branchData: Partial<Branch> = {
        ...formData,
        regulations,
        ...(mode === 'edit' && branch ? { id: branch.id } : {}),
        lastAssessment: mode === 'create' ? new Date() : branch?.lastAssessment,
        dataFlows: branch?.dataFlows || []
      };

      await onSave(branchData);
      onClose();
    } catch (error) {
      console.error('Error saving branch:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldError = (field: string) => errors[field] || null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Add New Branch' : 'Edit Branch'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormInput
            label="Branch Name"
            value={formData.name}
            onChange={(e) => handleInputChange('name', e.target.value)}
            error={getFieldError('name')}
            placeholder="e.g. London Headquarters"
            required
          />

          <FormInput
            label="Location"
            value={formData.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            error={getFieldError('location')}
            placeholder="e.g. 123 Business Street"
            required
          />

          <FormInput
            label="Country"
            value={formData.country}
            onChange={(e) => handleInputChange('country', e.target.value)}
            error={getFieldError('country')}
            placeholder="e.g. United Kingdom"
            required
          />

          <FormSelect
            label="Region"
            value={formData.region}
            onChange={(e) => handleInputChange('region', e.target.value)}
            options={regionOptions}
            error={getFieldError('region')}
            required
          />

          <FormInput
            label="Contact Person"
            value={formData.contactPerson}
            onChange={(e) => handleInputChange('contactPerson', e.target.value)}
            error={getFieldError('contactPerson')}
            placeholder="e.g. John Smith"
            required
          />

          <FormSelect
            label="Risk Level"
            value={formData.riskLevel}
            onChange={(e) => handleInputChange('riskLevel', e.target.value)}
            options={riskLevelOptions}
            error={getFieldError('riskLevel')}
            required
          />
        </div>

        {mode === 'edit' && (
          <div>
            <label className="block text-sm font-medium text-text mb-2">
              Compliance Score: {formData.complianceScore}%
            </label>
            <input
              type="range"
              min="0"
              max="100"
              value={formData.complianceScore}
              onChange={(e) => handleInputChange('complianceScore', parseInt(e.target.value))}
              className={`
                w-full h-2 rounded-lg appearance-none cursor-pointer
                ${themeMode === 'dark' ? 'bg-surface' : 'bg-surface'}
              `}
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${formData.complianceScore}%, ${themeMode === 'dark' ? '#374151' : '#f3f4f6'} ${formData.complianceScore}%, ${themeMode === 'dark' ? '#374151' : '#f3f4f6'} 100%)`
              }}
            />
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Applicable Regulations
          </label>
          <div className="flex items-center gap-2 mb-2">
            <FormInput
              value={newRegulation}
              onChange={(e) => setNewRegulation(e.target.value)}
              placeholder="e.g. GDPR, CCPA, LGPD"
              className="flex-1"
            />
            <Button
              type="button"
              onClick={handleAddRegulation}
              variant="outline"
              size="sm"
              leftIcon={<Plus className="w-4 h-4" />}
            >
              Add
            </Button>
          </div>
          
          <div className="mt-3 flex flex-wrap gap-2">
            {regulations.map((regulation, index) => (
              <div 
                key={index} 
                className={`
                  px-3 py-1 rounded-full text-sm flex items-center gap-1
                  ${themeMode === 'dark' 
                    ? 'bg-surface text-text border border-border' 
                    : 'bg-surface text-text border border-border'
                  }
                `}
              >
                <span>{regulation}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveRegulation(index)}
                  className="text-text-secondary hover:text-text"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            {regulations.length === 0 && (
              <p className="text-sm text-text-secondary">No regulations added yet</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3 justify-end pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create Branch' : 'Update Branch'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};

// Data Flow Modal Component
interface DataFlowModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (dataFlow: Partial<DataFlow>) => Promise<void>;
  dataFlow?: DataFlow | null;
  mode: 'create' | 'edit';
  branches: Branch[];
}

const frequencyOptions = [
  { value: 'daily', label: 'Daily' },
  { value: 'weekly', label: 'Weekly' },
  { value: 'monthly', label: 'Monthly' },
  { value: 'on-demand', label: 'On Demand' }
];

const dataTypeOptions = [
  { value: 'personal_data', label: 'Personal Data' },
  { value: 'sensitive_data', label: 'Sensitive Data' },
  { value: 'financial_data', label: 'Financial Data' },
  { value: 'health_data', label: 'Health Data' },
  { value: 'biometric_data', label: 'Biometric Data' },
  { value: 'location_data', label: 'Location Data' }
];

const legalBasisOptions = [
  { value: 'consent', label: 'Consent' },
  { value: 'contract', label: 'Contract' },
  { value: 'legal_obligation', label: 'Legal Obligation' },
  { value: 'vital_interests', label: 'Vital Interests' },
  { value: 'public_task', label: 'Public Task' },
  { value: 'legitimate_interests', label: 'Legitimate Interests' }
];

export const DataFlowModal: React.FC<DataFlowModalProps> = ({
  isOpen,
  onClose,
  onSave,
  dataFlow,
  mode,
  branches
}) => {
  const { mode: themeMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  const [safeguards, setSafeguards] = useState<string[]>([]);
  const [newSafeguard, setNewSafeguard] = useState('');

  const [formData, setFormData] = useState({
    fromBranch: '',
    toBranch: '',
    dataType: 'personal_data',
    volume: 0,
    frequency: 'monthly' as 'daily' | 'weekly' | 'monthly' | 'on-demand',
    purpose: '',
    legalBasis: 'consent',
    riskScore: 1
  });

  const validator = createValidator({
    fromBranch: { ...validateRequired() },
    toBranch: { ...validateRequired() },
    dataType: { ...validateRequired() },
    purpose: { ...validateRequired(), minLength: 10 },
    legalBasis: { ...validateRequired() },
    volume: { required: true, min: 1 }
  });

  useEffect(() => {
    if (dataFlow && mode === 'edit') {
      setFormData({
        fromBranch: dataFlow.fromBranch,
        toBranch: dataFlow.toBranch,
        dataType: dataFlow.dataType,
        volume: dataFlow.volume,
        frequency: dataFlow.frequency,
        purpose: dataFlow.purpose,
        legalBasis: dataFlow.legalBasis,
        riskScore: dataFlow.riskScore
      });
      setSafeguards(dataFlow.safeguards || []);
    } else {
      // Reset form for create mode
      setFormData({
        fromBranch: '',
        toBranch: '',
        dataType: 'personal_data',
        volume: 0,
        frequency: 'monthly',
        purpose: '',
        legalBasis: 'consent',
        riskScore: 1
      });
      setSafeguards([]);
    }
    setErrors({});
  }, [dataFlow, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const handleAddSafeguard = () => {
    if (newSafeguard.trim()) {
      setSafeguards(prev => [...prev, newSafeguard.trim()]);
      setNewSafeguard('');
    }
  };

  const handleRemoveSafeguard = (index: number) => {
    setSafeguards(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    const validation = validator.validate(formData);
    if (!validation.isValid) {
      const fieldErrors: any = {};
      validation.errors.forEach(error => {
        fieldErrors[error.field] = error.message;
      });
      setErrors(fieldErrors);
      return;
    }

    if (formData.fromBranch === formData.toBranch) {
      setErrors({ toBranch: 'Destination branch must be different from source branch' });
      return;
    }

    setLoading(true);
    try {
      const dataFlowData: Partial<DataFlow> = {
        ...formData,
        safeguards,
        ...(mode === 'edit' && dataFlow ? { id: dataFlow.id } : {}),
        lastReview: mode === 'create' ? new Date() : dataFlow?.lastReview
      };

      await onSave(dataFlowData);
      onClose();
    } catch (error) {
      console.error('Error saving data flow:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldError = (field: string) => errors[field] || null;

  const branchOptions = branches.map(branch => ({
    value: branch.id,
    label: `${branch.name} (${branch.location})`
  }));

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Add Data Flow' : 'Edit Data Flow'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormSelect
            label="From Branch"
            value={formData.fromBranch}
            onChange={(e) => handleInputChange('fromBranch', e.target.value)}
            options={branchOptions}
            error={getFieldError('fromBranch')}
            placeholder="Select source branch"
            required
          />

          <FormSelect
            label="To Branch"
            value={formData.toBranch}
            onChange={(e) => handleInputChange('toBranch', e.target.value)}
            options={branchOptions.filter(b => b.value !== formData.fromBranch)}
            error={getFieldError('toBranch')}
            placeholder="Select destination branch"
            required
          />

          <FormSelect
            label="Data Type"
            value={formData.dataType}
            onChange={(e) => handleInputChange('dataType', e.target.value)}
            options={dataTypeOptions}
            error={getFieldError('dataType')}
            required
          />

          <FormSelect
            label="Frequency"
            value={formData.frequency}
            onChange={(e) => handleInputChange('frequency', e.target.value)}
            options={frequencyOptions}
            error={getFieldError('frequency')}
            required
          />

          <FormInput
            label="Volume (records/month)"
            type="number"
            value={formData.volume}
            onChange={(e) => handleInputChange('volume', parseInt(e.target.value) || 0)}
            error={getFieldError('volume')}
            min="1"
            required
          />

          <FormSelect
            label="Legal Basis"
            value={formData.legalBasis}
            onChange={(e) => handleInputChange('legalBasis', e.target.value)}
            options={legalBasisOptions}
            error={getFieldError('legalBasis')}
            required
          />
        </div>

        <FormTextarea
          label="Purpose"
          value={formData.purpose}
          onChange={(e) => handleInputChange('purpose', e.target.value)}
          error={getFieldError('purpose')}
          placeholder="Describe the purpose of this data transfer..."
          rows={3}
          required
        />

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Risk Score: {formData.riskScore}/10
          </label>
          <input
            type="range"
            min="1"
            max="10"
            value={formData.riskScore}
            onChange={(e) => handleInputChange('riskScore', parseInt(e.target.value))}
            className={`
              w-full h-2 rounded-lg appearance-none cursor-pointer
              ${themeMode === 'dark' ? 'bg-surface' : 'bg-surface'}
            `}
            style={{
              background: `linear-gradient(to right, #10b981 0%, #10b981 ${(formData.riskScore - 1) * 11.11}%, #ef4444 ${(formData.riskScore - 1) * 11.11}%, #ef4444 100%)`
            }}
          />
          <div className="flex justify-between text-xs text-text-secondary mt-1">
            <span>Low Risk</span>
            <span>High Risk</span>
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-text mb-2">
            Safeguards
          </label>
          <div className="flex items-center gap-2 mb-2">
            <FormInput
              value={newSafeguard}
              onChange={(e) => setNewSafeguard(e.target.value)}
              placeholder="e.g. Encryption, Pseudonymization, Access Controls"
              className="flex-1"
            />
            <Button
              type="button"
              onClick={handleAddSafeguard}
              variant="outline"
              size="sm"
              leftIcon={<Plus className="w-4 h-4" />}
            >
              Add
            </Button>
          </div>

          <div className="mt-3 flex flex-wrap gap-2">
            {safeguards.map((safeguard, index) => (
              <div
                key={index}
                className={`
                  px-3 py-1 rounded-full text-sm flex items-center gap-1
                  ${themeMode === 'dark'
                    ? 'bg-surface text-text border border-border'
                    : 'bg-surface text-text border border-border'
                  }
                `}
              >
                <span>{safeguard}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveSafeguard(index)}
                  className="text-text-secondary hover:text-text"
                >
                  <X className="w-3 h-3" />
                </button>
              </div>
            ))}
            {safeguards.length === 0 && (
              <p className="text-sm text-text-secondary">No safeguards added yet</p>
            )}
          </div>
        </div>

        <div className="flex items-center gap-3 justify-end pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create Data Flow' : 'Update Data Flow'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
