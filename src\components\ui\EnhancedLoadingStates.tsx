import React from 'react';
import { RefreshCw, Loader, Database, FileText, Users, BarChart3, Shield, AlertTriangle } from 'lucide-react';

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'error';
  className?: string;
}

interface LoadingStateProps {
  type?: 'spinner' | 'skeleton' | 'pulse' | 'dots';
  size?: 'sm' | 'md' | 'lg' | 'xl';
  message?: string;
  progress?: number;
  className?: string;
}

interface SkeletonProps {
  variant?: 'text' | 'rectangular' | 'circular' | 'card' | 'table';
  width?: string | number;
  height?: string | number;
  lines?: number;
  className?: string;
}

interface LoadingOverlayProps {
  isVisible: boolean;
  message?: string;
  progress?: number;
  onCancel?: () => void;
  className?: string;
}

interface DataLoadingStateProps {
  type: 'departments' | 'policies' | 'metrics' | 'assessments' | 'general';
  message?: string;
  progress?: number;
  className?: string;
}

// Enhanced Loading Spinner
export const EnhancedLoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className = ''
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  };

  const colorClasses = {
    primary: 'text-primary',
    secondary: 'text-text-secondary',
    success: 'text-green-500',
    warning: 'text-yellow-500',
    error: 'text-red-500'
  };

  return (
    <RefreshCw 
      className={`animate-spin ${sizeClasses[size]} ${colorClasses[color]} ${className}`}
    />
  );
};

// Skeleton Loading Component
export const Skeleton: React.FC<SkeletonProps> = ({
  variant = 'text',
  width = '100%',
  height,
  lines = 1,
  className = ''
}) => {
  const baseClasses = 'bg-surface animate-pulse rounded';
  
  if (variant === 'text') {
    return (
      <div className={`space-y-2 ${className}`}>
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={`${baseClasses} h-4`}
            style={{ 
              width: index === lines - 1 && lines > 1 ? '75%' : width 
            }}
          />
        ))}
      </div>
    );
  }

  if (variant === 'circular') {
    return (
      <div
        className={`${baseClasses} rounded-full ${className}`}
        style={{ width: width || height || '40px', height: height || width || '40px' }}
      />
    );
  }

  if (variant === 'card') {
    return (
      <div className={`${baseClasses} p-4 ${className}`} style={{ width, height: height || '200px' }}>
        <div className="space-y-3">
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          <div className="space-y-2">
            <div className="h-3 bg-gray-300 rounded"></div>
            <div className="h-3 bg-gray-300 rounded w-5/6"></div>
          </div>
          <div className="flex space-x-2">
            <div className="h-8 bg-gray-300 rounded w-20"></div>
            <div className="h-8 bg-gray-300 rounded w-20"></div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'table') {
    return (
      <div className={`space-y-2 ${className}`}>
        {/* Table header */}
        <div className="grid grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <div key={index} className={`${baseClasses} h-4`} />
          ))}
        </div>
        {/* Table rows */}
        {Array.from({ length: 5 }).map((_, rowIndex) => (
          <div key={rowIndex} className="grid grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, colIndex) => (
              <div key={colIndex} className={`${baseClasses} h-3`} />
            ))}
          </div>
        ))}
      </div>
    );
  }

  return (
    <div
      className={`${baseClasses} ${className}`}
      style={{ width, height: height || '20px' }}
    />
  );
};

// Loading State with Progress
export const LoadingState: React.FC<LoadingStateProps> = ({
  type = 'spinner',
  size = 'md',
  message = 'Loading...',
  progress,
  className = ''
}) => {
  if (type === 'skeleton') {
    return (
      <div className={`space-y-4 ${className}`}>
        <Skeleton variant="card" />
        {message && (
          <div className="text-center text-text-secondary text-sm">{message}</div>
        )}
      </div>
    );
  }

  if (type === 'dots') {
    return (
      <div className={`flex items-center justify-center space-x-1 ${className}`}>
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="w-2 h-2 bg-primary rounded-full animate-bounce"
            style={{ animationDelay: `${index * 0.1}s` }}
          />
        ))}
        {message && (
          <span className="ml-3 text-text-secondary text-sm">{message}</span>
        )}
      </div>
    );
  }

  if (type === 'pulse') {
    return (
      <div className={`flex items-center justify-center ${className}`}>
        <div className="w-8 h-8 bg-primary rounded-full animate-pulse" />
        {message && (
          <span className="ml-3 text-text-secondary text-sm">{message}</span>
        )}
      </div>
    );
  }

  // Default spinner
  return (
    <div className={`flex flex-col items-center justify-center space-y-3 ${className}`}>
      <EnhancedLoadingSpinner size={size} />
      {message && (
        <div className="text-text-secondary text-sm text-center">{message}</div>
      )}
      {progress !== undefined && (
        <div className="w-full max-w-xs">
          <div className="flex justify-between text-xs text-text-secondary mb-1">
            <span>Progress</span>
            <span>{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-surface rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>
      )}
    </div>
  );
};

// Loading Overlay
export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
  isVisible,
  message = 'Loading...',
  progress,
  onCancel,
  className = ''
}) => {
  if (!isVisible) return null;

  return (
    <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${className}`}>
      <div className="bg-background border border-border rounded-lg p-8 max-w-sm w-full mx-4">
        <LoadingState
          message={message}
          progress={progress}
          size="lg"
          className="mb-6"
        />
        {onCancel && (
          <button
            onClick={onCancel}
            className="w-full px-4 py-2 border border-border text-text rounded-lg hover:bg-surface-hover transition-colors"
          >
            Cancel
          </button>
        )}
      </div>
    </div>
  );
};

// Data-specific Loading States
export const DataLoadingState: React.FC<DataLoadingStateProps> = ({
  type,
  message,
  progress,
  className = ''
}) => {
  const getIcon = () => {
    switch (type) {
      case 'departments':
        return <Users className="w-8 h-8 text-primary" />;
      case 'policies':
        return <FileText className="w-8 h-8 text-primary" />;
      case 'metrics':
        return <BarChart3 className="w-8 h-8 text-primary" />;
      case 'assessments':
        return <Shield className="w-8 h-8 text-primary" />;
      default:
        return <Database className="w-8 h-8 text-primary" />;
    }
  };

  const getDefaultMessage = () => {
    switch (type) {
      case 'departments':
        return 'Loading department data...';
      case 'policies':
        return 'Loading policy information...';
      case 'metrics':
        return 'Calculating compliance metrics...';
      case 'assessments':
        return 'Loading impact assessments...';
      default:
        return 'Loading data...';
    }
  };

  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      <div className="relative mb-4">
        {getIcon()}
        <div className="absolute -bottom-1 -right-1">
          <EnhancedLoadingSpinner size="sm" />
        </div>
      </div>
      <div className="text-text-secondary text-sm text-center mb-2">
        {message || getDefaultMessage()}
      </div>
      {progress !== undefined && (
        <div className="w-full max-w-xs">
          <div className="w-full bg-surface rounded-full h-2">
            <div
              className="bg-primary h-2 rounded-full transition-all duration-300"
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className="text-xs text-text-secondary text-center mt-1">
            {Math.round(progress)}% complete
          </div>
        </div>
      )}
    </div>
  );
};

// Loading Card Component
export const LoadingCard: React.FC<{ title?: string; className?: string }> = ({
  title = 'Loading',
  className = ''
}) => {
  return (
    <div className={`bg-surface border border-border rounded-lg p-6 ${className}`}>
      <div className="flex items-center justify-center space-x-3">
        <EnhancedLoadingSpinner />
        <span className="text-text-secondary">{title}...</span>
      </div>
    </div>
  );
};

// Table Loading State
export const TableLoadingState: React.FC<{ 
  rows?: number; 
  columns?: number; 
  className?: string 
}> = ({
  rows = 5,
  columns = 4,
  className = ''
}) => {
  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <div className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
        {Array.from({ length: columns }).map((_, index) => (
          <Skeleton key={index} height="20px" />
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="grid gap-4" style={{ gridTemplateColumns: `repeat(${columns}, 1fr)` }}>
          {Array.from({ length: columns }).map((_, colIndex) => (
            <Skeleton key={colIndex} height="16px" />
          ))}
        </div>
      ))}
    </div>
  );
};

// Chart Loading State
export const ChartLoadingState: React.FC<{ 
  type?: 'bar' | 'line' | 'pie' | 'area';
  className?: string 
}> = ({
  type = 'bar',
  className = ''
}) => {
  return (
    <div className={`h-64 flex items-center justify-center border-2 border-dashed border-border rounded-lg ${className}`}>
      <div className="text-center">
        <BarChart3 className="w-12 h-12 text-text-secondary mx-auto mb-2 animate-pulse" />
        <p className="text-text-secondary">Loading {type} chart...</p>
        <div className="mt-2">
          <EnhancedLoadingSpinner size="sm" />
        </div>
      </div>
    </div>
  );
};

// Error State Component
export const ErrorState: React.FC<{
  title?: string;
  message?: string;
  onRetry?: () => void;
  className?: string;
}> = ({
  title = 'Something went wrong',
  message = 'Unable to load data. Please try again.',
  onRetry,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      <AlertTriangle className="w-12 h-12 text-red-500 mb-4" />
      <h3 className="text-lg font-semibold text-text mb-2">{title}</h3>
      <p className="text-text-secondary text-center mb-4 max-w-md">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          className="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
        >
          <RefreshCw className="w-4 h-4" />
          <span>Try Again</span>
        </button>
      )}
    </div>
  );
};

// Empty State Component
export const EmptyState: React.FC<{
  title?: string;
  message?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  icon?: React.ReactNode;
  className?: string;
}> = ({
  title = 'No data available',
  message = 'There is no data to display at this time.',
  action,
  icon,
  className = ''
}) => {
  return (
    <div className={`flex flex-col items-center justify-center py-12 ${className}`}>
      {icon || <Database className="w-12 h-12 text-text-secondary mb-4" />}
      <h3 className="text-lg font-semibold text-text mb-2">{title}</h3>
      <p className="text-text-secondary text-center mb-4 max-w-md">{message}</p>
      {action && (
        <button
          onClick={action.onClick}
          className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
        >
          {action.label}
        </button>
      )}
    </div>
  );
};
