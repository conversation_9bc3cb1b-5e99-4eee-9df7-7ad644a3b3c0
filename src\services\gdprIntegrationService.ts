import { OrganizationalMappingService } from './organizationalMappingService';
import { PoliciesManagementService } from './policiesManagementService';
import { ConsentManagementService } from './consentManagementService';
import { ImpactAssessmentService } from './impactAssessmentService';
import { DataSubjectRequestService } from './dataSubjectRequestService';

// Unified GDPR Dashboard Data Interface
export interface GDPRDashboardData {
  overview: {
    totalDataSubjectRequests: number;
    pendingRequests: number;
    overdueRequests: number;
    completedRequests: number;
    averageProcessingTime: number;
    
    totalPolicies: number;
    activePolicies: number;
    policiesNeedingReview: number;
    averagePolicyCompliance: number;
    
    totalConsentRecords: number;
    activeConsents: number;
    consentRate: number;
    withdrawalRate: number;
    
    totalImpactAssessments: number;
    completedAssessments: number;
    highRiskAssessments: number;
    averageRiskScore: number;
    
    totalOrganizationalUnits: number;
    compliantUnits: number;
    averageComplianceScore: number;
    highRiskUnits: number;
  };
  
  recentActivity: GDPRActivity[];
  upcomingTasks: GDPRTask[];
  alerts: GDPRAlert[];
  complianceMetrics: ComplianceMetric[];
  lastUpdated: Date;
}

export interface GDPRActivity {
  id: string;
  type: 'data_subject_request' | 'policy_update' | 'consent_change' | 'assessment_completed' | 'unit_updated';
  title: string;
  description: string;
  timestamp: Date;
  userId: string;
  userName: string;
  entityId: string;
  entityType: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export interface GDPRTask {
  id: string;
  type: 'review_request' | 'policy_review' | 'assessment_due' | 'consent_renewal' | 'compliance_check';
  title: string;
  description: string;
  dueDate: Date;
  assignee: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  entityId: string;
  entityType: string;
  estimatedEffort: string;
}

export interface GDPRAlert {
  id: string;
  type: 'overdue_request' | 'policy_expired' | 'high_risk_assessment' | 'consent_withdrawal' | 'compliance_breach';
  severity: 'info' | 'warning' | 'error' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  entityId: string;
  entityType: string;
  actionRequired: boolean;
  acknowledged: boolean;
}

export interface ComplianceMetric {
  id: string;
  name: string;
  category: 'requests' | 'policies' | 'consents' | 'assessments' | 'organizational';
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  trendPercentage: number;
  lastUpdated: Date;
}

// Unified search interface
export interface GDPRSearchResult {
  type: 'data_subject_request' | 'policy' | 'consent_record' | 'impact_assessment' | 'organizational_unit';
  id: string;
  title: string;
  description: string;
  status: string;
  lastUpdated: Date;
  relevanceScore: number;
}

// Common filter interface
export interface GDPRFilters {
  dateRange?: {
    start: Date;
    end: Date;
  };
  status?: string[];
  priority?: string[];
  assignee?: string[];
  department?: string[];
  category?: string[];
  riskLevel?: string[];
}

export class GDPRIntegrationService {
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    
    // Initialize all component services
    OrganizationalMappingService.initialize();
    PoliciesManagementService.initialize();
    ConsentManagementService.initialize();
    ImpactAssessmentService.initialize();
    DataSubjectRequestService.initialize();
    
    this.initialized = true;
  }

  // Unified dashboard data aggregation
  static async getDashboardData(): Promise<GDPRDashboardData> {
    this.initialize();

    const [
      requestMetrics,
      policyMetrics,
      consentMetrics,
      assessmentMetrics,
      orgMetrics
    ] = await Promise.all([
      DataSubjectRequestService.getRequestMetrics(),
      PoliciesManagementService.getMetrics(),
      ConsentManagementService.getConsentMetrics(),
      ImpactAssessmentService.getAssessmentMetrics(),
      OrganizationalMappingService.getMetrics()
    ]);

    const overview = {
      totalDataSubjectRequests: requestMetrics.total,
      pendingRequests: requestMetrics.pending,
      overdueRequests: requestMetrics.overdue,
      completedRequests: requestMetrics.completed,
      averageProcessingTime: requestMetrics.averageProcessingTime || 0,
      
      totalPolicies: policyMetrics.totalPolicies,
      activePolicies: policyMetrics.activePolicies,
      policiesNeedingReview: policyMetrics.policiesNeedingReview,
      averagePolicyCompliance: policyMetrics.averageComplianceScore,
      
      totalConsentRecords: consentMetrics.totalUsers,
      activeConsents: consentMetrics.consentedUsers,
      consentRate: consentMetrics.consentRate,
      withdrawalRate: consentMetrics.withdrawalRate || 0,
      
      totalImpactAssessments: assessmentMetrics.total,
      completedAssessments: assessmentMetrics.completed,
      highRiskAssessments: assessmentMetrics.highRiskAssessments,
      averageRiskScore: assessmentMetrics.averageRiskScore,
      
      totalOrganizationalUnits: orgMetrics.totalUnits,
      compliantUnits: orgMetrics.totalUnits - orgMetrics.highRiskUnits,
      averageComplianceScore: orgMetrics.averageComplianceScore,
      highRiskUnits: orgMetrics.highRiskUnits
    };

    const recentActivity = await this.getRecentActivity();
    const upcomingTasks = await this.getUpcomingTasks();
    const alerts = await this.getAlerts();
    const complianceMetrics = await this.getComplianceMetrics();

    return {
      overview,
      recentActivity,
      upcomingTasks,
      alerts,
      complianceMetrics,
      lastUpdated: new Date()
    };
  }

  // Unified search across all GDPR components
  static async search(query: string, filters?: GDPRFilters): Promise<GDPRSearchResult[]> {
    this.initialize();

    const [
      requests,
      policies,
      consentRecords,
      assessments,
      orgUnits
    ] = await Promise.all([
      DataSubjectRequestService.searchRequests(query),
      PoliciesManagementService.searchPolicies(query),
      ConsentManagementService.searchConsentRecords(query),
      ImpactAssessmentService.searchAssessments(query),
      OrganizationalMappingService.searchUnits(query)
    ]);

    const results: GDPRSearchResult[] = [];

    // Convert requests to search results
    requests.forEach(request => {
      results.push({
        type: 'data_subject_request',
        id: request.id,
        title: `${request.type.toUpperCase()} Request - ${request.email}`,
        description: request.description || `${request.type} request from ${request.email}`,
        status: request.status,
        lastUpdated: request.submittedAt,
        relevanceScore: this.calculateRelevance(query, [request.email, request.description || '', request.type])
      });
    });

    // Convert policies to search results
    policies.forEach(policy => {
      results.push({
        type: 'policy',
        id: policy.id,
        title: policy.name,
        description: policy.description,
        status: policy.status,
        lastUpdated: policy.updatedAt,
        relevanceScore: this.calculateRelevance(query, [policy.name, policy.description, policy.category])
      });
    });

    // Convert consent records to search results
    consentRecords.forEach(record => {
      results.push({
        type: 'consent_record',
        id: record.id,
        title: `Consent Record - ${record.email}`,
        description: `Consent record for ${record.firstName} ${record.lastName}`,
        status: record.status,
        lastUpdated: record.lastUpdated,
        relevanceScore: this.calculateRelevance(query, [record.email, record.firstName, record.lastName])
      });
    });

    // Convert assessments to search results
    assessments.forEach(assessment => {
      results.push({
        type: 'impact_assessment',
        id: assessment.id,
        title: assessment.title,
        description: assessment.description,
        status: assessment.status,
        lastUpdated: assessment.updatedAt,
        relevanceScore: this.calculateRelevance(query, [assessment.title, assessment.description])
      });
    });

    // Convert organizational units to search results
    orgUnits.forEach(unit => {
      results.push({
        type: 'organizational_unit',
        id: unit.id,
        title: unit.name,
        description: `${unit.type} in ${unit.location}`,
        status: unit.riskLevel,
        lastUpdated: unit.updatedAt,
        relevanceScore: this.calculateRelevance(query, [unit.name, unit.location, unit.contactPerson])
      });
    });

    // Sort by relevance score and apply filters
    return results
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .filter(result => this.applyFilters(result, filters))
      .slice(0, 50); // Limit to top 50 results
  }

  // Get recent activity across all components
  private static async getRecentActivity(): Promise<GDPRActivity[]> {
    // This would typically aggregate from audit logs across all services
    // For now, return mock data
    return [
      {
        id: 'activity-001',
        type: 'data_subject_request',
        title: 'New Data Access Request',
        description: 'Data access request <NAME_EMAIL>',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        userId: 'system',
        userName: 'System',
        entityId: 'dsr-001',
        entityType: 'data_subject_request',
        priority: 'medium'
      },
      {
        id: 'activity-002',
        type: 'policy_update',
        title: 'Policy Updated',
        description: 'Data Protection Policy v2.1 has been updated',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000), // 4 hours ago
        userId: 'user-001',
        userName: 'Privacy Officer',
        entityId: 'policy-001',
        entityType: 'policy',
        priority: 'low'
      }
    ];
  }

  // Get upcoming tasks across all components
  private static async getUpcomingTasks(): Promise<GDPRTask[]> {
    return [
      {
        id: 'task-001',
        type: 'review_request',
        title: 'Review Data Access Request',
        description: 'Review and process data access <NAME_EMAIL>',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
        assignee: 'Privacy Officer',
        priority: 'high',
        entityId: 'dsr-001',
        entityType: 'data_subject_request',
        estimatedEffort: '2 hours'
      },
      {
        id: 'task-002',
        type: 'policy_review',
        title: 'Policy Review Due',
        description: 'Information Security Policy requires scheduled review',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        assignee: 'CISO',
        priority: 'medium',
        entityId: 'policy-002',
        entityType: 'policy',
        estimatedEffort: '4 hours'
      }
    ];
  }

  // Get alerts across all components
  private static async getAlerts(): Promise<GDPRAlert[]> {
    return [
      {
        id: 'alert-001',
        type: 'overdue_request',
        severity: 'error',
        title: 'Overdue Data Subject Request',
        message: 'Data portability <NAME_EMAIL> is overdue',
        timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
        entityId: 'dsr-003',
        entityType: 'data_subject_request',
        actionRequired: true,
        acknowledged: false
      }
    ];
  }

  // Get compliance metrics across all components
  private static async getComplianceMetrics(): Promise<ComplianceMetric[]> {
    return [
      {
        id: 'metric-001',
        name: 'Request Processing Time',
        category: 'requests',
        value: 18,
        target: 30,
        unit: 'days',
        trend: 'down',
        trendPercentage: -15,
        lastUpdated: new Date()
      },
      {
        id: 'metric-002',
        name: 'Policy Compliance Rate',
        category: 'policies',
        value: 94,
        target: 95,
        unit: '%',
        trend: 'up',
        trendPercentage: 2,
        lastUpdated: new Date()
      },
      {
        id: 'metric-003',
        name: 'Consent Rate',
        category: 'consents',
        value: 85,
        target: 80,
        unit: '%',
        trend: 'stable',
        trendPercentage: 0,
        lastUpdated: new Date()
      }
    ];
  }

  // Calculate relevance score for search results
  private static calculateRelevance(query: string, fields: string[]): number {
    const lowercaseQuery = query.toLowerCase();
    let score = 0;
    
    fields.forEach(field => {
      if (field && field.toLowerCase().includes(lowercaseQuery)) {
        // Exact match gets higher score
        if (field.toLowerCase() === lowercaseQuery) {
          score += 100;
        } else if (field.toLowerCase().startsWith(lowercaseQuery)) {
          score += 75;
        } else {
          score += 50;
        }
      }
    });
    
    return score;
  }

  // Apply filters to search results
  private static applyFilters(result: GDPRSearchResult, filters?: GDPRFilters): boolean {
    if (!filters) return true;

    if (filters.dateRange) {
      if (result.lastUpdated < filters.dateRange.start || result.lastUpdated > filters.dateRange.end) {
        return false;
      }
    }

    if (filters.status && filters.status.length > 0) {
      if (!filters.status.includes(result.status)) {
        return false;
      }
    }

    return true;
  }

  // Cross-component data validation
  static async validateDataConsistency(): Promise<{
    isValid: boolean;
    issues: string[];
  }> {
    this.initialize();
    const issues: string[] = [];

    try {
      // Check if organizational units referenced in policies exist
      const [policies, orgUnits] = await Promise.all([
        PoliciesManagementService.getAllPolicies(),
        OrganizationalMappingService.getAllUnits()
      ]);

      const orgUnitIds = new Set(orgUnits.map(unit => unit.id));
      
      policies.forEach(policy => {
        policy.organizationalUnits.forEach(unitId => {
          if (!orgUnitIds.has(unitId)) {
            issues.push(`Policy "${policy.name}" references non-existent organizational unit: ${unitId}`);
          }
        });
      });

      // Add more validation checks as needed...

    } catch (error) {
      issues.push(`Data validation error: ${error}`);
    }

    return {
      isValid: issues.length === 0,
      issues
    };
  }

  // Bulk operations across components
  static async bulkUpdate(updates: {
    type: 'status_change' | 'assignment_change' | 'priority_change';
    entityType: 'data_subject_request' | 'policy' | 'impact_assessment';
    entityIds: string[];
    updateData: Record<string, any>;
  }): Promise<{
    success: boolean;
    updatedCount: number;
    errors: string[];
  }> {
    this.initialize();
    const errors: string[] = [];
    let updatedCount = 0;

    try {
      for (const entityId of updates.entityIds) {
        let updateResult = false;

        switch (updates.entityType) {
          case 'data_subject_request':
            const updatedRequest = await DataSubjectRequestService.updateRequest(entityId, updates.updateData);
            updateResult = updatedRequest !== null;
            break;
          case 'policy':
            const updatedPolicy = await PoliciesManagementService.updatePolicy(entityId, updates.updateData);
            updateResult = updatedPolicy !== null;
            break;
          case 'impact_assessment':
            const updatedAssessment = await ImpactAssessmentService.updateAssessment(entityId, updates.updateData);
            updateResult = updatedAssessment !== null;
            break;
        }

        if (updateResult) {
          updatedCount++;
        } else {
          errors.push(`Failed to update ${updates.entityType} with ID: ${entityId}`);
        }
      }
    } catch (error) {
      errors.push(`Bulk update error: ${error}`);
    }

    return {
      success: errors.length === 0,
      updatedCount,
      errors
    };
  }

  // Export data across all components
  static async exportData(options: {
    components: ('requests' | 'policies' | 'consents' | 'assessments' | 'organizational')[];
    format: 'json' | 'csv' | 'xlsx';
    filters?: GDPRFilters;
  }): Promise<{
    success: boolean;
    data?: any;
    error?: string;
  }> {
    this.initialize();

    try {
      const exportData: any = {};

      if (options.components.includes('requests')) {
        exportData.dataSubjectRequests = await DataSubjectRequestService.getAllRequests();
      }

      if (options.components.includes('policies')) {
        exportData.policies = await PoliciesManagementService.getAllPolicies();
      }

      if (options.components.includes('consents')) {
        exportData.consentRecords = await ConsentManagementService.getAllConsentRecords();
      }

      if (options.components.includes('assessments')) {
        exportData.impactAssessments = await ImpactAssessmentService.getAllAssessments();
      }

      if (options.components.includes('organizational')) {
        exportData.organizationalUnits = await OrganizationalMappingService.getAllUnits();
      }

      return {
        success: true,
        data: exportData
      };
    } catch (error) {
      return {
        success: false,
        error: `Export failed: ${error}`
      };
    }
  }
}
