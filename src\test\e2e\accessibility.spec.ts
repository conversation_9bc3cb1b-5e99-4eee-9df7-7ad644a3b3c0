import { test, expect } from '@playwright/test';

test.describe('Accessibility E2E Tests', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/');
    await page.waitForSelector('[data-testid="app-loaded"]', { timeout: 10000 });
  });

  test.describe('Keyboard Navigation', () => {
    test('should support keyboard navigation through dashboard tabs', async ({ page }) => {
      // Focus on the first tab
      await page.keyboard.press('Tab');
      
      // Verify Overview tab is focused
      const overviewTab = page.locator('text=Overview');
      await expect(overviewTab).toBeFocused();
      
      // Navigate to next tab using arrow keys
      await page.keyboard.press('ArrowRight');
      
      // Verify Data Subject Requests tab is focused
      const requestsTab = page.locator('text=Data Subject Requests');
      await expect(requestsTab).toBeFocused();
      
      // Continue navigation
      await page.keyboard.press('ArrowRight');
      const branchTab = page.locator('text=Branch Detection');
      await expect(branchTab).toBeFocused();
      
      // Test activation with Enter key
      await page.keyboard.press('Enter');
      await expect(page.locator('text=Branch Detection & Data Flow')).toBeVisible();
    });

    test('should support keyboard navigation in data tables', async ({ page }) => {
      // Navigate to Data Subject Requests
      await page.click('text=Data Subject Requests');
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Tab to the table
      await page.keyboard.press('Tab');
      
      // Navigate through table rows using arrow keys
      await page.keyboard.press('ArrowDown');
      await page.keyboard.press('ArrowDown');
      
      // Test action buttons accessibility
      await page.keyboard.press('Tab'); // Focus on edit button
      await page.keyboard.press('Enter'); // Activate edit
      
      // Verify modal opens
      await expect(page.locator('text=Edit Data Subject Request')).toBeVisible();
      
      // Test modal keyboard navigation
      await page.keyboard.press('Escape'); // Close modal
      await expect(page.locator('text=Edit Data Subject Request')).not.toBeVisible();
    });

    test('should support keyboard navigation in forms', async ({ page }) => {
      // Navigate to Data Subject Requests
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      // Verify modal opens
      await expect(page.locator('text=Create New Data Subject Request')).toBeVisible();
      
      // Tab through form fields
      await page.keyboard.press('Tab'); // Email field
      await page.type('[data-testid="email-input"]', '<EMAIL>');
      
      await page.keyboard.press('Tab'); // Type select
      await page.keyboard.press('Space'); // Open select
      await page.keyboard.press('ArrowDown'); // Navigate options
      await page.keyboard.press('Enter'); // Select option
      
      await page.keyboard.press('Tab'); // Priority select
      await page.keyboard.press('Tab'); // Description field
      await page.type('[data-testid="description-input"]', 'Test description');
      
      await page.keyboard.press('Tab'); // Submit button
      await page.keyboard.press('Enter'); // Submit form
      
      // Verify form submission
      await expect(page.locator('text=Request created successfully')).toBeVisible();
    });

    test('should support skip links for main content', async ({ page }) => {
      // Press Tab to focus on skip link
      await page.keyboard.press('Tab');
      
      // Verify skip link is visible when focused
      const skipLink = page.locator('[data-testid="skip-to-main"]');
      await expect(skipLink).toBeVisible();
      
      // Activate skip link
      await page.keyboard.press('Enter');
      
      // Verify main content is focused
      const mainContent = page.locator('[data-testid="main-content"]');
      await expect(mainContent).toBeFocused();
    });
  });

  test.describe('Screen Reader Support', () => {
    test('should have proper heading structure', async ({ page }) => {
      // Check for proper heading hierarchy
      const h1 = page.locator('h1');
      await expect(h1).toHaveCount(1);
      await expect(h1).toContainText('GDPR Command Center');
      
      // Check for h2 headings in sections
      const h2Elements = page.locator('h2');
      await expect(h2Elements).toHaveCount.greaterThan(0);
      
      // Navigate to different sections and verify headings
      await page.click('text=Data Subject Requests');
      await expect(page.locator('h2:has-text("Data Subject Request Management")')).toBeVisible();
      
      await page.click('text=Branch Detection');
      await expect(page.locator('h2:has-text("Branch Detection & Data Flow")')).toBeVisible();
    });

    test('should have proper ARIA labels and roles', async ({ page }) => {
      // Check navigation has proper role
      const navigation = page.locator('[role="navigation"]');
      await expect(navigation).toBeVisible();
      
      // Check buttons have proper labels
      const buttons = page.locator('button');
      for (const button of await buttons.all()) {
        const ariaLabel = await button.getAttribute('aria-label');
        const textContent = await button.textContent();
        
        // Button should have either aria-label or text content
        expect(ariaLabel || textContent).toBeTruthy();
      }
      
      // Check form inputs have proper labels
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      const inputs = page.locator('input, select, textarea');
      for (const input of await inputs.all()) {
        const id = await input.getAttribute('id');
        if (id) {
          const label = page.locator(`label[for="${id}"]`);
          await expect(label).toBeVisible();
        }
      }
    });

    test('should announce dynamic content changes', async ({ page }) => {
      // Navigate to Data Subject Requests
      await page.click('text=Data Subject Requests');
      
      // Create a new request
      await page.click('button:has-text("Create Request")');
      await page.fill('[data-testid="email-input"]', '<EMAIL>');
      await page.fill('[data-testid="description-input"]', 'Test announcement');
      await page.click('button:has-text("Create Request")');
      
      // Check for aria-live region with success message
      const liveRegion = page.locator('[aria-live="polite"]');
      await expect(liveRegion).toContainText('Request created successfully');
    });

    test('should provide proper table structure', async ({ page }) => {
      // Navigate to Data Subject Requests
      await page.click('text=Data Subject Requests');
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Check table has proper structure
      const table = page.locator('table');
      await expect(table).toHaveAttribute('role', 'table');
      
      // Check for table headers
      const headers = page.locator('th');
      await expect(headers).toHaveCount.greaterThan(0);
      
      // Check headers have proper scope
      for (const header of await headers.all()) {
        const scope = await header.getAttribute('scope');
        expect(scope).toBe('col');
      }
      
      // Check table caption or aria-label
      const caption = page.locator('caption');
      const tableAriaLabel = await table.getAttribute('aria-label');
      
      expect(await caption.count() > 0 || tableAriaLabel).toBeTruthy();
    });
  });

  test.describe('Focus Management', () => {
    test('should manage focus in modals correctly', async ({ page }) => {
      // Open modal
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      // Verify focus is trapped in modal
      const modal = page.locator('[role="dialog"]');
      await expect(modal).toBeVisible();
      
      // First focusable element should be focused
      const firstInput = page.locator('[data-testid="email-input"]');
      await expect(firstInput).toBeFocused();
      
      // Tab through modal elements
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      await page.keyboard.press('Tab');
      
      // Shift+Tab should go backwards
      await page.keyboard.press('Shift+Tab');
      
      // Escape should close modal and restore focus
      await page.keyboard.press('Escape');
      await expect(modal).not.toBeVisible();
      
      // Focus should return to trigger button
      const createButton = page.locator('button:has-text("Create Request")');
      await expect(createButton).toBeFocused();
    });

    test('should manage focus when navigating between sections', async ({ page }) => {
      // Focus on a tab
      const requestsTab = page.locator('text=Data Subject Requests');
      await requestsTab.focus();
      
      // Activate tab
      await page.keyboard.press('Enter');
      
      // Verify content is loaded and focusable
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Navigate to another tab
      const branchTab = page.locator('text=Branch Detection');
      await branchTab.focus();
      await page.keyboard.press('Enter');
      
      // Verify new content is loaded
      await page.waitForSelector('[data-testid="branches-table"]');
    });

    test('should provide visible focus indicators', async ({ page }) => {
      // Tab through interactive elements
      await page.keyboard.press('Tab');
      
      // Check that focused elements have visible focus indicators
      const focusedElement = page.locator(':focus');
      
      // Verify focus indicator is visible (check for outline or box-shadow)
      const styles = await focusedElement.evaluate(el => {
        const computed = window.getComputedStyle(el);
        return {
          outline: computed.outline,
          boxShadow: computed.boxShadow,
          border: computed.border
        };
      });
      
      // At least one focus indicator should be present
      const hasFocusIndicator = styles.outline !== 'none' || 
                               styles.boxShadow !== 'none' || 
                               styles.border !== 'none';
      
      expect(hasFocusIndicator).toBeTruthy();
    });
  });

  test.describe('Color Contrast and Visual Accessibility', () => {
    test('should have sufficient color contrast in light theme', async ({ page }) => {
      // Ensure light theme is active
      const body = page.locator('body');
      if (await body.getAttribute('class') !== 'light') {
        await page.click('[data-testid="theme-toggle"]');
      }
      
      // Check text contrast on main elements
      const textElements = page.locator('h1, h2, h3, p, button, a');
      
      for (const element of await textElements.all()) {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor
          };
        });
        
        // This is a simplified check - in real implementation,
        // you would calculate actual contrast ratios
        expect(styles.color).not.toBe(styles.backgroundColor);
      }
    });

    test('should have sufficient color contrast in dark theme', async ({ page }) => {
      // Switch to dark theme
      await page.click('[data-testid="theme-toggle"]');
      
      // Verify dark theme is active
      const body = page.locator('body');
      await expect(body).toHaveClass(/dark/);
      
      // Check text contrast on main elements
      const textElements = page.locator('h1, h2, h3, p, button, a');
      
      for (const element of await textElements.all()) {
        const styles = await element.evaluate(el => {
          const computed = window.getComputedStyle(el);
          return {
            color: computed.color,
            backgroundColor: computed.backgroundColor
          };
        });
        
        expect(styles.color).not.toBe(styles.backgroundColor);
      }
    });

    test('should not rely solely on color for information', async ({ page }) => {
      // Navigate to a section with status indicators
      await page.click('text=Data Subject Requests');
      await page.waitForSelector('[data-testid="requests-table"]');
      
      // Check that status indicators have text or icons, not just color
      const statusElements = page.locator('[data-testid="status-indicator"]');
      
      for (const element of await statusElements.all()) {
        const textContent = await element.textContent();
        const hasIcon = await element.locator('svg, i').count() > 0;
        
        // Status should have text or icon, not just color
        expect(textContent || hasIcon).toBeTruthy();
      }
    });
  });

  test.describe('Form Accessibility', () => {
    test('should provide clear error messages', async ({ page }) => {
      // Navigate to form
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      // Submit form without required fields
      await page.click('button:has-text("Create Request")');
      
      // Check for error messages
      const errorMessages = page.locator('[role="alert"], .error-message');
      await expect(errorMessages).toHaveCount.greaterThan(0);
      
      // Error messages should be associated with form fields
      const emailError = page.locator('[data-testid="email-error"]');
      if (await emailError.count() > 0) {
        const emailInput = page.locator('[data-testid="email-input"]');
        const ariaDescribedBy = await emailInput.getAttribute('aria-describedby');
        const errorId = await emailError.getAttribute('id');
        
        expect(ariaDescribedBy).toContain(errorId);
      }
    });

    test('should provide helpful field descriptions', async ({ page }) => {
      // Navigate to form
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      // Check for field descriptions
      const inputs = page.locator('input, select, textarea');
      
      for (const input of await inputs.all()) {
        const ariaDescribedBy = await input.getAttribute('aria-describedby');
        
        if (ariaDescribedBy) {
          const description = page.locator(`#${ariaDescribedBy}`);
          await expect(description).toBeVisible();
        }
      }
    });

    test('should indicate required fields clearly', async ({ page }) => {
      // Navigate to form
      await page.click('text=Data Subject Requests');
      await page.click('button:has-text("Create Request")');
      
      // Check for required field indicators
      const requiredInputs = page.locator('input[required], select[required], textarea[required]');
      
      for (const input of await requiredInputs.all()) {
        const ariaRequired = await input.getAttribute('aria-required');
        const hasRequiredIndicator = await input.locator('..').locator('text=*').count() > 0;
        
        // Required fields should have aria-required or visual indicator
        expect(ariaRequired === 'true' || hasRequiredIndicator).toBeTruthy();
      }
    });
  });

  test.describe('Mobile Accessibility', () => {
    test('should be accessible on mobile devices', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Check mobile navigation accessibility
      const mobileMenuToggle = page.locator('[data-testid="mobile-menu-toggle"]');
      await expect(mobileMenuToggle).toBeVisible();
      
      // Verify button has proper label
      const ariaLabel = await mobileMenuToggle.getAttribute('aria-label');
      expect(ariaLabel).toBeTruthy();
      
      // Test mobile menu functionality
      await mobileMenuToggle.click();
      
      const mobileNav = page.locator('[data-testid="mobile-nav"]');
      await expect(mobileNav).toBeVisible();
      
      // Check that mobile nav is properly labeled
      const navAriaLabel = await mobileNav.getAttribute('aria-label');
      expect(navAriaLabel).toBeTruthy();
    });

    test('should have appropriate touch targets on mobile', async ({ page }) => {
      // Set mobile viewport
      await page.setViewportSize({ width: 375, height: 667 });
      
      // Check that interactive elements are large enough for touch
      const buttons = page.locator('button, a, [role="button"]');
      
      for (const button of await buttons.all()) {
        const boundingBox = await button.boundingBox();
        
        if (boundingBox) {
          // Touch targets should be at least 44x44 pixels
          expect(boundingBox.width).toBeGreaterThanOrEqual(44);
          expect(boundingBox.height).toBeGreaterThanOrEqual(44);
        }
      }
    });
  });
});
