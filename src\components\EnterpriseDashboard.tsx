import React, { useState } from 'react';

const ConsentMetric: React.FC<{
  percentage: number;
  color: string;
  label: string;
  sublabel: string;
  size?: 'normal' | 'large';
}> = ({ percentage, color, label, sublabel, size = 'normal' }) => {
  const sizeClasses = size === 'large' ? 'w-40 h-40' : 'w-28 h-28';
  const radius = size === 'large' ? 65 : 40;
  const strokeWidth = size === 'large' ? 12 : 8;
  const circumference = 2 * Math.PI * radius;
  const strokeDashoffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="flex flex-col items-center">
      <div className={`${sizeClasses} relative`}>
        <svg
          className="w-full h-full transform -rotate-90"
          viewBox="0 0 150 150"
        >
          {/* Background circle */}
          <circle
            cx="75"
            cy="75"
            r={radius}
            fill="transparent"
            stroke="var(--color-border)"
            strokeWidth={strokeWidth}
          />
          {/* Progress circle */}
          <circle
            cx="75"
            cy="75"
            r={radius}
            fill="transparent"
            stroke={color}
            strokeWidth={strokeWidth}
            strokeLinecap="round"
            style={{
              strokeDasharray: circumference,
              strokeDashoffset: strokeDashoffset,
              transition: 'stroke-dashoffset 1s ease-in-out',
            }}
          />
        </svg>
        {/* Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center">
            <span className="text-2xl font-bold text-text">{percentage}%</span>
          </div>
        </div>
      </div>
      <div className="mt-3 text-center">
        <p className="text-sm font-medium text-text">{label}</p>
        <p className="text-xs text-text-secondary">{sublabel}</p>
      </div>
    </div>
  );
};

const EnterpriseDashboard = () => {
  // TODO: Modify navigation structure
  const [activeSection, setActiveSection] = useState('metrics');

  // Sample data for different sections
  const complianceData = [
    { name: 'GDPR', completed: 85, total: 100 },
    { name: 'CCPA', completed: 92, total: 100 },
    { name: 'HIPAA', completed: 78, total: 100 },
    { name: 'SOX', completed: 95, total: 100 },
  ];

  const riskData = [
    { name: 'High', value: 12, color: '#ef4444' },
    { name: 'Medium', value: 28, color: '#f59e0b' },
    { name: 'Low', value: 60, color: '#10b981' },
  ];

  const trendData = [
    { month: 'Jan', compliance: 75, risk: 30 },
    { month: 'Feb', compliance: 78, risk: 28 },
    { month: 'Mar', compliance: 82, risk: 25 },
    { month: 'Apr', compliance: 85, risk: 22 },
    { month: 'May', compliance: 87, risk: 20 },
    { month: 'Jun', compliance: 90, risk: 18 },
  ];

  const violationsData = [
    { 
      id: 1, 
      policy: 'Data Retention Policy', 
      severity: 'High', 
      department: 'Marketing',
      date: '2024-01-15',
      status: 'Open'
    },
    { 
      id: 2, 
      policy: 'Access Control Policy', 
      severity: 'Medium', 
      department: 'IT',
      date: '2024-01-12',
      status: 'In Progress'
    },
    { 
      id: 3, 
      policy: 'Privacy Policy', 
      severity: 'Low', 
      department: 'HR',
      date: '2024-01-10',
      status: 'Resolved'
    },
  ];

  // Render functions for different sections
  const renderComplianceMetrics = () => (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-semibold mb-4">Compliance Metrics</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {complianceData.map((item, index) => (
          <div key={index} className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-semibold text-lg">{item.name}</h4>
            <p className="text-2xl font-bold text-blue-600">{item.completed}%</p>
            <p className="text-sm text-gray-500">Compliance Rate</p>
          </div>
        ))}
      </div>
      <div className="text-center">
        <p className="text-gray-600">Detailed compliance metrics charts would be displayed here</p>
      </div>
    </div>
  );

  const renderRiskAssessment = () => (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <h3 className="text-xl font-semibold mb-4">Risk Assessment</h3>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        {riskData.map((item, index) => (
          <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center">
              <div 
                className="w-4 h-4 rounded mr-3" 
                style={{ backgroundColor: item.color }}
              ></div>
              <span className="font-medium">{item.name} Risk</span>
            </div>
            <span className="text-xl font-bold">{item.value}</span>
          </div>
        ))}
      </div>
      <div className="text-center">
        <p className="text-gray-600">Risk assessment charts and detailed analysis would be displayed here</p>
      </div>
    </div>
  );

  const renderComplianceOverview = () => (
    <div className="space-y-6">
      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700">Total Policies</h3>
          <p className="text-3xl font-bold text-blue-600">127</p>
          <p className="text-sm text-gray-500">+5 this month</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700">Compliance Score</h3>
          <p className="text-3xl font-bold text-green-600">87%</p>
          <p className="text-sm text-gray-500">+2% from last month</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700">Active Violations</h3>
          <p className="text-3xl font-bold text-red-600">12</p>
          <p className="text-sm text-gray-500">-3 from last week</p>
        </div>
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h3 className="text-lg font-semibold text-gray-700">Risk Score</h3>
          <p className="text-3xl font-bold text-yellow-600">Medium</p>
          <p className="text-sm text-gray-500">Trending down</p>
        </div>
      </div>

      {/* Compliance Trends */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-semibold mb-4">Compliance Trends</h3>
        <div className="h-64 flex items-center justify-center bg-gray-50 rounded">
          <p className="text-gray-600">Compliance trends chart would be displayed here</p>
        </div>
      </div>

      {/* Recent Violations */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h3 className="text-xl font-semibold mb-4">Recent Violations</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full table-auto">
            <thead>
              <tr className="bg-gray-50">
                <th className="px-4 py-2 text-left">Policy</th>
                <th className="px-4 py-2 text-left">Severity</th>
                <th className="px-4 py-2 text-left">Department</th>
                <th className="px-4 py-2 text-left">Date</th>
                <th className="px-4 py-2 text-left">Status</th>
              </tr>
            </thead>
            <tbody>
              {violationsData.map((violation) => (
                <tr key={violation.id} className="border-t">
                  <td className="px-4 py-2">{violation.policy}</td>
                  <td className="px-4 py-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      violation.severity === 'High' ? 'bg-red-100 text-red-800' :
                      violation.severity === 'Medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {violation.severity}
                    </span>
                  </td>
                  <td className="px-4 py-2">{violation.department}</td>
                  <td className="px-4 py-2">{violation.date}</td>
                  <td className="px-4 py-2">
                    <span className={`px-2 py-1 rounded text-xs ${
                      violation.status === 'Open' ? 'bg-red-100 text-red-800' :
                      violation.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                      'bg-green-100 text-green-800'
                    }`}>
                      {violation.status}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  // Function to render content based on active section
  const renderContent = () => {
    switch (activeSection) {
      case 'metrics':
        return renderComplianceMetrics();
      case 'risk':
        return renderRiskAssessment();
      case 'overview':
        return renderComplianceOverview();
      default:
        return <div className="text-center py-8 text-gray-500">Select a section to view details</div>;
    }
  };

  return (
    <div className="p-6 bg-gray-50 min-h-screen">
      <h1 className="text-3xl font-bold text-gray-900 mb-8">Enterprise Dashboard</h1>
      
      {/* Navigation Section */}
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Compliance & Risk</h2>
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveSection('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Compliance Overview
          </button>
          <button
            onClick={() => setActiveSection('metrics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'metrics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Compliance Metrics
          </button>
          <button
            onClick={() => setActiveSection('risk')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeSection === 'risk'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Risk Assessment
          </button>
        </nav>
      </div>

      {/* Content Area */}
      <div>
        {renderContent()}
      </div>
    </div>
  );
};
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
              <h1 className="text-3xl font-bold text-gray-900 mb-8">Enterprise Dashboard</h1>
      </div>

      <div className="bg-card p-8 rounded-lg shadow-sm">
        <h3 className="text-lg font-semibold mb-8 text-text">Data Consent Overview</h3>

        <div className="flex justify-center space-x-8 mb-12">
          <ConsentMetric
            percentage={31}
            color="#4ade80"
            label="Green Level"
            sublabel="Data Consent"
          />
          <ConsentMetric
            percentage={58}
            color="#fbbf24"
            label="Amber Level"
            sublabel="Data Consent"
          />
          <ConsentMetric
            percentage={11}
            color="#f87171"
            label="Red Level"
            sublabel="Data Consent"
          />
        </div>

        <div className="flex flex-col items-center mb-8">
          <ConsentMetric
            percentage={100}
            color="#1A1F2E"
            label="Data Privacy"
            sublabel="Acknowledgement"
            size="large"
          />
        </div>

        <div className="flex justify-center">
          <button className="px-4 py-2 bg-gradient-to-r from-secondary to-primary hover:from-secondary-light hover:to-primary-light text-white rounded transition-all duration-200 shadow-md hover:shadow-lg">
            Download Report
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Total Enterprises</h3>
          <p className="text-3xl font-bold text-blue-500">150</p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Active Users</h3>
          <p className="text-3xl font-bold text-green-500">1,234</p>
        </div>
        <div className="bg-card p-6 rounded-lg shadow">
          <h3 className="text-lg font-semibold text-text mb-2">Total Revenue</h3>
          <p className="text-3xl font-bold text-purple-500">$50,000</p>
        </div>
      </div>

      <div className="bg-card p-6 rounded-lg shadow">
        <h2 className="text-xl font-semibold text-text mb-4">Enterprise List</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-surface">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Name</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Users</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">Last Active</th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-text">Enterprise A</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700/50">Active</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">250</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">2 hours ago</td>
              </tr>
              <tr>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-text">Enterprise B</td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700/50">Active</span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">180</td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">5 hours ago</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseDashboard;
export { ConsentMetric };