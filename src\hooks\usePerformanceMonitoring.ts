import { useEffect, useRef, useCallback } from 'react';
import { performanceMonitoringService, startPerformanceTimer } from '../services/performanceMonitoringService';
import { reportError } from '../services/errorReportingService';

export interface UsePerformanceMonitoringOptions {
  componentName: string;
  trackRender?: boolean;
  trackMount?: boolean;
  trackUnmount?: boolean;
  trackUserInteractions?: boolean;
  performanceThreshold?: number; // ms
}

export interface PerformanceHookReturn {
  startTimer: (operation: string) => () => void;
  recordMetric: (operation: string, duration: number, additionalData?: Record<string, any>) => void;
  trackUserInteraction: (interactionType: string, callback: () => void) => () => void;
  getComponentHealth: () => any;
}

export const usePerformanceMonitoring = (
  options: UsePerformanceMonitoringOptions
): PerformanceHookReturn => {
  const {
    componentName,
    trackRender = true,
    trackMount = true,
    trackUnmount = true,
    trackUserInteractions = false,
    performanceThreshold = 100
  } = options;

  const mountTimeRef = useRef<number>();
  const renderCountRef = useRef<number>(0);
  const lastRenderTimeRef = useRef<number>();

  // Track component mount
  useEffect(() => {
    if (trackMount) {
      mountTimeRef.current = performance.now();
      
      const endTimer = startPerformanceTimer(componentName, 'mount');
      
      // Record mount completion
      const mountDuration = performance.now() - mountTimeRef.current;
      performanceMonitoringService.recordMetric({
        componentName,
        metricType: 'mount',
        duration: mountDuration,
        timestamp: new Date().toISOString(),
        additionalData: {
          renderCount: renderCountRef.current
        }
      });

      endTimer();
    }

    // Track component unmount
    return () => {
      if (trackUnmount && mountTimeRef.current) {
        const unmountStartTime = performance.now();
        
        try {
          // Perform cleanup operations
          const unmountDuration = performance.now() - unmountStartTime;
          
          performanceMonitoringService.recordMetric({
            componentName,
            metricType: 'unmount',
            duration: unmountDuration,
            timestamp: new Date().toISOString(),
            additionalData: {
              totalRenderCount: renderCountRef.current,
              totalMountTime: unmountStartTime - (mountTimeRef.current || 0)
            }
          });
        } catch (error) {
          reportError(error, componentName, {
            category: 'ui',
            severity: 'medium'
          });
        }
      }
    };
  }, [componentName, trackMount, trackUnmount]);

  // Track renders
  useEffect(() => {
    if (trackRender) {
      const renderStartTime = performance.now();
      renderCountRef.current += 1;

      // Use requestAnimationFrame to measure actual render completion
      requestAnimationFrame(() => {
        const renderDuration = performance.now() - renderStartTime;
        lastRenderTimeRef.current = renderDuration;

        performanceMonitoringService.recordMetric({
          componentName,
          metricType: 'render',
          duration: renderDuration,
          timestamp: new Date().toISOString(),
          additionalData: {
            renderCount: renderCountRef.current,
            isSlowRender: renderDuration > performanceThreshold
          }
        });

        // Warn about slow renders in development
        if (process.env.NODE_ENV === 'development' && renderDuration > performanceThreshold) {
          console.warn(
            `🐌 Slow render in ${componentName}: ${renderDuration.toFixed(2)}ms (threshold: ${performanceThreshold}ms)`
          );
        }
      });
    }
  });

  // Start a performance timer for custom operations
  const startTimer = useCallback((operation: string) => {
    return startPerformanceTimer(componentName, operation);
  }, [componentName]);

  // Record a custom metric
  const recordMetric = useCallback((
    operation: string, 
    duration: number, 
    additionalData?: Record<string, any>
  ) => {
    performanceMonitoringService.recordMetric({
      componentName,
      metricType: operation as any,
      duration,
      timestamp: new Date().toISOString(),
      additionalData
    });
  }, [componentName]);

  // Track user interactions with performance monitoring
  const trackUserInteraction = useCallback((
    interactionType: string,
    callback: () => void
  ) => {
    return () => {
      if (!trackUserInteractions) {
        callback();
        return;
      }

      const startTime = performance.now();
      
      try {
        callback();
        
        // Measure interaction response time
        requestAnimationFrame(() => {
          const duration = performance.now() - startTime;
          
          performanceMonitoringService.recordMetric({
            componentName,
            metricType: 'user_interaction',
            duration,
            timestamp: new Date().toISOString(),
            additionalData: {
              interactionType,
              isSlowInteraction: duration > 50 // 50ms threshold for interactions
            }
          });

          // Warn about slow interactions
          if (duration > 50) {
            console.warn(
              `🐌 Slow interaction in ${componentName}: ${interactionType} took ${duration.toFixed(2)}ms`
            );
          }
        });
      } catch (error) {
        const duration = performance.now() - startTime;
        
        // Record failed interaction
        performanceMonitoringService.recordMetric({
          componentName,
          metricType: 'user_interaction',
          duration,
          timestamp: new Date().toISOString(),
          additionalData: {
            interactionType,
            failed: true,
            error: error instanceof Error ? error.message : 'Unknown error'
          }
        });

        // Report the error
        reportError(error, componentName, {
          category: 'ui',
          severity: 'medium',
          additionalContext: {
            interactionType,
            duration
          }
        });

        throw error; // Re-throw to maintain original behavior
      }
    };
  }, [componentName, trackUserInteractions]);

  // Get component health information
  const getComponentHealth = useCallback(() => {
    return performanceMonitoringService.getComponentHealth(componentName);
  }, [componentName]);

  return {
    startTimer,
    recordMetric,
    trackUserInteraction,
    getComponentHealth
  };
};

// Hook for monitoring data loading operations
export const useDataLoadingMonitoring = (componentName: string) => {
  const startDataLoad = useCallback((operation: string = 'data_load') => {
    return startPerformanceTimer(componentName, operation);
  }, [componentName]);

  const recordDataLoadError = useCallback((error: Error, operation: string = 'data_load') => {
    reportError(error, componentName, {
      category: 'data',
      severity: 'medium',
      additionalContext: {
        operation
      }
    });
  }, [componentName]);

  return {
    startDataLoad,
    recordDataLoadError
  };
};

// Hook for monitoring chart operations
export const useChartMonitoring = (componentName: string) => {
  const startChartOperation = useCallback((operation: 'chart_create' | 'chart_update' | 'chart_destroy' = 'chart_create') => {
    return startPerformanceTimer(componentName, operation);
  }, [componentName]);

  const recordChartError = useCallback((error: Error, operation: string = 'chart_create') => {
    reportError(error, componentName, {
      category: 'chart',
      severity: 'high',
      additionalContext: {
        operation
      }
    });
  }, [componentName]);

  const recordChartMetric = useCallback((
    operation: 'chart_create' | 'chart_update' | 'chart_destroy',
    duration: number,
    additionalData?: Record<string, any>
  ) => {
    performanceMonitoringService.recordMetric({
      componentName,
      metricType: operation,
      duration,
      timestamp: new Date().toISOString(),
      additionalData
    });
  }, [componentName]);

  return {
    startChartOperation,
    recordChartError,
    recordChartMetric
  };
};

export default usePerformanceMonitoring;
