import { 
  Department, 
  CompliancePolicy, 
  TimelineEvent, 
  ComplianceMetric, 
  ComplianceFramework, 
  EnhancedComplianceData 
} from '../types/ComplianceTypes';

// Generate comprehensive compliance data
export const generateEnhancedComplianceData = (): EnhancedComplianceData => {
  const departments = generateDepartments();
  const policies = generatePolicies();
  const timeline = generateTimelineEvents();
  const metrics = generateMetrics();
  const frameworks = generateFrameworks(departments, policies, timeline);

  return {
    overview: {
      totalMetrics: metrics.length,
      compliantMetrics: metrics.filter(m => m.status === 'compliant').length,
      warningMetrics: metrics.filter(m => m.status === 'warning').length,
      criticalMetrics: metrics.filter(m => m.status === 'critical').length,
      overallScore: Math.round(metrics.reduce((acc, m) => acc + (m.currentValue / m.targetValue * 100), 0) / metrics.length),
      lastUpdated: new Date(),
      trendsLastMonth: {
        improvement: Math.floor(Math.random() * 15) + 5,
        degradation: Math.floor(Math.random() * 8) + 2,
        stable: Math.floor(Math.random() * 20) + 10
      }
    },
    metrics,
    frameworks,
    categoryBreakdown: {
      'Privacy': { score: 92, count: 12, trend: 'up' },
      'Security': { score: 88, count: 15, trend: 'stable' },
      'Operational': { score: 85, count: 8, trend: 'down' },
      'Regulatory': { score: 95, count: 10, trend: 'up' }
    },
    realTimeUpdates: {
      lastSync: new Date(),
      isLive: true,
      pendingUpdates: Math.floor(Math.random() * 5)
    }
  };
};

const generateDepartments = (): Department[] => {
  const departmentNames = [
    'Human Resources', 'Marketing', 'Finance', 'IT Security', 'Legal',
    'Operations', 'Customer Service', 'Research & Development', 'Sales', 'Procurement'
  ];

  return departmentNames.map((name, index) => ({
    id: `dept-${index + 1}`,
    name,
    description: `${name} department compliance management`,
    complianceScore: Math.floor(Math.random() * 30) + 70,
    riskLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    dataSubjectsCount: Math.floor(Math.random() * 5000) + 1000,
    processingActivities: generateProcessingActivities(),
    responsibleOfficer: `Officer ${index + 1}`,
    lastAuditDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    nextAuditDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
    violations: Math.floor(Math.random() * 10),
    trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateProcessingActivities = () => {
  const activities = [
    'Employee Data Processing', 'Customer Data Management', 'Marketing Analytics',
    'Financial Reporting', 'Security Monitoring', 'Compliance Tracking'
  ];

  return activities.slice(0, Math.floor(Math.random() * 4) + 2).map((name, index) => ({
    id: `activity-${index + 1}`,
    name,
    purpose: `Purpose for ${name}`,
    legalBasis: ['Consent', 'Contract', 'Legal Obligation', 'Legitimate Interest'][Math.floor(Math.random() * 4)],
    dataCategories: ['Personal Data', 'Financial Data', 'Contact Information'].slice(0, Math.floor(Math.random() * 3) + 1),
    retentionPeriod: `${Math.floor(Math.random() * 7) + 1} years`,
    status: ['active', 'inactive', 'under_review'][Math.floor(Math.random() * 3)] as any
  }));
};

const generatePolicies = (): CompliancePolicy[] => {
  const policyTitles = [
    'Data Protection Policy', 'Consent Management Framework', 'Data Retention Guidelines',
    'Breach Response Procedure', 'Access Control Standards', 'Security Compliance Policy',
    'Privacy Impact Assessment', 'Data Subject Rights Management', 'Third-Party Data Sharing',
    'Employee Privacy Training'
  ];

  return policyTitles.map((title, index) => ({
    id: `policy-${index + 1}`,
    title,
    description: `Comprehensive ${title.toLowerCase()} for organizational compliance`,
    category: ['data_protection', 'consent_management', 'data_retention', 'breach_response', 'access_control', 'security'][Math.floor(Math.random() * 6)] as any,
    framework: ['gdpr', 'sox', 'iso27001', 'ccpa', 'hipaa'][Math.floor(Math.random() * 5)] as any,
    status: ['draft', 'under_review', 'approved', 'active', 'archived'][Math.floor(Math.random() * 5)] as any,
    version: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}`,
    effectiveDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    reviewDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
    owner: `Policy Owner ${index + 1}`,
    assignedDepartments: [`dept-${Math.floor(Math.random() * 5) + 1}`, `dept-${Math.floor(Math.random() * 5) + 6}`],
    adherenceRate: Math.floor(Math.random() * 30) + 70,
    violations: generateViolations(),
    changeHistory: generateChangeHistory(),
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateViolations = () => {
  const violationCount = Math.floor(Math.random() * 5);
  return Array.from({ length: violationCount }, (_, index) => ({
    id: `violation-${index + 1}`,
    description: `Violation description ${index + 1}`,
    severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    reportedDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    resolvedDate: Math.random() > 0.3 ? new Date() : undefined,
    status: ['open', 'investigating', 'resolved'][Math.floor(Math.random() * 3)] as any
  }));
};

const generateChangeHistory = () => {
  const changeCount = Math.floor(Math.random() * 3) + 1;
  return Array.from({ length: changeCount }, (_, index) => ({
    version: `v${index + 1}.0`,
    changes: `Changes made in version ${index + 1}`,
    changedBy: `User ${index + 1}`,
    changeDate: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000)
  }));
};

const generateTimelineEvents = (): TimelineEvent[] => {
  const eventTitles = [
    'GDPR Policy Implementation', 'Annual Compliance Audit', 'Data Subject Access Request',
    'Security Breach Investigation', 'Regulatory Deadline Compliance', 'Privacy Training Session',
    'Data Retention Review', 'Consent Management Update', 'Third-Party Assessment',
    'Compliance Framework Upgrade'
  ];

  return eventTitles.map((title, index) => ({
    id: `event-${index + 1}`,
    title,
    description: `Detailed description for ${title}`,
    type: ['policy_implementation', 'audit_activity', 'data_subject_request', 'breach_incident', 'regulatory_deadline', 'compliance_milestone'][Math.floor(Math.random() * 6)] as any,
    framework: ['gdpr', 'sox', 'iso27001', 'ccpa', 'hipaa'][Math.floor(Math.random() * 5)] as any,
    date: new Date(Date.now() + (Math.random() - 0.5) * 180 * 24 * 60 * 60 * 1000),
    status: ['planned', 'in_progress', 'completed', 'overdue', 'cancelled'][Math.floor(Math.random() * 5)] as any,
    priority: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    department: `dept-${Math.floor(Math.random() * 10) + 1}`,
    assignedTo: `Assignee ${index + 1}`,
    outcome: Math.random() > 0.5 ? `Outcome for ${title}` : undefined,
    relatedEntities: [
      {
        type: 'department',
        id: `dept-${Math.floor(Math.random() * 5) + 1}`,
        name: 'Related Department'
      }
    ],
    attachments: Math.random() > 0.7 ? [`attachment-${index + 1}.pdf`] : undefined,
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateMetrics = (): ComplianceMetric[] => {
  const metricNames = [
    'Data Subject Response Time', 'Policy Adherence Rate', 'Security Incident Count',
    'Training Completion Rate', 'Audit Finding Resolution', 'Consent Withdrawal Rate',
    'Data Breach Response Time', 'Privacy Impact Assessments', 'Third-Party Compliance',
    'Regulatory Fine Risk Score'
  ];

  return metricNames.map((name, index) => ({
    id: `metric-${index + 1}`,
    name,
    category: ['privacy', 'security', 'operational', 'regulatory'][Math.floor(Math.random() * 4)] as any,
    currentValue: Math.floor(Math.random() * 100) + 1,
    targetValue: Math.floor(Math.random() * 20) + 80,
    unit: ['%', 'days', 'count', 'score'][Math.floor(Math.random() * 4)],
    trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
    status: ['compliant', 'warning', 'critical'][Math.floor(Math.random() * 3)] as any,
    lastUpdated: new Date(),
    historicalData: generateHistoricalData(),
    drillDownData: {
      departments: [
        { name: 'HR', value: Math.floor(Math.random() * 100), status: 'compliant' },
        { name: 'IT', value: Math.floor(Math.random() * 100), status: 'warning' }
      ],
      policies: [
        { name: 'Data Protection', value: Math.floor(Math.random() * 100), violations: Math.floor(Math.random() * 5) }
      ],
      timeBreakdown: [
        { period: 'Q1', value: Math.floor(Math.random() * 100), change: Math.floor(Math.random() * 20) - 10 }
      ]
    }
  }));
};

const generateHistoricalData = () => {
  return Array.from({ length: 12 }, (_, index) => ({
    date: new Date(Date.now() - (11 - index) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    value: Math.floor(Math.random() * 100) + 1,
    status: ['compliant', 'warning', 'critical'][Math.floor(Math.random() * 3)],
    notes: Math.random() > 0.7 ? `Note for month ${index + 1}` : undefined
  }));
};

const generateFrameworks = (departments: Department[], policies: CompliancePolicy[], timeline: TimelineEvent[]): ComplianceFramework[] => {
  const frameworks = ['gdpr', 'sox', 'iso27001', 'ccpa', 'hipaa'];
  
  return frameworks.map((type, index) => ({
    id: `framework-${index + 1}`,
    name: type.toUpperCase(),
    type: type as any,
    description: `${type.toUpperCase()} compliance framework`,
    departments: departments.slice(0, Math.floor(Math.random() * 5) + 3),
    policies: policies.filter(p => p.framework === type),
    timeline: timeline.filter(t => t.framework === type),
    overallScore: Math.floor(Math.random() * 30) + 70,
    lastAssessment: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
    nextAssessment: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
    status: ['compliant', 'warning', 'critical'][Math.floor(Math.random() * 3)] as any
  }));
};
