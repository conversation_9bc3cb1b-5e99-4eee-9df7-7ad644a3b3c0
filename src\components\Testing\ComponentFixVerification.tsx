import React, { useState } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Test the simplified components
const EnhancedComplianceMetricsSimple = React.lazy(() => import('../compliance/EnhancedComplianceMetricsSimple'));
const SecurityOverviewDashboardSimple = React.lazy(() => import('../Security/SecurityOverviewDashboardSimple'));

// Test the original components
const EnhancedComplianceMetricsOriginal = React.lazy(() => import('../compliance/EnhancedComplianceMetrics'));
const SecurityOverviewDashboardOriginal = React.lazy(() => import('../Security/SecurityOverviewDashboard'));

export const ComponentFixVerification: React.FC = () => {
  const [activeTest, setActiveTest] = useState<'none' | 'simple-compliance' | 'simple-security' | 'original-compliance' | 'original-security'>('none');

  const tests = [
    { id: 'simple-compliance', name: 'Enhanced Compliance Metrics (Simple)', component: EnhancedComplianceMetricsSimple },
    { id: 'simple-security', name: 'Security Overview Dashboard (Simple)', component: SecurityOverviewDashboardSimple },
    { id: 'original-compliance', name: 'Enhanced Compliance Metrics (Original)', component: EnhancedComplianceMetricsOriginal },
    { id: 'original-security', name: 'Security Overview Dashboard (Original)', component: SecurityOverviewDashboardOriginal }
  ];

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Component Fix Verification</h1>
          <p className="text-text-secondary mb-6">
            This test verifies that both simplified and original components can be loaded successfully.
          </p>
          
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            {tests.map((test) => (
              <button
                key={test.id}
                onClick={() => setActiveTest(test.id as any)}
                className={`p-4 rounded-lg border text-left transition-colors ${
                  activeTest === test.id
                    ? 'bg-primary text-white border-primary'
                    : 'bg-surface text-text border-border hover:bg-card'
                }`}
              >
                <div className="font-medium text-sm">{test.name}</div>
                <div className="text-xs opacity-75 mt-1">
                  {test.id.includes('simple') ? 'Simplified Version' : 'Original Version'}
                </div>
              </button>
            ))}
            
            <button
              onClick={() => setActiveTest('none')}
              className="p-4 rounded-lg border bg-gray-500 text-white border-gray-500 hover:bg-gray-600 transition-colors"
            >
              <div className="font-medium text-sm">Clear</div>
              <div className="text-xs opacity-75 mt-1">Hide Component</div>
            </button>
          </div>
        </div>

        {activeTest !== 'none' && (
          <div className="bg-surface rounded-lg border border-border overflow-hidden">
            <div className="p-4 border-b border-border bg-card">
              <h2 className="text-lg font-semibold text-text">
                {tests.find(t => t.id === activeTest)?.name}
              </h2>
              <p className="text-sm text-text-secondary mt-1">
                Testing component loading and rendering
              </p>
            </div>
            
            <div className="p-6">
              <ErrorBoundary
                type="section"
                fallbackTitle="Component Loading Error"
                fallbackMessage="This component failed to load or render properly."
                onError={(error, errorInfo) => {
                  console.error(`${activeTest} Component Error:`, error);
                  console.error('Error Info:', errorInfo);
                }}
              >
                <React.Suspense
                  fallback={
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-secondary">
                          Loading {tests.find(t => t.id === activeTest)?.name}...
                        </p>
                      </div>
                    </div>
                  }
                >
                  {activeTest === 'simple-compliance' && (
                    <div className="max-h-[600px] overflow-auto">
                      <EnhancedComplianceMetricsSimple />
                    </div>
                  )}
                  
                  {activeTest === 'simple-security' && (
                    <div className="max-h-[600px] overflow-auto">
                      <SecurityOverviewDashboardSimple />
                    </div>
                  )}
                  
                  {activeTest === 'original-compliance' && (
                    <div className="max-h-[600px] overflow-auto">
                      <EnhancedComplianceMetricsOriginal />
                    </div>
                  )}
                  
                  {activeTest === 'original-security' && (
                    <div className="max-h-[600px] overflow-auto">
                      <SecurityOverviewDashboardOriginal />
                    </div>
                  )}
                </React.Suspense>
              </ErrorBoundary>
            </div>
          </div>
        )}

        {activeTest === 'none' && (
          <div className="bg-surface rounded-lg p-8 border border-border text-center">
            <h2 className="text-xl font-semibold text-text mb-4">Component Fix Status</h2>
            <p className="text-text-secondary mb-6">
              Select a component above to test its loading and rendering capabilities.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
              <div className="p-6 bg-card rounded-lg border border-border">
                <h3 className="font-semibold text-text mb-3">Simplified Components</h3>
                <div className="space-y-2 text-sm text-text-secondary">
                  <p>✅ Lightweight and optimized</p>
                  <p>✅ Essential features only</p>
                  <p>✅ Fast loading and rendering</p>
                  <p>✅ Minimal dependencies</p>
                </div>
              </div>
              
              <div className="p-6 bg-card rounded-lg border border-border">
                <h3 className="font-semibold text-text mb-3">Original Components</h3>
                <div className="space-y-2 text-sm text-text-secondary">
                  <p>🔧 Full feature set</p>
                  <p>🔧 Comprehensive CRUD operations</p>
                  <p>🔧 Advanced data visualization</p>
                  <p>🔧 Complex data management</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
            Fix Strategy
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
            <div className="space-y-2">
              <p><strong>Phase 1:</strong> Deploy simplified components for immediate functionality</p>
              <p><strong>Phase 2:</strong> Identify and fix issues in original components</p>
              <p><strong>Phase 3:</strong> Gradually migrate back to full-featured components</p>
            </div>
            <div className="space-y-2">
              <p>✅ Simplified components provide core functionality</p>
              <p>🔧 Original components being debugged and fixed</p>
              <p>🚀 Users get working dashboard immediately</p>
            </div>
          </div>
        </div>

        <div className="mt-6 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <h3 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">
            Current Status
          </h3>
          <div className="text-sm text-green-700 dark:text-green-300 space-y-1">
            <p>✅ Simplified components are working and deployed</p>
            <p>✅ Enterprise Dashboard now loads without errors</p>
            <p>✅ Users can access compliance and security dashboards</p>
            <p>🔧 Original components being analyzed for root cause issues</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComponentFixVerification;
