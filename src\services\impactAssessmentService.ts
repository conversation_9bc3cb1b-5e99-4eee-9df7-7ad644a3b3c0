import { ImpactAssessment, Risk, RiskMatrix, MitigationMeasure, WorkflowStep, AuditEntry } from '../types/compliance';

export class ImpactAssessmentService {
  private static assessments: ImpactAssessment[] = [];
  private static riskMatrix: RiskMatrix = {
    likelihood: {
      veryLow: 1,
      low: 2,
      medium: 3,
      high: 4,
      veryHigh: 5
    },
    impact: {
      veryLow: 1,
      low: 2,
      medium: 3,
      high: 4,
      veryHigh: 5
    },
    riskLevels: {
      low: { min: 1, max: 6, color: '#10B981' },
      medium: { min: 7, max: 12, color: '#F59E0B' },
      high: { min: 13, max: 20, color: '#EF4444' },
      critical: { min: 21, max: 25, color: '#DC2626' }
    }
  };
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.assessments = this.generateMockAssessments();
    this.initialized = true;
  }

  // CRUD Operations
  static async getAllAssessments(): Promise<ImpactAssessment[]> {
    this.initialize();
    return [...this.assessments];
  }

  static async getAssessmentById(id: string): Promise<ImpactAssessment | null> {
    this.initialize();
    return this.assessments.find(assessment => assessment.id === id) || null;
  }

  static async createAssessment(assessmentData: Partial<ImpactAssessment>): Promise<ImpactAssessment> {
    this.initialize();
    const newAssessment: ImpactAssessment = {
      id: `ia-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: assessmentData.title || '',
      description: assessmentData.description || '',
      type: assessmentData.type || 'DPIA',
      status: 'draft',
      version: '1.0',
      createdAt: new Date(),
      lastUpdated: new Date(),
      dueDate: assessmentData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      assignee: assessmentData.assignee || 'Privacy Officer',
      assigneeId: assessmentData.assigneeId || 'privacy-001',
      reviewer: assessmentData.reviewer || 'Legal Counsel',
      reviewerId: assessmentData.reviewerId || 'legal-001',
      processingActivity: assessmentData.processingActivity || {
        name: '',
        description: '',
        purpose: [],
        legalBasis: '',
        dataCategories: [],
        dataSubjects: [],
        recipients: [],
        internationalTransfers: false,
        retentionPeriod: '',
        automatedDecisionMaking: false,
        profiling: false
      },
      riskAssessment: {
        overallRiskScore: 0,
        riskLevel: 'low',
        identifiedRisks: [],
        riskMatrix: this.riskMatrix,
        residualRisk: 0
      },
      mitigationMeasures: [],
      consultation: {
        dataSubjectsConsulted: false,
        dpoConsulted: false,
        dpoRecommendations: [],
        externalConsultation: false,
        feedback: []
      },
      monitoring: {
        reviewFrequency: 'annually',
        nextReviewDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        kpis: [],
        monitoringPlan: ''
      },
      attachments: [],
      relatedAssessments: [],
      workflow: this.generateWorkflow(assessmentData.type || 'DPIA'),
      auditTrail: [{
        id: `audit-${Date.now()}`,
        timestamp: new Date(),
        userId: 'system',
        userName: 'System',
        action: 'Assessment Created',
        details: `Impact assessment created: ${assessmentData.title}`,
        newValue: 'draft'
      }],
      completionPercentage: 0,
      sectionsCompleted: [],
      sectionsRemaining: ['Processing Activity', 'Risk Assessment', 'Mitigation Measures', 'Consultation', 'Monitoring']
    };

    this.assessments.push(newAssessment);
    return newAssessment;
  }

  static async updateAssessment(id: string, updates: Partial<ImpactAssessment>): Promise<ImpactAssessment | null> {
    this.initialize();
    const index = this.assessments.findIndex(assessment => assessment.id === id);
    if (index === -1) return null;

    const oldAssessment = { ...this.assessments[index] };
    this.assessments[index] = { 
      ...this.assessments[index], 
      ...updates,
      lastUpdated: new Date()
    };

    // Recalculate completion percentage
    this.assessments[index].completionPercentage = this.calculateCompletionPercentage(this.assessments[index]);

    // Add audit entry
    this.assessments[index].auditTrail.push({
      id: `audit-${Date.now()}`,
      timestamp: new Date(),
      userId: 'user-001',
      userName: 'Current User',
      action: 'Assessment Updated',
      details: 'Assessment details updated',
      previousValue: oldAssessment,
      newValue: updates
    });

    return this.assessments[index];
  }

  static async deleteAssessment(id: string): Promise<boolean> {
    this.initialize();
    const index = this.assessments.findIndex(assessment => assessment.id === id);
    if (index === -1) return false;

    this.assessments.splice(index, 1);
    return true;
  }

  // Risk Assessment Operations
  static async addRisk(assessmentId: string, riskData: Partial<Risk>): Promise<ImpactAssessment | null> {
    const assessment = await this.getAssessmentById(assessmentId);
    if (!assessment) return null;

    const newRisk: Risk = {
      id: `risk-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      category: riskData.category || 'privacy',
      description: riskData.description || '',
      likelihood: riskData.likelihood || 3,
      impact: riskData.impact || 3,
      riskScore: (riskData.likelihood || 3) * (riskData.impact || 3),
      inherentRisk: (riskData.likelihood || 3) * (riskData.impact || 3),
      residualRisk: (riskData.likelihood || 3) * (riskData.impact || 3),
      riskOwner: riskData.riskOwner || 'Privacy Officer',
      identifiedDate: new Date(),
      mitigationMeasures: [],
      status: 'identified'
    };

    assessment.riskAssessment.identifiedRisks.push(newRisk);
    assessment.riskAssessment.overallRiskScore = this.calculateOverallRiskScore(assessment.riskAssessment.identifiedRisks);
    assessment.riskAssessment.riskLevel = this.determineRiskLevel(assessment.riskAssessment.overallRiskScore);

    return await this.updateAssessment(assessmentId, assessment);
  }

  static async addMitigationMeasure(assessmentId: string, measureData: Partial<MitigationMeasure>): Promise<ImpactAssessment | null> {
    const assessment = await this.getAssessmentById(assessmentId);
    if (!assessment) return null;

    const newMeasure: MitigationMeasure = {
      id: `measure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: measureData.title || '',
      description: measureData.description || '',
      category: measureData.category || 'technical',
      priority: measureData.priority || 'medium',
      status: 'planned',
      assignee: measureData.assignee || 'Technical Team',
      dueDate: measureData.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      effectiveness: measureData.effectiveness || 3,
      cost: measureData.cost || 0,
      effort: measureData.effort || 0,
      relatedRisks: measureData.relatedRisks || [],
      verificationMethod: measureData.verificationMethod || ''
    };

    assessment.mitigationMeasures.push(newMeasure);
    return await this.updateAssessment(assessmentId, assessment);
  }

  // Workflow Management
  static async advanceWorkflow(assessmentId: string, stepId: string, notes?: string): Promise<ImpactAssessment | null> {
    const assessment = await this.getAssessmentById(assessmentId);
    if (!assessment) return null;

    const step = assessment.workflow.steps.find(s => s.id === stepId);
    if (!step) return null;

    step.status = 'completed';
    step.completedAt = new Date();
    step.notes = notes;

    // Check if all steps are completed
    const completedSteps = assessment.workflow.steps.filter(s => s.status === 'completed').length;
    if (completedSteps === assessment.workflow.totalSteps) {
      assessment.status = 'approved';
      assessment.completedAt = new Date();
    } else {
      // Start next step
      const nextStep = assessment.workflow.steps.find(s => s.status === 'pending');
      if (nextStep) {
        nextStep.status = 'in_progress';
        nextStep.startedAt = new Date();
      }
    }

    return await this.updateAssessment(assessmentId, assessment);
  }

  // Analytics and Metrics
  static async getAssessmentMetrics(): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byType: Record<string, number>;
    byRiskLevel: Record<string, number>;
    averageCompletionTime: number;
    overdueAssessments: number;
    completionRate: number;
  }> {
    this.initialize();
    
    const byStatus = this.assessments.reduce((acc, assessment) => {
      acc[assessment.status] = (acc[assessment.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byType = this.assessments.reduce((acc, assessment) => {
      acc[assessment.type] = (acc[assessment.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byRiskLevel = this.assessments.reduce((acc, assessment) => {
      acc[assessment.riskAssessment.riskLevel] = (acc[assessment.riskAssessment.riskLevel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const completedAssessments = this.assessments.filter(a => a.status === 'approved');
    const averageCompletionTime = completedAssessments.length > 0 
      ? completedAssessments.reduce((sum, assessment) => {
          if (assessment.completedAt) {
            return sum + (assessment.completedAt.getTime() - assessment.createdAt.getTime());
          }
          return sum;
        }, 0) / completedAssessments.length / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    const overdueAssessments = this.assessments.filter(assessment => 
      assessment.status !== 'approved' && new Date() > assessment.dueDate
    ).length;

    const completionRate = this.assessments.length > 0 
      ? (completedAssessments.length / this.assessments.length) * 100 
      : 0;

    return {
      total: this.assessments.length,
      byStatus,
      byType,
      byRiskLevel,
      averageCompletionTime,
      overdueAssessments,
      completionRate
    };
  }

  // Private helper methods
  private static calculateOverallRiskScore(risks: Risk[]): number {
    if (risks.length === 0) return 0;
    const totalScore = risks.reduce((sum, risk) => sum + risk.riskScore, 0);
    return Math.round(totalScore / risks.length);
  }

  private static determineRiskLevel(score: number): 'low' | 'medium' | 'high' | 'critical' {
    if (score >= this.riskMatrix.riskLevels.critical.min) return 'critical';
    if (score >= this.riskMatrix.riskLevels.high.min) return 'high';
    if (score >= this.riskMatrix.riskLevels.medium.min) return 'medium';
    return 'low';
  }

  private static calculateCompletionPercentage(assessment: ImpactAssessment): number {
    let completedSections = 0;
    const totalSections = 5;

    // Check Processing Activity
    if (assessment.processingActivity.name && assessment.processingActivity.description) {
      completedSections++;
    }

    // Check Risk Assessment
    if (assessment.riskAssessment.identifiedRisks.length > 0) {
      completedSections++;
    }

    // Check Mitigation Measures
    if (assessment.mitigationMeasures.length > 0) {
      completedSections++;
    }

    // Check Consultation
    if (assessment.consultation.dpoConsulted) {
      completedSections++;
    }

    // Check Monitoring
    if (assessment.monitoring.monitoringPlan) {
      completedSections++;
    }

    return Math.round((completedSections / totalSections) * 100);
  }

  private static generateWorkflow(type: string): { currentStep: number; totalSteps: number; steps: WorkflowStep[] } {
    const workflows = {
      DPIA: [
        { name: 'Initial Assessment', description: 'Determine if DPIA is required', estimatedDuration: 4 },
        { name: 'Processing Activity Analysis', description: 'Analyze the processing activity', estimatedDuration: 8 },
        { name: 'Risk Identification', description: 'Identify and assess risks', estimatedDuration: 12 },
        { name: 'Mitigation Planning', description: 'Plan mitigation measures', estimatedDuration: 8 },
        { name: 'Consultation', description: 'Consult with stakeholders', estimatedDuration: 6 },
        { name: 'Review & Approval', description: 'Final review and approval', estimatedDuration: 4 }
      ],
      LIA: [
        { name: 'Legitimate Interest Test', description: 'Perform the three-part test', estimatedDuration: 6 },
        { name: 'Balancing Test', description: 'Balance interests against rights', estimatedDuration: 8 },
        { name: 'Safeguards Assessment', description: 'Assess necessary safeguards', estimatedDuration: 4 },
        { name: 'Documentation', description: 'Document the assessment', estimatedDuration: 3 },
        { name: 'Review & Approval', description: 'Final review and approval', estimatedDuration: 2 }
      ],
      TIA: [
        { name: 'Transfer Mechanism Review', description: 'Review transfer mechanisms', estimatedDuration: 4 },
        { name: 'Adequacy Assessment', description: 'Assess adequacy of protection', estimatedDuration: 6 },
        { name: 'Safeguards Evaluation', description: 'Evaluate additional safeguards', estimatedDuration: 5 },
        { name: 'Risk Mitigation', description: 'Plan risk mitigation measures', estimatedDuration: 4 },
        { name: 'Approval', description: 'Final approval', estimatedDuration: 2 }
      ]
    };

    const workflowTemplate = workflows[type as keyof typeof workflows] || workflows.DPIA;
    const steps: WorkflowStep[] = workflowTemplate.map((step, index) => ({
      id: `step-${index + 1}`,
      name: step.name,
      description: step.description,
      status: index === 0 ? 'in_progress' : 'pending',
      assignee: 'Privacy Officer',
      estimatedDuration: step.estimatedDuration,
      startedAt: index === 0 ? new Date() : undefined,
      requiredActions: [],
      completedActions: []
    }));

    return {
      currentStep: 1,
      totalSteps: steps.length,
      steps
    };
  }

  private static generateMockAssessments(): ImpactAssessment[] {
    return [
      {
        id: 'ia-001',
        title: 'Customer Data Processing DPIA',
        description: 'Data Protection Impact Assessment for new customer data processing system',
        type: 'DPIA',
        status: 'review',
        version: '1.2',
        createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
        lastUpdated: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        assignee: 'Privacy Officer',
        assigneeId: 'privacy-001',
        reviewer: 'Legal Counsel',
        reviewerId: 'legal-001',
        processingActivity: {
          name: 'Customer Data Processing System',
          description: 'Automated processing of customer personal data for service delivery',
          purpose: ['Service Delivery', 'Customer Support', 'Billing'],
          legalBasis: 'Article 6(1)(b) - Contract',
          dataCategories: ['Personal Information', 'Contact Details', 'Financial Information'],
          dataSubjects: ['Customers', 'Prospects'],
          recipients: ['Internal Teams', 'Payment Processors'],
          internationalTransfers: true,
          transferCountries: ['United States', 'Canada'],
          retentionPeriod: '7 years after contract termination',
          automatedDecisionMaking: true,
          profiling: false
        },
        riskAssessment: {
          overallRiskScore: 12,
          riskLevel: 'medium',
          identifiedRisks: [
            {
              id: 'risk-001',
              category: 'confidentiality',
              description: 'Unauthorized access to customer data',
              likelihood: 3,
              impact: 4,
              riskScore: 12,
              inherentRisk: 12,
              residualRisk: 8,
              riskOwner: 'IT Security Manager',
              identifiedDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
              mitigationMeasures: ['Encryption', 'Access Controls'],
              status: 'mitigated'
            }
          ],
          riskMatrix: this.riskMatrix,
          residualRisk: 8
        },
        mitigationMeasures: [
          {
            id: 'measure-001',
            title: 'Implement End-to-End Encryption',
            description: 'Deploy AES-256 encryption for all customer data',
            category: 'technical',
            priority: 'high',
            status: 'implemented',
            assignee: 'IT Security Team',
            dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
            completedDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
            effectiveness: 4,
            cost: 25000,
            effort: 120,
            relatedRisks: ['risk-001'],
            verificationMethod: 'Security Audit',
            verificationDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
            verifiedBy: 'Security Auditor'
          }
        ],
        consultation: {
          dataSubjectsConsulted: true,
          consultationMethod: 'Online Survey',
          consultationDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
          feedback: ['Concerns about data sharing', 'Request for more transparency'],
          dpoConsulted: true,
          dpoRecommendations: ['Implement privacy by design', 'Regular compliance reviews'],
          externalConsultation: false
        },
        monitoring: {
          reviewFrequency: 'quarterly',
          nextReviewDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000),
          kpis: ['Data breach incidents', 'Access control violations', 'User complaints'],
          monitoringPlan: 'Quarterly review of processing activities and risk assessments'
        },
        attachments: [],
        relatedAssessments: [],
        workflow: this.generateWorkflow('DPIA'),
        auditTrail: [
          {
            id: 'audit-001',
            timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
            userId: 'system',
            userName: 'System',
            action: 'Assessment Created',
            details: 'DPIA created for customer data processing',
            newValue: 'draft'
          }
        ],
        completionPercentage: 85,
        sectionsCompleted: ['Processing Activity', 'Risk Assessment', 'Mitigation Measures', 'Consultation'],
        sectionsRemaining: ['Monitoring']
      }
    ];
  }
}
