import { RiskAssessment, RiskMitigationMeasure, RiskComment, RiskMatrix, RiskTrend, RiskCategory, RiskLikelihood, RiskImpact, RiskLevel } from '../types/gdprTypes';

export class RiskAssessmentService {
  private static riskAssessments: RiskAssessment[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.riskAssessments = this.generateMockRiskAssessments();
    this.initialized = true;
  }

  // CRUD Operations
  static async getAllRiskAssessments(): Promise<RiskAssessment[]> {
    this.initialize();
    return [...this.riskAssessments];
  }

  static async getRiskAssessmentById(id: string): Promise<RiskAssessment | null> {
    this.initialize();
    return this.riskAssessments.find(assessment => assessment.id === id) || null;
  }

  static async createRiskAssessment(assessmentData: Partial<RiskAssessment>): Promise<RiskAssessment> {
    this.initialize();
    const riskScore = (assessmentData.likelihood || 1) * (assessmentData.impact || 1);
    const riskLevel = this.calculateRiskLevel(riskScore);

    const newAssessment: RiskAssessment = {
      id: `risk-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: assessmentData.title || '',
      description: assessmentData.description || '',
      category: assessmentData.category || 'operational',
      likelihood: assessmentData.likelihood || 1,
      impact: assessmentData.impact || 1,
      riskScore,
      riskLevel,
      status: assessmentData.status || 'draft',
      owner: assessmentData.owner || '',
      assessor: assessmentData.assessor || '',
      createdAt: new Date(),
      updatedAt: new Date(),
      reviewDate: assessmentData.reviewDate || new Date(),
      nextReviewDate: assessmentData.nextReviewDate || new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
      mitigationMeasures: assessmentData.mitigationMeasures || [],
      residualRisk: assessmentData.residualRisk || {
        likelihood: assessmentData.likelihood || 1,
        impact: assessmentData.impact || 1,
        score: riskScore,
        level: riskLevel
      },
      tags: assessmentData.tags || [],
      attachments: assessmentData.attachments || [],
      comments: assessmentData.comments || []
    };

    this.riskAssessments.push(newAssessment);
    return newAssessment;
  }

  static async updateRiskAssessment(id: string, updates: Partial<RiskAssessment>): Promise<RiskAssessment | null> {
    this.initialize();
    const index = this.riskAssessments.findIndex(assessment => assessment.id === id);
    if (index === -1) return null;

    // Recalculate risk score if likelihood or impact changed
    if (updates.likelihood || updates.impact) {
      const likelihood = updates.likelihood || this.riskAssessments[index].likelihood;
      const impact = updates.impact || this.riskAssessments[index].impact;
      updates.riskScore = likelihood * impact;
      updates.riskLevel = this.calculateRiskLevel(updates.riskScore);
    }

    updates.updatedAt = new Date();
    this.riskAssessments[index] = { ...this.riskAssessments[index], ...updates };
    return this.riskAssessments[index];
  }

  static async deleteRiskAssessment(id: string): Promise<boolean> {
    this.initialize();
    const index = this.riskAssessments.findIndex(assessment => assessment.id === id);
    if (index === -1) return false;

    this.riskAssessments.splice(index, 1);
    return true;
  }

  // Mitigation Measures Management
  static async addMitigationMeasure(assessmentId: string, measure: Partial<RiskMitigationMeasure>): Promise<RiskMitigationMeasure | null> {
    const assessment = await this.getRiskAssessmentById(assessmentId);
    if (!assessment) return null;

    const newMeasure: RiskMitigationMeasure = {
      id: `measure-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: measure.title || '',
      description: measure.description || '',
      type: measure.type || 'preventive',
      status: measure.status || 'planned',
      priority: measure.priority || 'medium',
      owner: measure.owner || '',
      dueDate: measure.dueDate || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
      completedDate: measure.completedDate,
      cost: measure.cost,
      effectiveness: measure.effectiveness || 0,
      notes: measure.notes || ''
    };

    assessment.mitigationMeasures.push(newMeasure);
    await this.updateRiskAssessment(assessmentId, { mitigationMeasures: assessment.mitigationMeasures });
    return newMeasure;
  }

  static async updateMitigationMeasure(assessmentId: string, measureId: string, updates: Partial<RiskMitigationMeasure>): Promise<RiskMitigationMeasure | null> {
    const assessment = await this.getRiskAssessmentById(assessmentId);
    if (!assessment) return null;

    const measureIndex = assessment.mitigationMeasures.findIndex(m => m.id === measureId);
    if (measureIndex === -1) return null;

    assessment.mitigationMeasures[measureIndex] = { ...assessment.mitigationMeasures[measureIndex], ...updates };
    await this.updateRiskAssessment(assessmentId, { mitigationMeasures: assessment.mitigationMeasures });
    return assessment.mitigationMeasures[measureIndex];
  }

  static async deleteMitigationMeasure(assessmentId: string, measureId: string): Promise<boolean> {
    const assessment = await this.getRiskAssessmentById(assessmentId);
    if (!assessment) return false;

    const measureIndex = assessment.mitigationMeasures.findIndex(m => m.id === measureId);
    if (measureIndex === -1) return false;

    assessment.mitigationMeasures.splice(measureIndex, 1);
    await this.updateRiskAssessment(assessmentId, { mitigationMeasures: assessment.mitigationMeasures });
    return true;
  }

  // Comments Management
  static async addComment(assessmentId: string, comment: Partial<RiskComment>): Promise<RiskComment | null> {
    const assessment = await this.getRiskAssessmentById(assessmentId);
    if (!assessment) return null;

    const newComment: RiskComment = {
      id: `comment-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      author: comment.author || '',
      content: comment.content || '',
      timestamp: new Date(),
      type: comment.type || 'general'
    };

    assessment.comments.push(newComment);
    await this.updateRiskAssessment(assessmentId, { comments: assessment.comments });
    return newComment;
  }

  // Advanced Search and Filtering
  static async searchRiskAssessments(query: string): Promise<RiskAssessment[]> {
    this.initialize();
    const searchTerm = query.toLowerCase();

    return this.riskAssessments.filter(assessment =>
      assessment.title.toLowerCase().includes(searchTerm) ||
      assessment.description.toLowerCase().includes(searchTerm) ||
      assessment.category.toLowerCase().includes(searchTerm) ||
      assessment.owner.toLowerCase().includes(searchTerm) ||
      assessment.assessor.toLowerCase().includes(searchTerm) ||
      assessment.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  static async filterRiskAssessments(filters: {
    category?: string[];
    riskLevel?: string[];
    status?: string[];
    owner?: string[];
    dateRange?: { start: Date; end: Date };
  }): Promise<RiskAssessment[]> {
    this.initialize();
    let filtered = [...this.riskAssessments];

    if (filters.category?.length) {
      filtered = filtered.filter(assessment => filters.category!.includes(assessment.category));
    }

    if (filters.riskLevel?.length) {
      filtered = filtered.filter(assessment => filters.riskLevel!.includes(assessment.riskLevel));
    }

    if (filters.status?.length) {
      filtered = filtered.filter(assessment => filters.status!.includes(assessment.status));
    }

    if (filters.owner?.length) {
      filtered = filtered.filter(assessment => filters.owner!.includes(assessment.owner));
    }

    if (filters.dateRange) {
      filtered = filtered.filter(assessment =>
        assessment.createdAt >= filters.dateRange!.start &&
        assessment.createdAt <= filters.dateRange!.end
      );
    }

    return filtered;
  }

  static async sortRiskAssessments(
    assessments: RiskAssessment[],
    sortBy: 'title' | 'riskScore' | 'createdAt' | 'nextReviewDate',
    order: 'asc' | 'desc' = 'desc'
  ): Promise<RiskAssessment[]> {
    return [...assessments].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'title':
          comparison = a.title.localeCompare(b.title);
          break;
        case 'riskScore':
          comparison = a.riskScore - b.riskScore;
          break;
        case 'createdAt':
          comparison = a.createdAt.getTime() - b.createdAt.getTime();
          break;
        case 'nextReviewDate':
          comparison = a.nextReviewDate.getTime() - b.nextReviewDate.getTime();
          break;
      }

      return order === 'asc' ? comparison : -comparison;
    });
  }

  // Risk Analytics and Advanced Reporting
  static async getRiskTrendAnalysis(months: number = 12): Promise<{
    labels: string[];
    datasets: Array<{
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }>;
  }> {
    this.initialize();
    const now = new Date();
    const labels: string[] = [];
    const highRiskData: number[] = [];
    const mediumRiskData: number[] = [];
    const lowRiskData: number[] = [];

    for (let i = months - 1; i >= 0; i--) {
      const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
      const monthName = date.toLocaleDateString('en-US', { month: 'short', year: '2-digit' });
      labels.push(monthName);

      const monthAssessments = this.riskAssessments.filter(assessment => {
        const assessmentMonth = new Date(assessment.createdAt.getFullYear(), assessment.createdAt.getMonth(), 1);
        return assessmentMonth.getTime() === date.getTime();
      });

      highRiskData.push(monthAssessments.filter(a => a.riskLevel === 'high' || a.riskLevel === 'very_high').length);
      mediumRiskData.push(monthAssessments.filter(a => a.riskLevel === 'medium').length);
      lowRiskData.push(monthAssessments.filter(a => a.riskLevel === 'low').length);
    }

    return {
      labels,
      datasets: [
        {
          label: 'High Risk',
          data: highRiskData,
          borderColor: '#ef4444',
          backgroundColor: 'rgba(239, 68, 68, 0.1)'
        },
        {
          label: 'Medium Risk',
          data: mediumRiskData,
          borderColor: '#f59e0b',
          backgroundColor: 'rgba(245, 158, 11, 0.1)'
        },
        {
          label: 'Low Risk',
          data: lowRiskData,
          borderColor: '#10b981',
          backgroundColor: 'rgba(16, 185, 129, 0.1)'
        }
      ]
    };
  }

  static async getRiskScoringAlgorithm(): Promise<{
    algorithm: string;
    factors: Array<{
      name: string;
      weight: number;
      description: string;
    }>;
    thresholds: {
      low: { min: number; max: number };
      medium: { min: number; max: number };
      high: { min: number; max: number };
      very_high: { min: number; max: number };
    };
  }> {
    return {
      algorithm: 'Likelihood × Impact Matrix',
      factors: [
        {
          name: 'Likelihood',
          weight: 0.5,
          description: 'Probability of risk occurrence (1-5 scale)'
        },
        {
          name: 'Impact',
          weight: 0.5,
          description: 'Severity of consequences if risk occurs (1-5 scale)'
        }
      ],
      thresholds: {
        low: { min: 1, max: 6 },
        medium: { min: 7, max: 12 },
        high: { min: 13, max: 20 },
        very_high: { min: 21, max: 25 }
      }
    };
  }

  // Analytics and Reporting
  static async getRiskMatrix(): Promise<RiskMatrix[]> {
    this.initialize();
    const matrix: RiskMatrix[] = [];

    for (let likelihood = 1; likelihood <= 5; likelihood++) {
      for (let impact = 1; impact <= 5; impact++) {
        const count = this.riskAssessments.filter(
          r => r.likelihood === likelihood && r.impact === impact
        ).length;
        
        const score = likelihood * impact;
        const riskLevel = this.calculateRiskLevel(score);

        matrix.push({
          likelihood: likelihood as RiskLikelihood,
          impact: impact as RiskImpact,
          count,
          riskLevel
        });
      }
    }

    return matrix;
  }

  static async getRiskTrends(days: number = 30): Promise<RiskTrend[]> {
    this.initialize();
    const trends: RiskTrend[] = [];
    const now = new Date();

    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const dayRisks = this.riskAssessments.filter(
        r => r.createdAt >= dayStart && r.createdAt < dayEnd
      );

      const highRisks = dayRisks.filter(r => r.riskLevel === 'high' || r.riskLevel === 'very_high').length;
      const mediumRisks = dayRisks.filter(r => r.riskLevel === 'medium').length;
      const lowRisks = dayRisks.filter(r => r.riskLevel === 'low').length;
      const averageScore = dayRisks.length > 0 
        ? dayRisks.reduce((sum, r) => sum + r.riskScore, 0) / dayRisks.length 
        : 0;

      trends.push({
        date: dayStart,
        totalRisks: dayRisks.length,
        highRisks,
        mediumRisks,
        lowRisks,
        averageScore
      });
    }

    return trends;
  }

  static async getRiskMetrics(): Promise<{
    totalRisks: number;
    byCategory: Record<RiskCategory, number>;
    byRiskLevel: Record<RiskLevel, number>;
    byStatus: Record<string, number>;
    averageScore: number;
    overdue: number;
  }> {
    this.initialize();

    const byCategory = this.riskAssessments.reduce((acc, risk) => {
      acc[risk.category] = (acc[risk.category] || 0) + 1;
      return acc;
    }, {} as Record<RiskCategory, number>);

    const byRiskLevel = this.riskAssessments.reduce((acc, risk) => {
      acc[risk.riskLevel] = (acc[risk.riskLevel] || 0) + 1;
      return acc;
    }, {} as Record<RiskLevel, number>);

    const byStatus = this.riskAssessments.reduce((acc, risk) => {
      acc[risk.status] = (acc[risk.status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageScore = this.riskAssessments.length > 0
      ? this.riskAssessments.reduce((sum, risk) => sum + risk.riskScore, 0) / this.riskAssessments.length
      : 0;

    const now = new Date();
    const overdue = this.riskAssessments.filter(risk => risk.nextReviewDate < now).length;

    return {
      totalRisks: this.riskAssessments.length,
      byCategory,
      byRiskLevel,
      byStatus,
      averageScore,
      overdue
    };
  }

  // Utility Methods
  private static calculateRiskLevel(score: number): RiskLevel {
    if (score >= 20) return 'very_high';
    if (score >= 15) return 'high';
    if (score >= 8) return 'medium';
    return 'low';
  }

  // Mock Data Generation
  private static generateMockRiskAssessments(): RiskAssessment[] {
    return [
      {
        id: 'risk-001',
        title: 'Data Breach Risk - Customer Database',
        description: 'Risk of unauthorized access to customer personal data stored in primary database',
        category: 'compliance',
        likelihood: 3,
        impact: 5,
        riskScore: 15,
        riskLevel: 'high',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'John Smith',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date('2024-01-20'),
        reviewDate: new Date('2024-01-15'),
        nextReviewDate: new Date('2024-07-15'),
        mitigationMeasures: [
          {
            id: 'measure-001',
            title: 'Implement Multi-Factor Authentication',
            description: 'Deploy MFA for all database access points',
            type: 'preventive',
            status: 'implemented',
            priority: 'high',
            owner: 'IT Security',
            dueDate: new Date('2024-02-01'),
            completedDate: new Date('2024-01-28'),
            effectiveness: 85,
            notes: 'MFA successfully deployed across all systems'
          },
          {
            id: 'measure-002',
            title: 'Database Encryption at Rest',
            description: 'Implement AES-256 encryption for all customer data at rest',
            type: 'preventive',
            status: 'in_progress',
            priority: 'high',
            owner: 'Database Team',
            dueDate: new Date('2024-03-15'),
            completedDate: undefined,
            effectiveness: 90,
            notes: 'Encryption implementation 70% complete'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 4,
          score: 8,
          level: 'medium'
        },
        tags: ['data-protection', 'cybersecurity', 'gdpr'],
        attachments: ['risk-assessment-report.pdf', 'security-audit-2024.pdf'],
        comments: [
          {
            id: 'comment-001',
            author: 'Risk Manager',
            content: 'Risk assessment approved with current mitigation measures',
            timestamp: new Date('2024-01-20'),
            type: 'approval'
          },
          {
            id: 'comment-002',
            author: 'CISO',
            content: 'Additional encryption measures recommended',
            timestamp: new Date('2024-01-22'),
            type: 'review'
          }
        ]
      },
      {
        id: 'risk-002',
        title: 'Third-Party Vendor Data Processing Risk',
        description: 'Risk associated with data processing by external vendors without adequate safeguards',
        category: 'operational',
        likelihood: 4,
        impact: 4,
        riskScore: 16,
        riskLevel: 'high',
        status: 'under_review',
        owner: '<EMAIL>',
        assessor: 'Sarah Johnson',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-25'),
        reviewDate: new Date('2024-01-10'),
        nextReviewDate: new Date('2024-04-10'),
        mitigationMeasures: [
          {
            id: 'measure-003',
            title: 'Vendor Security Assessment',
            description: 'Conduct comprehensive security assessment of all third-party vendors',
            type: 'detective',
            status: 'planned',
            priority: 'high',
            owner: 'Vendor Management',
            dueDate: new Date('2024-03-01'),
            completedDate: undefined,
            effectiveness: 75,
            notes: 'Assessment framework being developed'
          }
        ],
        residualRisk: {
          likelihood: 3,
          impact: 3,
          score: 9,
          level: 'medium'
        },
        tags: ['vendor-management', 'third-party', 'data-processing'],
        attachments: ['vendor-risk-matrix.xlsx'],
        comments: []
      },
      {
        id: 'risk-003',
        title: 'Cloud Infrastructure Security Risk',
        description: 'Risk of misconfigured cloud services leading to data exposure',
        category: 'technology',
        likelihood: 3,
        impact: 4,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'Mike Chen',
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-18'),
        reviewDate: new Date('2024-01-05'),
        nextReviewDate: new Date('2024-07-05'),
        mitigationMeasures: [
          {
            id: 'measure-004',
            title: 'Cloud Security Posture Management',
            description: 'Implement CSPM tools for continuous cloud security monitoring',
            type: 'detective',
            status: 'implemented',
            priority: 'medium',
            owner: 'Cloud Security',
            dueDate: new Date('2024-02-15'),
            completedDate: new Date('2024-02-10'),
            effectiveness: 80,
            notes: 'CSPM tools deployed and monitoring active'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 3,
          score: 6,
          level: 'low'
        },
        tags: ['cloud-security', 'infrastructure', 'monitoring'],
        attachments: ['cloud-security-config.pdf'],
        comments: [
          {
            id: 'comment-003',
            author: 'Cloud Architect',
            content: 'CSPM implementation successful, monitoring active',
            timestamp: new Date('2024-02-12'),
            type: 'general'
          }
        ]
      },
      {
        id: 'risk-004',
        title: 'Insider Threat Risk',
        description: 'Risk of malicious or negligent actions by internal employees',
        category: 'operational',
        likelihood: 2,
        impact: 5,
        riskScore: 10,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'Lisa Wang',
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-30'),
        reviewDate: new Date('2024-01-08'),
        nextReviewDate: new Date('2024-07-08'),
        mitigationMeasures: [
          {
            id: 'measure-005',
            title: 'User Behavior Analytics',
            description: 'Deploy UBA solution to detect anomalous user behavior',
            type: 'detective',
            status: 'implemented',
            priority: 'medium',
            owner: 'Security Operations',
            dueDate: new Date('2024-02-28'),
            completedDate: new Date('2024-02-25'),
            effectiveness: 70,
            notes: 'UBA solution deployed, baseline established'
          },
          {
            id: 'measure-006',
            title: 'Privileged Access Management',
            description: 'Implement PAM solution for privileged account management',
            type: 'preventive',
            status: 'in_progress',
            priority: 'high',
            owner: 'Identity Management',
            dueDate: new Date('2024-04-15'),
            completedDate: undefined,
            effectiveness: 85,
            notes: 'PAM solution 60% deployed'
          }
        ],
        residualRisk: {
          likelihood: 1,
          impact: 4,
          score: 4,
          level: 'low'
        },
        tags: ['insider-threat', 'user-behavior', 'privileged-access'],
        attachments: ['insider-threat-policy.pdf'],
        comments: []
      },
      {
        id: 'risk-005',
        title: 'Supply Chain Security Risk',
        description: 'Risk of security vulnerabilities in software supply chain dependencies',
        category: 'operational',
        likelihood: 4,
        impact: 3,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'under_review',
        owner: '<EMAIL>',
        assessor: 'Sarah Johnson',
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date('2024-01-18'),
        reviewDate: new Date('2024-01-10'),
        nextReviewDate: new Date('2024-04-10'),
        mitigationMeasures: [
          {
            id: 'measure-002',
            title: 'Vendor Due Diligence Process',
            description: 'Implement comprehensive vendor assessment process',
            type: 'preventive',
            status: 'in_progress',
            priority: 'medium',
            owner: 'Legal Team',
            dueDate: new Date('2024-03-01'),
            effectiveness: 60,
            notes: 'Process documentation in progress'
          }
        ],
        residualRisk: {
          likelihood: 3,
          impact: 2,
          score: 6,
          level: 'low'
        },
        tags: ['vendor-management', 'data-processing'],
        attachments: [],
        comments: []
      },
      {
        id: 'risk-003',
        title: 'Cross-Border Data Transfer Compliance',
        description: 'Risk of non-compliance with international data transfer regulations',
        category: 'legal',
        likelihood: 2,
        impact: 4,
        riskScore: 8,
        riskLevel: 'medium',
        status: 'draft',
        owner: '<EMAIL>',
        assessor: 'Michael Chen',
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date('2024-01-12'),
        reviewDate: new Date('2024-01-05'),
        nextReviewDate: new Date('2024-06-05'),
        mitigationMeasures: [
          {
            id: 'measure-003',
            title: 'Standard Contractual Clauses Implementation',
            description: 'Deploy SCCs for all international data transfers',
            type: 'corrective',
            status: 'planned',
            priority: 'high',
            owner: 'Legal Team',
            dueDate: new Date('2024-02-15'),
            effectiveness: 0,
            notes: 'Awaiting legal review'
          }
        ],
        residualRisk: {
          likelihood: 1,
          impact: 3,
          score: 3,
          level: 'low'
        },
        tags: ['international-transfers', 'compliance'],
        attachments: ['transfer-impact-assessment.pdf'],
        comments: [
          {
            id: 'comment-002',
            author: 'Legal Counsel',
            content: 'Need to review adequacy decisions for target countries',
            timestamp: new Date('2024-01-12'),
            type: 'review'
          }
        ]
      },
      {
        id: 'risk-004',
        title: 'Cloud Infrastructure Security Risk',
        description: 'Risk of security vulnerabilities in cloud infrastructure and services',
        category: 'technology',
        likelihood: 3,
        impact: 4,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'David Wilson',
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date('2024-01-25'),
        reviewDate: new Date('2024-01-08'),
        nextReviewDate: new Date('2024-04-08'),
        mitigationMeasures: [
          {
            id: 'measure-004',
            title: 'Cloud Security Monitoring',
            description: 'Implement comprehensive cloud security monitoring and alerting',
            type: 'detective',
            status: 'implemented',
            priority: 'high',
            owner: 'Cloud Security Team',
            dueDate: new Date('2024-02-01'),
            completedDate: new Date('2024-01-30'),
            effectiveness: 80,
            notes: 'Monitoring tools deployed and configured'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 3,
          score: 6,
          level: 'low'
        },
        tags: ['cloud-security', 'infrastructure', 'monitoring'],
        attachments: ['cloud-security-assessment.pdf'],
        comments: []
      },
      {
        id: 'risk-005',
        title: 'Employee Data Handling Training Gap',
        description: 'Risk of data mishandling due to insufficient employee training on data protection',
        category: 'operational',
        likelihood: 4,
        impact: 2,
        riskScore: 8,
        riskLevel: 'medium',
        status: 'under_review',
        owner: '<EMAIL>',
        assessor: 'Lisa Anderson',
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-01-22'),
        reviewDate: new Date('2024-01-12'),
        nextReviewDate: new Date('2024-07-12'),
        mitigationMeasures: [
          {
            id: 'measure-005',
            title: 'Data Protection Training Program',
            description: 'Develop and deploy comprehensive data protection training for all employees',
            type: 'preventive',
            status: 'in_progress',
            priority: 'medium',
            owner: 'Training Team',
            dueDate: new Date('2024-03-15'),
            effectiveness: 40,
            notes: 'Training materials under development'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 2,
          score: 4,
          level: 'low'
        },
        tags: ['training', 'human-resources', 'data-handling'],
        attachments: [],
        comments: [
          {
            id: 'comment-003',
            author: 'HR Manager',
            content: 'Training program scheduled for Q1 2024 rollout',
            timestamp: new Date('2024-01-22'),
            type: 'update'
          }
        ]
      },
      {
        id: 'risk-006',
        title: 'Financial Fraud Detection System Gaps',
        description: 'Risk of undetected financial fraud due to inadequate detection systems',
        category: 'financial',
        likelihood: 2,
        impact: 5,
        riskScore: 10,
        riskLevel: 'medium',
        status: 'draft',
        owner: '<EMAIL>',
        assessor: 'Robert Chen',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-01-20'),
        reviewDate: new Date('2024-01-20'),
        nextReviewDate: new Date('2024-04-20'),
        mitigationMeasures: [
          {
            id: 'measure-006',
            title: 'Enhanced Fraud Detection System',
            description: 'Implement AI-powered fraud detection and monitoring system',
            type: 'detective',
            status: 'planned',
            priority: 'high',
            owner: 'Finance IT Team',
            dueDate: new Date('2024-04-01'),
            effectiveness: 0,
            notes: 'System procurement in progress'
          }
        ],
        residualRisk: {
          likelihood: 1,
          impact: 4,
          score: 4,
          level: 'low'
        },
        tags: ['fraud-detection', 'financial-controls', 'ai-monitoring'],
        attachments: ['fraud-risk-analysis.pdf'],
        comments: []
      },
      {
        id: 'risk-007',
        title: 'API Security Vulnerabilities',
        description: 'Risk of security vulnerabilities in public-facing APIs',
        category: 'technology',
        likelihood: 3,
        impact: 4,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'David Kim',
        createdAt: new Date('2024-01-12'),
        updatedAt: new Date('2024-02-01'),
        reviewDate: new Date('2024-01-12'),
        nextReviewDate: new Date('2024-07-12'),
        mitigationMeasures: [
          {
            id: 'measure-009',
            title: 'API Security Gateway',
            description: 'Deploy API gateway with security controls and rate limiting',
            type: 'preventive',
            status: 'implemented',
            priority: 'high',
            owner: 'API Security Team',
            dueDate: new Date('2024-02-15'),
            completedDate: new Date('2024-02-12'),
            effectiveness: 85,
            notes: 'API gateway deployed with comprehensive security controls'
          },
          {
            id: 'measure-010',
            title: 'API Penetration Testing',
            description: 'Conduct regular penetration testing of all public APIs',
            type: 'detective',
            status: 'in_progress',
            priority: 'medium',
            owner: 'Security Testing',
            dueDate: new Date('2024-03-30'),
            completedDate: undefined,
            effectiveness: 75,
            notes: 'Quarterly penetration testing scheduled'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 3,
          score: 6,
          level: 'low'
        },
        tags: ['api-security', 'web-security', 'penetration-testing'],
        attachments: ['api-security-assessment.pdf'],
        comments: [
          {
            id: 'comment-004',
            author: 'Security Architect',
            content: 'API gateway implementation successful, monitoring active',
            timestamp: new Date('2024-02-14'),
            type: 'general'
          }
        ]
      },
      {
        id: 'risk-008',
        title: 'Mobile Application Security Risk',
        description: 'Risk of security vulnerabilities in mobile applications',
        category: 'technology',
        likelihood: 3,
        impact: 3,
        riskScore: 9,
        riskLevel: 'medium',
        status: 'under_review',
        owner: '<EMAIL>',
        assessor: 'Jennifer Liu',
        createdAt: new Date('2024-01-20'),
        updatedAt: new Date('2024-02-05'),
        reviewDate: new Date('2024-01-20'),
        nextReviewDate: new Date('2024-04-20'),
        mitigationMeasures: [
          {
            id: 'measure-011',
            title: 'Mobile App Security Testing',
            description: 'Implement SAST/DAST tools for mobile app security testing',
            type: 'detective',
            status: 'planned',
            priority: 'medium',
            owner: 'Mobile Security',
            dueDate: new Date('2024-03-15'),
            completedDate: undefined,
            effectiveness: 80,
            notes: 'Security testing tools evaluation in progress'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 2,
          score: 4,
          level: 'low'
        },
        tags: ['mobile-security', 'application-security', 'sast-dast'],
        attachments: ['mobile-security-guidelines.pdf'],
        comments: []
      },
      {
        id: 'risk-009',
        title: 'Business Continuity Risk',
        description: 'Risk of business disruption due to inadequate disaster recovery planning',
        category: 'operational',
        likelihood: 2,
        impact: 5,
        riskScore: 10,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'Robert Taylor',
        createdAt: new Date('2024-01-25'),
        updatedAt: new Date('2024-02-10'),
        reviewDate: new Date('2024-01-25'),
        nextReviewDate: new Date('2024-07-25'),
        mitigationMeasures: [
          {
            id: 'measure-012',
            title: 'Disaster Recovery Plan Update',
            description: 'Update and test disaster recovery procedures',
            type: 'corrective',
            status: 'implemented',
            priority: 'high',
            owner: 'Business Continuity',
            dueDate: new Date('2024-03-01'),
            completedDate: new Date('2024-02-28'),
            effectiveness: 90,
            notes: 'DR plan updated and successfully tested'
          }
        ],
        residualRisk: {
          likelihood: 1,
          impact: 4,
          score: 4,
          level: 'low'
        },
        tags: ['business-continuity', 'disaster-recovery', 'operational-resilience'],
        attachments: ['dr-plan-2024.pdf', 'bc-test-results.pdf'],
        comments: [
          {
            id: 'comment-005',
            author: 'BC Manager',
            content: 'DR testing completed successfully, RTO/RPO targets met',
            timestamp: new Date('2024-03-01'),
            type: 'general'
          }
        ]
      },
      {
        id: 'risk-010',
        title: 'Regulatory Compliance Risk - GDPR',
        description: 'Risk of non-compliance with GDPR data protection requirements',
        category: 'compliance',
        likelihood: 2,
        impact: 5,
        riskScore: 10,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'Maria Garcia',
        createdAt: new Date('2024-01-30'),
        updatedAt: new Date('2024-02-15'),
        reviewDate: new Date('2024-01-30'),
        nextReviewDate: new Date('2024-07-30'),
        mitigationMeasures: [
          {
            id: 'measure-013',
            title: 'GDPR Compliance Audit',
            description: 'Conduct comprehensive GDPR compliance audit',
            type: 'detective',
            status: 'implemented',
            priority: 'high',
            owner: 'Privacy Team',
            dueDate: new Date('2024-03-15'),
            completedDate: new Date('2024-03-10'),
            effectiveness: 95,
            notes: 'Comprehensive audit completed, minor gaps identified and addressed'
          }
        ],
        residualRisk: {
          likelihood: 1,
          impact: 3,
          score: 3,
          level: 'low'
        },
        tags: ['gdpr', 'privacy', 'regulatory-compliance'],
        attachments: ['gdpr-audit-report.pdf'],
        comments: [
          {
            id: 'comment-006',
            author: 'DPO',
            content: 'GDPR compliance audit completed successfully',
            timestamp: new Date('2024-03-12'),
            type: 'approval'
          }
        ]
      },
      {
        id: 'risk-011',
        title: 'Network Security Perimeter Risk',
        description: 'Risk of network perimeter breaches and unauthorized access',
        category: 'technology',
        likelihood: 3,
        impact: 4,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'approved',
        owner: '<EMAIL>',
        assessor: 'Alex Thompson',
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date('2024-02-20'),
        reviewDate: new Date('2024-02-01'),
        nextReviewDate: new Date('2024-08-01'),
        mitigationMeasures: [
          {
            id: 'measure-014',
            title: 'Next-Generation Firewall Upgrade',
            description: 'Upgrade to NGFW with advanced threat detection',
            type: 'preventive',
            status: 'implemented',
            priority: 'high',
            owner: 'Network Security',
            dueDate: new Date('2024-03-01'),
            completedDate: new Date('2024-02-28'),
            effectiveness: 85,
            notes: 'NGFW deployed with advanced threat protection'
          },
          {
            id: 'measure-015',
            title: 'Network Segmentation',
            description: 'Implement micro-segmentation for critical assets',
            type: 'preventive',
            status: 'in_progress',
            priority: 'medium',
            owner: 'Network Architecture',
            dueDate: new Date('2024-04-30'),
            completedDate: undefined,
            effectiveness: 80,
            notes: 'Segmentation design 80% complete'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 3,
          score: 6,
          level: 'low'
        },
        tags: ['network-security', 'firewall', 'segmentation'],
        attachments: ['network-security-assessment.pdf'],
        comments: []
      },
      {
        id: 'risk-012',
        title: 'Data Loss Prevention Risk',
        description: 'Risk of sensitive data exfiltration through various channels',
        category: 'compliance',
        likelihood: 3,
        impact: 4,
        riskScore: 12,
        riskLevel: 'medium',
        status: 'under_review',
        owner: '<EMAIL>',
        assessor: 'Emma Wilson',
        createdAt: new Date('2024-02-05'),
        updatedAt: new Date('2024-02-25'),
        reviewDate: new Date('2024-02-05'),
        nextReviewDate: new Date('2024-05-05'),
        mitigationMeasures: [
          {
            id: 'measure-016',
            title: 'DLP Solution Deployment',
            description: 'Deploy comprehensive DLP solution across all endpoints',
            type: 'preventive',
            status: 'planned',
            priority: 'high',
            owner: 'Data Protection',
            dueDate: new Date('2024-04-15'),
            completedDate: undefined,
            effectiveness: 90,
            notes: 'DLP solution evaluation in progress'
          }
        ],
        residualRisk: {
          likelihood: 2,
          impact: 3,
          score: 6,
          level: 'low'
        },
        tags: ['data-loss-prevention', 'data-protection', 'endpoint-security'],
        attachments: ['dlp-requirements.pdf'],
        comments: []
      }
    ];
  }

}
