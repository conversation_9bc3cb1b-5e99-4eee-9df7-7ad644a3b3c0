import React, { useState, useMemo, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Bar, Doughnut } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Archive,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Database,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Calendar,
  History,
  FileText,
  ArrowRight,
  BarChart3,
  Activity,
  Zap,
  Target
} from 'lucide-react';

// Types for SLO Classic Dashboard
interface ClassicSLAMetric {
  id: string;
  name: string;
  description: string;
  currentValue: number;
  targetValue: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  category: 'availability' | 'performance' | 'reliability';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    target: number;
    breaches: number;
  }>;
  migrationStatus: 'legacy' | 'migrated' | 'pending';
  legacySystemId?: string;
}

interface MigrationTool {
  id: string;
  name: string;
  description: string;
  sourceSystem: string;
  targetSystem: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  estimatedCompletion?: Date;
  migratedMetrics: number;
  totalMetrics: number;
}

interface ClassicDashboardData {
  overview: {
    totalSLAs: number;
    legacySLAs: number;
    migratedSLAs: number;
    pendingSLAs: number;
    averageCompliance: number;
    migrationProgress: number;
  };
  metrics: ClassicSLAMetric[];
  migrationTools: MigrationTool[];
  historicalTrends: {
    complianceTrend: Array<{ date: string; compliance: number; breaches: number }>;
    migrationTrend: Array<{ date: string; migrated: number; pending: number }>;
  };
}

interface ServiceLevelObjectivesClassicProps {
  className?: string;
}

// Mock data generator for Classic SLO
const generateClassicSLOData = (): ClassicDashboardData => {
  const metrics: ClassicSLAMetric[] = [
    {
      id: 'classic-001',
      name: 'Legacy API Uptime',
      description: 'Traditional API availability monitoring from legacy system',
      currentValue: 99.2,
      targetValue: 99.0,
      unit: '%',
      status: 'healthy',
      category: 'availability',
      lastUpdated: new Date(Date.now() - 5 * 60 * 1000),
      migrationStatus: 'legacy',
      legacySystemId: 'LEGACY_SYS_001',
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 98.5 + Math.random() * 1.5,
        target: 99.0,
        breaches: Math.floor(Math.random() * 3)
      }))
    },
    {
      id: 'classic-002',
      name: 'Database Response Time',
      description: 'Legacy database performance monitoring',
      currentValue: 250,
      targetValue: 300,
      unit: 'ms',
      status: 'healthy',
      category: 'performance',
      lastUpdated: new Date(Date.now() - 2 * 60 * 1000),
      migrationStatus: 'migrated',
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 200 + Math.random() * 100,
        target: 300,
        breaches: Math.floor(Math.random() * 2)
      }))
    },
    {
      id: 'classic-003',
      name: 'System Reliability Index',
      description: 'Overall system reliability from legacy monitoring',
      currentValue: 97.8,
      targetValue: 98.5,
      unit: '%',
      status: 'warning',
      category: 'reliability',
      lastUpdated: new Date(Date.now() - 10 * 60 * 1000),
      migrationStatus: 'pending',
      legacySystemId: 'LEGACY_SYS_002',
      historicalData: Array.from({ length: 30 }, (_, i) => ({
        date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        value: 96 + Math.random() * 3,
        target: 98.5,
        breaches: Math.floor(Math.random() * 4)
      }))
    }
  ];

  const migrationTools: MigrationTool[] = [
    {
      id: 'migration-001',
      name: 'Legacy SLA Migrator',
      description: 'Migrate SLA definitions from legacy monitoring system',
      sourceSystem: 'Legacy Monitor v2.1',
      targetSystem: 'Modern SLO Platform',
      status: 'in_progress',
      progress: 65,
      estimatedCompletion: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      migratedMetrics: 13,
      totalMetrics: 20
    },
    {
      id: 'migration-002',
      name: 'Historical Data Importer',
      description: 'Import historical SLA data for trend analysis',
      sourceSystem: 'Legacy Database',
      targetSystem: 'Time Series DB',
      status: 'completed',
      progress: 100,
      migratedMetrics: 45,
      totalMetrics: 45
    },
    {
      id: 'migration-003',
      name: 'Alert Rule Converter',
      description: 'Convert legacy alert rules to modern format',
      sourceSystem: 'Legacy Alerting',
      targetSystem: 'Modern Alert Manager',
      status: 'pending',
      progress: 0,
      migratedMetrics: 0,
      totalMetrics: 8
    }
  ];

  const legacySLAs = metrics.filter(m => m.migrationStatus === 'legacy').length;
  const migratedSLAs = metrics.filter(m => m.migrationStatus === 'migrated').length;
  const pendingSLAs = metrics.filter(m => m.migrationStatus === 'pending').length;

  return {
    overview: {
      totalSLAs: metrics.length,
      legacySLAs,
      migratedSLAs,
      pendingSLAs,
      averageCompliance: metrics.reduce((sum, m) => sum + m.currentValue, 0) / metrics.length,
      migrationProgress: (migratedSLAs / metrics.length) * 100
    },
    metrics,
    migrationTools,
    historicalTrends: {
      complianceTrend: Array.from({ length: 12 }, (_, i) => ({
        date: new Date(Date.now() - (11 - i) * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'short' }),
        compliance: 95 + Math.random() * 4,
        breaches: Math.floor(Math.random() * 10)
      })),
      migrationTrend: Array.from({ length: 6 }, (_, i) => ({
        date: new Date(Date.now() - (5 - i) * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('en-US', { month: 'short' }),
        migrated: Math.min(i * 2 + Math.floor(Math.random() * 3), metrics.length),
        pending: Math.max(metrics.length - (i * 2 + Math.floor(Math.random() * 3)), 0)
      }))
    }
  };
};

export const ServiceLevelObjectivesClassic: React.FC<ServiceLevelObjectivesClassicProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<ClassicDashboardData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'availability' | 'performance' | 'reliability'>('all');
  const [selectedMigrationStatus, setSelectedMigrationStatus] = useState<'all' | 'legacy' | 'migrated' | 'pending'>('all');

  const chartTheme = getChartTheme(mode === 'dark');

  // Simulate data loading
  useEffect(() => {
    const loadData = async () => {
      setIsLoading(true);
      try {
        await new Promise(resolve => setTimeout(resolve, 1200));
        const data = generateClassicSLOData();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load Classic SLO data');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  // Filter metrics based on category and migration status
  const filteredMetrics = useMemo(() => {
    if (!dashboardData) return [];
    
    return dashboardData.metrics.filter(metric => {
      const categoryMatch = selectedCategory === 'all' || metric.category === selectedCategory;
      const migrationMatch = selectedMigrationStatus === 'all' || metric.migrationStatus === selectedMigrationStatus;
      return categoryMatch && migrationMatch;
    });
  }, [dashboardData, selectedCategory, selectedMigrationStatus]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getMigrationStatusIcon = (status: string) => {
    switch (status) {
      case 'legacy':
        return <Archive className="w-4 h-4 text-amber-500" />;
      case 'migrated':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'pending':
        return <Clock className="w-4 h-4 text-blue-500" />;
      default:
        return <Database className="w-4 h-4 text-gray-500" />;
    }
  };

  const getMigrationStatusColorClass = (status: string) => {
    switch (status) {
      case 'legacy':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'migrated':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'pending':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  // Generate migration status chart data
  const migrationStatusChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: ['Legacy', 'Migrated', 'Pending'],
      datasets: [{
        data: [
          dashboardData.overview.legacySLAs,
          dashboardData.overview.migratedSLAs,
          dashboardData.overview.pendingSLAs
        ],
        backgroundColor: [
          'rgba(251, 191, 36, 0.8)',
          'rgba(52, 211, 153, 0.8)',
          'rgba(79, 142, 247, 0.8)'
        ],
        borderColor: [
          'rgb(251, 191, 36)',
          'rgb(52, 211, 153)',
          'rgb(79, 142, 247)'
        ],
        borderWidth: 2,
      }]
    };
  }, [dashboardData]);

  // Generate compliance trend chart data
  const complianceTrendChartData = useMemo(() => {
    if (!dashboardData) return null;

    return {
      labels: dashboardData.historicalTrends.complianceTrend.map(d => d.date),
      datasets: [
        {
          label: 'Compliance %',
          data: dashboardData.historicalTrends.complianceTrend.map(d => d.compliance),
          borderColor: 'rgb(79, 142, 247)',
          backgroundColor: 'rgba(79, 142, 247, 0.1)',
          borderWidth: 3,
          fill: true,
          tension: 0.4,
          yAxisID: 'y',
        },
        {
          label: 'Breaches',
          data: dashboardData.historicalTrends.complianceTrend.map(d => d.breaches),
          borderColor: 'rgb(248, 113, 113)',
          backgroundColor: 'rgba(248, 113, 113, 0.8)',
          borderWidth: 2,
          type: 'bar' as const,
          yAxisID: 'y1',
        }
      ]
    };
  }, [dashboardData]);

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 800));
      const data = generateClassicSLOData();
      setDashboardData(data);
      setError(null);
    } catch (err) {
      setError('Failed to refresh Classic SLO data');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Classic SLO Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={handleRefresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Archive className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Service-Level Objectives Classic</h1>
              <p className="text-text-secondary">
                Legacy SLA management with migration tools and backward compatibility
              </p>
            </div>
          </div>

          <div className="flex items-center gap-2">
            <button
              onClick={handleRefresh}
              disabled={isLoading}
              className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
              title="Refresh data"
            >
              <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
            </button>

            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors">
              <Download className="w-4 h-4" />
              Export Legacy Data
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        {dashboardData && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Total SLAs</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.totalSLAs}</p>
                </div>
                <Target className="w-8 h-8 text-primary" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Legacy SLAs</p>
                  <p className="text-2xl font-bold text-amber-500">{dashboardData.overview.legacySLAs}</p>
                </div>
                <Archive className="w-8 h-8 text-amber-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Migrated</p>
                  <p className="text-2xl font-bold text-green-500">{dashboardData.overview.migratedSLAs}</p>
                </div>
                <CheckCircle className="w-8 h-8 text-green-500" />
              </div>
            </div>

            <div className="bg-card rounded-lg p-4 border border-border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-text-secondary">Migration Progress</p>
                  <p className="text-2xl font-bold text-text">{dashboardData.overview.migrationProgress.toFixed(1)}%</p>
                </div>
                <Activity className="w-8 h-8 text-blue-500" />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Migration Status Distribution */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <BarChart3 className="w-5 h-5" />
            Migration Status Distribution
          </h3>
          {migrationStatusChartData && (
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={migrationStatusChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* Historical Compliance Trend */}
        <div className="bg-surface rounded-lg p-6">
          <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
            <History className="w-5 h-5" />
            Historical Compliance & Breaches
          </h3>
          {complianceTrendChartData && (
            <div className="h-64">
              <Line
                data={complianceTrendChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  interaction: {
                    mode: 'index' as const,
                    intersect: false,
                  },
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      type: 'linear' as const,
                      display: true,
                      position: 'left' as const,
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 90,
                      max: 100
                    },
                    y1: {
                      type: 'linear' as const,
                      display: true,
                      position: 'right' as const,
                      grid: {
                        drawOnChartArea: false,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      },
                    },
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Migration Tools */}
      <div className="bg-surface rounded-lg p-6">
        <h3 className="text-lg font-semibold text-text mb-4 flex items-center gap-2">
          <Settings className="w-5 h-5" />
          Migration Tools
        </h3>

        {dashboardData && (
          <div className="grid grid-cols-1 gap-4">
            {dashboardData.migrationTools.map((tool) => (
              <div key={tool.id} className="bg-card rounded-lg p-4 border border-border">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <h4 className="text-lg font-semibold text-text">{tool.name}</h4>
                    <p className="text-sm text-text-secondary mb-2">{tool.description}</p>
                    <div className="flex items-center gap-4 text-sm text-text-secondary">
                      <span>From: {tool.sourceSystem}</span>
                      <ArrowRight className="w-4 h-4" />
                      <span>To: {tool.targetSystem}</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      tool.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      tool.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400' :
                      tool.status === 'failed' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400'
                    }`}>
                      {tool.status.replace('_', ' ').charAt(0).toUpperCase() + tool.status.replace('_', ' ').slice(1)}
                    </span>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-text-secondary">Progress</span>
                    <span className="text-text font-medium">{tool.progress}%</span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        tool.status === 'completed' ? 'bg-green-500' :
                        tool.status === 'in_progress' ? 'bg-blue-500' :
                        tool.status === 'failed' ? 'bg-red-500' : 'bg-gray-500'
                      }`}
                      style={{ width: `${tool.progress}%` }}
                    />
                  </div>

                  <div className="flex items-center justify-between text-sm text-text-secondary">
                    <span>Metrics: {tool.migratedMetrics}/{tool.totalMetrics}</span>
                    {tool.estimatedCompletion && (
                      <span>ETA: {tool.estimatedCompletion.toLocaleDateString()}</span>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Filters and Legacy SLA Metrics */}
      <div className="bg-surface rounded-lg p-6">
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex items-center gap-2">
            <Filter className="w-4 h-4 text-text-secondary" />
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Categories</option>
              <option value="availability">Availability</option>
              <option value="performance">Performance</option>
              <option value="reliability">Reliability</option>
            </select>
          </div>

          <div className="flex items-center gap-2">
            <Database className="w-4 h-4 text-text-secondary" />
            <select
              value={selectedMigrationStatus}
              onChange={(e) => setSelectedMigrationStatus(e.target.value as any)}
              className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            >
              <option value="all">All Migration Status</option>
              <option value="legacy">Legacy Only</option>
              <option value="migrated">Migrated Only</option>
              <option value="pending">Pending Migration</option>
            </select>
          </div>
        </div>

        {/* Legacy SLA Metrics List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-text">Legacy SLA Metrics</h3>

          {filteredMetrics.length === 0 ? (
            <div className="text-center py-8">
              <Archive className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <p className="text-text-secondary">No legacy SLA metrics found matching your criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 gap-4">
              {filteredMetrics.map((metric) => (
                <div key={metric.id} className="bg-card rounded-lg p-6 border border-border hover:shadow-md transition-all duration-200">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStatusIcon(metric.status)}
                        <h4 className="text-lg font-semibold text-text">{metric.name}</h4>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(metric.status)}`}>
                          {metric.status.charAt(0).toUpperCase() + metric.status.slice(1)}
                        </span>
                        <div className="flex items-center gap-1">
                          {getMigrationStatusIcon(metric.migrationStatus)}
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getMigrationStatusColorClass(metric.migrationStatus)}`}>
                            {metric.migrationStatus.charAt(0).toUpperCase() + metric.migrationStatus.slice(1)}
                          </span>
                        </div>
                      </div>

                      <p className="text-sm text-text-secondary mb-4">{metric.description}</p>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                          <p className="text-xs text-text-secondary">Current Value</p>
                          <p className="text-lg font-bold text-text">
                            {metric.currentValue.toFixed(1)}{metric.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Target</p>
                          <p className="text-lg font-bold text-text">
                            {metric.targetValue.toFixed(1)}{metric.unit}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Last Updated</p>
                          <p className="text-sm font-semibold text-text">
                            {metric.lastUpdated.toLocaleTimeString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-text-secondary">Legacy System</p>
                          <p className="text-sm font-semibold text-text">
                            {metric.legacySystemId || 'N/A'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Progress Bar */}
                  <div>
                    <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                      <span>Compliance Progress</span>
                      <span>{Math.min((metric.currentValue / metric.targetValue) * 100, 100).toFixed(1)}%</span>
                    </div>
                    <div className="w-full bg-border rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          metric.status === 'healthy' ? 'bg-green-500' :
                          metric.status === 'warning' ? 'bg-amber-500' : 'bg-red-500'
                        }`}
                        style={{
                          width: `${Math.min((metric.currentValue / metric.targetValue) * 100, 100)}%`
                        }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ServiceLevelObjectivesClassic;
