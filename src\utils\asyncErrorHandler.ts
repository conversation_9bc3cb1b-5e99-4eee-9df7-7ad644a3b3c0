// Async error handling utilities for the Enterprise Dashboard

export interface AsyncError {
  message: string;
  code?: string;
  status?: number;
  timestamp: Date;
  operation: string;
  retryable: boolean;
}

export interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  retryableErrors?: string[];
}

export interface AsyncOperationResult<T> {
  data?: T;
  error?: AsyncError;
  isLoading: boolean;
  retryCount: number;
}

// Default retry configuration
const DEFAULT_RETRY_CONFIG: RetryConfig = {
  maxRetries: 3,
  baseDelay: 1000,
  maxDelay: 10000,
  backoffMultiplier: 2,
  retryableErrors: ['NETWORK_ERROR', 'TIMEOUT', 'SERVER_ERROR']
};

// Create an async error object
export const createAsyncError = (
  message: string,
  operation: string,
  options: {
    code?: string;
    status?: number;
    retryable?: boolean;
  } = {}
): AsyncError => ({
  message,
  operation,
  timestamp: new Date(),
  retryable: options.retryable ?? isRetryableError(options.code, options.status),
  ...options
});

// Determine if an error is retryable
export const isRetryableError = (code?: string, status?: number): boolean => {
  // Network errors
  if (code === 'NETWORK_ERROR' || code === 'TIMEOUT') return true;
  
  // HTTP status codes that are retryable
  if (status) {
    return status >= 500 || status === 408 || status === 429;
  }
  
  return false;
};

// Sleep utility for delays
const sleep = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// Calculate delay with exponential backoff
const calculateDelay = (retryCount: number, config: RetryConfig): number => {
  const delay = config.baseDelay * Math.pow(config.backoffMultiplier, retryCount);
  return Math.min(delay, config.maxDelay);
};

// Retry wrapper for async operations
export const withRetry = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  config: Partial<RetryConfig> = {}
): Promise<T> => {
  const retryConfig = { ...DEFAULT_RETRY_CONFIG, ...config };
  let lastError: AsyncError;
  
  for (let attempt = 0; attempt <= retryConfig.maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      const asyncError = createAsyncError(
        error instanceof Error ? error.message : 'Unknown error',
        operationName,
        {
          code: (error as any)?.code,
          status: (error as any)?.status || (error as any)?.response?.status
        }
      );
      
      lastError = asyncError;
      
      // Don't retry if it's the last attempt or error is not retryable
      if (attempt === retryConfig.maxRetries || !asyncError.retryable) {
        break;
      }
      
      // Wait before retrying
      const delay = calculateDelay(attempt, retryConfig);
      await sleep(delay);
      
      console.warn(`Retrying ${operationName} (attempt ${attempt + 1}/${retryConfig.maxRetries}) after ${delay}ms delay`);
    }
  }
  
  throw lastError;
};

// Safe async wrapper that catches and transforms errors
export const safeAsync = async <T>(
  operation: () => Promise<T>,
  operationName: string,
  config: Partial<RetryConfig> = {}
): Promise<AsyncOperationResult<T>> => {
  const result: AsyncOperationResult<T> = {
    isLoading: true,
    retryCount: 0
  };
  
  try {
    const data = await withRetry(operation, operationName, config);
    return {
      ...result,
      data,
      isLoading: false
    };
  } catch (error) {
    const asyncError = error instanceof Error 
      ? createAsyncError(error.message, operationName)
      : error as AsyncError;
      
    return {
      ...result,
      error: asyncError,
      isLoading: false,
      retryCount: config.maxRetries || DEFAULT_RETRY_CONFIG.maxRetries
    };
  }
};

// Hook-like utility for managing async operations in components
export class AsyncOperationManager<T> {
  private operation: () => Promise<T>;
  private operationName: string;
  private config: RetryConfig;
  private listeners: Set<(result: AsyncOperationResult<T>) => void> = new Set();
  private currentResult: AsyncOperationResult<T> = {
    isLoading: false,
    retryCount: 0
  };

  constructor(
    operation: () => Promise<T>,
    operationName: string,
    config: Partial<RetryConfig> = {}
  ) {
    this.operation = operation;
    this.operationName = operationName;
    this.config = { ...DEFAULT_RETRY_CONFIG, ...config };
  }

  // Subscribe to result changes
  subscribe(listener: (result: AsyncOperationResult<T>) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  // Notify all listeners
  private notify() {
    this.listeners.forEach(listener => listener(this.currentResult));
  }

  // Execute the operation
  async execute(): Promise<void> {
    this.currentResult = { ...this.currentResult, isLoading: true };
    this.notify();

    try {
      const data = await withRetry(this.operation, this.operationName, this.config);
      this.currentResult = {
        data,
        isLoading: false,
        retryCount: this.currentResult.retryCount,
        error: undefined
      };
    } catch (error) {
      const asyncError = error instanceof Error 
        ? createAsyncError(error.message, this.operationName)
        : error as AsyncError;
        
      this.currentResult = {
        error: asyncError,
        isLoading: false,
        retryCount: this.config.maxRetries,
        data: undefined
      };
    }

    this.notify();
  }

  // Retry the operation
  async retry(): Promise<void> {
    this.currentResult = { ...this.currentResult, retryCount: 0 };
    await this.execute();
  }

  // Get current result
  getCurrentResult(): AsyncOperationResult<T> {
    return this.currentResult;
  }

  // Reset the operation state
  reset(): void {
    this.currentResult = {
      isLoading: false,
      retryCount: 0
    };
    this.notify();
  }
}

// Utility for handling fetch operations
export const safeFetch = async (
  url: string,
  options: RequestInit = {},
  operationName?: string
): Promise<Response> => {
  const operation = async (): Promise<Response> => {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout

    try {
      const response = await fetch(url, {
        ...options,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        throw createAsyncError(
          `HTTP ${response.status}: ${response.statusText}`,
          operationName || `fetch ${url}`,
          { status: response.status }
        );
      }

      return response;
    } catch (error) {
      clearTimeout(timeoutId);
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw createAsyncError(
          'Request timeout',
          operationName || `fetch ${url}`,
          { code: 'TIMEOUT' }
        );
      }
      
      throw error;
    }
  };

  return withRetry(operation, operationName || `fetch ${url}`);
};

// Error boundary integration
export const handleAsyncError = (error: AsyncError, context?: string): void => {
  console.error(`Async error in ${context || 'unknown context'}:`, error);
  
  // In production, you might want to send this to an error reporting service
  if (process.env.NODE_ENV === 'production') {
    // Example: Send to error reporting service
    // errorReportingService.captureException(error);
  }
};

export default {
  createAsyncError,
  isRetryableError,
  withRetry,
  safeAsync,
  AsyncOperationManager,
  safeFetch,
  handleAsyncError
};
