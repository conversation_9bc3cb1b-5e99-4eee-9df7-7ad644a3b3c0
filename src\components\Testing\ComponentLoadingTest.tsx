import React, { Suspense, useState } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Import all dashboard components to test loading
const SiteReliabilityGuardian = React.lazy(() => import('../SiteReliability/SiteReliabilityGuardian'));
const ServiceLevelObjectivesDashboard = React.lazy(() => import('../SLO/ServiceLevelObjectivesDashboard'));
const ServiceLevelObjectivesClassic = React.lazy(() => import('../SLO/ServiceLevelObjectivesClassic'));
const EnhancedComplianceMetrics = React.lazy(() => import('../compliance/EnhancedComplianceMetrics'));
const RiskAssessmentDashboard = React.lazy(() => import('../Risk/RiskAssessmentDashboard'));
const SecurityOverviewDashboard = React.lazy(() => import('../Security/SecurityOverviewDashboard'));
const RegulatoryFrameworkDashboard = React.lazy(() => import('../compliance/RegulatoryFrameworkDashboard'));
const GDPRCommandCenter = React.lazy(() => import('../GDPR/GDPRCommandCenter'));

interface ComponentTestProps {
  name: string;
  component: React.ComponentType;
}

const ComponentTest: React.FC<ComponentTestProps> = ({ name, component: Component }) => {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  return (
    <div className="border border-border rounded-lg p-4 mb-4">
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-lg font-semibold text-text">{name}</h3>
        <div className="flex items-center gap-2">
          <div className={`w-3 h-3 rounded-full ${hasError ? 'bg-red-500' : isLoaded ? 'bg-green-500' : 'bg-yellow-500'}`} />
          <span className="text-sm text-text-secondary">
            {hasError ? 'Error' : isLoaded ? 'Loaded' : 'Loading'}
          </span>
        </div>
      </div>
      
      <ErrorBoundary
        type="component"
        fallbackTitle={`${name} Test Failed`}
        fallbackMessage={`The ${name} component failed to load or render properly.`}
        onError={(error, errorInfo) => {
          console.error(`${name} Error:`, error, errorInfo);
          setHasError(true);
        }}
      >
        <Suspense 
          fallback={
            <div className="flex items-center justify-center p-8 bg-surface rounded-lg">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2 text-text-secondary">Loading {name}...</span>
            </div>
          }
        >
          <div 
            className="bg-surface rounded-lg p-4 max-h-96 overflow-hidden"
            onLoad={() => setIsLoaded(true)}
          >
            <Component />
          </div>
        </Suspense>
      </ErrorBoundary>
    </div>
  );
};

export const ComponentLoadingTest: React.FC = () => {
  const components = [
    { name: 'GDPR Command Center', component: GDPRCommandCenter },
    { name: 'Enhanced Compliance Metrics', component: EnhancedComplianceMetrics },
    { name: 'Risk Assessment Dashboard', component: RiskAssessmentDashboard },
    { name: 'Security Overview Dashboard', component: SecurityOverviewDashboard },
    { name: 'Regulatory Framework Dashboard', component: RegulatoryFrameworkDashboard },
    { name: 'Site Reliability Guardian', component: SiteReliabilityGuardian },
    { name: 'Service Level Objectives Dashboard', component: ServiceLevelObjectivesDashboard },
    { name: 'Service Level Objectives Classic', component: ServiceLevelObjectivesClassic },
  ];

  const [testAll, setTestAll] = useState(false);

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Component Loading Test Suite</h1>
          <p className="text-text-secondary mb-4">
            This test suite verifies that all dashboard components can be loaded and rendered without errors.
          </p>
          
          <div className="flex items-center gap-4 mb-6">
            <button
              onClick={() => setTestAll(!testAll)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                testAll 
                  ? 'bg-primary text-white' 
                  : 'bg-surface text-text border border-border hover:bg-card'
              }`}
            >
              {testAll ? 'Hide All Tests' : 'Test All Components'}
            </button>
            
            <div className="text-sm text-text-secondary">
              Click to load and test all dashboard components simultaneously
            </div>
          </div>
        </div>

        {testAll && (
          <div className="space-y-6">
            <div className="bg-surface rounded-lg p-6 border border-border">
              <h2 className="text-xl font-semibold text-text mb-4">Component Loading Results</h2>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {components.map((comp) => (
                  <ComponentTest
                    key={comp.name}
                    name={comp.name}
                    component={comp.component}
                  />
                ))}
              </div>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <h2 className="text-xl font-semibold text-text mb-4">Test Instructions</h2>
              <div className="space-y-3 text-text-secondary">
                <p>• <strong>Green dot:</strong> Component loaded and rendered successfully</p>
                <p>• <strong>Yellow dot:</strong> Component is currently loading</p>
                <p>• <strong>Red dot:</strong> Component failed to load or encountered an error</p>
                <p>• Check the browser console for detailed error messages</p>
                <p>• Each component should display actual data, not fallback error messages</p>
              </div>
            </div>
          </div>
        )}

        {!testAll && (
          <div className="bg-surface rounded-lg p-6 border border-border text-center">
            <h2 className="text-xl font-semibold text-text mb-4">Ready to Test</h2>
            <p className="text-text-secondary mb-6">
              Click "Test All Components" above to begin loading and testing all dashboard components.
            </p>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {components.map((comp) => (
                <div key={comp.name} className="p-3 bg-card rounded-lg border border-border">
                  <div className="text-sm font-medium text-text">{comp.name}</div>
                  <div className="text-xs text-text-secondary mt-1">Ready to test</div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComponentLoadingTest;
