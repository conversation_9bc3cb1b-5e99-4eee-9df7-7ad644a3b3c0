/**
 * Production-ready error reporting service for compliance components
 * Handles error logging, reporting, and monitoring without exposing sensitive data
 */

export interface ErrorContext {
  componentName: string;
  userId?: string;
  sessionId?: string;
  timestamp: string;
  userAgent: string;
  url: string;
  buildVersion?: string;
  environment: 'development' | 'staging' | 'production';
}

export interface SanitizedError {
  message: string;
  stack?: string;
  name: string;
  code?: string | number;
  context: ErrorContext;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'ui' | 'data' | 'network' | 'chart' | 'validation' | 'unknown';
}

export interface ErrorMetrics {
  errorCount: number;
  errorRate: number;
  lastErrorTime: string;
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurrence: string;
  }>;
}

class ErrorReportingService {
  private errorBuffer: SanitizedError[] = [];
  private maxBufferSize = 100;
  private flushInterval = 30000; // 30 seconds
  private isInitialized = false;
  private sessionId: string;
  private errorCounts = new Map<string, number>();

  constructor() {
    this.sessionId = this.generateSessionId();
    this.initialize();
  }

  private initialize() {
    if (this.isInitialized) return;

    // Set up periodic error buffer flushing
    setInterval(() => {
      this.flushErrorBuffer();
    }, this.flushInterval);

    // Set up global error handlers
    this.setupGlobalErrorHandlers();

    this.isInitialized = true;
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private setupGlobalErrorHandlers() {
    // Handle unhandled promise rejections
    window.addEventListener('unhandledrejection', (event) => {
      this.captureError(event.reason, {
        componentName: 'Global',
        category: 'unknown',
        severity: 'high'
      });
    });

    // Handle JavaScript errors
    window.addEventListener('error', (event) => {
      this.captureError(new Error(event.message), {
        componentName: 'Global',
        category: 'unknown',
        severity: 'high'
      });
    });
  }

  private sanitizeError(error: Error | any): Partial<SanitizedError> {
    // Remove sensitive information from error messages and stack traces
    const sensitivePatterns = [
      /password/gi,
      /token/gi,
      /key/gi,
      /secret/gi,
      /auth/gi,
      /session/gi,
      /cookie/gi,
      /bearer/gi
    ];

    let message = error?.message || 'Unknown error';
    let stack = error?.stack || '';

    // Sanitize message
    sensitivePatterns.forEach(pattern => {
      message = message.replace(pattern, '[REDACTED]');
    });

    // Sanitize and limit stack trace
    if (stack) {
      sensitivePatterns.forEach(pattern => {
        stack = stack.replace(pattern, '[REDACTED]');
      });
      // Limit stack trace to first 10 lines
      stack = stack.split('\n').slice(0, 10).join('\n');
    }

    return {
      message,
      stack,
      name: error?.name || 'Error',
      code: error?.code
    };
  }

  private createErrorContext(componentName: string): ErrorContext {
    return {
      componentName,
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      buildVersion: process.env.REACT_APP_VERSION || 'unknown',
      environment: (process.env.NODE_ENV as any) || 'development'
    };
  }

  private categorizeError(error: Error | any, context?: any): SanitizedError['category'] {
    const message = error?.message?.toLowerCase() || '';
    const stack = error?.stack?.toLowerCase() || '';

    if (message.includes('chart') || message.includes('canvas') || stack.includes('chart.js')) {
      return 'chart';
    }
    if (message.includes('network') || message.includes('fetch') || message.includes('xhr')) {
      return 'network';
    }
    if (message.includes('validation') || message.includes('invalid') || message.includes('required')) {
      return 'validation';
    }
    if (message.includes('data') || message.includes('parse') || message.includes('json')) {
      return 'data';
    }
    if (context?.componentName || message.includes('component') || message.includes('render')) {
      return 'ui';
    }
    return 'unknown';
  }

  private determineSeverity(error: Error | any, category: SanitizedError['category']): SanitizedError['severity'] {
    const message = error?.message?.toLowerCase() || '';

    // Critical errors that break core functionality
    if (message.includes('cannot read property') || 
        message.includes('is not a function') ||
        message.includes('maximum call stack') ||
        category === 'chart' && message.includes('failed to create')) {
      return 'critical';
    }

    // High severity errors that impact user experience
    if (message.includes('failed to') || 
        message.includes('network error') ||
        category === 'data' && message.includes('invalid')) {
      return 'high';
    }

    // Medium severity errors that are recoverable
    if (message.includes('warning') || 
        message.includes('deprecated') ||
        category === 'validation') {
      return 'medium';
    }

    return 'low';
  }

  public captureError(
    error: Error | any, 
    options: {
      componentName: string;
      category?: SanitizedError['category'];
      severity?: SanitizedError['severity'];
      additionalContext?: Record<string, any>;
    }
  ): void {
    try {
      const sanitizedError = this.sanitizeError(error);
      const context = this.createErrorContext(options.componentName);
      const category = options.category || this.categorizeError(error, options);
      const severity = options.severity || this.determineSeverity(error, category);

      const errorReport: SanitizedError = {
        ...sanitizedError,
        context,
        category,
        severity
      } as SanitizedError;

      // Track error frequency
      const errorKey = `${errorReport.name}:${errorReport.message}`;
      this.errorCounts.set(errorKey, (this.errorCounts.get(errorKey) || 0) + 1);

      // Add to buffer
      this.errorBuffer.push(errorReport);

      // Log to console in development
      if (process.env.NODE_ENV === 'development') {
        console.group(`🚨 Error Report [${severity.toUpperCase()}]`);
        console.error('Original Error:', error);
        console.log('Sanitized Report:', errorReport);
        console.groupEnd();
      }

      // Immediate flush for critical errors
      if (severity === 'critical') {
        this.flushErrorBuffer();
      }

      // Prevent buffer overflow
      if (this.errorBuffer.length > this.maxBufferSize) {
        this.errorBuffer = this.errorBuffer.slice(-this.maxBufferSize);
      }
    } catch (reportingError) {
      console.error('Error in error reporting service:', reportingError);
    }
  }

  private async flushErrorBuffer(): Promise<void> {
    if (this.errorBuffer.length === 0) return;

    const errorsToFlush = [...this.errorBuffer];
    this.errorBuffer = [];

    try {
      // In production, send to error reporting service
      if (process.env.NODE_ENV === 'production') {
        // Example: Send to external service
        // await fetch('/api/errors', {
        //   method: 'POST',
        //   headers: { 'Content-Type': 'application/json' },
        //   body: JSON.stringify({ errors: errorsToFlush })
        // });
        
        console.log(`Would send ${errorsToFlush.length} errors to reporting service`);
      }

      // Store in local storage for debugging (limited)
      this.storeErrorsLocally(errorsToFlush);
    } catch (flushError) {
      console.error('Failed to flush error buffer:', flushError);
      // Restore errors to buffer for retry
      this.errorBuffer.unshift(...errorsToFlush);
    }
  }

  private storeErrorsLocally(errors: SanitizedError[]): void {
    try {
      const existingErrors = JSON.parse(localStorage.getItem('compliance_errors') || '[]');
      const allErrors = [...existingErrors, ...errors].slice(-50); // Keep last 50 errors
      localStorage.setItem('compliance_errors', JSON.stringify(allErrors));
    } catch (storageError) {
      console.warn('Failed to store errors locally:', storageError);
    }
  }

  public getErrorMetrics(): ErrorMetrics {
    const now = new Date();
    const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

    const recentErrors = this.errorBuffer.filter(
      error => new Date(error.context.timestamp) > oneHourAgo
    );

    const topErrors = Array.from(this.errorCounts.entries())
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([errorKey, count]) => ({
        message: errorKey.split(':')[1] || errorKey,
        count,
        lastOccurrence: new Date().toISOString()
      }));

    return {
      errorCount: recentErrors.length,
      errorRate: recentErrors.length / 60, // errors per minute
      lastErrorTime: recentErrors[recentErrors.length - 1]?.context.timestamp || 'N/A',
      topErrors
    };
  }

  public clearErrorBuffer(): void {
    this.errorBuffer = [];
    this.errorCounts.clear();
    localStorage.removeItem('compliance_errors');
  }
}

// Singleton instance
export const errorReportingService = new ErrorReportingService();

// Convenience function for components
export const reportError = (
  error: Error | any,
  componentName: string,
  options?: {
    category?: SanitizedError['category'];
    severity?: SanitizedError['severity'];
    additionalContext?: Record<string, any>;
  }
) => {
  errorReportingService.captureError(error, {
    componentName,
    ...options
  });
};

export default errorReportingService;
