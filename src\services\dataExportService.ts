import { DataSubjectRequestService } from './dataSubjectRequestService';
import { BranchDetectionService } from './branchDetectionService';
import { ConsentManagementService } from './consentManagementService';
import { ImpactAssessmentService } from './impactAssessmentService';
import { GDPRService } from './gdprService';

export interface ExportOptions {
  format: 'csv' | 'json' | 'xlsx' | 'pdf';
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeFields?: string[];
  excludeFields?: string[];
  filters?: Record<string, any>;
}

export interface ExportResult {
  filename: string;
  data: string | Blob;
  mimeType: string;
  size: number;
}

export class DataExportService {
  
  // Data Subject Requests Export
  static async exportDataSubjectRequests(options: ExportOptions): Promise<ExportResult> {
    const requests = await DataSubjectRequestService.getAllRequests();
    let filteredRequests = requests;

    // Apply date range filter
    if (options.dateRange) {
      filteredRequests = filteredRequests.filter(request => 
        request.submittedAt >= options.dateRange!.start && 
        request.submittedAt <= options.dateRange!.end
      );
    }

    // Apply additional filters
    if (options.filters) {
      if (options.filters.status) {
        filteredRequests = filteredRequests.filter(r => options.filters!.status.includes(r.status));
      }
      if (options.filters.type) {
        filteredRequests = filteredRequests.filter(r => options.filters!.type.includes(r.type));
      }
      if (options.filters.priority) {
        filteredRequests = filteredRequests.filter(r => options.filters!.priority.includes(r.priority));
      }
    }

    const exportData = filteredRequests.map(request => ({
      id: request.id,
      type: request.type,
      email: request.email,
      firstName: request.firstName,
      lastName: request.lastName,
      status: request.status,
      submittedAt: request.submittedAt.toISOString(),
      dueDate: request.dueDate.toISOString(),
      completedAt: request.completedAt?.toISOString() || '',
      progress: request.progress,
      priority: request.priority,
      assignee: request.assignee,
      description: request.description,
      dataCategories: request.requestDetails.dataCategories.join('; '),
      processingPurposes: request.requestDetails.processingPurposes.join('; '),
      legalBasis: request.requestDetails.legalBasis,
      workflowCurrentStep: request.workflow.currentStep,
      workflowTotalSteps: request.workflow.totalSteps,
      communicationsCount: request.communications.length,
      attachmentsCount: request.attachments.length
    }));

    return this.formatExportData(exportData, options, 'data_subject_requests');
  }

  // Consent Records Export
  static async exportConsentRecords(options: ExportOptions): Promise<ExportResult> {
    const records = await ConsentManagementService.getAllConsentRecords();
    let filteredRecords = records;

    // Apply date range filter
    if (options.dateRange) {
      filteredRecords = filteredRecords.filter(record => 
        record.consentTimestamp >= options.dateRange!.start && 
        record.consentTimestamp <= options.dateRange!.end
      );
    }

    // Apply additional filters
    if (options.filters) {
      if (options.filters.status) {
        filteredRecords = filteredRecords.filter(r => options.filters!.status.includes(r.status));
      }
      if (options.filters.consentMethod) {
        filteredRecords = filteredRecords.filter(r => options.filters!.consentMethod.includes(r.consentMethod));
      }
    }

    const exportData = filteredRecords.map(record => {
      const consentCategories = record.consentCategories.map(cat => ({
        name: cat.name,
        granted: cat.consentGiven,
        grantedAt: cat.consentTimestamp?.toISOString() || '',
        withdrawnAt: cat.withdrawalTimestamp?.toISOString() || ''
      }));

      return {
        id: record.id,
        email: record.email,
        firstName: record.firstName,
        lastName: record.lastName,
        status: record.status,
        consentTimestamp: record.consentTimestamp.toISOString(),
        consentMethod: record.consentMethod,
        consentVersion: record.consentVersion,
        source: record.source,
        lastUpdated: record.lastUpdated.toISOString(),
        withdrawalHistoryCount: record.withdrawalHistory.length,
        preferencesCount: record.preferences.length,
        marketingConsent: consentCategories.find(c => c.name.includes('Marketing'))?.granted || false,
        analyticsConsent: consentCategories.find(c => c.name.includes('Analytics'))?.granted || false,
        essentialConsent: consentCategories.find(c => c.name.includes('Essential'))?.granted || false,
        thirdPartyConsent: consentCategories.find(c => c.name.includes('Third-party'))?.granted || false
      };
    });

    return this.formatExportData(exportData, options, 'consent_records');
  }

  // Branch Compliance Export
  static async exportBranchCompliance(options: ExportOptions): Promise<ExportResult> {
    const branches = await BranchDetectionService.getAllBranches();
    
    const exportData = branches.map(branch => ({
      id: branch.id,
      name: branch.name,
      type: branch.type,
      country: branch.location.country,
      region: branch.location.region,
      city: branch.location.city,
      complianceStatus: branch.complianceStatus.overall,
      riskLevel: branch.complianceStatus.riskLevel,
      lastAssessment: branch.complianceStatus.lastAssessment.toISOString(),
      nextAssessment: branch.complianceStatus.nextAssessment.toISOString(),
      openIssues: branch.complianceStatus.openIssues,
      resolvedIssues: branch.complianceStatus.resolvedIssues,
      dataProtectionLaws: branch.jurisdiction.dataProtectionLaw.join('; '),
      supervisoryAuthority: branch.jurisdiction.supervisoryAuthority,
      adequacyDecision: branch.jurisdiction.adequacyDecision,
      transferMechanisms: branch.jurisdiction.transferMechanisms.join('; '),
      dpoName: branch.contacts.dpo?.name || '',
      dpoEmail: branch.contacts.dpo?.email || '',
      technicalContactName: branch.contacts.technicalContact.name,
      technicalContactEmail: branch.contacts.technicalContact.email,
      businessContactName: branch.contacts.businessContact.name,
      businessContactEmail: branch.contacts.businessContact.email,
      certificationsCount: branch.certifications.length,
      auditHistoryCount: branch.auditHistory.length
    }));

    return this.formatExportData(exportData, options, 'branch_compliance');
  }

  // Impact Assessments Export
  static async exportImpactAssessments(options: ExportOptions): Promise<ExportResult> {
    const assessments = await ImpactAssessmentService.getAllAssessments();
    let filteredAssessments = assessments;

    // Apply date range filter
    if (options.dateRange) {
      filteredAssessments = filteredAssessments.filter(assessment => 
        assessment.createdAt >= options.dateRange!.start && 
        assessment.createdAt <= options.dateRange!.end
      );
    }

    // Apply additional filters
    if (options.filters) {
      if (options.filters.status) {
        filteredAssessments = filteredAssessments.filter(a => options.filters!.status.includes(a.status));
      }
      if (options.filters.type) {
        filteredAssessments = filteredAssessments.filter(a => options.filters!.type.includes(a.type));
      }
      if (options.filters.riskLevel) {
        filteredAssessments = filteredAssessments.filter(a => options.filters!.riskLevel.includes(a.riskAssessment.riskLevel));
      }
    }

    const exportData = filteredAssessments.map(assessment => ({
      id: assessment.id,
      title: assessment.title,
      description: assessment.description,
      type: assessment.type,
      status: assessment.status,
      version: assessment.version,
      createdAt: assessment.createdAt.toISOString(),
      lastUpdated: assessment.lastUpdated.toISOString(),
      dueDate: assessment.dueDate.toISOString(),
      completedAt: assessment.completedAt?.toISOString() || '',
      assignee: assessment.assignee,
      reviewer: assessment.reviewer,
      processingActivityName: assessment.processingActivity.name,
      processingActivityDescription: assessment.processingActivity.description,
      legalBasis: assessment.processingActivity.legalBasis,
      dataCategories: assessment.processingActivity.dataCategories.join('; '),
      dataSubjects: assessment.processingActivity.dataSubjects.join('; '),
      internationalTransfers: assessment.processingActivity.internationalTransfers,
      automatedDecisionMaking: assessment.processingActivity.automatedDecisionMaking,
      profiling: assessment.processingActivity.profiling,
      overallRiskScore: assessment.riskAssessment.overallRiskScore,
      riskLevel: assessment.riskAssessment.riskLevel,
      identifiedRisksCount: assessment.riskAssessment.identifiedRisks.length,
      mitigationMeasuresCount: assessment.mitigationMeasures.length,
      completionPercentage: assessment.completionPercentage,
      sectionsCompleted: assessment.sectionsCompleted.join('; '),
      sectionsRemaining: assessment.sectionsRemaining.join('; '),
      dpoConsulted: assessment.consultation.dpoConsulted,
      dataSubjectsConsulted: assessment.consultation.dataSubjectsConsulted,
      externalConsultation: assessment.consultation.externalConsultation,
      reviewFrequency: assessment.monitoring.reviewFrequency,
      nextReviewDate: assessment.monitoring.nextReviewDate.toISOString()
    }));

    return this.formatExportData(exportData, options, 'impact_assessments');
  }

  // Comprehensive GDPR Report Export
  static async exportComprehensiveReport(options: ExportOptions): Promise<ExportResult> {
    const [
      dashboardMetrics,
      alerts,
      activities
    ] = await Promise.all([
      GDPRService.getDashboardMetrics(),
      GDPRService.getAlerts({ dismissed: false }),
      GDPRService.getActivities(100)
    ]);

    const reportData = {
      generatedAt: new Date().toISOString(),
      reportPeriod: options.dateRange ? {
        start: options.dateRange.start.toISOString(),
        end: options.dateRange.end.toISOString()
      } : null,
      
      // Executive Summary
      executiveSummary: {
        overallComplianceScore: dashboardMetrics.complianceScore.overall,
        complianceTrend: dashboardMetrics.complianceScore.trend,
        totalDataSubjectRequests: dashboardMetrics.dataSubjectRequests.total,
        requestCompletionRate: dashboardMetrics.dataSubjectRequests.completionRate,
        totalBranches: dashboardMetrics.branchCompliance.totalBranches,
        branchComplianceScore: dashboardMetrics.branchCompliance.complianceScore,
        totalUsers: dashboardMetrics.consentManagement.totalUsers,
        consentRate: dashboardMetrics.consentManagement.consentRate,
        totalAssessments: dashboardMetrics.impactAssessments.total,
        highRiskAssessments: dashboardMetrics.impactAssessments.highRiskAssessments,
        securityIncidents: dashboardMetrics.securityIncidents.total,
        dataBreaches: dashboardMetrics.securityIncidents.dataBreaches
      },

      // Detailed Metrics
      detailedMetrics: dashboardMetrics,

      // Active Alerts
      activeAlerts: alerts.filter(a => !a.dismissed).map(alert => ({
        id: alert.id,
        type: alert.type,
        category: alert.category,
        title: alert.title,
        message: alert.message,
        priority: alert.priority,
        timestamp: alert.timestamp.toISOString(),
        actionRequired: alert.actionRequired
      })),

      // Recent Activities
      recentActivities: activities.slice(0, 20).map(activity => ({
        id: activity.id,
        type: activity.type,
        title: activity.title,
        description: activity.description,
        timestamp: activity.timestamp.toISOString(),
        userName: activity.userName
      })),

      // Compliance Recommendations
      recommendations: this.generateComplianceRecommendations(dashboardMetrics, alerts)
    };

    return this.formatExportData(reportData, options, 'gdpr_comprehensive_report');
  }

  // Private helper methods
  private static formatExportData(data: any, options: ExportOptions, filename: string): ExportResult {
    const timestamp = new Date().toISOString().split('T')[0];
    const fullFilename = `${filename}_${timestamp}`;

    switch (options.format) {
      case 'json':
        const jsonData = JSON.stringify(data, null, 2);
        return {
          filename: `${fullFilename}.json`,
          data: jsonData,
          mimeType: 'application/json',
          size: new Blob([jsonData]).size
        };

      case 'csv':
        const csvData = this.convertToCSV(data);
        return {
          filename: `${fullFilename}.csv`,
          data: csvData,
          mimeType: 'text/csv',
          size: new Blob([csvData]).size
        };

      case 'xlsx':
        // In a real implementation, you would use a library like xlsx or exceljs
        const xlsxData = JSON.stringify(data, null, 2); // Placeholder
        return {
          filename: `${fullFilename}.xlsx`,
          data: xlsxData,
          mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          size: new Blob([xlsxData]).size
        };

      case 'pdf':
        // In a real implementation, you would use a library like jsPDF or puppeteer
        const pdfData = JSON.stringify(data, null, 2); // Placeholder
        return {
          filename: `${fullFilename}.pdf`,
          data: pdfData,
          mimeType: 'application/pdf',
          size: new Blob([pdfData]).size
        };

      default:
        throw new Error(`Unsupported export format: ${options.format}`);
    }
  }

  private static convertToCSV(data: any[]): string {
    if (!Array.isArray(data) || data.length === 0) {
      return '';
    }

    const headers = Object.keys(data[0]);
    const csvHeaders = headers.join(',');
    
    const csvRows = data.map(row => 
      headers.map(header => {
        const value = row[header];
        // Escape commas and quotes in CSV
        if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return value;
      }).join(',')
    );

    return [csvHeaders, ...csvRows].join('\n');
  }

  private static generateComplianceRecommendations(metrics: any, alerts: any[]): string[] {
    const recommendations: string[] = [];

    // Data Subject Request recommendations
    if (metrics.dataSubjectRequests.completionRate < 90) {
      recommendations.push('Improve data subject request processing efficiency - current completion rate is below 90%');
    }
    if (metrics.dataSubjectRequests.overdue > 0) {
      recommendations.push(`Address ${metrics.dataSubjectRequests.overdue} overdue data subject requests immediately`);
    }

    // Branch compliance recommendations
    if (metrics.branchCompliance.complianceScore < 85) {
      recommendations.push('Enhance branch compliance programs - overall score is below target threshold');
    }
    if (metrics.branchCompliance.highRiskBranches > 0) {
      recommendations.push(`Prioritize compliance improvements for ${metrics.branchCompliance.highRiskBranches} high-risk branches`);
    }

    // Consent management recommendations
    if (metrics.consentManagement.consentRate < 70) {
      recommendations.push('Review consent collection processes - consent rate is below industry average');
    }
    if (metrics.consentManagement.withdrawalRate > 20) {
      recommendations.push('Investigate high consent withdrawal rate and improve user experience');
    }

    // Impact assessment recommendations
    if (metrics.impactAssessments.overdue > 0) {
      recommendations.push(`Complete ${metrics.impactAssessments.overdue} overdue impact assessments`);
    }
    if (metrics.impactAssessments.highRiskAssessments > 0) {
      recommendations.push(`Review and mitigate risks in ${metrics.impactAssessments.highRiskAssessments} high-risk assessments`);
    }

    // Security incident recommendations
    if (metrics.securityIncidents.open > 0) {
      recommendations.push(`Resolve ${metrics.securityIncidents.open} open security incidents`);
    }
    if (metrics.securityIncidents.dataBreaches > 0) {
      recommendations.push('Implement additional security measures to prevent future data breaches');
    }

    // Alert-based recommendations
    const criticalAlerts = alerts.filter(a => a.priority === 'critical' && !a.dismissed);
    if (criticalAlerts.length > 0) {
      recommendations.push(`Address ${criticalAlerts.length} critical alerts requiring immediate attention`);
    }

    // Overall compliance recommendations
    if (metrics.complianceScore.overall < 80) {
      recommendations.push('Develop comprehensive compliance improvement plan - overall score needs enhancement');
    }

    return recommendations;
  }

  // Utility method to download export data
  static downloadExport(exportResult: ExportResult): void {
    const blob = new Blob([exportResult.data], { type: exportResult.mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = exportResult.filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
  }
}
