/**
 * Accessibility Utilities for Enterprise Dashboard
 * Comprehensive accessibility helpers and hooks
 */

import { useEffect, useRef, useState, useCallback } from 'react';

// Keyboard navigation constants
export const KEYBOARD_KEYS = {
  ENTER: 'Enter',
  SPACE: ' ',
  ESCAPE: 'Escape',
  TAB: 'Tab',
  ARROW_UP: 'ArrowUp',
  ARROW_DOWN: 'ArrowDown',
  ARROW_LEFT: 'ArrowLeft',
  ARROW_RIGHT: 'ArrowRight',
  HOME: 'Home',
  END: 'End',
  PAGE_UP: 'PageUp',
  PAGE_DOWN: 'PageDown',
} as const;

// ARIA live region types
export type AriaLiveType = 'off' | 'polite' | 'assertive';

// Focus management utilities
export class FocusManager {
  private static focusableSelectors = [
    'button:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    'textarea:not([disabled])',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]',
  ].join(', ');

  static getFocusableElements(container: HTMLElement): HTMLElement[] {
    return Array.from(container.querySelectorAll(this.focusableSelectors));
  }

  static getFirstFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements[0] || null;
  }

  static getLastFocusableElement(container: HTMLElement): HTMLElement | null {
    const focusableElements = this.getFocusableElements(container);
    return focusableElements[focusableElements.length - 1] || null;
  }

  static trapFocus(container: HTMLElement, event: KeyboardEvent): void {
    if (event.key !== KEYBOARD_KEYS.TAB) return;

    const focusableElements = this.getFocusableElements(container);
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    if (event.shiftKey) {
      if (document.activeElement === firstElement) {
        event.preventDefault();
        lastElement?.focus();
      }
    } else {
      if (document.activeElement === lastElement) {
        event.preventDefault();
        firstElement?.focus();
      }
    }
  }

  static restoreFocus(previousActiveElement: HTMLElement | null): void {
    if (previousActiveElement && typeof previousActiveElement.focus === 'function') {
      previousActiveElement.focus();
    }
  }
}

// Screen reader utilities
export class ScreenReaderUtils {
  private static liveRegion: HTMLElement | null = null;

  static announce(message: string, priority: AriaLiveType = 'polite'): void {
    if (!this.liveRegion) {
      this.createLiveRegion();
    }

    if (this.liveRegion) {
      this.liveRegion.setAttribute('aria-live', priority);
      this.liveRegion.textContent = message;

      // Clear the message after a short delay to allow for re-announcements
      setTimeout(() => {
        if (this.liveRegion) {
          this.liveRegion.textContent = '';
        }
      }, 1000);
    }
  }

  private static createLiveRegion(): void {
    this.liveRegion = document.createElement('div');
    this.liveRegion.setAttribute('aria-live', 'polite');
    this.liveRegion.setAttribute('aria-atomic', 'true');
    this.liveRegion.style.position = 'absolute';
    this.liveRegion.style.left = '-10000px';
    this.liveRegion.style.width = '1px';
    this.liveRegion.style.height = '1px';
    this.liveRegion.style.overflow = 'hidden';
    document.body.appendChild(this.liveRegion);
  }

  static cleanup(): void {
    if (this.liveRegion) {
      document.body.removeChild(this.liveRegion);
      this.liveRegion = null;
    }
  }
}

// Custom hooks for accessibility
export function useKeyboardNavigation(
  onEnter?: () => void,
  onSpace?: () => void,
  onEscape?: () => void,
  onArrowKeys?: (direction: 'up' | 'down' | 'left' | 'right') => void
) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    switch (event.key) {
      case KEYBOARD_KEYS.ENTER:
        if (onEnter) {
          event.preventDefault();
          onEnter();
        }
        break;
      case KEYBOARD_KEYS.SPACE:
        if (onSpace) {
          event.preventDefault();
          onSpace();
        }
        break;
      case KEYBOARD_KEYS.ESCAPE:
        if (onEscape) {
          event.preventDefault();
          onEscape();
        }
        break;
      case KEYBOARD_KEYS.ARROW_UP:
        if (onArrowKeys) {
          event.preventDefault();
          onArrowKeys('up');
        }
        break;
      case KEYBOARD_KEYS.ARROW_DOWN:
        if (onArrowKeys) {
          event.preventDefault();
          onArrowKeys('down');
        }
        break;
      case KEYBOARD_KEYS.ARROW_LEFT:
        if (onArrowKeys) {
          event.preventDefault();
          onArrowKeys('left');
        }
        break;
      case KEYBOARD_KEYS.ARROW_RIGHT:
        if (onArrowKeys) {
          event.preventDefault();
          onArrowKeys('right');
        }
        break;
    }
  }, [onEnter, onSpace, onEscape, onArrowKeys]);

  return { onKeyDown: handleKeyDown };
}

export function useFocusManagement(isOpen: boolean) {
  const containerRef = useRef<HTMLElement>(null);
  const previousActiveElementRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    if (isOpen) {
      // Store the currently focused element
      previousActiveElementRef.current = document.activeElement as HTMLElement;

      // Focus the first focusable element in the container
      if (containerRef.current) {
        const firstFocusable = FocusManager.getFirstFocusableElement(containerRef.current);
        if (firstFocusable) {
          firstFocusable.focus();
        }
      }
    } else {
      // Restore focus to the previously focused element
      FocusManager.restoreFocus(previousActiveElementRef.current);
    }
  }, [isOpen]);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (isOpen && containerRef.current) {
      FocusManager.trapFocus(containerRef.current, event);
    }
  }, [isOpen]);

  return { containerRef, handleKeyDown };
}

export function useAnnouncements() {
  const announce = useCallback((message: string, priority: AriaLiveType = 'polite') => {
    ScreenReaderUtils.announce(message, priority);
  }, []);

  return { announce };
}

export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

export function useHighContrast() {
  const [prefersHighContrast, setPrefersHighContrast] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    setPrefersHighContrast(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersHighContrast(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast;
}

// Accessibility validation utilities
export class AccessibilityValidator {
  static validateAriaLabel(element: HTMLElement): string[] {
    const errors: string[] = [];

    if (element.hasAttribute('aria-labelledby')) {
      const labelledBy = element.getAttribute('aria-labelledby');
      if (labelledBy && !document.getElementById(labelledBy)) {
        errors.push(`Element references non-existent aria-labelledby ID: ${labelledBy}`);
      }
    }

    if (element.hasAttribute('aria-describedby')) {
      const describedBy = element.getAttribute('aria-describedby');
      if (describedBy && !document.getElementById(describedBy)) {
        errors.push(`Element references non-existent aria-describedby ID: ${describedBy}`);
      }
    }

    return errors;
  }

  static validateFocusableElements(container: HTMLElement): string[] {
    const errors: string[] = [];
    const focusableElements = FocusManager.getFocusableElements(container);

    focusableElements.forEach((element, index) => {
      if (!element.hasAttribute('aria-label') && !element.textContent?.trim()) {
        errors.push(`Focusable element at index ${index} lacks accessible name`);
      }
    });

    return errors;
  }

  static validateHeadingStructure(container: HTMLElement): string[] {
    const errors: string[] = [];
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6');
    let previousLevel = 0;

    headings.forEach((heading, index) => {
      const level = parseInt(heading.tagName.charAt(1));
      
      if (index === 0 && level !== 1) {
        errors.push('First heading should be h1');
      }
      
      if (level > previousLevel + 1) {
        errors.push(`Heading level jumps from h${previousLevel} to h${level}`);
      }
      
      previousLevel = level;
    });

    return errors;
  }
}

// ARIA attributes helper
export function createAriaAttributes(config: {
  label?: string;
  labelledBy?: string;
  describedBy?: string;
  expanded?: boolean;
  selected?: boolean;
  current?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  hidden?: boolean;
  live?: AriaLiveType;
  atomic?: boolean;
  busy?: boolean;
  controls?: string;
  owns?: string;
  role?: string;
}) {
  const attributes: Record<string, any> = {};

  if (config.label) attributes['aria-label'] = config.label;
  if (config.labelledBy) attributes['aria-labelledby'] = config.labelledBy;
  if (config.describedBy) attributes['aria-describedby'] = config.describedBy;
  if (config.expanded !== undefined) attributes['aria-expanded'] = config.expanded;
  if (config.selected !== undefined) attributes['aria-selected'] = config.selected;
  if (config.current !== undefined) attributes['aria-current'] = config.current;
  if (config.hidden !== undefined) attributes['aria-hidden'] = config.hidden;
  if (config.live) attributes['aria-live'] = config.live;
  if (config.atomic !== undefined) attributes['aria-atomic'] = config.atomic;
  if (config.busy !== undefined) attributes['aria-busy'] = config.busy;
  if (config.controls) attributes['aria-controls'] = config.controls;
  if (config.owns) attributes['aria-owns'] = config.owns;
  if (config.role) attributes['role'] = config.role;

  return attributes;
}

// Skip link component for keyboard navigation
export function createSkipLink(targetId: string, text: string = 'Skip to main content') {
  const skipLink = document.createElement('a');
  skipLink.href = `#${targetId}`;
  skipLink.textContent = text;
  skipLink.className = 'sr-only focus:not-sr-only focus:absolute focus:top-0 focus:left-0 focus:z-50 focus:p-2 focus:bg-primary focus:text-white';
  
  return skipLink;
}

// Cleanup function for accessibility utilities
export function cleanupAccessibility() {
  ScreenReaderUtils.cleanup();
}
