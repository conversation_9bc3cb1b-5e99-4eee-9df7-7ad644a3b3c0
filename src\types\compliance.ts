// Base interfaces
export interface Policy {
  id: string;
  name: string;
  description: string;
  status: 'compliant' | 'non_compliant' | 'pending';
  lastUpdated: string;
}

export interface Metrics {
  compliantPercentage: number;
  nonCompliantPercentage: number;
  pendingPercentage: number;
}

// GDPR Data Subject Request Management
export interface DataSubjectRequest {
  id: string;
  type: 'erasure' | 'portability' | 'access' | 'rectification' | 'objection';
  email: string;
  firstName: string;
  lastName: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected' | 'cancelled';
  submittedAt: Date;
  dueDate: Date;
  completedAt?: Date;
  progress: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignee: string;
  assigneeId: string;
  reviewer?: string;
  reviewerId?: string;
  description: string;
  requestDetails: {
    dataCategories: string[];
    processingPurposes: string[];
    legalBasis: string;
    retentionPeriod?: string;
    thirdParties?: string[];
  };
  workflow: {
    currentStep: number;
    totalSteps: number;
    steps: WorkflowStep[];
  };
  attachments: Attachment[];
  communications: Communication[];
  auditTrail: AuditEntry[];
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  assignee: string;
  estimatedDuration: number; // in hours
  actualDuration?: number;
  startedAt?: Date;
  completedAt?: Date;
  notes?: string;
  requiredActions: string[];
  completedActions: string[];
}

export interface Attachment {
  id: string;
  name: string;
  type: string;
  size: number;
  uploadedAt: Date;
  uploadedBy: string;
  url: string;
}

export interface Communication {
  id: string;
  type: 'email' | 'phone' | 'internal_note' | 'system_notification';
  direction: 'inbound' | 'outbound';
  subject?: string;
  content: string;
  timestamp: Date;
  from: string;
  to: string[];
  attachments?: Attachment[];
}

export interface AuditEntry {
  id: string;
  timestamp: Date;
  userId: string;
  userName: string;
  action: string;
  details: string;
  previousValue?: any;
  newValue?: any;
  ipAddress?: string;
  userAgent?: string;
}

// Branch Detection and Data Flow System
export interface OrganizationalBranch {
  id: string;
  name: string;
  type: 'headquarters' | 'subsidiary' | 'branch_office' | 'data_center' | 'service_provider';
  location: {
    country: string;
    region: string;
    city: string;
    address: string;
    coordinates: {
      lat: number;
      lng: number;
    };
  };
  jurisdiction: {
    dataProtectionLaw: string[];
    supervisoryAuthority: string;
    adequacyDecision: boolean;
    transferMechanisms: string[];
  };
  dataProcessing: {
    categories: DataCategory[];
    purposes: ProcessingPurpose[];
    legalBases: string[];
    retentionPeriods: RetentionPolicy[];
  };
  complianceStatus: {
    overall: 'compliant' | 'non_compliant' | 'partially_compliant' | 'under_review';
    lastAssessment: Date;
    nextAssessment: Date;
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    openIssues: number;
    resolvedIssues: number;
  };
  dataFlows: DataFlow[];
  contacts: {
    dpo?: Contact;
    legalRepresentative?: Contact;
    technicalContact: Contact;
    businessContact: Contact;
  };
  certifications: Certification[];
  auditHistory: ComplianceAudit[];
}

export interface DataCategory {
  id: string;
  name: string;
  description: string;
  sensitivity: 'public' | 'internal' | 'confidential' | 'restricted';
  personalDataTypes: string[];
  specialCategories: boolean;
  dataSubjects: string[];
  volume: {
    estimated: number;
    unit: 'records' | 'gb' | 'tb';
  };
}

export interface ProcessingPurpose {
  id: string;
  name: string;
  description: string;
  legalBasis: string;
  legitimateInterests?: string;
  dataMinimization: boolean;
  automated: boolean;
  profiling: boolean;
  dataRetention: {
    period: number;
    unit: 'days' | 'months' | 'years';
    criteria: string;
  };
}

export interface RetentionPolicy {
  id: string;
  dataCategory: string;
  purpose: string;
  retentionPeriod: number;
  retentionUnit: 'days' | 'months' | 'years';
  disposalMethod: string;
  reviewDate: Date;
  status: 'active' | 'under_review' | 'expired';
}

export interface DataFlow {
  id: string;
  name: string;
  description: string;
  sourceId: string;
  destinationId: string;
  dataCategories: string[];
  transferMechanism: 'adequacy_decision' | 'sccs' | 'bcrs' | 'certification' | 'cod' | 'derogation';
  frequency: 'real_time' | 'daily' | 'weekly' | 'monthly' | 'ad_hoc';
  volume: {
    estimated: number;
    unit: 'records' | 'gb' | 'tb';
  };
  encryption: {
    inTransit: boolean;
    atRest: boolean;
    algorithm?: string;
  };
  monitoring: {
    enabled: boolean;
    alertThreshold?: number;
    lastMonitored?: Date;
  };
  riskAssessment: {
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    identifiedRisks: string[];
    mitigationMeasures: string[];
    lastAssessed: Date;
  };
}

export interface Contact {
  id: string;
  name: string;
  title: string;
  email: string;
  phone: string;
  department: string;
  responsibilities: string[];
}

export interface Certification {
  id: string;
  name: string;
  issuer: string;
  issuedDate: Date;
  expiryDate: Date;
  status: 'active' | 'expired' | 'suspended' | 'revoked';
  scope: string[];
  certificateNumber: string;
  documentUrl?: string;
}

export interface ComplianceAudit {
  id: string;
  type: 'internal' | 'external' | 'regulatory';
  auditor: string;
  startDate: Date;
  endDate: Date;
  scope: string[];
  findings: AuditFinding[];
  recommendations: string[];
  status: 'planned' | 'in_progress' | 'completed' | 'follow_up_required';
  overallRating: 'excellent' | 'good' | 'satisfactory' | 'needs_improvement' | 'unsatisfactory';
}

export interface AuditFinding {
  id: string;
  category: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  recommendation: string;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  dueDate?: Date;
  assignee?: string;
}

// Consent Management System
export interface ConsentRecord {
  id: string;
  dataSubjectId: string;
  email: string;
  firstName: string;
  lastName: string;
  consentCategories: ConsentCategory[];
  consentTimestamp: Date;
  consentMethod: 'website' | 'email' | 'phone' | 'paper' | 'api';
  consentVersion: string;
  ipAddress?: string;
  userAgent?: string;
  withdrawalHistory: ConsentWithdrawal[];
  preferences: ConsentPreference[];
  status: 'active' | 'withdrawn' | 'expired' | 'pending_renewal';
  expiryDate?: Date;
  lastUpdated: Date;
  source: string;
  auditTrail: AuditEntry[];
}

export interface ConsentCategory {
  id: string;
  name: string;
  description: string;
  purpose: string;
  legalBasis: string;
  required: boolean;
  granular: boolean;
  subcategories?: ConsentSubcategory[];
  dataTypes: string[];
  processingActivities: string[];
  thirdParties: string[];
  retentionPeriod: {
    duration: number;
    unit: 'days' | 'months' | 'years';
  };
  consentGiven: boolean;
  consentTimestamp?: Date;
  withdrawalTimestamp?: Date;
  renewalRequired: boolean;
  renewalDate?: Date;
}

export interface ConsentSubcategory {
  id: string;
  name: string;
  description: string;
  consentGiven: boolean;
  consentTimestamp?: Date;
  withdrawalTimestamp?: Date;
}

export interface ConsentWithdrawal {
  id: string;
  categoryId: string;
  withdrawalTimestamp: Date;
  withdrawalMethod: 'website' | 'email' | 'phone' | 'paper' | 'api';
  reason?: string;
  ipAddress?: string;
  userAgent?: string;
  processedBy: string;
  effectiveDate: Date;
  dataRetentionAction: 'delete' | 'anonymize' | 'retain_legal_basis';
}

export interface ConsentPreference {
  id: string;
  categoryId: string;
  subcategoryId?: string;
  preferenceType: 'frequency' | 'channel' | 'content_type' | 'timing' | 'personalization';
  value: string;
  lastUpdated: Date;
}

export interface ConsentMetrics {
  totalUsers: number;
  consentedUsers: number;
  consentRate: number;
  trend: 'up' | 'down' | 'stable';
  weeklyChange: number;
}

// Enhanced Department Management
export interface Department {
  id: string;
  name: string;
  description: string;
  head: string;
  headId: string;
  employeeCount: number;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  policies: string[];
  assessments: string[];
  lastAudit: Date;
  nextAudit: Date;
  status: 'active' | 'inactive' | 'under_review';
  contactInfo: {
    email: string;
    phone: string;
    location: string;
  };
  complianceMetrics: {
    completedAssessments: number;
    pendingAssessments: number;
    overdueAssessments: number;
    policyCompliance: number;
    trainingCompletion: number;
  };
}

// Enhanced Policy Management
export interface EnhancedPolicy {
  id: string;
  name: string;
  description: string;
  version: string;
  status: 'draft' | 'active' | 'deprecated' | 'under_review';
  category: 'privacy' | 'security' | 'data_governance' | 'compliance' | 'operational';
  priority: 'low' | 'medium' | 'high' | 'critical';
  owner: string;
  ownerId: string;
  approver: string;
  approverId: string;
  effectiveDate: Date;
  expiryDate: Date;
  lastReview: Date;
  nextReview: Date;
  applicableDepartments: string[];
  relatedPolicies: string[];
  complianceRequirements: string[];
  attachments: string[];
  metrics: {
    complianceRate: number;
    violationCount: number;
    lastViolation: Date | null;
    trainingCompletion: number;
    acknowledgmentRate: number;
  };
  auditTrail: AuditEntry[];
}

// Enhanced Compliance Metrics
export interface EnhancedComplianceMetrics {
  overall: {
    score: number;
    trend: 'improving' | 'declining' | 'stable';
    lastUpdated: Date;
  };
  departments: {
    [departmentId: string]: {
      score: number;
      assessments: number;
      policies: number;
      risks: number;
      trend: 'improving' | 'declining' | 'stable';
    };
  };
  policies: {
    total: number;
    active: number;
    compliant: number;
    nonCompliant: number;
    pending: number;
    overdue: number;
    complianceRate: number;
  };
  assessments: {
    total: number;
    completed: number;
    inProgress: number;
    overdue: number;
    completionRate: number;
    averageRiskScore: number;
  };
  risks: {
    total: number;
    low: number;
    medium: number;
    high: number;
    critical: number;
    mitigated: number;
    open: number;
  };
  trends: {
    period: 'week' | 'month' | 'quarter' | 'year';
    complianceScore: number[];
    assessmentCompletion: number[];
    riskReduction: number[];
    policyCompliance: number[];
    dates: string[];
  };
}
  monthlyChange: number;
  lastUpdated: Date;
  categoryBreakdown: {
    categoryId: string;
    categoryName: string;
    consentRate: number;
    totalUsers: number;
    consentedUsers: number;
  }[];
}

// Impact Assessment (DPIA) System
export interface ImpactAssessment {
  id: string;
  title: string;
  description: string;
  type: 'DPIA' | 'LIA' | 'TIA' | 'CHIA'; // Data Protection, Legitimate Interest, Transfer, Children
  status: 'draft' | 'in_progress' | 'review' | 'approved' | 'rejected' | 'requires_update';
  version: string;
  createdAt: Date;
  lastUpdated: Date;
  dueDate: Date;
  completedAt?: Date;
  assignee: string;
  assigneeId: string;
  reviewer: string;
  reviewerId: string;
  approver?: string;
  approverId?: string;

  // Assessment Details
  processingActivity: {
    name: string;
    description: string;
    purpose: string[];
    legalBasis: string;
    dataCategories: string[];
    dataSubjects: string[];
    recipients: string[];
    internationalTransfers: boolean;
    transferCountries?: string[];
    retentionPeriod: string;
    automatedDecisionMaking: boolean;
    profiling: boolean;
  };

  // Risk Assessment
  riskAssessment: {
    overallRiskScore: number; // 1-10 scale
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
    identifiedRisks: Risk[];
    riskMatrix: RiskMatrix;
    residualRisk: number;
  };

  // Mitigation Measures
  mitigationMeasures: MitigationMeasure[];

  // Stakeholder Consultation
  consultation: {
    dataSubjectsConsulted: boolean;
    consultationMethod?: string;
    consultationDate?: Date;
    feedback: string[];
    dpoConsulted: boolean;
    dpoRecommendations: string[];
    externalConsultation: boolean;
    externalConsultants?: string[];
  };

  // Monitoring and Review
  monitoring: {
    reviewFrequency: 'monthly' | 'quarterly' | 'annually' | 'as_needed';
    nextReviewDate: Date;
    kpis: string[];
    monitoringPlan: string;
  };

  // Documentation
  attachments: Attachment[];
  relatedAssessments: string[];

  // Workflow
  workflow: {
    currentStep: number;
    totalSteps: number;
    steps: WorkflowStep[];
  };

  // Audit Trail
  auditTrail: AuditEntry[];

  // Completion Metrics
  completionPercentage: number;
  sectionsCompleted: string[];
  sectionsRemaining: string[];
}

export interface Risk {
  id: string;
  category: 'confidentiality' | 'integrity' | 'availability' | 'privacy' | 'compliance' | 'reputation';
  description: string;
  likelihood: 1 | 2 | 3 | 4 | 5; // Very Low to Very High
  impact: 1 | 2 | 3 | 4 | 5; // Very Low to Very High
  riskScore: number; // likelihood * impact
  inherentRisk: number;
  residualRisk: number;
  riskOwner: string;
  identifiedDate: Date;
  mitigationMeasures: string[];
  status: 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred';
}

export interface RiskMatrix {
  likelihood: {
    veryLow: number;
    low: number;
    medium: number;
    high: number;
    veryHigh: number;
  };
  impact: {
    veryLow: number;
    low: number;
    medium: number;
    high: number;
    veryHigh: number;
  };
  riskLevels: {
    low: { min: number; max: number; color: string };
    medium: { min: number; max: number; color: string };
    high: { min: number; max: number; color: string };
    critical: { min: number; max: number; color: string };
  };
}

export interface MitigationMeasure {
  id: string;
  title: string;
  description: string;
  category: 'technical' | 'organizational' | 'legal' | 'physical';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'planned' | 'in_progress' | 'implemented' | 'verified' | 'not_applicable';
  assignee: string;
  dueDate: Date;
  completedDate?: Date;
  effectiveness: 1 | 2 | 3 | 4 | 5; // Not Effective to Very Effective
  cost: number;
  effort: number; // in hours
  relatedRisks: string[];
  verificationMethod: string;
  verificationDate?: Date;
  verifiedBy?: string;
  notes?: string;
}

// Security Incident Management
export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  category: 'data_breach' | 'unauthorized_access' | 'malware' | 'phishing' | 'system_failure' | 'insider_threat' | 'physical_security';
  status: 'detected' | 'investigating' | 'contained' | 'eradicated' | 'recovered' | 'resolved' | 'closed';
  detectedAt: Date;
  reportedAt: Date;
  containedAt?: Date;
  resolvedAt?: Date;
  affectedUsers: number;
  affectedRecords: number;
  dataCategories: string[];

  // Incident Response
  assignee: string;
  responseTeam: string[];
  estimatedResolution: Date;
  actualResolution?: Date;

  // Breach Assessment
  isPersonalDataBreach: boolean;
  breachType?: 'confidentiality' | 'integrity' | 'availability';
  riskToRights: 'low' | 'medium' | 'high';
  notificationRequired: boolean;
  notificationDeadline?: Date;
  supervisoryAuthorityNotified: boolean;
  dataSubjectsNotified: boolean;

  // Investigation
  rootCause?: string;
  contributingFactors: string[];
  evidenceCollected: string[];
  forensicAnalysis?: string;

  // Response Actions
  immediateActions: string[];
  containmentActions: string[];
  eradicationActions: string[];
  recoveryActions: string[];
  preventiveActions: string[];

  // Communication
  internalCommunications: Communication[];
  externalCommunications: Communication[];
  mediaResponse?: string;

  // Lessons Learned
  lessonsLearned: string[];
  processImprovements: string[];
  policyUpdates: string[];
  trainingNeeds: string[];

  // Costs and Impact
  estimatedCost: number;
  businessImpact: string;
  reputationalImpact: string;

  // Audit Trail
  auditTrail: AuditEntry[];
}

export interface ComplianceContextType {
  metrics: Metrics;
  fetchData: () => Promise<void>;
  isLoading: boolean; // Add this line
  trendData: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }[];
  };
  distribution: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
    }[];
  };
  policies: Policy[];
  updateComplianceMetrics: (type: string, category: string, item: string, status: string) => void;
}