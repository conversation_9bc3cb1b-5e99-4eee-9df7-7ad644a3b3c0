import { Department, EnhancedPolicy, EnhancedComplianceMetrics, AuditEntry } from '../types/compliance';

export class EnhancedComplianceService {
  private static departments: Department[] = [];
  private static policies: EnhancedPolicy[] = [];
  private static metrics: EnhancedComplianceMetrics | null = null;
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    this.departments = this.generateMockDepartments();
    this.policies = this.generateMockPolicies();
    this.metrics = this.calculateMetrics();
    this.initialized = true;
  }

  // Department Management
  static async getAllDepartments(): Promise<Department[]> {
    this.initialize();
    return [...this.departments];
  }

  static async getDepartmentById(id: string): Promise<Department | null> {
    this.initialize();
    return this.departments.find(dept => dept.id === id) || null;
  }

  static async updateDepartment(id: string, updates: Partial<Department>): Promise<Department | null> {
    this.initialize();
    const index = this.departments.findIndex(dept => dept.id === id);
    if (index === -1) return null;

    this.departments[index] = { ...this.departments[index], ...updates };
    this.metrics = this.calculateMetrics(); // Recalculate metrics
    return this.departments[index];
  }

  // Policy Management
  static async getAllPolicies(): Promise<EnhancedPolicy[]> {
    this.initialize();
    return [...this.policies];
  }

  static async getPolicyById(id: string): Promise<EnhancedPolicy | null> {
    this.initialize();
    return this.policies.find(policy => policy.id === id) || null;
  }

  static async updatePolicyStatus(id: string, status: EnhancedPolicy['status']): Promise<EnhancedPolicy | null> {
    this.initialize();
    const index = this.policies.findIndex(policy => policy.id === id);
    if (index === -1) return null;

    this.policies[index].status = status;
    this.policies[index].auditTrail.push({
      id: `audit-${Date.now()}`,
      action: 'status_change',
      timestamp: new Date(),
      userId: 'system',
      userName: 'System',
      details: `Status changed to ${status}`,
      ipAddress: '127.0.0.1',
      userAgent: 'System'
    });

    this.metrics = this.calculateMetrics(); // Recalculate metrics
    return this.policies[index];
  }

  // Enhanced Metrics
  static async getEnhancedMetrics(): Promise<EnhancedComplianceMetrics> {
    this.initialize();
    return this.metrics!;
  }

  static async getMetricsByDepartment(departmentId: string): Promise<any> {
    this.initialize();
    const department = this.departments.find(dept => dept.id === departmentId);
    if (!department) return null;

    return {
      department: department.name,
      complianceScore: department.complianceScore,
      metrics: department.complianceMetrics,
      policies: this.policies.filter(policy => 
        policy.applicableDepartments.includes(departmentId)
      ).length,
      riskLevel: department.riskLevel
    };
  }

  // Real-time Updates
  static async refreshMetrics(): Promise<EnhancedComplianceMetrics> {
    this.initialize();
    this.metrics = this.calculateMetrics();
    return this.metrics;
  }

  // Private helper methods
  private static generateMockDepartments(): Department[] {
    const departments = [
      'Human Resources', 'Information Technology', 'Legal & Compliance', 
      'Marketing', 'Finance', 'Operations', 'Customer Service', 'Research & Development'
    ];

    return departments.map((name, index) => ({
      id: `dept-${index + 1}`,
      name,
      description: `${name} department responsible for ${name.toLowerCase()} operations`,
      head: `${name.split(' ')[0]} Manager`,
      headId: `head-${index + 1}`,
      employeeCount: Math.floor(Math.random() * 50) + 10,
      complianceScore: Math.floor(Math.random() * 30) + 70,
      riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      policies: [`policy-${index + 1}`, `policy-${index + 2}`],
      assessments: [`assessment-${index + 1}`],
      lastAudit: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
      nextAudit: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
      status: 'active' as const,
      contactInfo: {
        email: `${name.toLowerCase().replace(/\s+/g, '.')}@company.com`,
        phone: `******-${String(Math.floor(Math.random() * 9000) + 1000)}`,
        location: `Floor ${index + 1}, Building A`
      },
      complianceMetrics: {
        completedAssessments: Math.floor(Math.random() * 10) + 5,
        pendingAssessments: Math.floor(Math.random() * 5),
        overdueAssessments: Math.floor(Math.random() * 3),
        policyCompliance: Math.floor(Math.random() * 20) + 80,
        trainingCompletion: Math.floor(Math.random() * 25) + 75
      }
    }));
  }

  private static generateMockPolicies(): EnhancedPolicy[] {
    const policyNames = [
      'Data Privacy Policy', 'Information Security Policy', 'Employee Handbook',
      'Code of Conduct', 'Data Retention Policy', 'Incident Response Policy',
      'Access Control Policy', 'Training and Awareness Policy'
    ];

    return policyNames.map((name, index) => ({
      id: `policy-${index + 1}`,
      name,
      description: `Comprehensive ${name.toLowerCase()} for organizational compliance`,
      version: `${Math.floor(Math.random() * 3) + 1}.${Math.floor(Math.random() * 10)}`,
      status: ['active', 'draft', 'under_review'][Math.floor(Math.random() * 3)] as any,
      category: ['privacy', 'security', 'data_governance', 'compliance'][Math.floor(Math.random() * 4)] as any,
      priority: ['medium', 'high', 'critical'][Math.floor(Math.random() * 3)] as any,
      owner: `Policy Owner ${index + 1}`,
      ownerId: `owner-${index + 1}`,
      approver: `Approver ${index + 1}`,
      approverId: `approver-${index + 1}`,
      effectiveDate: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
      expiryDate: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
      lastReview: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
      nextReview: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
      applicableDepartments: [`dept-${index + 1}`, `dept-${(index + 1) % 8 + 1}`],
      relatedPolicies: [`policy-${(index + 1) % 8 + 1}`],
      complianceRequirements: ['GDPR', 'CCPA', 'SOX', 'HIPAA'].slice(0, Math.floor(Math.random() * 3) + 1),
      attachments: [`attachment-${index + 1}.pdf`],
      metrics: {
        complianceRate: Math.floor(Math.random() * 20) + 80,
        violationCount: Math.floor(Math.random() * 5),
        lastViolation: Math.random() > 0.7 ? new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000) : null,
        trainingCompletion: Math.floor(Math.random() * 25) + 75,
        acknowledgmentRate: Math.floor(Math.random() * 15) + 85
      },
      auditTrail: [{
        id: `audit-${Date.now()}-${index}`,
        action: 'created',
        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        userId: `user-${index + 1}`,
        userName: `User ${index + 1}`,
        details: 'Policy created',
        ipAddress: '***********',
        userAgent: 'Mozilla/5.0'
      }]
    }));
  }

  private static calculateMetrics(): EnhancedComplianceMetrics {
    const departments = this.departments;
    const policies = this.policies;

    // Calculate overall compliance score
    const overallScore = departments.reduce((sum, dept) => sum + dept.complianceScore, 0) / departments.length;

    // Calculate department metrics
    const departmentMetrics: any = {};
    departments.forEach(dept => {
      departmentMetrics[dept.id] = {
        score: dept.complianceScore,
        assessments: dept.assessments.length,
        policies: dept.policies.length,
        risks: dept.riskLevel === 'critical' ? 3 : dept.riskLevel === 'high' ? 2 : 1,
        trend: Math.random() > 0.5 ? 'improving' : 'stable'
      };
    });

    // Calculate policy metrics
    const activePolicies = policies.filter(p => p.status === 'active').length;
    const compliantPolicies = policies.filter(p => p.metrics.complianceRate >= 90).length;

    // Generate trend data
    const dates = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (11 - i));
      return date.toISOString().slice(0, 7);
    });

    return {
      overall: {
        score: Math.round(overallScore),
        trend: 'improving',
        lastUpdated: new Date()
      },
      departments: departmentMetrics,
      policies: {
        total: policies.length,
        active: activePolicies,
        compliant: compliantPolicies,
        nonCompliant: policies.length - compliantPolicies,
        pending: policies.filter(p => p.status === 'draft').length,
        overdue: policies.filter(p => p.nextReview < new Date()).length,
        complianceRate: Math.round((compliantPolicies / policies.length) * 100)
      },
      assessments: {
        total: departments.reduce((sum, dept) => sum + dept.complianceMetrics.completedAssessments, 0),
        completed: departments.reduce((sum, dept) => sum + dept.complianceMetrics.completedAssessments, 0),
        inProgress: departments.reduce((sum, dept) => sum + dept.complianceMetrics.pendingAssessments, 0),
        overdue: departments.reduce((sum, dept) => sum + dept.complianceMetrics.overdueAssessments, 0),
        completionRate: 85,
        averageRiskScore: 65
      },
      risks: {
        total: 45,
        low: 20,
        medium: 15,
        high: 8,
        critical: 2,
        mitigated: 35,
        open: 10
      },
      trends: {
        period: 'month',
        complianceScore: dates.map(() => Math.floor(Math.random() * 10) + 80),
        assessmentCompletion: dates.map(() => Math.floor(Math.random() * 15) + 75),
        riskReduction: dates.map(() => Math.floor(Math.random() * 20) + 60),
        policyCompliance: dates.map(() => Math.floor(Math.random() * 12) + 85),
        dates
      }
    };
  }
}
