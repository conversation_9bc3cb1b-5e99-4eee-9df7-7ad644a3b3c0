import { OrganizationalBranch, DataFlow as ComplianceDataFlow } from '../types/compliance';
import { Branch, DataFlow } from '../types/gdprTypes';

export class BranchDetectionService {
  private static branches: Branch[] = [];
  private static dataFlows: DataFlow[] = [];
  private static complianceBranches: OrganizationalBranch[] = [];
  private static complianceDataFlows: ComplianceDataFlow[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;

    try {
      this.complianceBranches = this.generateMockBranches() || [];
      this.complianceDataFlows = this.generateMockDataFlows() || [];
      this.branches = this.generateGDPRBranches() || [];
      this.dataFlows = this.generateGDPRDataFlows() || [];
      this.initialized = true;
      console.log('✅ BranchDetectionService initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing BranchDetectionService:', error);
      // Initialize with empty arrays to prevent crashes
      this.complianceBranches = [];
      this.complianceDataFlows = [];
      this.branches = [];
      this.dataFlows = [];
      this.initialized = true;
    }
  }

  // CRUD Operations for Branches (GDPR Types)
  static async getAllBranches(): Promise<Branch[]> {
    this.initialize();
    return [...this.branches];
  }

  static async getBranchById(id: string): Promise<Branch | null> {
    this.initialize();
    return this.branches.find(branch => branch.id === id) || null;
  }

  static async createBranch(branchData: Partial<Branch>): Promise<Branch> {
    this.initialize();
    const newBranch: Branch = {
      id: `branch-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      name: branchData.name || '',
      location: branchData.location || '',
      country: branchData.country || '',
      region: branchData.region || 'europe',
      complianceScore: branchData.complianceScore || 100,
      riskLevel: branchData.riskLevel || 'low',
      dataFlows: branchData.dataFlows || [],
      lastAssessment: branchData.lastAssessment || new Date(),
      contactPerson: branchData.contactPerson || '',
      regulations: branchData.regulations || []
    };

    this.branches.push(newBranch);
    return newBranch;
  }

  static async updateBranch(id: string, updates: Partial<Branch>): Promise<Branch | null> {
    this.initialize();
    const index = this.branches.findIndex(branch => branch.id === id);
    if (index === -1) return null;

    this.branches[index] = { ...this.branches[index], ...updates };
    return this.branches[index];
  }

  static async deleteBranch(id: string): Promise<boolean> {
    this.initialize();
    const index = this.branches.findIndex(branch => branch.id === id);
    if (index === -1) return false;

    this.branches.splice(index, 1);
    return true;
  }

  // Data Flow Operations
  static async getAllDataFlows(): Promise<DataFlow[]> {
    this.initialize();
    return [...this.dataFlows];
  }

  static async getDataFlowsByBranch(branchId: string): Promise<DataFlow[]> {
    this.initialize();
    return this.dataFlows.filter(flow => flow.fromBranch === branchId || flow.toBranch === branchId);
  }

  static async createDataFlow(flowData: Partial<DataFlow>): Promise<DataFlow> {
    this.initialize();
    const newFlow: DataFlow = {
      id: `flow-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      fromBranch: flowData.fromBranch || '',
      toBranch: flowData.toBranch || '',
      dataType: flowData.dataType || 'personal_data',
      volume: flowData.volume || 0,
      frequency: flowData.frequency || 'monthly',
      purpose: flowData.purpose || '',
      legalBasis: flowData.legalBasis || 'consent',
      safeguards: flowData.safeguards || [],
      riskScore: flowData.riskScore || 1,
      lastReview: flowData.lastReview || new Date()
    };

    this.dataFlows.push(newFlow);
    return newFlow;
  }

  static async updateDataFlow(id: string, updates: Partial<DataFlow>): Promise<DataFlow | null> {
    this.initialize();
    const index = this.dataFlows.findIndex(flow => flow.id === id);
    if (index === -1) return null;

    this.dataFlows[index] = { ...this.dataFlows[index], ...updates };
    return this.dataFlows[index];
  }

  static async deleteDataFlow(id: string): Promise<boolean> {
    this.initialize();
    const index = this.dataFlows.findIndex(flow => flow.id === id);
    if (index === -1) return false;

    this.dataFlows.splice(index, 1);
    return true;
  }

  // Analytics and Reporting
  static async getBranchMetrics(): Promise<{
    totalBranches: number;
    byRegion: Record<string, number>;
    byRiskLevel: Record<string, number>;
    totalDataFlows: number;
    crossBorderTransfers: number;
    complianceScore: number;
  }> {
    this.initialize();

    // Add null checks to prevent "Cannot convert undefined or null to object" errors
    const branches = this.branches || [];
    const dataFlows = this.dataFlows || [];

    const byRegion = branches.reduce((acc, branch) => {
      if (branch && branch.region) {
        acc[branch.region] = (acc[branch.region] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const byRiskLevel = branches.reduce((acc, branch) => {
      if (branch && branch.riskLevel) {
        acc[branch.riskLevel] = (acc[branch.riskLevel] || 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

    const crossBorderTransfers = dataFlows.filter(flow => {
      if (!flow || !flow.fromBranch || !flow.toBranch) return false;
      const source = branches.find(b => b && b.id === flow.fromBranch);
      const destination = branches.find(b => b && b.id === flow.toBranch);
      return source && destination && source.country !== destination.country;
    }).length;

    const avgComplianceScore = branches.length > 0
      ? branches.reduce((sum, branch) => sum + (branch?.complianceScore || 0), 0) / branches.length
      : 0;

    return {
      totalBranches: branches.length,
      byRegion,
      byRiskLevel,
      totalDataFlows: dataFlows.length,
      crossBorderTransfers,
      complianceScore: avgComplianceScore
    };
  }

  // Risk Assessment
  static async assessBranchRisk(branchId: string): Promise<{
    overallRisk: 'low' | 'medium' | 'high' | 'critical';
    riskFactors: string[];
    recommendations: string[];
    score: number;
  }> {
    const branch = await this.getBranchById(branchId);
    if (!branch) throw new Error('Branch not found');

    const riskFactors: string[] = [];
    const recommendations: string[] = [];
    let score = 0;

    // Assess compliance score
    if (branch.complianceScore < 80) {
      riskFactors.push('Low compliance score');
      recommendations.push('Improve compliance measures and processes');
      score += 30;
    }

    // Assess current risk level
    if (branch.riskLevel === 'high' || branch.riskLevel === 'critical') {
      riskFactors.push('High inherent risk level');
      recommendations.push('Implement additional risk mitigation measures');
      score += 25;
    }

    // Assess data flows
    const branchFlows = await this.getDataFlowsByBranch(branchId);
    const highRiskFlows = branchFlows.filter(flow => flow.riskScore >= 7);
    if (highRiskFlows.length > 0) {
      riskFactors.push(`${highRiskFlows.length} high-risk data flows`);
      recommendations.push('Review and mitigate high-risk data transfers');
      score += 25;
    }

    // Assess cross-border transfers
    const crossBorderFlows = branchFlows.filter(flow => {
      const destination = this.branches.find(b => b.id === flow.toBranch);
      return destination && destination.country !== branch.country;
    });
    if (crossBorderFlows.length > 0) {
      riskFactors.push(`${crossBorderFlows.length} cross-border data transfers`);
      recommendations.push('Ensure adequate safeguards for international transfers');
      score += 15;
    }

    // Assess last assessment date
    const daysSinceAssessment = Math.floor((Date.now() - branch.lastAssessment.getTime()) / (1000 * 60 * 60 * 24));
    if (daysSinceAssessment > 90) {
      riskFactors.push('Assessment overdue');
      recommendations.push('Conduct updated compliance assessment');
      score += 10;
    }

    // Determine overall risk level
    let overallRisk: 'low' | 'medium' | 'high' | 'critical';
    if (score >= 70) overallRisk = 'critical';
    else if (score >= 50) overallRisk = 'high';
    else if (score >= 30) overallRisk = 'medium';
    else overallRisk = 'low';

    return {
      overallRisk,
      riskFactors,
      recommendations,
      score
    };
  }

  // Private helper methods
  private static generateMockBranches(): OrganizationalBranch[] {
    return [
      {
        id: 'branch-001',
        name: 'European Headquarters',
        type: 'headquarters',
        location: {
          country: 'Germany',
          region: 'Bavaria',
          city: 'Munich',
          address: 'Maximilianstrasse 35, 80539 Munich',
          coordinates: { lat: 48.1351, lng: 11.5820 }
        },
        jurisdiction: {
          dataProtectionLaw: ['GDPR', 'BDSG'],
          supervisoryAuthority: 'BfDI (Federal Commissioner for Data Protection and Freedom of Information)',
          adequacyDecision: true,
          transferMechanisms: ['Adequacy Decision', 'Standard Contractual Clauses']
        },
        dataProcessing: {
          categories: [
            {
              id: 'cat-001',
              name: 'Customer Data',
              description: 'Personal information of customers',
              sensitivity: 'confidential',
              personalDataTypes: ['Name', 'Email', 'Phone', 'Address'],
              specialCategories: false,
              dataSubjects: ['Customers', 'Prospects'],
              volume: { estimated: 500000, unit: 'records' }
            }
          ],
          purposes: [
            {
              id: 'purpose-001',
              name: 'Customer Service',
              description: 'Providing customer support and service',
              legalBasis: 'Article 6(1)(b) - Contract',
              dataMinimization: true,
              automated: false,
              profiling: false,
              dataRetention: { period: 7, unit: 'years', criteria: 'Legal requirement' }
            }
          ],
          legalBases: ['Article 6(1)(a) - Consent', 'Article 6(1)(b) - Contract', 'Article 6(1)(f) - Legitimate Interest'],
          retentionPeriods: []
        },
        complianceStatus: {
          overall: 'compliant',
          lastAssessment: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          nextAssessment: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000),
          riskLevel: 'low',
          openIssues: 2,
          resolvedIssues: 15
        },
        dataFlows: [],
        contacts: {
          dpo: {
            id: 'dpo-001',
            name: 'Dr. Maria Schmidt',
            title: 'Data Protection Officer',
            email: '<EMAIL>',
            phone: '+49 89 1234567',
            department: 'Legal & Compliance',
            responsibilities: ['GDPR Compliance', 'Privacy Impact Assessments', 'Data Subject Rights']
          },
          technicalContact: {
            id: 'tech-001',
            name: 'Hans Mueller',
            title: 'IT Security Manager',
            email: '<EMAIL>',
            phone: '+49 89 1234568',
            department: 'IT Security',
            responsibilities: ['Technical Security', 'Data Encryption', 'Access Controls']
          },
          businessContact: {
            id: 'biz-001',
            name: 'Anna Weber',
            title: 'Operations Director',
            email: '<EMAIL>',
            phone: '+49 89 1234569',
            department: 'Operations',
            responsibilities: ['Business Operations', 'Process Management', 'Vendor Relations']
          }
        },
        certifications: [
          {
            id: 'cert-001',
            name: 'ISO 27001',
            issuer: 'TÜV SÜD',
            issuedDate: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
            expiryDate: new Date(Date.now() + 2 * 365 * 24 * 60 * 60 * 1000),
            status: 'active',
            scope: ['Information Security Management'],
            certificateNumber: 'ISO27001-2023-001'
          }
        ],
        auditHistory: []
      },
      {
        id: 'branch-002',
        name: 'North American Operations',
        type: 'subsidiary',
        location: {
          country: 'United States',
          region: 'California',
          city: 'San Francisco',
          address: '123 Market Street, San Francisco, CA 94105',
          coordinates: { lat: 37.7749, lng: -122.4194 }
        },
        jurisdiction: {
          dataProtectionLaw: ['CCPA', 'CPRA', 'Federal Privacy Laws'],
          supervisoryAuthority: 'California Attorney General',
          adequacyDecision: false,
          transferMechanisms: ['Standard Contractual Clauses', 'Binding Corporate Rules']
        },
        dataProcessing: {
          categories: [
            {
              id: 'cat-002',
              name: 'Employee Data',
              description: 'HR and employment information',
              sensitivity: 'confidential',
              personalDataTypes: ['Name', 'SSN', 'Salary', 'Performance'],
              specialCategories: false,
              dataSubjects: ['Employees', 'Contractors'],
              volume: { estimated: 5000, unit: 'records' }
            }
          ],
          purposes: [
            {
              id: 'purpose-002',
              name: 'Human Resources',
              description: 'Employee management and HR processes',
              legalBasis: 'Employment Contract',
              dataMinimization: true,
              automated: true,
              profiling: false,
              dataRetention: { period: 7, unit: 'years', criteria: 'Legal requirement' }
            }
          ],
          legalBases: ['Employment Contract', 'Legal Obligation', 'Legitimate Interest'],
          retentionPeriods: []
        },
        complianceStatus: {
          overall: 'partially_compliant',
          lastAssessment: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000),
          nextAssessment: new Date(Date.now() + 45 * 24 * 60 * 60 * 1000),
          riskLevel: 'medium',
          openIssues: 8,
          resolvedIssues: 12
        },
        dataFlows: [],
        contacts: {
          technicalContact: {
            id: 'tech-002',
            name: 'John Smith',
            title: 'Chief Technology Officer',
            email: '<EMAIL>',
            phone: '****** 555 0123',
            department: 'Technology',
            responsibilities: ['Technology Strategy', 'Data Architecture', 'Security']
          },
          businessContact: {
            id: 'biz-002',
            name: 'Sarah Johnson',
            title: 'General Manager',
            email: '<EMAIL>',
            phone: '****** 555 0124',
            department: 'Management',
            responsibilities: ['Business Operations', 'Compliance', 'Strategy']
          }
        },
        certifications: [],
        auditHistory: []
      }
    ];
  }

  private static generateMockDataFlows(): ComplianceDataFlow[] {
    return [
      {
        id: 'flow-001',
        name: 'Customer Data Sync',
        description: 'Synchronization of customer data between EU and US operations',
        sourceId: 'branch-001',
        destinationId: 'branch-002',
        dataCategories: ['Customer Data', 'Transaction History'],
        transferMechanism: 'sccs',
        frequency: 'daily',
        volume: { estimated: 10000, unit: 'records' },
        encryption: { inTransit: true, atRest: true, algorithm: 'AES-256' },
        monitoring: { enabled: true, alertThreshold: 50000, lastMonitored: new Date() },
        riskAssessment: {
          riskLevel: 'medium',
          identifiedRisks: ['Cross-border transfer without adequacy decision', 'Large data volume'],
          mitigationMeasures: ['Standard Contractual Clauses implemented', 'End-to-end encryption'],
          lastAssessed: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
        }
      }
    ];
  }

  // Generate GDPR-compatible mock data
  private static generateGDPRBranches(): Branch[] {
    return [
      {
        id: 'branch-london-001',
        name: 'London Headquarters',
        location: '123 Business Street, London',
        country: 'United Kingdom',
        region: 'europe',
        complianceScore: 95,
        riskLevel: 'low',
        dataFlows: [],
        lastAssessment: new Date('2024-01-15'),
        contactPerson: 'Sarah Johnson',
        regulations: ['GDPR', 'UK GDPR', 'DPA 2018']
      },
      {
        id: 'branch-paris-002',
        name: 'Paris Office',
        location: '456 Rue de Commerce, Paris',
        country: 'France',
        region: 'europe',
        complianceScore: 88,
        riskLevel: 'medium',
        dataFlows: [],
        lastAssessment: new Date('2024-01-10'),
        contactPerson: 'Pierre Dubois',
        regulations: ['GDPR', 'French Data Protection Act']
      },
      {
        id: 'branch-newyork-003',
        name: 'New York Branch',
        location: '789 Wall Street, New York',
        country: 'United States',
        region: 'north_america',
        complianceScore: 82,
        riskLevel: 'medium',
        dataFlows: [],
        lastAssessment: new Date('2024-01-05'),
        contactPerson: 'Michael Chen',
        regulations: ['CCPA', 'CPRA', 'NYDFS Cybersecurity']
      },
      {
        id: 'branch-tokyo-004',
        name: 'Tokyo Office',
        location: '321 Shibuya District, Tokyo',
        country: 'Japan',
        region: 'asia_pacific',
        complianceScore: 78,
        riskLevel: 'high',
        dataFlows: [],
        lastAssessment: new Date('2023-12-20'),
        contactPerson: 'Yuki Tanaka',
        regulations: ['APPI', 'Cybersecurity Basic Act']
      }
    ];
  }

  private static generateGDPRDataFlows(): DataFlow[] {
    return [
      {
        id: 'flow-001',
        fromBranch: 'branch-london-001',
        toBranch: 'branch-paris-002',
        dataType: 'personal_data',
        volume: 15000,
        frequency: 'daily',
        purpose: 'Customer service and support operations',
        legalBasis: 'legitimate_interests',
        safeguards: ['Encryption in transit', 'Access controls', 'Data minimization'],
        riskScore: 3,
        lastReview: new Date('2024-01-15')
      },
      {
        id: 'flow-002',
        fromBranch: 'branch-london-001',
        toBranch: 'branch-newyork-003',
        dataType: 'financial_data',
        volume: 8500,
        frequency: 'weekly',
        purpose: 'Financial reporting and compliance',
        legalBasis: 'legal_obligation',
        safeguards: ['End-to-end encryption', 'Multi-factor authentication', 'Audit logging'],
        riskScore: 6,
        lastReview: new Date('2024-01-10')
      },
      {
        id: 'flow-003',
        fromBranch: 'branch-paris-002',
        toBranch: 'branch-tokyo-004',
        dataType: 'sensitive_data',
        volume: 2300,
        frequency: 'monthly',
        purpose: 'HR management and payroll processing',
        legalBasis: 'consent',
        safeguards: ['Pseudonymization', 'Role-based access', 'Regular security assessments'],
        riskScore: 7,
        lastReview: new Date('2024-01-08')
      }
    ];
  }
}
