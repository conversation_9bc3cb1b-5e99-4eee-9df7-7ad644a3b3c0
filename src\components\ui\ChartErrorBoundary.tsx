/**
 * Chart Error Boundary Component
 * Specialized error boundary for chart components with fallback UI
 */

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { BarChart3, AlertTriangle, RefreshCw, TrendingUp } from 'lucide-react';

interface ChartErrorBoundaryProps {
  children: ReactNode;
  chartType?: 'bar' | 'line' | 'doughnut' | 'radar' | 'pie' | 'area';
  chartTitle?: string;
  fallbackHeight?: string;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  onRetry?: () => void;
  className?: string;
}

interface ChartErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  retryCount: number;
  isRetrying: boolean;
}

export class ChartErrorBoundary extends Component<ChartErrorBoundaryProps, ChartErrorBoundaryState> {
  private maxRetries = 3;
  private retryTimeoutId: NodeJS.Timeout | null = null;

  constructor(props: ChartErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      retryCount: 0,
      isRetrying: false,
    };
  }

  static getDerivedStateFromError(error: Error): Partial<ChartErrorBoundaryState> {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Chart Error Boundary caught an error:', error, errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });

    // Call the onError callback if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Log error details for debugging
    console.group('📊 Chart Error Details');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    console.error('Component Stack:', errorInfo.componentStack);
    console.error('Chart Type:', this.props.chartType);
    console.error('Chart Title:', this.props.chartTitle);
    console.groupEnd();
  }

  componentWillUnmount() {
    if (this.retryTimeoutId) {
      clearTimeout(this.retryTimeoutId);
    }
  }

  handleRetry = () => {
    if (this.state.retryCount >= this.maxRetries || this.state.isRetrying) {
      return;
    }

    this.setState({ isRetrying: true });

    // Call the onRetry callback if provided
    if (this.props.onRetry) {
      this.props.onRetry();
    }

    // Reset error state after a short delay
    this.retryTimeoutId = setTimeout(() => {
      this.setState({
        hasError: false,
        error: null,
        errorInfo: null,
        retryCount: this.state.retryCount + 1,
        isRetrying: false,
      });
    }, 1000);
  };

  getChartIcon = () => {
    switch (this.props.chartType) {
      case 'bar':
        return <BarChart3 className="w-8 h-8 text-gray-400" />;
      case 'line':
      case 'area':
        return <TrendingUp className="w-8 h-8 text-gray-400" />;
      case 'doughnut':
      case 'pie':
        return <div className="w-8 h-8 border-4 border-gray-400 border-dashed rounded-full" />;
      case 'radar':
        return <div className="w-8 h-8 border-2 border-gray-400 border-dashed rounded-full relative">
          <div className="absolute inset-2 border border-gray-400 border-dashed rounded-full" />
        </div>;
      default:
        return <BarChart3 className="w-8 h-8 text-gray-400" />;
    }
  };

  render() {
    if (this.state.hasError) {
      const { chartTitle, fallbackHeight = 'h-64', className = '' } = this.props;
      const canRetry = this.state.retryCount < this.maxRetries;

      return (
        <div className={`${fallbackHeight} flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700 ${className}`}>
          <div className="text-center p-6">
            <div className="mb-4">
              {this.getChartIcon()}
            </div>
            
            <div className="mb-2">
              <AlertTriangle className="w-5 h-5 text-yellow-500 mx-auto mb-2" />
              <h3 className="text-sm font-semibold text-gray-700 dark:text-gray-300">
                Chart Error
              </h3>
            </div>
            
            <p className="text-xs text-gray-500 dark:text-gray-400 mb-4 max-w-xs">
              {chartTitle ? `The "${chartTitle}" chart` : 'This chart'} could not be rendered due to a technical issue.
            </p>

            <div className="space-y-2">
              {canRetry && (
                <button
                  onClick={this.handleRetry}
                  disabled={this.state.isRetrying}
                  className="inline-flex items-center gap-2 px-3 py-1.5 text-xs bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded transition-colors"
                >
                  <RefreshCw className={`w-3 h-3 ${this.state.isRetrying ? 'animate-spin' : ''}`} />
                  {this.state.isRetrying ? 'Retrying...' : `Retry (${this.maxRetries - this.state.retryCount} left)`}
                </button>
              )}
              
              {!canRetry && (
                <div className="text-xs text-gray-400">
                  Max retries reached. Please refresh the page.
                </div>
              )}
            </div>

            {/* Error details for development */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <details className="mt-4 text-left">
                <summary className="text-xs text-gray-500 cursor-pointer hover:text-gray-700">
                  Error Details (Dev Only)
                </summary>
                <div className="mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded text-xs text-red-700 dark:text-red-300 font-mono">
                  <div className="mb-1">
                    <strong>Error:</strong> {this.state.error.message}
                  </div>
                  {this.state.error.stack && (
                    <div className="text-xs text-red-600 dark:text-red-400 whitespace-pre-wrap">
                      {this.state.error.stack.split('\n').slice(0, 3).join('\n')}
                    </div>
                  )}
                </div>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook for using chart error boundary programmatically
export const useChartErrorBoundary = () => {
  const [error, setError] = React.useState<Error | null>(null);

  const resetError = React.useCallback(() => {
    setError(null);
  }, []);

  const captureError = React.useCallback((error: Error) => {
    console.error('Chart error captured:', error);
    setError(error);
  }, []);

  return {
    error,
    resetError,
    captureError,
    hasError: error !== null,
  };
};

// Higher-order component for wrapping charts with error boundary
export const withChartErrorBoundary = <P extends object>(
  WrappedComponent: React.ComponentType<P>,
  chartType?: ChartErrorBoundaryProps['chartType'],
  chartTitle?: string
) => {
  const WithChartErrorBoundary = (props: P) => (
    <ChartErrorBoundary chartType={chartType} chartTitle={chartTitle}>
      <WrappedComponent {...props} />
    </ChartErrorBoundary>
  );

  WithChartErrorBoundary.displayName = `withChartErrorBoundary(${WrappedComponent.displayName || WrappedComponent.name})`;
  
  return WithChartErrorBoundary;
};

export default ChartErrorBoundary;
