import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, render } from '@testing-library/react';
import { ErrorBoundary } from '../../components/ui/ErrorBoundary';

// Component that throws an error
const ThrowError = ({ shouldThrow }: { shouldThrow: boolean }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>No error</div>;
};

// Component that throws an error during render
const AlwaysThrowsError = () => {
  throw new Error('Component render error');
};

describe('Error Boundary Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Suppress console.error for these tests
    vi.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('renders children when there is no error', () => {
    render(
      <ErrorBoundary fallback={<div>Error occurred</div>}>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    expect(screen.getByText('No error')).toBeInTheDocument();
    expect(screen.queryByText('Error occurred')).not.toBeInTheDocument();
  });

  it('renders fallback UI when there is an error', () => {
    render(
      <ErrorBoundary fallback={<div>Error occurred</div>}>
        <AlwaysThrowsError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Error occurred')).toBeInTheDocument();
    expect(screen.queryByText('No error')).not.toBeInTheDocument();
  });

  it('renders custom fallback with error details', () => {
    const customFallback = (
      <div>
        <h2>Something went wrong</h2>
        <p>Please refresh the page</p>
      </div>
    );

    render(
      <ErrorBoundary fallback={customFallback}>
        <AlwaysThrowsError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
    expect(screen.getByText('Please refresh the page')).toBeInTheDocument();
  });

  it('calls onError callback when error occurs', () => {
    const onErrorMock = vi.fn();

    render(
      <ErrorBoundary 
        fallback={<div>Error occurred</div>}
        onError={onErrorMock}
      >
        <AlwaysThrowsError />
      </ErrorBoundary>
    );

    expect(onErrorMock).toHaveBeenCalledTimes(1);
    expect(onErrorMock).toHaveBeenCalledWith(
      expect.any(Error),
      expect.objectContaining({
        componentStack: expect.any(String)
      })
    );
  });

  it('renders section-specific error boundary', () => {
    render(
      <ErrorBoundary 
        type="section"
        fallbackTitle="Section Error"
        fallbackMessage="This section failed to load"
      >
        <AlwaysThrowsError />
      </ErrorBoundary>
    );

    expect(screen.getByText('Section Error')).toBeInTheDocument();
    expect(screen.getByText('This section failed to load')).toBeInTheDocument();
  });

  it('handles multiple error boundaries independently', () => {
    render(
      <div>
        <ErrorBoundary fallback={<div>Error 1</div>}>
          <AlwaysThrowsError />
        </ErrorBoundary>
        <ErrorBoundary fallback={<div>Error 2</div>}>
          <ThrowError shouldThrow={false} />
        </ErrorBoundary>
      </div>
    );

    expect(screen.getByText('Error 1')).toBeInTheDocument();
    expect(screen.getByText('No error')).toBeInTheDocument();
    expect(screen.queryByText('Error 2')).not.toBeInTheDocument();
  });

  it('recovers when error is fixed', () => {
    const { rerender } = render(
      <ErrorBoundary fallback={<div>Error occurred</div>}>
        <ThrowError shouldThrow={true} />
      </ErrorBoundary>
    );

    expect(screen.getByText('Error occurred')).toBeInTheDocument();

    // Re-render with no error
    rerender(
      <ErrorBoundary fallback={<div>Error occurred</div>}>
        <ThrowError shouldThrow={false} />
      </ErrorBoundary>
    );

    // Error boundary should still show error state until reset
    expect(screen.getByText('Error occurred')).toBeInTheDocument();
  });
});
