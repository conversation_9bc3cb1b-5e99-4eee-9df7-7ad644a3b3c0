import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { RiskAssessment } from '../../types/gdprTypes';
import { RiskAssessmentService } from '../../services/riskAssessmentService';
import { RiskAssessmentModal } from './RiskAssessmentModal';
import { ConfirmationModal } from '../ui/Modal';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button, IconButton } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import {
  Shield,
  AlertTriangle,
  TrendingUp,
  Plus,
  Search,
  Edit,
  Trash2,
  Calendar,
  User,
  Tag,
  CheckSquare,
  Square,
  X
} from 'lucide-react';

interface RiskAssessmentDashboardProps {
  className?: string;
}

export const RiskAssessmentDashboard: React.FC<RiskAssessmentDashboardProps> = React.memo(({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  const [riskAssessments, setRiskAssessments] = useState<RiskAssessment[]>([]);
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  // Modal states
  const [showAssessmentModal, setShowAssessmentModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [editingAssessment, setEditingAssessment] = useState<RiskAssessment | null>(null);
  const [viewingAssessment, setViewingAssessment] = useState<RiskAssessment | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingAssessment, setDeletingAssessment] = useState<RiskAssessment | null>(null);

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [riskLevelFilter, setRiskLevelFilter] = useState('all');
  const [selectedAssessments, setSelectedAssessments] = useState<string[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [assessmentsData, metricsData] = await Promise.all([
        RiskAssessmentService.getAllRiskAssessments(),
        RiskAssessmentService.getRiskMetrics()
      ]);

      setRiskAssessments(assessmentsData);
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading risk assessment data:', error);
      notifications.error('Failed to load risk assessment data');
    } finally {
      setLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateAssessment = () => {
    setEditingAssessment(null);
    setShowAssessmentModal(true);
  };

  const handleEditAssessment = (assessment: RiskAssessment) => {
    setEditingAssessment(assessment);
    setShowAssessmentModal(true);
  };

  const handleViewAssessment = (assessment: RiskAssessment) => {
    setViewingAssessment(assessment);
    setShowViewModal(true);
  };

  const handleSaveAssessment = async (assessmentData: Partial<RiskAssessment>) => {
    try {
      if (editingAssessment) {
        // Update existing assessment
        const updatedAssessment = await RiskAssessmentService.updateRiskAssessment(editingAssessment.id, assessmentData);
        if (updatedAssessment) {
          setRiskAssessments(prev => prev.map(a => a.id === editingAssessment.id ? updatedAssessment : a));
          notifications.success('Risk assessment updated successfully');
        }
      } else {
        // Create new assessment
        const newAssessment = await RiskAssessmentService.createRiskAssessment(assessmentData);
        setRiskAssessments(prev => [newAssessment, ...prev]);
        notifications.success('Risk assessment created successfully');
      }

      // Refresh metrics and matrix
      const updatedMetrics = await RiskAssessmentService.getRiskMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error saving risk assessment:', error);
      notifications.error('Failed to save risk assessment');
      throw error;
    }
  };

  const handleDeleteAssessment = (assessment: RiskAssessment) => {
    setDeletingAssessment(assessment);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!deletingAssessment) return;

    try {
      await RiskAssessmentService.deleteRiskAssessment(deletingAssessment.id);
      setRiskAssessments(prev => prev.filter(a => a.id !== deletingAssessment.id));
      setSelectedAssessments(prev => prev.filter(id => id !== deletingAssessment.id));
      notifications.success('Risk assessment deleted successfully');

      // Refresh metrics and matrix
      const updatedMetrics = await RiskAssessmentService.getRiskMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error deleting risk assessment:', error);
      notifications.error('Failed to delete risk assessment');
    } finally {
      setShowDeleteConfirm(false);
      setDeletingAssessment(null);
    }
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    if (selectedAssessments.length === 0) return;

    try {
      await Promise.all(selectedAssessments.map(id => RiskAssessmentService.deleteRiskAssessment(id)));
      setRiskAssessments(prev => prev.filter(a => !selectedAssessments.includes(a.id)));
      setSelectedAssessments([]);
      notifications.success(`${selectedAssessments.length} risk assessments deleted successfully`);

      // Refresh metrics and matrix
      const updatedMetrics = await RiskAssessmentService.getRiskMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error bulk deleting assessments:', error);
      notifications.error('Failed to delete selected assessments');
    }
  };

  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedAssessments.length === 0) return;

    try {
      const updates = selectedAssessments.map(id =>
        RiskAssessmentService.updateRiskAssessment(id, { status: status as any })
      );
      const updatedAssessments = await Promise.all(updates);

      setRiskAssessments(prev => prev.map(a => {
        const updated = updatedAssessments.find(u => u && u.id === a.id);
        return updated || a;
      }));

      setSelectedAssessments([]);
      notifications.success(`${selectedAssessments.length} assessments updated successfully`);

      // Refresh metrics
      const updatedMetrics = await RiskAssessmentService.getRiskMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error bulk updating assessments:', error);
      notifications.error('Failed to update selected assessments');
    }
  };

  // Selection handlers
  const handleSelectAssessment = (assessmentId: string) => {
    setSelectedAssessments(prev =>
      prev.includes(assessmentId)
        ? prev.filter(id => id !== assessmentId)
        : [...prev, assessmentId]
    );
  };

  const handleSelectAll = () => {
    const filteredAssessments = getFilteredAssessments();
    const allSelected = filteredAssessments.every(a => selectedAssessments.includes(a.id));
    
    if (allSelected) {
      setSelectedAssessments(prev => prev.filter(id => !filteredAssessments.some(a => a.id === id)));
    } else {
      const newSelections = filteredAssessments.map(a => a.id);
      setSelectedAssessments(prev => [...new Set([...prev, ...newSelections])]);
    }
  };

  // Filtering
  const getFilteredAssessments = () => {
    return riskAssessments.filter(assessment => {
      const matchesSearch = !searchTerm || 
        assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.owner.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesCategory = categoryFilter === 'all' || assessment.category === categoryFilter;
      const matchesStatus = statusFilter === 'all' || assessment.status === statusFilter;
      const matchesRiskLevel = riskLevelFilter === 'all' || assessment.riskLevel === riskLevelFilter;

      return matchesSearch && matchesCategory && matchesStatus && matchesRiskLevel;
    });
  };

  // Utility functions
  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'very_high': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'under_review': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      case 'rejected': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'archived': return 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };





  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center h-64`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const filteredAssessments = getFilteredAssessments();
  const allSelected = filteredAssessments.length > 0 && filteredAssessments.every(a => selectedAssessments.includes(a.id));
  const someSelected = selectedAssessments.length > 0;

  return (
    <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Risk Assessment Dashboard</h2>
            <p className="text-text-secondary">Identify, assess, and manage organizational risks</p>
          </div>
        </div>
        <Button
          onClick={handleCreateAssessment}
          leftIcon={<Plus className="w-4 h-4" />}
        >
          Create Assessment
        </Button>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-primary/20 transition-all duration-200`}
            onClick={() => {
              // Filter to show all assessments
              setSearchTerm('');
              setCategoryFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Total Assessments</p>
                <p className="text-2xl font-bold text-text">{metrics.totalRisks}</p>
              </div>
              <Shield className="w-8 h-8 text-primary" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-orange-500/20 transition-all duration-200`}
            onClick={() => {
              // Filter to show high risk assessments
              setSearchTerm('');
              setCategoryFilter('all');
              setStatusFilter('all');
              // Additional filtering logic for high risk could be added here
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">High Risk</p>
                <p className="text-2xl font-bold text-orange-500">{metrics.byRiskLevel.high || 0}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-green-500/20 transition-all duration-200`}
            onClick={() => {
              // Show high risk assessments
              setSearchTerm('');
              setCategoryFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">High Risk Items</p>
                <p className="text-2xl font-bold text-text">{metrics.byRiskLevel.high || 0}</p>
              </div>
              <TrendingUp className="w-8 h-8 text-orange-500" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-red-500/20 transition-all duration-200`}
            onClick={() => {
              // Filter to show overdue assessments
              setSearchTerm('');
              setCategoryFilter('all');
              setStatusFilter('all');
              // Additional filtering logic for overdue assessments could be added here
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Overdue Reviews</p>
                <p className="text-2xl font-bold text-red-500">{metrics.overdue}</p>
              </div>
              <Calendar className="w-8 h-8 text-red-500" />
            </div>
          </div>
        </div>
      )}

      {/* Search and Filters */}
      <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
            <FormInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search assessments..."
              className="pl-10"
            />
          </div>

          <FormSelect
            value={categoryFilter}
            onChange={(e) => setCategoryFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Categories' },
              { value: 'operational', label: 'Operational' },
              { value: 'financial', label: 'Financial' },
              { value: 'compliance', label: 'Compliance' },
              { value: 'reputational', label: 'Reputational' },
              { value: 'strategic', label: 'Strategic' },
              { value: 'technology', label: 'Technology' },
              { value: 'legal', label: 'Legal' },
              { value: 'environmental', label: 'Environmental' }
            ]}
          />

          <FormSelect
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'draft', label: 'Draft' },
              { value: 'under_review', label: 'Under Review' },
              { value: 'approved', label: 'Approved' },
              { value: 'rejected', label: 'Rejected' },
              { value: 'archived', label: 'Archived' }
            ]}
          />

          <FormSelect
            value={riskLevelFilter}
            onChange={(e) => setRiskLevelFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Risk Levels' },
              { value: 'low', label: 'Low Risk' },
              { value: 'medium', label: 'Medium Risk' },
              { value: 'high', label: 'High Risk' },
              { value: 'very_high', label: 'Very High Risk' }
            ]}
          />
        </div>

        {/* Bulk Actions */}
        {someSelected && (
          <div className="flex items-center gap-3 mb-4 p-3 bg-primary/10 rounded-lg">
            <span className="text-sm text-primary font-medium">
              {selectedAssessments.length} assessment{selectedAssessments.length !== 1 ? 's' : ''} selected
            </span>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkStatusUpdate('approved')}
            >
              Approve Selected
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleBulkStatusUpdate('archived')}
            >
              Archive Selected
            </Button>
            <IconButton
              size="sm"
              variant="outline"
              onClick={handleBulkDelete}
              className="text-red-600 hover:text-red-700"
              icon={<Trash2 className="w-4 h-4" />}
              ariaLabel="Delete selected assessments"
            />
          </div>
        )}

        {/* Assessment Table */}
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-border">
                <th className="text-left py-3 px-4">
                  <button
                    onClick={handleSelectAll}
                    className="flex items-center gap-2"
                  >
                    {allSelected ? (
                      <CheckSquare className="w-4 h-4 text-primary" />
                    ) : (
                      <Square className="w-4 h-4 text-text-secondary" />
                    )}
                  </button>
                </th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Title</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Category</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Risk Level</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Status</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Owner</th>
                <th className="text-left py-3 px-4 text-sm font-medium text-text-secondary">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredAssessments.map((assessment) => (
                <tr
                  key={assessment.id}
                  className="border-b border-border hover:bg-surface/50 cursor-pointer transition-colors duration-150"
                  onClick={() => handleViewAssessment(assessment)}
                >
                  <td className="py-3 px-4">
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSelectAssessment(assessment.id);
                      }}
                      className="flex items-center gap-2"
                    >
                      {selectedAssessments.includes(assessment.id) ? (
                        <CheckSquare className="w-4 h-4 text-primary" />
                      ) : (
                        <Square className="w-4 h-4 text-text-secondary" />
                      )}
                    </button>
                  </td>
                  <td className="py-3 px-4">
                    <div>
                      <p className="font-medium text-text">{assessment.title}</p>
                      <p className="text-sm text-text-secondary truncate max-w-xs">
                        {assessment.description}
                      </p>
                    </div>
                  </td>
                  <td className="py-3 px-4">
                    <span className="capitalize text-sm text-text">
                      {assessment.category.replace('_', ' ')}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(assessment.riskLevel)}`}>
                      {assessment.riskLevel.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
                      {assessment.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="py-3 px-4">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-text-secondary" />
                      <span className="text-sm text-text">{assessment.owner}</span>
                    </div>
                  </td>
                  <td className="py-3 px-4" onClick={(e) => e.stopPropagation()}>
                    <div className="flex items-center gap-2">
                      <IconButton
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEditAssessment(assessment)}
                        icon={<Edit className="w-4 h-4" />}
                        ariaLabel="Edit assessment"
                      />
                      <IconButton
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteAssessment(assessment)}
                        className="text-red-600 hover:text-red-700"
                        icon={<Trash2 className="w-4 h-4" />}
                        ariaLabel="Delete assessment"
                      />
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          {filteredAssessments.length === 0 && (
            <div className="text-center py-12">
              <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text mb-2">No risk assessments found</h3>
              <p className="text-text-secondary mb-4">
                {searchTerm || categoryFilter !== 'all' || statusFilter !== 'all' || riskLevelFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first risk assessment to get started'
                }
              </p>
              {!searchTerm && categoryFilter === 'all' && statusFilter === 'all' && riskLevelFilter === 'all' && (
                <Button onClick={handleCreateAssessment} leftIcon={<Plus className="w-4 h-4" />}>
                  Create First Assessment
                </Button>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Modals */}
      <RiskAssessmentModal
        isOpen={showAssessmentModal}
        onClose={() => setShowAssessmentModal(false)}
        onSave={handleSaveAssessment}
        assessment={editingAssessment}
        mode={editingAssessment ? 'edit' : 'create'}
      />

      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
        title="Delete Risk Assessment"
        message={`Are you sure you want to delete "${deletingAssessment?.title}"? This action cannot be undone.`}
        confirmText="Delete"
        type="danger"
      />

      {/* View Details Modal */}
      {viewingAssessment && (
        <div className={`fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 ${showViewModal ? '' : 'hidden'}`}>
          <div className="bg-surface rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-semibold text-text">Risk Assessment Details</h3>
              <button
                onClick={() => setShowViewModal(false)}
                className="p-2 hover:bg-card rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-text-secondary" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Basic Information */}
              <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
                <h4 className="text-lg font-semibold text-text mb-4">Basic Information</h4>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-text-secondary">Title</p>
                    <p className="font-medium text-text">{viewingAssessment.title}</p>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Description</p>
                    <p className="text-text">{viewingAssessment.description}</p>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Category</p>
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-primary/10 text-primary">
                      {viewingAssessment.category}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Status</p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(viewingAssessment.status)}`}>
                      {viewingAssessment.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                </div>
              </div>

              {/* Risk Scoring */}
              <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
                <h4 className="text-lg font-semibold text-text mb-4">Risk Scoring</h4>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-text-secondary">Likelihood</p>
                      <p className="text-2xl font-bold text-text">{viewingAssessment.likelihood}</p>
                    </div>
                    <div>
                      <p className="text-sm text-text-secondary">Impact</p>
                      <p className="text-2xl font-bold text-text">{viewingAssessment.impact}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Risk Level</p>
                    <span className={`inline-flex px-3 py-1 text-lg font-semibold rounded-full ${getRiskLevelColor(viewingAssessment.riskLevel)}`}>
                      {viewingAssessment.riskLevel.toUpperCase()}
                    </span>
                    <p className="text-sm text-text-secondary mt-1">Based on L{viewingAssessment.likelihood} × I{viewingAssessment.impact}</p>
                  </div>
                </div>
              </div>

              {/* Ownership & Dates */}
              <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
                <h4 className="text-lg font-semibold text-text mb-4">Ownership & Timeline</h4>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-text-secondary">Owner</p>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-text-secondary" />
                      <p className="text-text">{viewingAssessment.owner}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Assessor</p>
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-text-secondary" />
                      <p className="text-text">{viewingAssessment.assessor}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Review Date</p>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-text-secondary" />
                      <p className="text-text">{new Date(viewingAssessment.reviewDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Next Review</p>
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-text-secondary" />
                      <p className="text-text">{new Date(viewingAssessment.nextReviewDate).toLocaleDateString()}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tags & Additional Info */}
              <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
                <h4 className="text-lg font-semibold text-text mb-4">Additional Information</h4>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-text-secondary">Created</p>
                    <p className="text-text">{new Date(viewingAssessment.createdAt).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <p className="text-sm text-text-secondary">Last Updated</p>
                    <p className="text-text">{new Date(viewingAssessment.updatedAt).toLocaleDateString()}</p>
                  </div>
                  {viewingAssessment.tags && viewingAssessment.tags.length > 0 && (
                    <div>
                      <p className="text-sm text-text-secondary mb-2">Tags</p>
                      <div className="flex flex-wrap gap-2">
                        {viewingAssessment.tags.map((tag, index) => (
                          <span key={index} className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full bg-secondary/10 text-secondary">
                            <Tag className="w-3 h-3" />
                            {tag}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-end gap-3 mt-6 pt-6 border-t border-border">
              <Button
                variant="secondary"
                onClick={() => setShowViewModal(false)}
              >
                Close
              </Button>
              <Button
                onClick={() => {
                  setShowViewModal(false);
                  handleEditAssessment(viewingAssessment);
                }}
                leftIcon={<Edit className="w-4 h-4" />}
              >
                Edit Assessment
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Notifications */}
      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
    </div>
  );
});

RiskAssessmentDashboard.displayName = 'RiskAssessmentDashboard';

export default RiskAssessmentDashboard;
