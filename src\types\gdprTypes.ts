// Data Subject Request Types
export type RequestType = 
  | 'access'
  | 'rectification'
  | 'erasure'
  | 'portability'
  | 'restriction'
  | 'objection';

export type RequestStatus = 
  | 'pending'
  | 'in_progress'
  | 'completed'
  | 'rejected'
  | 'cancelled';

export type RequestPriority = 
  | 'low'
  | 'medium'
  | 'high'
  | 'urgent';

export interface DataSubjectRequest {
  id: string;
  email: string;
  type: RequestType;
  status: RequestStatus;
  priority: RequestPriority;
  description: string;
  notes?: string;
  progress: number;
  submittedAt: Date;
  dueDate: Date;
  completedAt?: Date;
  assignedTo?: string;
  attachments?: string[];
  communicationLog?: CommunicationEntry[];
}

export interface CommunicationEntry {
  id: string;
  timestamp: Date;
  type: 'email' | 'phone' | 'internal_note';
  content: string;
  author: string;
}

// Branch Detection Types
export interface Branch {
  id: string;
  name: string;
  location: string;
  country: string;
  region: string;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  dataFlows: DataFlow[];
  lastAssessment: Date;
  contactPerson: string;
  regulations: string[];
}

export interface DataFlow {
  id: string;
  fromBranch: string;
  toBranch: string;
  dataType: string;
  volume: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'on-demand';
  purpose: string;
  legalBasis: string;
  safeguards: string[];
  riskScore: number;
  lastReview: Date;
}

// Consent Management Types
export interface ConsentCategory {
  id: string;
  name: string;
  description: string;
  purpose: string;
  legalBasis: string;
  consentRate: number;
  consentedUsers: number;
  totalUsers: number;
  isRequired: boolean;
  retentionPeriod: number;
  lastUpdated: Date;
  preferences: ConsentPreference[];
}

export interface ConsentPreference {
  id: string;
  userId: string;
  categoryId: string;
  granted: boolean;
  timestamp: Date;
  method: 'explicit' | 'implicit' | 'opt-in' | 'opt-out';
  ipAddress?: string;
  userAgent?: string;
}

export interface ConsentWithdrawal {
  id: string;
  userId: string;
  categoryId: string;
  reason?: string;
  timestamp: Date;
  processed: boolean;
  processedAt?: Date;
}

// Impact Assessment (DPIA) Types
export type AssessmentStatus = 
  | 'draft'
  | 'in_review'
  | 'approved'
  | 'rejected'
  | 'requires_revision';

export type RiskLevel = 'low' | 'medium' | 'high' | 'very_high';

export interface ImpactAssessment {
  id: string;
  title: string;
  description: string;
  status: AssessmentStatus;
  riskScore: number;
  riskLevel: RiskLevel;
  createdAt: Date;
  updatedAt: Date;
  dueDate: Date;
  assignedTo: string[];
  stakeholders: Stakeholder[];
  dataProcessing: DataProcessingActivity;
  riskAssessment: RiskAssessmentDetails;
  mitigationMeasures: MitigationMeasure[];
  consultationRequired: boolean;
  consultationCompleted: boolean;
  approvedBy?: string;
  approvedAt?: Date;
}

export interface Stakeholder {
  id: string;
  name: string;
  role: string;
  email: string;
  department: string;
  involvement: 'owner' | 'reviewer' | 'consulted' | 'informed';
}

export interface DataProcessingActivity {
  purpose: string;
  legalBasis: string;
  dataTypes: string[];
  dataSubjects: string[];
  recipients: string[];
  retentionPeriod: number;
  internationalTransfers: boolean;
  transferCountries?: string[];
  safeguards?: string[];
}

export interface RiskAssessmentDetails {
  inherentRisks: Risk[];
  residualRisks: Risk[];
  overallRiskScore: number;
  riskMatrix: RiskMatrixEntry[];
}

export interface Risk {
  id: string;
  category: string;
  description: string;
  likelihood: number;
  impact: number;
  riskScore: number;
  riskLevel: RiskLevel;
}

export interface RiskMatrixEntry {
  likelihood: number;
  impact: number;
  riskLevel: RiskLevel;
  count: number;
}

export interface MitigationMeasure {
  id: string;
  title: string;
  description: string;
  type: 'technical' | 'organizational' | 'legal';
  status: 'planned' | 'in_progress' | 'implemented' | 'verified';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo: string;
  dueDate: Date;
  completedAt?: Date;
  effectiveness: number;
  cost?: number;
  relatedRisks: string[];
}

// Template Types
export interface AssessmentTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  questions: TemplateQuestion[];
  riskFactors: RiskFactor[];
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TemplateQuestion {
  id: string;
  question: string;
  type: 'text' | 'boolean' | 'multiple_choice' | 'scale';
  required: boolean;
  options?: string[];
  weight: number;
  riskImplication: string;
}

export interface RiskFactor {
  id: string;
  factor: string;
  weight: number;
  description: string;
  mitigationSuggestions: string[];
}

// Export and Reporting Types
export interface ExportOptions {
  format: 'json' | 'csv' | 'pdf' | 'xlsx';
  dateRange?: {
    start: Date;
    end: Date;
  };
  includeAttachments?: boolean;
  sections?: string[];
}

export interface ReportData {
  generatedAt: Date;
  reportType: string;
  data: any;
  metadata: {
    totalRecords: number;
    filters: Record<string, any>;
    exportOptions: ExportOptions;
  };
}

// Search and Filter Types
export interface SearchFilters {
  searchTerm?: string;
  status?: string[];
  type?: string[];
  priority?: string[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  assignedTo?: string[];
}

export interface SortOptions {
  field: string;
  direction: 'asc' | 'desc';
}

export interface PaginationOptions {
  page: number;
  limit: number;
  total?: number;
}

// Audit and Compliance Types
export interface AuditEntry {
  id: string;
  timestamp: Date;
  action: string;
  entityType: string;
  entityId: string;
  userId: string;
  userEmail: string;
  changes?: Record<string, { old: any; new: any }>;
  ipAddress?: string;
  userAgent?: string;
}

export interface ComplianceMetrics {
  totalRequests: number;
  completedRequests: number;
  overdue: number;
  averageProcessingTime: number;
  complianceScore: number;
  riskDistribution: Record<RiskLevel, number>;
  trendData: TrendDataPoint[];
}

export interface TrendDataPoint {
  date: Date;
  value: number;
  label: string;
}

// Risk Assessment Types
export type RiskCategory =
  | 'operational'
  | 'financial'
  | 'compliance'
  | 'reputational'
  | 'strategic'
  | 'technology'
  | 'legal'
  | 'environmental';

export type RiskLikelihood = 1 | 2 | 3 | 4 | 5;
export type RiskImpact = 1 | 2 | 3 | 4 | 5;

export interface RiskAssessment {
  id: string;
  title: string;
  description: string;
  category: RiskCategory;
  likelihood: RiskLikelihood;
  impact: RiskImpact;
  riskScore: number; // likelihood * impact
  riskLevel: RiskLevel;
  status: 'draft' | 'under_review' | 'approved' | 'rejected' | 'archived';
  owner: string;
  assessor: string;
  createdAt: Date;
  updatedAt: Date;
  reviewDate: Date;
  nextReviewDate: Date;
  mitigationMeasures: RiskMitigationMeasure[];
  residualRisk: {
    likelihood: RiskLikelihood;
    impact: RiskImpact;
    score: number;
    level: RiskLevel;
  };
  tags: string[];
  attachments: string[];
  comments: RiskComment[];
}

export interface RiskMitigationMeasure {
  id: string;
  title: string;
  description: string;
  type: 'preventive' | 'detective' | 'corrective' | 'compensating';
  status: 'planned' | 'in_progress' | 'implemented' | 'verified' | 'ineffective';
  priority: 'low' | 'medium' | 'high' | 'critical';
  owner: string;
  dueDate: Date;
  completedDate?: Date;
  cost?: number;
  effectiveness: number; // 0-100%
  notes: string;
}

export interface RiskComment {
  id: string;
  author: string;
  content: string;
  timestamp: Date;
  type: 'general' | 'review' | 'approval' | 'rejection';
}

export interface RiskMatrix {
  likelihood: RiskLikelihood;
  impact: RiskImpact;
  count: number;
  riskLevel: RiskLevel;
}

export interface RiskTrend {
  date: Date;
  totalRisks: number;
  highRisks: number;
  mediumRisks: number;
  lowRisks: number;
  averageScore: number;
}

// Regulatory Framework Types
export type RegulationType =
  | 'gdpr'
  | 'ccpa'
  | 'cpra'
  | 'sox'
  | 'hipaa'
  | 'pci_dss'
  | 'iso_27001'
  | 'nist'
  | 'appi'
  | 'lgpd'
  | 'pipeda'
  | 'dpdp'
  | 'other';

export type JurisdictionType =
  | 'eu'
  | 'us'
  | 'california'
  | 'uk'
  | 'canada'
  | 'brazil'
  | 'japan'
  | 'australia'
  | 'singapore'
  | 'india'
  | 'global'
  | 'other';

export interface RegulatoryFramework {
  id: string;
  name: string;
  type: RegulationType;
  jurisdiction: JurisdictionType;
  description: string;
  version: string;
  effectiveDate: Date;
  lastUpdated: Date;
  status: 'active' | 'draft' | 'deprecated' | 'pending';
  requirements: RegulatoryRequirement[];
  complianceDeadlines: ComplianceDeadline[];
  applicableEntities: string[];
  penalties: RegulationPenalty[];
  references: RegulationReference[];
  tags: string[];
  owner: string;
  reviewers: string[];
  nextReviewDate: Date;
}

export interface RegulatoryRequirement {
  id: string;
  title: string;
  description: string;
  category: 'data_protection' | 'security' | 'governance' | 'reporting' | 'consent' | 'breach_notification' | 'other';
  priority: 'low' | 'medium' | 'high' | 'critical';
  status: 'not_started' | 'in_progress' | 'compliant' | 'non_compliant' | 'not_applicable';
  complianceScore: number; // 0-100%
  evidence: string[];
  assignedTo: string;
  dueDate: Date;
  completedDate?: Date;
  notes: string;
  subRequirements: RegulatorySubRequirement[];
  controls: ComplianceControl[];
}

export interface RegulatorySubRequirement {
  id: string;
  title: string;
  description: string;
  status: 'not_started' | 'in_progress' | 'compliant' | 'non_compliant';
  evidence: string[];
  notes: string;
}

export interface ComplianceDeadline {
  id: string;
  title: string;
  description: string;
  dueDate: Date;
  status: 'upcoming' | 'due' | 'overdue' | 'completed';
  priority: 'low' | 'medium' | 'high' | 'critical';
  assignedTo: string;
  requirements: string[]; // requirement IDs
  completedDate?: Date;
  notes: string;
}

export interface RegulationPenalty {
  id: string;
  violationType: string;
  description: string;
  minPenalty: number;
  maxPenalty: number;
  currency: string;
  additionalConsequences: string[];
}

export interface RegulationReference {
  id: string;
  title: string;
  url: string;
  type: 'official_text' | 'guidance' | 'case_study' | 'template' | 'other';
  description: string;
}

export interface ComplianceControl {
  id: string;
  title: string;
  description: string;
  type: 'preventive' | 'detective' | 'corrective' | 'compensating';
  status: 'not_implemented' | 'partially_implemented' | 'implemented' | 'needs_review';
  effectiveness: number; // 0-100%
  owner: string;
  lastTested: Date;
  nextTestDate: Date;
  evidence: string[];
  notes: string;
}

export interface RegulatoryComplianceMetrics {
  totalFrameworks: number;
  byJurisdiction: Record<JurisdictionType, number>;
  byStatus: Record<string, number>;
  totalRequirements: number;
  compliantRequirements: number;
  overallComplianceScore: number;
  upcomingDeadlines: number;
  overdueDeadlines: number;
}
