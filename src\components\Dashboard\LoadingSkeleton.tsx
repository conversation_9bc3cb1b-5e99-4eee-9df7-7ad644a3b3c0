import React from 'react';

interface LoadingSkeletonProps {
  className?: string;
}

export const LoadingSkeleton: React.FC<LoadingSkeletonProps> = ({ className = '' }) => (
  <div className={`animate-pulse bg-border rounded ${className}`} />
);

export const StatCardSkeleton: React.FC = () => (
  <div className="bg-surface rounded-lg p-4">
    <div className="flex items-center mb-2">
      <LoadingSkeleton className="w-5 h-5 mr-2" />
      <LoadingSkeleton className="h-4 w-24" />
    </div>
    <LoadingSkeleton className="h-8 w-16 mb-1" />
    <LoadingSkeleton className="h-3 w-20" />
  </div>
);

export const MetricRowSkeleton: React.FC = () => (
  <div className="flex justify-between items-center">
    <LoadingSkeleton className="h-4 w-24" />
    <LoadingSkeleton className="h-4 w-12" />
  </div>
);

export const ActivityItemSkeleton: React.FC = () => (
  <div className="flex items-center text-sm">
    <LoadingSkeleton className="w-4 h-4 mr-2" />
    <LoadingSkeleton className="h-4 w-32" />
  </div>
);

export const OverviewSkeleton: React.FC = () => (
  <div className="lg:col-span-2 bg-card rounded-lg p-6 shadow-sm">
    <div className="flex items-center mb-6">
      <LoadingSkeleton className="w-16 h-16 rounded-full mr-4" />
      <div>
        <LoadingSkeleton className="h-6 w-48 mb-2" />
        <LoadingSkeleton className="h-4 w-32" />
      </div>
    </div>

    <div className="space-y-6">
      <div>
        <LoadingSkeleton className="h-5 w-24 mb-3" />
        <LoadingSkeleton className="h-16 w-full" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <StatCardSkeleton />
        <StatCardSkeleton />
      </div>
    </div>
  </div>
);

export const SidebarSkeleton: React.FC = () => (
  <div className="space-y-6">
    <div className="bg-card rounded-lg p-6 shadow-sm">
      <LoadingSkeleton className="h-5 w-32 mb-4" />
      <div className="space-y-4">
        <MetricRowSkeleton />
        <MetricRowSkeleton />
        <MetricRowSkeleton />
        <MetricRowSkeleton />
      </div>
    </div>

    <div className="bg-card rounded-lg p-6 shadow-sm">
      <LoadingSkeleton className="h-5 w-28 mb-4" />
      <div className="space-y-3">
        <ActivityItemSkeleton />
        <div className="text-sm space-y-1">
          <LoadingSkeleton className="h-4 w-40" />
          <LoadingSkeleton className="h-4 w-36" />
          <LoadingSkeleton className="h-4 w-32" />
        </div>
      </div>
    </div>

    <div className="bg-card rounded-lg p-6 shadow-sm">
      <LoadingSkeleton className="h-5 w-36 mb-4" />
      <div className="space-y-3">
        <MetricRowSkeleton />
        <MetricRowSkeleton />
        <MetricRowSkeleton />
      </div>
    </div>
  </div>
);

export const FullPageSkeleton: React.FC = () => (
  <div className="flex-1 bg-background">
    <div className="p-8">
      {/* Header Skeleton */}
      <div className="mb-8">
        <div className="mb-4 flex items-center">
          <LoadingSkeleton className="w-5 h-5 mr-2" />
          <LoadingSkeleton className="h-4 w-48" />
        </div>
        <LoadingSkeleton className="h-4 w-64 mb-2" />
        <LoadingSkeleton className="h-8 w-80" />
      </div>

      {/* Main Content Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <OverviewSkeleton />
        <SidebarSkeleton />
      </div>

      {/* Action Buttons Skeleton */}
      <div className="mt-8 flex gap-4">
        <LoadingSkeleton className="h-10 w-40" />
        <LoadingSkeleton className="h-10 w-36" />
        <LoadingSkeleton className="h-10 w-44" />
      </div>
    </div>
  </div>
);

export const ErrorState: React.FC<{ 
  error: string; 
  onRetry?: () => void;
  onBack?: () => void;
}> = ({ error, onRetry, onBack }) => (
  <div className="flex-1 bg-background">
    <div className="p-8">
      <div className="flex flex-col items-center justify-center min-h-96">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/20 flex items-center justify-center">
            <svg className="w-8 h-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-text mb-2">Something went wrong</h3>
          <p className="text-text-secondary mb-6">{error}</p>
          <div className="flex gap-4 justify-center">
            {onBack && (
              <button
                onClick={onBack}
                className="px-4 py-2 border border-border rounded-md text-text-secondary hover:text-text hover:bg-surface transition-colors"
              >
                Go Back
              </button>
            )}
            {onRetry && (
              <button
                onClick={onRetry}
                className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors"
              >
                Try Again
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default LoadingSkeleton;
