import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';

// Import all GDPR components
const OrganizationalMappingDashboard = React.lazy(() => import('../../components/GDPR/OrganizationalMappingDashboard'));
const PoliciesManagementDashboard = React.lazy(() => import('../../components/GDPR/PoliciesManagementDashboard'));
const ConsentManagementDashboard = React.lazy(() => import('../../components/GDPR/ConsentManagementDashboard'));
const ImpactAssessmentDashboard = React.lazy(() => import('../../components/GDPR/ImpactAssessmentDashboard'));

// Mock all services
vi.mock('../../services/organizationalMappingService', () => ({
  OrganizationalMappingService: {
    initialize: vi.fn(),
    getAllUnits: vi.fn().mockResolvedValue([
      {
        id: 'unit-001',
        name: 'Human Resources',
        type: 'department',
        location: 'New York, NY',
        country: 'United States',
        region: 'North America',
        headCount: 25,
        complianceScore: 92,
        riskLevel: 'low',
        contactPerson: 'Sarah Johnson',
        email: '<EMAIL>',
        dataProcessingActivities: ['Employee Records', 'Payroll'],
        regulations: ['GDPR', 'CCPA'],
        lastAssessment: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]),
    getAllDataFlows: vi.fn().mockResolvedValue([]),
    getMetrics: vi.fn().mockResolvedValue({
      totalUnits: 8,
      byType: { department: 4, team: 2, business_unit: 2 },
      byRiskLevel: { low: 5, medium: 2, high: 1 },
      byRegion: { 'North America': 4, 'Europe': 3, 'Asia-Pacific': 1 },
      averageComplianceScore: 88,
      totalHeadCount: 200,
      totalDataFlows: 15,
      highRiskUnits: 1,
      lastUpdated: new Date()
    }),
    createUnit: vi.fn().mockResolvedValue({
      id: 'unit-new',
      name: 'New Unit',
      type: 'department',
      location: 'Test Location',
      country: 'Test Country',
      region: 'Test Region',
      headCount: 10,
      complianceScore: 85,
      riskLevel: 'medium',
      contactPerson: 'Test Person',
      email: '<EMAIL>',
      dataProcessingActivities: [],
      regulations: [],
      lastAssessment: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }),
    updateUnit: vi.fn().mockResolvedValue({
      id: 'unit-001',
      name: 'Updated Unit',
      type: 'department',
      location: 'Updated Location',
      country: 'United States',
      region: 'North America',
      headCount: 30,
      complianceScore: 95,
      riskLevel: 'low',
      contactPerson: 'Updated Person',
      email: '<EMAIL>',
      dataProcessingActivities: ['Updated Activities'],
      regulations: ['GDPR', 'CCPA'],
      lastAssessment: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    }),
    deleteUnit: vi.fn().mockResolvedValue(true)
  }
}));

vi.mock('../../services/policiesManagementService', () => ({
  PoliciesManagementService: {
    initialize: vi.fn(),
    getAllPolicies: vi.fn().mockResolvedValue([
      {
        id: 'policy-001',
        name: 'Data Protection Policy',
        description: 'Comprehensive data protection policy',
        category: 'privacy',
        type: 'policy',
        status: 'active',
        version: '2.1',
        effectiveDate: new Date(),
        nextReviewDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
        owner: 'Privacy Officer',
        ownerId: 'user-001',
        organizationalUnits: ['unit-001'],
        regulations: ['GDPR', 'CCPA'],
        tags: ['privacy', 'data protection'],
        content: {
          purpose: 'Test purpose',
          scope: 'Test scope',
          definitions: {},
          requirements: [],
          procedures: [],
          responsibilities: [],
          exceptions: [],
          relatedPolicies: []
        },
        attachments: [],
        auditTrail: [],
        metrics: {
          complianceScore: 94,
          adherenceRate: 89,
          violationCount: 2
        },
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'Privacy Officer',
        updatedBy: 'Privacy Officer'
      }
    ]),
    getMetrics: vi.fn().mockResolvedValue({
      totalPolicies: 25,
      byStatus: { active: 20, draft: 3, review: 2 },
      byCategory: { privacy: 10, security: 8, compliance: 7 },
      byType: { policy: 15, procedure: 6, guideline: 4 },
      averageComplianceScore: 94,
      policiesNeedingReview: 3,
      expiredPolicies: 0,
      draftPolicies: 3,
      activePolicies: 20,
      totalViolations: 5,
      averageAdherenceRate: 89,
      lastUpdated: new Date()
    }),
    createPolicy: vi.fn().mockResolvedValue({
      id: 'policy-new',
      name: 'New Policy',
      description: 'New test policy',
      category: 'privacy',
      type: 'policy',
      status: 'draft',
      version: '1.0',
      effectiveDate: new Date(),
      nextReviewDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
      owner: 'Test User',
      ownerId: 'user-test',
      organizationalUnits: [],
      regulations: [],
      tags: [],
      content: {
        purpose: '',
        scope: '',
        definitions: {},
        requirements: [],
        procedures: [],
        responsibilities: [],
        exceptions: [],
        relatedPolicies: []
      },
      attachments: [],
      auditTrail: [],
      metrics: {
        complianceScore: 0,
        adherenceRate: 0,
        violationCount: 0
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'Test User',
      updatedBy: 'Test User'
    }),
    updatePolicy: vi.fn().mockResolvedValue({
      id: 'policy-001',
      name: 'Updated Policy',
      description: 'Updated test policy',
      category: 'privacy',
      type: 'policy',
      status: 'active',
      version: '2.2',
      effectiveDate: new Date(),
      nextReviewDate: new Date(Date.now() + 6 * 30 * 24 * 60 * 60 * 1000),
      owner: 'Privacy Officer',
      ownerId: 'user-001',
      organizationalUnits: ['unit-001'],
      regulations: ['GDPR', 'CCPA'],
      tags: ['privacy', 'data protection', 'updated'],
      content: {
        purpose: 'Updated purpose',
        scope: 'Updated scope',
        definitions: {},
        requirements: [],
        procedures: [],
        responsibilities: [],
        exceptions: [],
        relatedPolicies: []
      },
      attachments: [],
      auditTrail: [],
      metrics: {
        complianceScore: 96,
        adherenceRate: 92,
        violationCount: 1
      },
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'Privacy Officer',
      updatedBy: 'Privacy Officer'
    }),
    deletePolicy: vi.fn().mockResolvedValue(true)
  }
}));

vi.mock('../../services/consentManagementService', () => ({
  ConsentManagementService: {
    initialize: vi.fn(),
    getAllConsentRecords: vi.fn().mockResolvedValue([
      {
        id: 'consent-001',
        dataSubjectId: 'ds-001',
        email: '<EMAIL>',
        firstName: 'John',
        lastName: 'Doe',
        consentCategories: ['marketing', 'analytics'],
        consentTimestamp: new Date(),
        consentMethod: 'website',
        consentVersion: '1.0',
        status: 'active',
        lastUpdated: new Date(),
        source: 'website',
        withdrawalHistory: [],
        preferences: [],
        auditTrail: []
      }
    ]),
    getAllConsentCategories: vi.fn().mockResolvedValue([
      {
        id: 'cat-001',
        name: 'Marketing Communications',
        description: 'Email marketing and promotional content',
        purpose: 'Marketing',
        legalBasis: 'Consent',
        isRequired: false,
        isActive: true,
        retentionPeriod: '2 years',
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ]),
    getConsentMetrics: vi.fn().mockResolvedValue({
      totalUsers: 1000,
      consentedUsers: 850,
      consentRate: 85,
      trend: 'up',
      weeklyChange: 2.3,
      monthlyChange: 5.7,
      lastUpdated: new Date(),
      categoryBreakdown: [
        {
          categoryId: 'cat-001',
          categoryName: 'Marketing Communications',
          consentedUsers: 750,
          consentRate: 75,
          trend: 'up'
        }
      ]
    }),
    getConsentTrends: vi.fn().mockResolvedValue([])
  }
}));

vi.mock('../../services/impactAssessmentService', () => ({
  ImpactAssessmentService: {
    initialize: vi.fn(),
    getAllAssessments: vi.fn().mockResolvedValue([
      {
        id: 'ia-001',
        title: 'Customer Data Processing Assessment',
        description: 'DPIA for customer data processing activities',
        type: 'dpia',
        status: 'completed',
        priority: 'high',
        riskLevel: 'medium',
        overallRiskScore: 65,
        dataCategories: ['Personal Information', 'Contact Details'],
        processingPurposes: ['Customer Service', 'Marketing'],
        legalBasis: 'Consent',
        dataSubjects: ['Customers', 'Prospects'],
        thirdPartySharing: false,
        internationalTransfers: false,
        retentionPeriod: '5 years',
        securityMeasures: ['Encryption', 'Access Controls'],
        risks: [],
        mitigationMeasures: [],
        workflow: [],
        reviewers: [],
        approvers: [],
        createdAt: new Date(),
        updatedAt: new Date(),
        createdBy: 'Privacy Officer',
        updatedBy: 'Privacy Officer'
      }
    ]),
    getAssessmentMetrics: vi.fn().mockResolvedValue({
      total: 20,
      completed: 15,
      inProgress: 3,
      overdue: 2,
      averageRiskScore: 65,
      highRiskAssessments: 3
    })
  }
}));

// Mock chart.js
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>,
  Radar: () => <div data-testid="radar-chart">Radar Chart</div>
}));

describe('GDPR Components Integration', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Organizational Mapping Dashboard', () => {
    it('loads and displays organizational units', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should display organizational units
      expect(screen.getByText('Human Resources')).toBeInTheDocument();
      expect(screen.getByText('New York, NY')).toBeInTheDocument();
    });

    it('supports CRUD operations for organizational units', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      });

      // Should have create button
      const createButton = screen.getByText('Add Unit');
      expect(createButton).toBeInTheDocument();

      // Should have edit and delete buttons for existing units
      const editButtons = screen.getAllByTitle('Edit Unit');
      expect(editButtons.length).toBeGreaterThan(0);

      const deleteButtons = screen.getAllByTitle('Delete Unit');
      expect(deleteButtons.length).toBeGreaterThan(0);
    });
  });

  describe('Policies Management Dashboard', () => {
    it('loads and displays policies', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should display policies
      expect(screen.getByText('Data Protection Policy')).toBeInTheDocument();
    });

    it('displays policy metrics correctly', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      });

      // Should display metrics
      expect(screen.getByText('25')).toBeInTheDocument(); // Total policies
      expect(screen.getByText('20')).toBeInTheDocument(); // Active policies
    });
  });

  describe('Consent Management Dashboard', () => {
    it('loads and displays consent records', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should display consent information
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
    });

    it('displays consent metrics correctly', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      });

      // Should display metrics
      expect(screen.getByText('1000')).toBeInTheDocument(); // Total users
      expect(screen.getByText('850')).toBeInTheDocument(); // Consented users
    });
  });

  describe('Impact Assessment Dashboard', () => {
    it('loads and displays impact assessments', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should display assessments
      expect(screen.getByText('Customer Data Processing Assessment')).toBeInTheDocument();
    });

    it('displays assessment metrics correctly', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      });

      // Should display metrics
      expect(screen.getByText('20')).toBeInTheDocument(); // Total assessments
      expect(screen.getByText('15')).toBeInTheDocument(); // Completed assessments
    });
  });

  describe('Cross-Component Integration', () => {
    it('all components can load simultaneously without conflicts', async () => {
      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div data-testid="loading-org">Loading Org...</div>}>
            <OrganizationalMappingDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-policies">Loading Policies...</div>}>
            <PoliciesManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-consent">Loading Consent...</div>}>
            <ConsentManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div data-testid="loading-impact">Loading Impact...</div>}>
            <ImpactAssessmentDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Wait for all components to load
      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 10000 });

      // Ensure no loading indicators remain
      expect(screen.queryByTestId('loading-org')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-policies')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-consent')).not.toBeInTheDocument();
      expect(screen.queryByTestId('loading-impact')).not.toBeInTheDocument();
    });

    it('components share data consistently', async () => {
      // This test would verify that organizational units referenced in policies
      // are consistent with the organizational mapping data
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      });

      // Verify that the organizational unit exists
      expect(screen.getByText('Human Resources')).toBeInTheDocument();

      // Now render policies dashboard
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      });

      // The policy should reference the same organizational unit
      expect(screen.getByText('Data Protection Policy')).toBeInTheDocument();
    });
  });

  describe('Error Handling Across Components', () => {
    it('components handle service errors gracefully', async () => {
      // Mock service to throw error
      vi.mocked(require('../../services/organizationalMappingService').OrganizationalMappingService.getAllUnits)
        .mockRejectedValueOnce(new Error('Service error'));

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      // Should still render without crashing
      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      });

      consoleSpy.mockRestore();
    });

    it('error boundaries prevent component crashes from affecting others', async () => {
      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div>Loading Org...</div>}>
            <OrganizationalMappingDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Policies...</div>}>
            <PoliciesManagementDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Even if one component has issues, others should still work
      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      }, { timeout: 10000 });
    });
  });
});
