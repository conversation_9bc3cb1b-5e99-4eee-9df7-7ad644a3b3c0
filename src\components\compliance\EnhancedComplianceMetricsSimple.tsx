import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  BarChart3,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Users,
  Building,
  Target
} from 'lucide-react';

interface EnhancedComplianceMetricsProps {
  className?: string;
}

const EnhancedComplianceMetrics: React.FC<EnhancedComplianceMetricsProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const chartTheme = getChartTheme(mode === 'dark');

  useEffect(() => {
    const loadData = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setIsLoading(false);
      } catch (err) {
        setError('Failed to load compliance data');
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const overviewData = {
    labels: ['Compliant', 'Warning', 'Critical'],
    datasets: [{
      data: [75, 20, 5],
      backgroundColor: [
        'rgba(52, 211, 153, 0.8)',
        'rgba(251, 191, 36, 0.8)',
        'rgba(248, 113, 113, 0.8)'
      ],
      borderColor: [
        'rgb(52, 211, 153)',
        'rgb(251, 191, 36)',
        'rgb(248, 113, 113)'
      ],
      borderWidth: 2
    }]
  };

  const trendData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [{
      label: 'Compliance Score',
      data: [85, 87, 89, 91, 88, 92],
      borderColor: 'rgb(59, 130, 246)',
      backgroundColor: 'rgba(59, 130, 246, 0.1)',
      tension: 0.4
    }]
  };

  if (isLoading) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Compliance Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <BarChart3 className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Enhanced Compliance Metrics</h1>
              <p className="text-text-secondary">Comprehensive compliance monitoring and analytics</p>
            </div>
          </div>
          
          <button className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
            <RefreshCw className="w-4 h-4" />
            Refresh
          </button>
        </div>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Total Metrics</p>
                <p className="text-2xl font-bold text-text">24</p>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Compliant</p>
                <p className="text-2xl font-bold text-green-500">18</p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Departments</p>
                <p className="text-2xl font-bold text-text">5</p>
              </div>
              <Building className="w-8 h-8 text-purple-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Overall Score</p>
                <p className="text-2xl font-bold text-text">92.5%</p>
              </div>
              <TrendingUp className="w-8 h-8 text-green-500" />
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card rounded-lg p-6 border border-border">
            <h3 className="text-lg font-semibold text-text mb-4">Compliance Status Overview</h3>
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={overviewData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20
                      }
                    }
                  }
                }}
              />
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 border border-border">
            <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
            <div className="h-64">
              <Line
                data={trendData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor
                      },
                      ticks: {
                        color: chartTheme.textSecondary
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      }
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* Department List */}
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">Department Compliance</h3>
          <div className="space-y-3">
            {[
              { name: 'Human Resources', score: 95, status: 'compliant' },
              { name: 'Marketing', score: 88, status: 'warning' },
              { name: 'Finance', score: 97, status: 'compliant' },
              { name: 'IT Security', score: 92, status: 'compliant' },
              { name: 'Legal', score: 99, status: 'compliant' }
            ].map((dept, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-surface rounded-lg">
                <div className="flex items-center gap-3">
                  <Users className="w-5 h-5 text-text-secondary" />
                  <span className="font-medium text-text">{dept.name}</span>
                </div>
                <div className="flex items-center gap-3">
                  <span className="text-text">{dept.score}%</span>
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    dept.status === 'compliant' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                  }`}>
                    {dept.status === 'compliant' ? 'Compliant' : 'Warning'}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EnhancedComplianceMetrics;
