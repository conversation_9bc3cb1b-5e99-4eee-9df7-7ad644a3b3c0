import React, { useState, useEffect, useCallback } from 'react';
import { PolicyList } from './PolicyList';
import { ComplianceErrorBoundary } from './compliance/ComplianceErrorBoundary';
import { TrendingUp, TrendingDown, Minus, AlertTriangle, Shield, CheckCircle } from 'lucide-react';

interface ViolationCard {
  count: number;
  type: 'Critical' | 'Moderate' | 'Marginal';
  color: string;
  policies: string[];
}

// Type guard for ViolationCard
const isViolationCard = (data: any): data is ViolationCard => {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.count === 'number' &&
    typeof data.type === 'string' &&
    ['Critical', 'Moderate', 'Marginal'].includes(data.type) &&
    typeof data.color === 'string' &&
    Array.isArray(data.policies) &&
    data.count >= 0
  );
};

// Validate and sanitize violation data
const validateViolationData = (violations: any[]): ViolationCard[] => {
  if (!Array.isArray(violations)) {
    console.warn('ViolationsSummary: Invalid violations data, using fallback');
    return [];
  }

  return violations
    .filter(isViolationCard)
    .map(violation => ({
      ...violation,
      count: Math.max(0, violation.count),
      policies: violation.policies.filter(p => typeof p === 'string' && p.length > 0)
    }));
};

const ViolationsSummaryContent: React.FC = () => {
  const [selectedViolation, setSelectedViolation] = useState<ViolationCard | null>(null);
  const [showPolicyList, setShowPolicyList] = useState(false);
  const [violations, setViolations] = useState<ViolationCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize violations with error handling
  useEffect(() => {
    try {
      const rawViolations = [
        {
          count: 17,
          type: 'Critical' as const,
          color: 'bg-red-500',
          policies: [
            'Regulatory Compliance Policies',
            'Data Encryption',
            'Secure Storage',
            'Multi-Factor Authentication (MFA)',
            'Role-Based Access Control (RBAC)',
            'Privileged Access Management (PAM)',
            'Biometric Authentication',
            'Incident Response Plans',
            'Risk Management Frameworks',
            'Employee Security Awareness Training',
            'Continuous Monitoring',
            'Auditing and Logging',
            'Data Classification Policies',
            'Data Masking and Anonymization',
            'Secure Software Development Practices',
            'Patch Management',
            'Zero Trust Architecture (ZTA)',
          ]
        },
        {
          count: 3,
          type: 'Moderate' as const,
          color: 'bg-orange-500',
          policies: [
            'Third-Party Risk Management',
            'Backup Policies',
            'Endpoint Security',
          ]
        },
        {
          count: 5,
          type: 'Marginal' as const,
          color: 'bg-yellow-500',
          policies: [
            'Network Security Policies',
            'Data Retention Policies',
            'Fraud Detection Systems',
            'Breach Notification Protocols',
            'Governance Frameworks',
          ]
        }
      ];

      const validatedViolations = validateViolationData(rawViolations);

      if (validatedViolations.length === 0) {
        throw new Error('No valid violation data available');
      }

      setViolations(validatedViolations);
      setError(null);
    } catch (err) {
      console.error('Error initializing violation data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load violation data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleViolationClick = useCallback((violation: ViolationCard) => {
    try {
      if (!isViolationCard(violation)) {
        throw new Error('Invalid violation data');
      }

      setSelectedViolation(violation);
      setShowPolicyList(true);
    } catch (err) {
      console.error('Error handling violation click:', err);
      setError('Failed to process violation selection');
    }
  }, []);

  const handleCloseModal = useCallback(() => {
    try {
      setShowPolicyList(false);
      setSelectedViolation(null);
    } catch (err) {
      console.error('Error closing modal:', err);
    }
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-lg border border-border">
        <div className="animate-pulse">
          <div className="flex justify-between items-center mb-6">
            <div>
              <div className="h-6 bg-surface rounded w-1/3 mb-2"></div>
              <div className="h-4 bg-surface rounded w-1/2"></div>
            </div>
            <div className="flex gap-2">
              <div className="h-8 bg-surface rounded w-16"></div>
              <div className="h-8 bg-surface rounded w-20"></div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="bg-surface rounded-xl p-6 h-32"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-lg border border-border">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">Violations Data Unavailable</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (violations.length === 0) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-lg border border-border">
        <div className="text-center">
          <CheckCircle className="w-8 h-8 text-green-500 mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">No Violations Found</h3>
          <p className="text-text-secondary">All compliance policies are currently in good standing.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card p-6 rounded-xl shadow-lg border border-border">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-2">
          <Shield className="w-5 h-5 text-primary" />
          <div>
            <h2 className="text-xl font-semibold text-text">Violations Summary</h2>
            <p className="text-sm text-text-secondary mt-1">Overview of policy violations by severity</p>
          </div>
        </div>
        <div className="flex gap-2">
          <button
            className="px-4 py-2 text-sm font-medium text-text-secondary hover:text-text transition-colors"
            onClick={() => console.log('Export violations data')}
          >
            Export
          </button>
          <button
            className="px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors"
            onClick={() => console.log('View all violations')}
          >
            View All
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {violations.map((violation) => {
          // Additional safety check for each violation
          if (!isViolationCard(violation)) {
            return null;
          }

          return (
            <button
              key={violation.type}
              onClick={() => handleViolationClick(violation)}
              className="group relative bg-card rounded-xl p-6 hover:shadow-md transition-all duration-200 border border-border hover:border-purple-300 dark:hover:border-purple-600"
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <div className="text-3xl font-bold text-text">{violation.count}</div>
                  <div className="text-sm text-text-secondary">{violation.type} Violations</div>
                </div>
                <div className={`${violation.color} w-8 h-8 rounded-lg opacity-80`} />
              </div>

              {/* Simple trend indicator */}
              <div className="flex items-center justify-center h-8 mt-2">
                <div className="flex items-center gap-1">
                  {violation.type === 'Critical' ? (
                    <TrendingUp className="w-4 h-4 text-red-500" />
                  ) : violation.type === 'Moderate' ? (
                    <Minus className="w-4 h-4 text-orange-500" />
                  ) : (
                    <TrendingDown className="w-4 h-4 text-yellow-500" />
                  )}
                  <span className="text-xs text-text-secondary">
                    {violation.type === 'Critical' ? '+2.3%' :
                     violation.type === 'Moderate' ? '0.0%' :
                     '-1.2%'}
                  </span>
                </div>
              </div>

              <div className="mt-4 text-xs text-text-secondary">
                Last updated: {new Date().toLocaleDateString()}
              </div>
            </button>
          );
        })}
      </div>

      {showPolicyList && selectedViolation && (
        <PolicyList
          type={selectedViolation.type}
          policies={selectedViolation.policies}
          onClose={handleCloseModal}
        />
      )}
    </div>
  );
};

// Main component with error boundary
export const ViolationsSummary: React.FC = () => {
  return (
    <ComplianceErrorBoundary
      componentName="ViolationsSummary"
      fallbackType="violations"
    >
      <ViolationsSummaryContent />
    </ComplianceErrorBoundary>
  );
};

export default ViolationsSummary;
