/**
 * Tooltip Component
 * Provides contextual help and information tooltips
 */

import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { HelpCircle, Info, AlertCircle, CheckCircle } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';

interface TooltipProps {
  content: string | React.ReactNode;
  children: React.ReactNode;
  position?: 'top' | 'bottom' | 'left' | 'right';
  trigger?: 'hover' | 'click';
  delay?: number;
  maxWidth?: string;
  className?: string;
}

export const Tooltip: React.FC<TooltipProps> = ({
  content,
  children,
  position = 'top',
  trigger = 'hover',
  delay = 200,
  maxWidth = '300px',
  className = ''
}) => {
  const { mode } = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const [tooltipPosition, setTooltipPosition] = useState({ x: 0, y: 0 });
  const triggerRef = useRef<HTMLDivElement>(null);
  const tooltipRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<NodeJS.Timeout>();

  const showTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      if (triggerRef.current) {
        const rect = triggerRef.current.getBoundingClientRect();
        const scrollX = window.pageXOffset;
        const scrollY = window.pageYOffset;
        
        let x = 0;
        let y = 0;
        
        switch (position) {
          case 'top':
            x = rect.left + scrollX + rect.width / 2;
            y = rect.top + scrollY - 10;
            break;
          case 'bottom':
            x = rect.left + scrollX + rect.width / 2;
            y = rect.bottom + scrollY + 10;
            break;
          case 'left':
            x = rect.left + scrollX - 10;
            y = rect.top + scrollY + rect.height / 2;
            break;
          case 'right':
            x = rect.right + scrollX + 10;
            y = rect.top + scrollY + rect.height / 2;
            break;
        }
        
        setTooltipPosition({ x, y });
        setIsVisible(true);
      }
    }, delay);
  };

  const hideTooltip = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setIsVisible(false);
  };

  const handleClick = () => {
    if (trigger === 'click') {
      if (isVisible) {
        hideTooltip();
      } else {
        showTooltip();
      }
    }
  };

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (trigger === 'click' && isVisible) {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          triggerRef.current &&
          tooltipRef.current &&
          !triggerRef.current.contains(event.target as Node) &&
          !tooltipRef.current.contains(event.target as Node)
        ) {
          hideTooltip();
        }
      };

      document.addEventListener('mousedown', handleClickOutside);
      return () => {
        document.removeEventListener('mousedown', handleClickOutside);
      };
    }
  }, [isVisible, trigger]);

  const getTooltipClasses = () => {
    const baseClasses = `
      absolute z-50 px-3 py-2 text-sm rounded-lg shadow-lg border transition-opacity duration-200
      ${mode === 'dark' 
        ? 'bg-gray-800 text-white border-gray-700' 
        : 'bg-white text-gray-900 border-gray-200'
      }
    `;
    
    const positionClasses = {
      top: 'transform -translate-x-1/2 -translate-y-full',
      bottom: 'transform -translate-x-1/2',
      left: 'transform -translate-x-full -translate-y-1/2',
      right: 'transform -translate-y-1/2'
    };

    return `${baseClasses} ${positionClasses[position]} ${isVisible ? 'opacity-100' : 'opacity-0 pointer-events-none'}`;
  };

  const getArrowClasses = () => {
    const arrowSize = 'w-2 h-2';
    const arrowColor = mode === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200';
    
    const positionClasses = {
      top: 'absolute top-full left-1/2 transform -translate-x-1/2 rotate-45 border-b border-r',
      bottom: 'absolute bottom-full left-1/2 transform -translate-x-1/2 rotate-45 border-t border-l',
      left: 'absolute left-full top-1/2 transform -translate-y-1/2 rotate-45 border-t border-r',
      right: 'absolute right-full top-1/2 transform -translate-y-1/2 rotate-45 border-b border-l'
    };

    return `${arrowSize} ${arrowColor} ${positionClasses[position]}`;
  };

  return (
    <>
      <div
        ref={triggerRef}
        className={`inline-block ${className}`}
        onMouseEnter={trigger === 'hover' ? showTooltip : undefined}
        onMouseLeave={trigger === 'hover' ? hideTooltip : undefined}
        onClick={handleClick}
      >
        {children}
      </div>

      {isVisible && createPortal(
        <div
          ref={tooltipRef}
          className={getTooltipClasses()}
          style={{
            left: tooltipPosition.x,
            top: tooltipPosition.y,
            maxWidth
          }}
        >
          <div className={getArrowClasses()} />
          {typeof content === 'string' ? (
            <span>{content}</span>
          ) : (
            content
          )}
        </div>,
        document.body
      )}
    </>
  );
};

// Pre-configured tooltip variants
export const HelpTooltip: React.FC<Omit<TooltipProps, 'children'> & { size?: number }> = ({ 
  content, 
  size = 16, 
  ...props 
}) => (
  <Tooltip content={content} {...props}>
    <HelpCircle className={`w-${size/4} h-${size/4} text-text-secondary hover:text-primary cursor-help transition-colors`} />
  </Tooltip>
);

export const InfoTooltip: React.FC<Omit<TooltipProps, 'children'> & { size?: number }> = ({ 
  content, 
  size = 16, 
  ...props 
}) => (
  <Tooltip content={content} {...props}>
    <Info className={`w-${size/4} h-${size/4} text-blue-500 hover:text-blue-600 cursor-help transition-colors`} />
  </Tooltip>
);

export const WarningTooltip: React.FC<Omit<TooltipProps, 'children'> & { size?: number }> = ({ 
  content, 
  size = 16, 
  ...props 
}) => (
  <Tooltip content={content} {...props}>
    <AlertCircle className={`w-${size/4} h-${size/4} text-yellow-500 hover:text-yellow-600 cursor-help transition-colors`} />
  </Tooltip>
);

export const SuccessTooltip: React.FC<Omit<TooltipProps, 'children'> & { size?: number }> = ({ 
  content, 
  size = 16, 
  ...props 
}) => (
  <Tooltip content={content} {...props}>
    <CheckCircle className={`w-${size/4} h-${size/4} text-green-500 hover:text-green-600 cursor-help transition-colors`} />
  </Tooltip>
);

// Hook for programmatic tooltips
export const useTooltip = () => {
  const [tooltip, setTooltip] = useState<{
    isVisible: boolean;
    content: string | React.ReactNode;
    position: { x: number; y: number };
  }>({
    isVisible: false,
    content: '',
    position: { x: 0, y: 0 }
  });

  const showTooltip = (content: string | React.ReactNode, event: MouseEvent) => {
    setTooltip({
      isVisible: true,
      content,
      position: { x: event.clientX, y: event.clientY }
    });
  };

  const hideTooltip = () => {
    setTooltip(prev => ({ ...prev, isVisible: false }));
  };

  return {
    tooltip,
    showTooltip,
    hideTooltip
  };
};

export default Tooltip;
