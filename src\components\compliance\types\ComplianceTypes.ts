// Enhanced Compliance Types with comprehensive CRUD functionality
export interface Department {
  id: string;
  name: string;
  description: string;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  dataSubjectsCount: number;
  processingActivities: Array<{
    id: string;
    name: string;
    purpose: string;
    legalBasis: string;
    dataCategories: string[];
    retentionPeriod: string;
    status: 'active' | 'inactive' | 'under_review';
  }>;
  responsibleOfficer: string;
  lastAuditDate: Date;
  nextAuditDate: Date;
  violations: number;
  trend: 'up' | 'down' | 'stable';
  createdAt: Date;
  updatedAt: Date;
}

export interface CompliancePolicy {
  id: string;
  title: string;
  description: string;
  category: 'data_protection' | 'consent_management' | 'data_retention' | 'breach_response' | 'access_control' | 'security';
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  status: 'draft' | 'under_review' | 'approved' | 'active' | 'archived';
  version: string;
  effectiveDate: Date;
  reviewDate: Date;
  owner: string;
  assignedDepartments: string[];
  adherenceRate: number;
  violations: Array<{
    id: string;
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    reportedDate: Date;
    resolvedDate?: Date;
    status: 'open' | 'investigating' | 'resolved';
  }>;
  changeHistory: Array<{
    version: string;
    changes: string;
    changedBy: string;
    changeDate: Date;
  }>;
  createdAt: Date;
  updatedAt: Date;
}

export interface TimelineEvent {
  id: string;
  title: string;
  description: string;
  type: 'policy_implementation' | 'audit_activity' | 'data_subject_request' | 'breach_incident' | 'regulatory_deadline' | 'compliance_milestone';
  framework: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  date: Date;
  status: 'planned' | 'in_progress' | 'completed' | 'overdue' | 'cancelled';
  priority: 'low' | 'medium' | 'high' | 'critical';
  department: string;
  assignedTo: string;
  outcome?: string;
  relatedEntities: Array<{
    type: 'department' | 'policy' | 'incident';
    id: string;
    name: string;
  }>;
  attachments?: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ComplianceMetric {
  id: string;
  name: string;
  category: 'privacy' | 'security' | 'operational' | 'regulatory';
  currentValue: number;
  targetValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'compliant' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    status: string;
    notes?: string;
  }>;
  drillDownData?: {
    departments: Array<{ name: string; value: number; status: string }>;
    policies: Array<{ name: string; value: number; violations: number }>;
    timeBreakdown: Array<{ period: string; value: number; change: number }>;
  };
}

export interface ComplianceFramework {
  id: string;
  name: string;
  type: 'gdpr' | 'sox' | 'iso27001' | 'ccpa' | 'hipaa';
  description: string;
  departments: Department[];
  policies: CompliancePolicy[];
  timeline: TimelineEvent[];
  overallScore: number;
  lastAssessment: Date;
  nextAssessment: Date;
  status: 'compliant' | 'warning' | 'critical';
}

export interface EnhancedComplianceData {
  overview: ComplianceOverview;
  metrics: ComplianceMetric[];
  frameworks: ComplianceFramework[];
  categoryBreakdown: Record<string, { score: number; count: number; trend: string }>;
  realTimeUpdates: {
    lastSync: Date;
    isLive: boolean;
    pendingUpdates: number;
  };
}

export interface ComplianceOverview {
  totalMetrics: number;
  compliantMetrics: number;
  warningMetrics: number;
  criticalMetrics: number;
  overallScore: number;
  lastUpdated: Date;
  trendsLastMonth: {
    improvement: number;
    degradation: number;
    stable: number;
  };
}

export interface EnhancedComplianceMetricsProps {
  className?: string;
}
