import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { OrganizationalMappingService, OrganizationalUnit, DataFlow, OrganizationalMappingMetrics } from '../../services/organizationalMappingService';
import { Modal, ConfirmationModal } from '../ui/Modal';
import {
  Building2,
  Users,
  MapPin,
  Shield,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  ArrowRight,
  Plus,
  Filter,
  Search,
  Edit,
  Trash2,
  X,
  Save,
  RefreshCw,
  Eye,
  Network,
  GitBranch,
  Building,
  Settings
} from 'lucide-react';

// Types are now imported from the service

interface OrganizationalMappingDashboardProps {
  className?: string;
}

export const OrganizationalMappingDashboard: React.FC<OrganizationalMappingDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  // State management
  const [units, setUnits] = useState<OrganizationalUnit[]>([]);
  const [dataFlows, setDataFlows] = useState<DataFlow[]>([]);
  const [filteredUnits, setFilteredUnits] = useState<OrganizationalUnit[]>([]);
  const [metrics, setMetrics] = useState<OrganizationalMappingMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedFilters, setSelectedFilters] = useState({
    type: [] as string[],
    riskLevel: [] as string[],
    region: [] as string[]
  });
  const [activeView, setActiveView] = useState<'overview' | 'units' | 'flows' | 'hierarchy'>('overview');

  // CRUD Modal States
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [selectedUnit, setSelectedUnit] = useState<OrganizationalUnit | null>(null);
  const [editingUnit, setEditingUnit] = useState<OrganizationalUnit | null>(null);

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    type: 'department' as const,
    parentId: '',
    location: '',
    country: '',
    region: '',
    headCount: 0,
    contactPerson: '',
    email: '',
    phone: '',
    dataProcessingActivities: [] as string[],
    regulations: [] as string[]
  });

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    applyFilters();
  }, [units, searchTerm, selectedFilters]);

  const loadData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Use the actual service instead of mock data
      const [unitsData, dataFlowsData, metricsData] = await Promise.all([
        OrganizationalMappingService.getAllUnits(),
        OrganizationalMappingService.getAllDataFlows(),
        OrganizationalMappingService.getMetrics()
      ]);

      setUnits(unitsData);
      setDataFlows(dataFlowsData);
      setMetrics(metricsData);

      notifications.addNotification({
        type: 'success',
        title: 'Data Loaded',
        message: 'Organizational mapping data loaded successfully.'
      });
    } catch (error) {
      console.error('Error loading organizational data:', error);
      setError('Failed to load organizational mapping data. Please try again.');
      notifications.addNotification({
        type: 'error',
        title: 'Error Loading Data',
        message: 'Failed to load organizational mapping data. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = units;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(unit =>
        unit.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unit.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        unit.contactPerson.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply type filter
    if (selectedFilters.type.length > 0) {
      filtered = filtered.filter(unit => selectedFilters.type.includes(unit.type));
    }

    // Apply risk level filter
    if (selectedFilters.riskLevel.length > 0) {
      filtered = filtered.filter(unit => selectedFilters.riskLevel.includes(unit.riskLevel));
    }

    // Apply region filter
    if (selectedFilters.region.length > 0) {
      filtered = filtered.filter(unit => selectedFilters.region.includes(unit.region));
    }

    setFilteredUnits(filtered);
  };

  // CRUD Operations
  const handleCreateUnit = async () => {
    try {
      const newUnit = await OrganizationalMappingService.createUnit(formData);
      setUnits(prev => [newUnit, ...prev]);
      setShowCreateModal(false);
      resetForm();

      // Refresh metrics
      const updatedMetrics = await OrganizationalMappingService.getMetrics();
      setMetrics(updatedMetrics);

      notifications.addNotification({
        type: 'success',
        title: 'Unit Created',
        message: 'Organizational unit has been created successfully.'
      });
    } catch (error) {
      console.error('Error creating unit:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Creating Unit',
        message: 'Failed to create organizational unit. Please try again.'
      });
    }
  };

  const handleUpdateUnit = async () => {
    if (!editingUnit) return;

    try {
      const updatedUnit = await OrganizationalMappingService.updateUnit(editingUnit.id, formData);
      if (updatedUnit) {
        setUnits(prev => prev.map(unit => unit.id === editingUnit.id ? updatedUnit : unit));
        setShowEditModal(false);
        setEditingUnit(null);
        resetForm();

        // Refresh metrics
        const updatedMetrics = await OrganizationalMappingService.getMetrics();
        setMetrics(updatedMetrics);

        notifications.addNotification({
          type: 'success',
          title: 'Unit Updated',
          message: 'Organizational unit has been updated successfully.'
        });
      }
    } catch (error) {
      console.error('Error updating unit:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Updating Unit',
        message: 'Failed to update organizational unit. Please try again.'
      });
    }
  };

  const handleDeleteUnit = async () => {
    if (!selectedUnit) return;

    try {
      const success = await OrganizationalMappingService.deleteUnit(selectedUnit.id);
      if (success) {
        setUnits(prev => prev.filter(unit => unit.id !== selectedUnit.id));
        setShowDeleteModal(false);
        setSelectedUnit(null);

        // Refresh metrics
        const updatedMetrics = await OrganizationalMappingService.getMetrics();
        setMetrics(updatedMetrics);

        notifications.addNotification({
          type: 'success',
          title: 'Unit Deleted',
          message: 'Organizational unit has been deleted successfully.'
        });
      }
    } catch (error) {
      console.error('Error deleting unit:', error);
      notifications.addNotification({
        type: 'error',
        title: 'Error Deleting Unit',
        message: 'Failed to delete organizational unit. Please try again.'
      });
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      type: 'department',
      parentId: '',
      location: '',
      country: '',
      region: '',
      headCount: 0,
      contactPerson: '',
      email: '',
      phone: '',
      dataProcessingActivities: [],
      regulations: []
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (unit: OrganizationalUnit) => {
    setEditingUnit(unit);
    setFormData({
      name: unit.name,
      type: unit.type,
      parentId: unit.parentId || '',
      location: unit.location,
      country: unit.country,
      region: unit.region,
      headCount: unit.headCount,
      contactPerson: unit.contactPerson,
      email: unit.email,
      phone: unit.phone || '',
      dataProcessingActivities: unit.dataProcessingActivities,
      regulations: unit.regulations
    });
    setShowEditModal(true);
  };

  const openViewModal = (unit: OrganizationalUnit) => {
    setSelectedUnit(unit);
    setShowViewModal(true);
  };

  const openDeleteModal = (unit: OrganizationalUnit) => {
    setSelectedUnit(unit);
    setShowDeleteModal(true);
  };

  const handleRefresh = async () => {
    await loadData();
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'department': return Building2;
      case 'team': return Users;
      case 'business_unit': return Building;
      case 'subsidiary': return Network;
      case 'branch': return GitBranch;
      default: return Building2;
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  if (loading) {
    return (
      <div className={`${className} space-y-6`}>
        <div className="animate-pulse">
          <div className="h-8 bg-border rounded w-1/3 mb-4"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-24 bg-border rounded"></div>
            ))}
          </div>
          <div className="h-64 bg-border rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Organizational Mapping Error</h2>
            <p className="text-text-secondary">Something went wrong loading the organizational mapping dashboard. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-secondary/20 to-secondary/10'} rounded-xl`}>
            <Building2 className="w-6 h-6 text-secondary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Organizational Mapping</h2>
            <p className="text-text-secondary">Manage organizational structure, teams, and data flows</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="secondary" onClick={loadData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Refresh
          </Button>
          <Button variant="secondary">
            <Settings className="w-4 h-4 mr-2" />
            Settings
          </Button>
          <Button variant="primary" onClick={openCreateModal}>
            <Plus className="w-4 h-4 mr-2" />
            Add Unit
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="border-b border-border">
        <nav className="flex space-x-8">
          {[
            { id: 'overview', label: 'Overview', icon: Building2 },
            { id: 'units', label: 'Organizational Units', icon: Users },
            { id: 'flows', label: 'Data Flows', icon: ArrowRight },
            { id: 'hierarchy', label: 'Hierarchy View', icon: Network }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 ${
                  activeView === tab.id
                    ? 'border-secondary text-secondary'
                    : 'border-transparent text-text-secondary hover:text-text hover:border-border'
                }`}
              >
                <Icon className="w-4 h-4" />
                {tab.label}
              </button>
            );
          })}
        </nav>
      </div>

      {/* Overview Tab */}
      {activeView === 'overview' && metrics && (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                  <Building2 className="w-6 h-6 text-secondary" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalUnits}</div>
              <div className="text-text-secondary text-sm">Total Units</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-green-500/10'} rounded-lg`}>
                  <Shield className="w-6 h-6 text-green-500" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.averageComplianceScore.toFixed(1)}%</div>
              <div className="text-text-secondary text-sm">Avg Compliance Score</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-blue-500/10'} rounded-lg`}>
                  <Users className="w-6 h-6 text-blue-500" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalHeadCount}</div>
              <div className="text-text-secondary text-sm">Total Employees</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-orange-500/10'} rounded-lg`}>
                  <AlertTriangle className="w-6 h-6 text-orange-500" />
                </div>
                <TrendingDown className="w-5 h-5 text-orange-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.byRiskLevel.high + metrics.byRiskLevel.critical}</div>
              <div className="text-text-secondary text-sm">High Risk Units</div>
            </div>
          </div>

          {/* Charts and Analytics */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Unit Types Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byType).map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                        {React.createElement(getTypeIcon(type), { className: "w-4 h-4 text-secondary" })}
                      </div>
                      <span className="text-text capitalize">{type.replace('_', ' ')}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div
                          className="bg-secondary h-2 rounded-full transition-all duration-500"
                          style={{width: `${metrics.totalUnits ? ((count as number / metrics.totalUnits) * 100) : 0}%`}}
                        />
                      </div>
                      <span className="text-sm font-medium text-text w-8">{count as number}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Risk Level Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byRiskLevel).map(([level, count]) => (
                  <div key={level} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getRiskLevelColor(level)} text-xs font-bold rounded-full uppercase`}>
                        {level}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${metrics.totalUnits ? ((count as number / metrics.totalUnits) * 100) : 0}%`}}
                        />
                      </div>
                      <span className="text-sm font-medium text-text w-8">{count as number}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Units Tab */}
      {activeView === 'units' && (
        <div className="space-y-6">
          {/* Search and Filters */}
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-secondary w-4 h-4" />
                <FormInput
                  type="text"
                  placeholder="Search units..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button variant="secondary">
                <Filter className="w-4 h-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>

          {/* Units List */}
          <div className="space-y-4">
            {filteredUnits.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <h3 className="text-lg font-medium text-text mb-2">No organizational units found</h3>
                <p className="text-text-secondary">Try adjusting your search or filters</p>
              </div>
            ) : (
              filteredUnits.map((unit) => {
                const TypeIcon = getTypeIcon(unit.type);
                return (
                  <div
                    key={unit.id}
                    className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-secondary/30 hover:shadow-lg'} rounded-xl transition-all duration-300 cursor-pointer`}
                    onClick={() => openViewModal(unit)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 flex-1">
                        <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-xl`}>
                          <TypeIcon className="w-6 h-6 text-secondary" />
                        </div>

                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-text">{unit.name}</h3>
                            <span className={`px-3 py-1 ${getRiskLevelColor(unit.riskLevel)} text-xs font-bold rounded-full uppercase`}>
                              {unit.riskLevel}
                            </span>
                            <span className="px-3 py-1 bg-border text-text text-xs font-medium rounded-full capitalize">
                              {unit.type.replace('_', ' ')}
                            </span>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <MapPin className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">{unit.location}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Users className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">{unit.headCount} employees</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Shield className="w-4 h-4 text-text-secondary" />
                              <span className="text-text-secondary">Compliance: {unit.complianceScore}%</span>
                            </div>
                          </div>

                          <div className="mt-3 flex items-center gap-2">
                            <span className="text-sm text-text-secondary">Contact:</span>
                            <span className="text-sm text-text">{unit.contactPerson}</span>
                            <span className="text-sm text-text-secondary">•</span>
                            <span className="text-sm text-text">{unit.email}</span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openViewModal(unit);
                          }}
                          className="p-2 text-text-secondary hover:text-primary hover:bg-primary/10 rounded-lg transition-colors"
                          title="View Details"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openEditModal(unit);
                          }}
                          className="p-2 text-text-secondary hover:text-secondary hover:bg-secondary/10 rounded-lg transition-colors"
                          title="Edit Unit"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            openDeleteModal(unit);
                          }}
                          className="p-2 text-text-secondary hover:text-red-500 hover:bg-red-500/10 rounded-lg transition-colors"
                          title="Delete Unit"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </div>
      )}

      {/* Data Flows Tab */}
      {activeView === 'flows' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <ArrowRight className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Data Flows</h3>
            <p className="text-text-secondary">Data flow visualization coming soon</p>
          </div>
        </div>
      )}

      {/* Hierarchy Tab */}
      {activeView === 'hierarchy' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <Network className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Organizational Hierarchy</h3>
            <p className="text-text-secondary">Hierarchy visualization coming soon</p>
          </div>
        </div>
      )}

      {/* Create/Edit Modal */}
      {(showCreateModal || showEditModal) && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text">
                {editingUnit ? 'Edit Organizational Unit' : 'Create New Unit'}
              </h3>
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setShowEditModal(false);
                  setEditingUnit(null);
                  resetForm();
                }}
                className="p-2 hover:bg-border rounded-lg transition-colors"
              >
                <X className="w-5 h-5 text-text-secondary" />
              </button>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text mb-2">Unit Name</label>
                  <FormInput
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter unit name"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text mb-2">Type</label>
                    <FormSelect
                      value={formData.type}
                      onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value as any }))}
                      options={[
                        { value: 'department', label: 'Department' },
                        { value: 'team', label: 'Team' },
                        { value: 'business_unit', label: 'Business Unit' },
                        { value: 'subsidiary', label: 'Subsidiary' },
                        { value: 'branch', label: 'Branch' }
                      ]}
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-2">Parent Unit</label>
                    <FormSelect
                      value={formData.parentId}
                      onChange={(e) => setFormData(prev => ({ ...prev, parentId: e.target.value }))}
                      options={[
                        { value: '', label: 'No Parent' },
                        ...units.filter(u => u.id !== editingUnit?.id).map(u => ({
                          value: u.id,
                          label: u.name
                        }))
                      ]}
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Location</label>
                  <FormInput
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="City, State/Province"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text mb-2">Country</label>
                    <FormInput
                      type="text"
                      value={formData.country}
                      onChange={(e) => setFormData(prev => ({ ...prev, country: e.target.value }))}
                      placeholder="Country"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-text mb-2">Region</label>
                    <FormSelect
                      value={formData.region}
                      onChange={(e) => setFormData(prev => ({ ...prev, region: e.target.value }))}
                      options={[
                        { value: 'North America', label: 'North America' },
                        { value: 'Europe', label: 'Europe' },
                        { value: 'Asia Pacific', label: 'Asia Pacific' },
                        { value: 'Latin America', label: 'Latin America' },
                        { value: 'Middle East & Africa', label: 'Middle East & Africa' }
                      ]}
                      required
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Head Count</label>
                  <FormInput
                    type="number"
                    value={formData.headCount.toString()}
                    onChange={(e) => setFormData(prev => ({ ...prev, headCount: parseInt(e.target.value) || 0 }))}
                    placeholder="Number of employees"
                    min="0"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text mb-2">Contact Person</label>
                  <FormInput
                    type="text"
                    value={formData.contactPerson}
                    onChange={(e) => setFormData(prev => ({ ...prev, contactPerson: e.target.value }))}
                    placeholder="Full name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Email</label>
                  <FormInput
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Phone (Optional)</label>
                  <FormInput
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="******-0123"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Data Processing Activities</label>
                  <textarea
                    value={formData.dataProcessingActivities.join(', ')}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      dataProcessingActivities: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                    }))}
                    placeholder="Enter activities separated by commas"
                    rows={3}
                    className={`w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent ${
                      mode === 'dark' ? 'bg-surface text-text' : 'bg-white text-gray-900'
                    }`}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-text mb-2">Applicable Regulations</label>
                  <textarea
                    value={formData.regulations.join(', ')}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      regulations: e.target.value.split(',').map(s => s.trim()).filter(s => s)
                    }))}
                    placeholder="Enter regulations separated by commas (e.g., GDPR, CCPA)"
                    rows={2}
                    className={`w-full px-3 py-2 border border-border rounded-lg focus:ring-2 focus:ring-secondary focus:border-transparent ${
                      mode === 'dark' ? 'bg-surface text-text' : 'bg-white text-gray-900'
                    }`}
                  />
                </div>
              </div>
            </div>

            <div className="flex items-center justify-end gap-3 mt-6 pt-6 border-t border-border">
              <Button
                variant="secondary"
                onClick={() => {
                  setShowCreateModal(false);
                  setShowEditModal(false);
                  setEditingUnit(null);
                  resetForm();
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={editingUnit ? handleUpdateUnit : handleCreateUnit}
                disabled={!formData.name || !formData.location || !formData.contactPerson || !formData.email}
              >
                <Save className="w-4 h-4 mr-2" />
                {editingUnit ? 'Update Unit' : 'Create Unit'}
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* View Modal */}
      {showViewModal && selectedUnit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-xl font-bold text-text">Unit Details</h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="secondary"
                  onClick={() => openEditModal(selectedUnit)}
                >
                  <Edit className="w-4 h-4 mr-2" />
                  Edit
                </Button>
                <button
                  onClick={() => {
                    setShowViewModal(false);
                    setSelectedUnit(null);
                  }}
                  className="p-2 hover:bg-border rounded-lg transition-colors"
                >
                  <X className="w-5 h-5 text-text-secondary" />
                </button>
              </div>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Unit Name</label>
                  <p className="text-text text-lg font-semibold">{selectedUnit.name}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-1">Type</label>
                    <span className="inline-flex px-3 py-1 text-xs font-semibold rounded-full bg-secondary/10 text-secondary">
                      {selectedUnit.type.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-1">Risk Level</label>
                    <span className={`inline-flex px-3 py-1 text-xs font-semibold rounded-full ${getRiskLevelColor(selectedUnit.riskLevel)}`}>
                      {selectedUnit.riskLevel.toUpperCase()}
                    </span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Location</label>
                  <p className="text-text">{selectedUnit.location}</p>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-1">Country</label>
                    <p className="text-text">{selectedUnit.country}</p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-1">Region</label>
                    <p className="text-text">{selectedUnit.region}</p>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Head Count</label>
                  <p className="text-text">{selectedUnit.headCount} employees</p>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Contact Person</label>
                  <p className="text-text">{selectedUnit.contactPerson}</p>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Email</label>
                  <p className="text-text">{selectedUnit.email}</p>
                </div>
                {selectedUnit.phone && (
                  <div>
                    <label className="block text-sm font-medium text-text-secondary mb-1">Phone</label>
                    <p className="text-text">{selectedUnit.phone}</p>
                  </div>
                )}
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Compliance Score</label>
                  <div className="flex items-center gap-3">
                    <div className="flex-1 bg-border rounded-full h-2">
                      <div
                        className="bg-green-500 h-2 rounded-full transition-all duration-500"
                        style={{width: `${selectedUnit.complianceScore}%`}}
                      />
                    </div>
                    <span className="text-sm text-text font-medium">{selectedUnit.complianceScore}%</span>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text-secondary mb-1">Last Assessment</label>
                  <p className="text-text">{selectedUnit.lastAssessment.toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            <div className="mt-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">Data Processing Activities</label>
                <div className="flex flex-wrap gap-2">
                  {selectedUnit.dataProcessingActivities.map((activity, index) => (
                    <span key={index} className="px-3 py-1 bg-border text-text text-sm rounded-full">
                      {activity}
                    </span>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-text-secondary mb-2">Applicable Regulations</label>
                <div className="flex flex-wrap gap-2">
                  {selectedUnit.regulations.map((regulation, index) => (
                    <span key={index} className="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full">
                      {regulation}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedUnit && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-xl p-6 max-w-md w-full mx-4`}>
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded-full">
                <AlertTriangle className="w-6 h-6 text-red-600 dark:text-red-400" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-text">Delete Unit</h3>
                <p className="text-text-secondary">This action cannot be undone.</p>
              </div>
            </div>

            <p className="text-text mb-6">
              Are you sure you want to delete <strong>{selectedUnit.name}</strong>? This will also remove all associated data flows and relationships.
            </p>

            <div className="flex items-center justify-end gap-3">
              <Button
                variant="secondary"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedUnit(null);
                }}
              >
                Cancel
              </Button>
              <Button
                variant="danger"
                onClick={handleDeleteUnit}
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Delete Unit
              </Button>
            </div>
          </div>
        </div>
      )}

      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
      </div>
    </ErrorBoundary>
  );
};

export default OrganizationalMappingDashboard;
