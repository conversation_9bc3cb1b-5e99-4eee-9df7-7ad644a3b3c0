import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import { GDPRCommandCenter } from '../../components/GDPR/GDPRCommandCenter';
import { GDPRService } from '../../services/gdprService';
import { DataSubjectRequestService } from '../../services/dataSubjectRequestService';
import { BranchDetectionService } from '../../services/branchDetectionService';
import { ConsentManagementService } from '../../services/consentManagementService';
import { ImpactAssessmentService } from '../../services/impactAssessmentService';

// Mock all services
vi.mock('../../services/gdprService');
vi.mock('../../services/dataSubjectRequestService');
vi.mock('../../services/branchDetectionService');
vi.mock('../../services/consentManagementService');
vi.mock('../../services/impactAssessmentService');

const setupMockData = () => {
  const mockMetrics = {
    dataSubjectRequests: { total: 100, pending: 20, inProgress: 30, completed: 50, overdue: 5, averageProcessingTime: 15, completionRate: 85 },
    branchCompliance: { totalBranches: 10, compliantBranches: 8, complianceScore: 80, crossBorderTransfers: 3, highRiskBranches: 2 },
    consentManagement: { totalUsers: 5000, consentedUsers: 4000, consentRate: 80, withdrawalRate: 5, categoryBreakdown: [] },
    impactAssessments: { total: 25, completed: 20, inProgress: 3, overdue: 2, averageRiskScore: 65, highRiskAssessments: 5 }
  };

  const mockRequests = [
    { id: '1', email: '<EMAIL>', type: 'access', status: 'pending', priority: 'high', description: 'Test request 1', createdAt: new Date().toISOString() },
    { id: '2', email: '<EMAIL>', type: 'deletion', status: 'in_progress', priority: 'medium', description: 'Test request 2', createdAt: new Date().toISOString() }
  ];

  const mockBranches = [
    { id: '1', name: 'London Office', location: 'London, UK', country: 'UK', complianceScore: 95, riskLevel: 'low', dataFlows: [] },
    { id: '2', name: 'Paris Office', location: 'Paris, France', country: 'France', complianceScore: 75, riskLevel: 'medium', dataFlows: [] }
  ];

  const mockConsentRecords = [
    { id: '1', userId: 'user1', email: '<EMAIL>', consentGiven: true, consentDate: new Date().toISOString(), categories: ['marketing'] },
    { id: '2', userId: 'user2', email: '<EMAIL>', consentGiven: false, consentDate: new Date().toISOString(), categories: ['analytics'] }
  ];

  const mockAssessments = [
    { id: '1', title: 'Customer DPIA', description: 'Customer data processing assessment', status: 'completed', riskScore: 70, type: 'DPIA', createdAt: new Date().toISOString() },
    { id: '2', title: 'Employee LIA', description: 'Employee monitoring assessment', status: 'in_progress', riskScore: 60, type: 'LIA', createdAt: new Date().toISOString() }
  ];

  // Setup service mocks
  vi.mocked(GDPRService.getDashboardMetrics).mockResolvedValue(mockMetrics);
  vi.mocked(GDPRService.getAlerts).mockResolvedValue([]);
  vi.mocked(GDPRService.getActivities).mockResolvedValue([]);
  vi.mocked(DataSubjectRequestService.getAllRequests).mockResolvedValue(mockRequests);
  vi.mocked(DataSubjectRequestService.createRequest).mockResolvedValue(mockRequests[0]);
  vi.mocked(DataSubjectRequestService.updateRequest).mockResolvedValue(mockRequests[0]);
  vi.mocked(DataSubjectRequestService.deleteRequest).mockResolvedValue(undefined);
  vi.mocked(BranchDetectionService.getAllBranches).mockResolvedValue(mockBranches);
  vi.mocked(BranchDetectionService.getMetrics).mockResolvedValue({ totalBranches: 2, compliantBranches: 1, complianceScore: 85, crossBorderTransfers: 1, highRiskBranches: 1 });
  vi.mocked(ConsentManagementService.getAllConsentRecords).mockResolvedValue(mockConsentRecords);
  vi.mocked(ConsentManagementService.getMetrics).mockResolvedValue({ totalUsers: 2, consentedUsers: 1, consentRate: 50, withdrawalRate: 50, categoryBreakdown: [] });
  vi.mocked(ImpactAssessmentService.getAllAssessments).mockResolvedValue(mockAssessments);
  vi.mocked(ImpactAssessmentService.getMetrics).mockResolvedValue({ total: 2, completed: 1, inProgress: 1, overdue: 0, averageRiskScore: 65, highRiskAssessments: 1 });

  return { mockMetrics, mockRequests, mockBranches, mockConsentRecords, mockAssessments };
};

describe('GDPR Dashboard Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    setupMockData();
  });

  describe('Dashboard Navigation Integration', () => {
    it('navigates between all dashboard sections seamlessly', async () => {
      render(<GDPRCommandCenter />);
      
      // Wait for initial load
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Test navigation to Data Subject Requests
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // Test navigation to Branch Detection
      const branchTab = screen.getByText('Branch Detection');
      await user.click(branchTab);
      
      await waitFor(() => {
        expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      });

      // Test navigation to Consent Management
      const consentTab = screen.getByText('Consent Management');
      await user.click(consentTab);
      
      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      });

      // Test navigation to Impact Assessments
      const assessmentTab = screen.getByText('Impact Assessments');
      await user.click(assessmentTab);
      
      await waitFor(() => {
        expect(screen.getByText('Impact Assessments (DPIA)')).toBeInTheDocument();
      });

      // Return to Overview
      const overviewTab = screen.getByText('Overview');
      await user.click(overviewTab);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });
    });

    it('maintains state when switching between sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Navigate to requests and verify data loads
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Navigate away and back
      const overviewTab = screen.getByText('Overview');
      await user.click(overviewTab);
      
      await user.click(requestsTab);
      
      // Data should still be there (cached)
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });
  });

  describe('Modal Integration Tests', () => {
    it('opens and closes data subject request modal correctly', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Navigate to requests section
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // Open create modal
      const createButton = screen.getByRole('button', { name: /create request/i });
      await user.click(createButton);
      
      expect(screen.getByText('Create New Data Subject Request')).toBeInTheDocument();
      
      // Close modal
      const closeButton = screen.getByRole('button', { name: /close/i });
      await user.click(closeButton);
      
      expect(screen.queryByText('Create New Data Subject Request')).not.toBeInTheDocument();
    });

    it('handles modal form submission and updates data', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // Open create modal
      const createButton = screen.getByRole('button', { name: /create request/i });
      await user.click(createButton);
      
      // Fill out form
      await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
      await user.selectOptions(screen.getByLabelText(/request type/i), 'access');
      await user.type(screen.getByLabelText(/description/i), 'New test request description');
      
      // Submit form
      const submitButton = screen.getByRole('button', { name: /create request/i });
      await user.click(submitButton);
      
      // Verify service was called
      await waitFor(() => {
        expect(DataSubjectRequestService.createRequest).toHaveBeenCalledWith(
          expect.objectContaining({
            email: '<EMAIL>',
            type: 'access',
            description: 'New test request description'
          })
        );
      });
    });

    it('handles edit modal with pre-filled data', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Click edit button for first request
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      // Verify modal opens with pre-filled data
      expect(screen.getByText('Edit Data Subject Request')).toBeInTheDocument();
      expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByDisplayValue('Test request 1')).toBeInTheDocument();
    });
  });

  describe('CRUD Operations Integration', () => {
    it('performs complete CRUD cycle for data subject requests', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // CREATE: Add new request
      const createButton = screen.getByRole('button', { name: /create request/i });
      await user.click(createButton);
      
      await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/description/i), 'CRUD test request');
      
      const submitButton = screen.getByRole('button', { name: /create request/i });
      await user.click(submitButton);
      
      await waitFor(() => {
        expect(DataSubjectRequestService.createRequest).toHaveBeenCalled();
      });

      // READ: Verify data is displayed
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();

      // UPDATE: Edit existing request
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      const statusSelect = screen.getByLabelText(/status/i);
      await user.selectOptions(statusSelect, 'completed');
      
      const updateButton = screen.getByRole('button', { name: /update request/i });
      await user.click(updateButton);
      
      await waitFor(() => {
        expect(DataSubjectRequestService.updateRequest).toHaveBeenCalled();
      });

      // DELETE: Remove request
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(DataSubjectRequestService.deleteRequest).toHaveBeenCalledWith('1');
      });
    });

    it('handles CRUD operations across different dashboard sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Test Branch Detection CRUD
      const branchTab = screen.getByText('Branch Detection');
      await user.click(branchTab);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      // Test Consent Management CRUD
      const consentTab = screen.getByText('Consent Management');
      await user.click(consentTab);
      
      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      });

      // Test Impact Assessment CRUD
      const assessmentTab = screen.getByText('Impact Assessments');
      await user.click(assessmentTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer DPIA')).toBeInTheDocument();
      });
    });
  });

  describe('Action Button Integration', () => {
    it('handles action buttons consistently across components', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Test action buttons in requests section
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Verify Edit and Delete buttons are present and functional
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      
      expect(editButtons.length).toBeGreaterThan(0);
      expect(deleteButtons.length).toBeGreaterThan(0);

      // Test edit button functionality
      await user.click(editButtons[0]);
      expect(screen.getByText('Edit Data Subject Request')).toBeInTheDocument();
      
      const cancelButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelButton);

      // Test delete button functionality
      await user.click(deleteButtons[0]);
      expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
      
      const cancelDeleteButton = screen.getByRole('button', { name: /cancel/i });
      await user.click(cancelDeleteButton);
    });

    it('maintains consistent action button behavior across sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Test in Branch Detection
      const branchTab = screen.getByText('Branch Detection');
      await user.click(branchTab);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const branchEditButtons = screen.getAllByRole('button', { name: /edit/i });
      const branchDeleteButtons = screen.getAllByRole('button', { name: /delete/i });
      
      expect(branchEditButtons.length).toBeGreaterThan(0);
      expect(branchDeleteButtons.length).toBeGreaterThan(0);

      // Test consistent behavior
      await user.click(branchEditButtons[0]);
      expect(screen.getByText('Edit Branch')).toBeInTheDocument();
    });
  });

  describe('Error Handling Integration', () => {
    it('handles service errors gracefully across all sections', async () => {
      // Mock service errors
      vi.mocked(DataSubjectRequestService.getAllRequests).mockRejectedValue(new Error('Service error'));
      
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      // Should show error message but not crash
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });
    });

    it('recovers from errors when retrying operations', async () => {
      // Mock initial error then success
      vi.mocked(DataSubjectRequestService.getAllRequests)
        .mockRejectedValueOnce(new Error('Service error'))
        .mockResolvedValue([]);
      
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      // Should show error initially
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });

      // Retry operation
      const retryButton = screen.getByRole('button', { name: /retry/i });
      await user.click(retryButton);
      
      // Should recover
      await waitFor(() => {
        expect(screen.queryByText(/failed to load/i)).not.toBeInTheDocument();
      });
    });
  });

  describe('Data Flow Integration', () => {
    it('maintains data consistency across dashboard sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Verify metrics are displayed correctly
      expect(screen.getByText('100')).toBeInTheDocument(); // Total requests
      expect(screen.getByText('80%')).toBeInTheDocument(); // Compliance score

      // Navigate to detailed view and verify consistency
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      // Return to overview and verify metrics are still correct
      const overviewTab = screen.getByText('Overview');
      await user.click(overviewTab);
      
      await waitFor(() => {
        expect(screen.getByText('100')).toBeInTheDocument();
      });
    });

    it('updates metrics when data changes', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Create new request
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      const createButton = screen.getByRole('button', { name: /create request/i });
      await user.click(createButton);
      
      await user.type(screen.getByLabelText(/email address/i), '<EMAIL>');
      await user.type(screen.getByLabelText(/description/i), 'Metrics test request');
      
      const submitButton = screen.getByRole('button', { name: /create request/i });
      await user.click(submitButton);
      
      // Verify service was called
      await waitFor(() => {
        expect(DataSubjectRequestService.createRequest).toHaveBeenCalled();
      });

      // Return to overview - metrics should be refreshed
      const overviewTab = screen.getByText('Overview');
      await user.click(overviewTab);
      
      // Verify metrics refresh was triggered
      expect(GDPRService.getDashboardMetrics).toHaveBeenCalledTimes(2);
    });
  });
});
