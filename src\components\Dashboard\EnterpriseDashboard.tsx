import React, { useState, useEffect, useMemo, Suspense, lazy } from 'react';
import { useCompliance } from '../../context/ComplianceContext';
import { useTheme } from '../../context/ThemeContext';
import { useNavigation } from '../../context/NavigationContext';
import { Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler,
} from 'chart.js/auto';


// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  Filler
);

// Fallback components are now handled by error boundaries

// Lazy load all dashboard components - let errors bubble up to error boundaries
const SiteReliabilityGuardian = lazy(() => import('../SiteReliability/SiteReliabilityGuardian'));
const ServiceLevelObjectivesDashboard = lazy(() => import('../SLO/ServiceLevelObjectivesDashboard'));
const ServiceLevelObjectivesClassic = lazy(() => import('../SLO/ServiceLevelObjectivesClassic'));
const EnhancedComplianceMetrics = lazy(() => import('../compliance/EnhancedComplianceMetricsOptimized'));
const ComplianceMetricsFallback = lazy(() => import('../compliance/ComplianceMetricsFallback'));
const RiskAssessmentDashboard = lazy(() => import('../Risk/RiskAssessmentDashboard'));
const SecurityOverviewDashboard = lazy(() =>
  import('../Security/SecurityOverviewDashboardOptimized').catch(error => {
    console.error('Failed to load SecurityOverviewDashboard:', error);
    // Return a fallback component
    return {
      default: () => (
        <div className="p-8 text-center">
          <div className="flex flex-col items-center gap-4">
            <AlertTriangle className="w-12 h-12 text-red-500" />
            <h3 className="text-lg font-semibold text-text">Security Overview Module Failed to Load</h3>
            <p className="text-text-secondary max-w-md">
              There was an error loading the security overview module. This may be due to a build issue or network problem.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              Reload Page
            </button>
          </div>
        </div>
      )
    };
  })
);
const RegulatoryFrameworkDashboard = lazy(() => import('../compliance/RegulatoryFrameworkDashboard'));
const GDPRCommandCenter = lazy(() => import('../GDPR/GDPRCommandCenter'));

// Import the missing GDPR dashboard components
const OrganizationalMappingDashboard = lazy(() => import('../GDPR/OrganizationalMappingDashboard'));
const PoliciesManagementDashboard = lazy(() => import('../GDPR/PoliciesManagementDashboard'));
const ConsentManagementDashboard = lazy(() => import('../GDPR/ConsentManagementDashboard'));
const ImpactAssessmentDashboard = lazy(() => import('../GDPR/ImpactAssessmentDashboard'));

// Import services for initialization
import { RiskAssessmentService } from '../../services/riskAssessmentService';
import { RegulatoryFrameworkService } from '../../services/regulatoryFrameworkService';
import { BranchDetectionService } from '../../services/branchDetectionService';
import { DataSubjectRequestService } from '../../services/dataSubjectRequestService';

// Import UI components
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Import async error handling utilities
import { useErrorHandler } from '../../hooks/useAsyncError';

// Lazy load additional components - let errors bubble up to error boundaries
const ComplianceByPolicy = lazy(() => import('../ComplianceByPolicy'));
const ViolationsSummary = lazy(() => import('../ViolationsSummary'));
const Analytics = lazy(() => import('../Analytics'));
const PolicyDistributionChart = lazy(() => import('../PolicyDistributionChart'));

import {
  Shield,
  Target,
  Archive,
  BarChart3,
  AlertTriangle,
  Scale,
  Database,
  Download,
  CheckCircle,
  Clock,
  FileText,
  HelpCircle,
  TrendingUp,
  Users,
  Calendar,
  RefreshCw
} from 'lucide-react';

// Enhanced loading fallback component for lazy-loaded components
const LoadingFallback: React.FC<{ componentName?: string }> = ({ componentName = 'Component' }) => (
  <div className="animate-pulse bg-surface rounded-xl h-[400px] flex flex-col items-center justify-center border border-border">
    <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"></div>
    <div className="text-text-secondary text-lg font-medium mb-2">Loading {componentName}...</div>
    <div className="text-text-secondary text-sm">Please wait while we load the dashboard component</div>
  </div>
);







// GDPR Data Types
interface DataSubjectRequest {
  id: string;
  type: 'erasure' | 'portability' | 'access' | 'rectification' | 'restriction' | 'objection';
  email: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected' | 'overdue';
  submittedAt: Date;
  dueDate: Date;
  progress: number;
  priority: 'low' | 'medium' | 'high' | 'urgent' | 'critical';
  assignee: string;
  description: string;
}

interface SecurityIncident {
  id: string;
  title: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  detectedAt: Date;
  affectedUsers: number;
  status: 'detected' | 'investigating' | 'contained' | 'resolved';
  category: 'data_breach' | 'unauthorized_access' | 'malware' | 'phishing' | 'system_failure' | 'network_attack';
  description: string;
  assignee: string;
  estimatedResolution: Date;
}

interface ConsentCategory {
  id: string;
  name: string;
  description: string;
  totalUsers: number;
  consentedUsers: number;
  consentRate: number;
  trend: 'up' | 'down' | 'stable';
  weeklyChange: number;
  lastUpdated: Date;
}

interface ImpactAssessment {
  id: string;
  title: string;
  type: 'DPIA' | 'LIA' | 'TIA';
  status: 'draft' | 'review' | 'approved' | 'rejected' | 'in_progress';
  riskScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  completionPercentage: number;
  assignee: string;
  reviewer: string;
  createdAt: Date;
  dueDate: Date;
  mitigationActions: number;
  completedActions: number;
}

// Generate dummy data for GDPR components
const generateDataSubjectRequests = (): DataSubjectRequest[] => [
  {
    id: 'dsr-001',
    type: 'erasure',
    email: '<EMAIL>',
    status: 'in_progress',
    submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
    progress: 15,
    priority: 'urgent',
    assignee: 'Privacy Team',
    description: 'Request for complete data erasure under GDPR Article 17'
  },
  {
    id: 'dsr-002',
    type: 'portability',
    email: '<EMAIL>',
    status: 'in_progress',
    submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
    progress: 65,
    priority: 'medium',
    assignee: 'Data Team',
    description: 'Data portability request for customer profile and transaction history'
  },
  {
    id: 'dsr-003',
    type: 'access',
    email: '<EMAIL>',
    status: 'pending',
    submittedAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 26 * 24 * 60 * 60 * 1000),
    progress: 0,
    priority: 'low',
    assignee: 'Support Team',
    description: 'Subject access request for personal data processing activities'
  },
  {
    id: 'dsr-004',
    type: 'rectification',
    email: '<EMAIL>',
    status: 'completed',
    submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 27 * 24 * 60 * 60 * 1000),
    progress: 100,
    priority: 'medium',
    assignee: 'Data Team',
    description: 'Request to correct inaccurate personal information'
  },
  {
    id: 'dsr-005',
    type: 'erasure',
    email: '<EMAIL>',
    status: 'pending',
    submittedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 24 * 24 * 60 * 60 * 1000),
    progress: 0,
    priority: 'high',
    assignee: 'Privacy Team',
    description: 'Right to be forgotten request for marketing data'
  },
  {
    id: 'dsr-006',
    type: 'restriction',
    email: '<EMAIL>',
    status: 'in_progress',
    submittedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 26 * 24 * 60 * 60 * 1000),
    progress: 40,
    priority: 'urgent',
    assignee: 'Legal Team',
    description: 'Request to restrict processing of personal data pending legal review'
  },
  {
    id: 'dsr-007',
    type: 'objection',
    email: '<EMAIL>',
    status: 'completed',
    submittedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    progress: 100,
    priority: 'medium',
    assignee: 'Marketing Team',
    description: 'Objection to direct marketing communications'
  },
  {
    id: 'dsr-008',
    type: 'access',
    email: '<EMAIL>',
    status: 'overdue',
    submittedAt: new Date(Date.now() - 35 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    progress: 80,
    priority: 'critical',
    assignee: 'Privacy Team',
    description: 'Comprehensive access request for all personal data across systems'
  },
  {
    id: 'dsr-009',
    type: 'portability',
    email: '<EMAIL>',
    status: 'in_progress',
    submittedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
    progress: 25,
    priority: 'medium',
    assignee: 'Technical Team',
    description: 'Data portability request for migration to new service provider'
  },
  {
    id: 'dsr-010',
    type: 'rectification',
    email: '<EMAIL>',
    status: 'completed',
    submittedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    progress: 100,
    priority: 'low',
    assignee: 'Customer Service',
    description: 'Correction of billing address and contact information'
  },
  {
    id: 'dsr-011',
    type: 'access',
    email: '<EMAIL>',
    status: 'in_progress',
    submittedAt: new Date(Date.now() - 18 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 28 * 24 * 60 * 60 * 1000),
    progress: 55,
    priority: 'medium',
    assignee: 'Privacy Team',
    description: 'Access request for transaction history and profile data'
  },
  {
    id: 'dsr-012',
    type: 'erasure',
    email: '<EMAIL>',
    status: 'pending',
    submittedAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 29 * 24 * 60 * 60 * 1000),
    progress: 0,
    priority: 'urgent',
    assignee: 'Privacy Team',
    description: 'Complete data deletion following account termination'
  }
];

const generateSecurityIncidents = (): SecurityIncident[] => [
  {
    id: 'inc-001',
    title: 'Suspicious Login Activity Detected',
    severity: 'medium',
    detectedAt: new Date(Date.now() - 3 * 60 * 60 * 1000),
    affectedUsers: 12,
    status: 'investigating',
    category: 'unauthorized_access',
    description: 'Multiple failed login attempts from unusual geographic locations',
    assignee: 'Security Operations',
    estimatedResolution: new Date(Date.now() + 6 * 60 * 60 * 1000)
  },
  {
    id: 'inc-002',
    title: 'Phishing Email Campaign',
    severity: 'high',
    detectedAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
    affectedUsers: 247,
    status: 'contained',
    category: 'phishing',
    description: 'Targeted phishing emails sent to employees with malicious attachments',
    assignee: 'Incident Response Team',
    estimatedResolution: new Date(Date.now() + 2 * 60 * 60 * 1000)
  },
  {
    id: 'inc-003',
    title: 'Database Access Anomaly',
    severity: 'critical',
    detectedAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
    affectedUsers: 1543,
    status: 'resolved',
    category: 'data_breach',
    description: 'Unauthorized access attempt to customer database detected and blocked',
    assignee: 'Security Team Lead',
    estimatedResolution: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 'inc-004',
    title: 'Malware Detection on Endpoint',
    severity: 'high',
    detectedAt: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000),
    affectedUsers: 1,
    status: 'resolved',
    category: 'malware',
    description: 'Trojan malware detected on employee workstation, system isolated and cleaned',
    assignee: 'Endpoint Security Team',
    estimatedResolution: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'inc-005',
    title: 'DDoS Attack Attempt',
    severity: 'medium',
    detectedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
    affectedUsers: 0,
    status: 'resolved',
    category: 'network_attack',
    description: 'Distributed denial of service attack successfully mitigated by CDN',
    assignee: 'Network Security Team',
    estimatedResolution: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000 + 2 * 60 * 60 * 1000)
  }
];

const generateConsentCategories = (): ConsentCategory[] => [
  {
    id: 'consent-001',
    name: 'Marketing Communications',
    description: 'Email marketing, newsletters, and promotional content',
    totalUsers: 14720,
    consentedUsers: 12847,
    consentRate: 87.3,
    trend: 'up',
    weeklyChange: 3.2,
    lastUpdated: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 'consent-002',
    name: 'Analytics & Performance',
    description: 'Website analytics, performance tracking, and user behavior analysis',
    totalUsers: 14720,
    consentedUsers: 10621,
    consentRate: 72.1,
    trend: 'down',
    weeklyChange: -1.1,
    lastUpdated: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    id: 'consent-003',
    name: 'Essential Functions',
    description: 'Core platform functionality and security features',
    totalUsers: 14720,
    consentedUsers: 13956,
    consentRate: 94.8,
    trend: 'stable',
    weeklyChange: 0.8,
    lastUpdated: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 'consent-004',
    name: 'Third-party Integrations',
    description: 'Social media plugins, external analytics, and partner services',
    totalUsers: 14720,
    consentedUsers: 8932,
    consentRate: 60.7,
    trend: 'up',
    weeklyChange: 2.4,
    lastUpdated: new Date(Date.now() - 45 * 60 * 1000)
  }
];

const generateImpactAssessments = (): ImpactAssessment[] => [
  {
    id: 'ia-001',
    title: 'Customer Data Processing DPIA',
    type: 'DPIA',
    status: 'review',
    riskScore: 7.2,
    riskLevel: 'medium',
    completionPercentage: 85,
    assignee: 'Privacy Officer',
    reviewer: 'Legal Counsel',
    createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    mitigationActions: 12,
    completedActions: 8
  },
  {
    id: 'ia-002',
    title: 'AI-Powered Analytics Implementation',
    type: 'DPIA',
    status: 'draft',
    riskScore: 8.9,
    riskLevel: 'high',
    completionPercentage: 45,
    assignee: 'Data Protection Team',
    reviewer: 'Chief Privacy Officer',
    createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 21 * 24 * 60 * 60 * 1000),
    mitigationActions: 18,
    completedActions: 6
  },
  {
    id: 'ia-003',
    title: 'Employee Monitoring System',
    type: 'LIA',
    status: 'approved',
    riskScore: 6.1,
    riskLevel: 'medium',
    completionPercentage: 100,
    assignee: 'HR Privacy Lead',
    reviewer: 'Legal Team',
    createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    mitigationActions: 8,
    completedActions: 8
  },
  {
    id: 'ia-004',
    title: 'Cloud Migration Data Transfer',
    type: 'DPIA',
    status: 'in_progress',
    riskScore: 7.8,
    riskLevel: 'high',
    completionPercentage: 60,
    assignee: 'Cloud Security Team',
    reviewer: 'Data Protection Officer',
    createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000),
    mitigationActions: 15,
    completedActions: 9
  },
  {
    id: 'ia-005',
    title: 'Third-Party Vendor Integration',
    type: 'DPIA',
    status: 'draft',
    riskScore: 5.4,
    riskLevel: 'medium',
    completionPercentage: 25,
    assignee: 'Vendor Management',
    reviewer: 'Privacy Team',
    createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 25 * 24 * 60 * 60 * 1000),
    mitigationActions: 10,
    completedActions: 2
  },
  {
    id: 'ia-006',
    title: 'Biometric Authentication System',
    type: 'DPIA',
    status: 'review',
    riskScore: 9.2,
    riskLevel: 'high',
    completionPercentage: 90,
    assignee: 'Security Architecture',
    reviewer: 'Chief Privacy Officer',
    createdAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
    mitigationActions: 20,
    completedActions: 18
  },
  {
    id: 'ia-007',
    title: 'Customer Profiling Algorithm',
    type: 'DPIA',
    status: 'approved',
    riskScore: 6.7,
    riskLevel: 'medium',
    completionPercentage: 100,
    assignee: 'Data Science Team',
    reviewer: 'Ethics Committee',
    createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    dueDate: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
    mitigationActions: 14,
    completedActions: 14
  }
];

// Generate comprehensive audit trail data
const generateAuditTrail = () => [
  {
    id: 'audit-001',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Data Subject Request Processed',
    details: 'Completed erasure request <NAME_EMAIL>',
    system: 'GDPR Management System',
    riskLevel: 'low',
    outcome: 'success',
    affectedRecords: 1,
    category: 'data_subject_rights'
  },
  {
    id: 'audit-002',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Security Incident Resolved',
    details: 'Phishing email campaign contained and users notified',
    system: 'Security Operations Center',
    riskLevel: 'high',
    outcome: 'success',
    affectedRecords: 247,
    category: 'security_incident'
  },
  {
    id: 'audit-003',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Risk Assessment Updated',
    details: 'Updated mitigation measures for API security vulnerabilities',
    system: 'Risk Management Platform',
    riskLevel: 'medium',
    outcome: 'success',
    affectedRecords: 1,
    category: 'risk_management'
  },
  {
    id: 'audit-004',
    timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'DPIA Review Completed',
    details: 'Approved Data Protection Impact Assessment for cloud migration',
    system: 'Privacy Management System',
    riskLevel: 'high',
    outcome: 'approved',
    affectedRecords: 1,
    category: 'privacy_assessment'
  },
  {
    id: 'audit-005',
    timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Regulatory Framework Updated',
    details: 'Updated GDPR compliance requirements following regulatory guidance',
    system: 'Compliance Management System',
    riskLevel: 'medium',
    outcome: 'success',
    affectedRecords: 15,
    category: 'regulatory_compliance'
  },
  {
    id: 'audit-006',
    timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Compliance Audit Initiated',
    details: 'Started quarterly compliance audit for SOX requirements',
    system: 'Audit Management Platform',
    riskLevel: 'medium',
    outcome: 'in_progress',
    affectedRecords: 1,
    category: 'compliance_audit'
  },
  {
    id: 'audit-007',
    timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Access Control Modified',
    details: 'Updated user permissions for sensitive data access',
    system: 'Identity Management System',
    riskLevel: 'medium',
    outcome: 'success',
    affectedRecords: 12,
    category: 'access_control'
  },
  {
    id: 'audit-008',
    timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Consent Preferences Updated',
    details: 'Processed batch consent withdrawal requests',
    system: 'Consent Management Platform',
    riskLevel: 'low',
    outcome: 'success',
    affectedRecords: 45,
    category: 'consent_management'
  },
  {
    id: 'audit-009',
    timestamp: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Vulnerability Assessment',
    details: 'Completed security vulnerability scan of web applications',
    system: 'Security Assessment Tools',
    riskLevel: 'high',
    outcome: 'findings_identified',
    affectedRecords: 8,
    category: 'security_assessment'
  },
  {
    id: 'audit-010',
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
    user: '<EMAIL>',
    action: 'Data Retention Policy Applied',
    details: 'Automated data deletion based on retention policy',
    system: 'Data Lifecycle Management',
    riskLevel: 'low',
    outcome: 'success',
    affectedRecords: 1250,
    category: 'data_retention'
  }
];

// Generate comprehensive compliance metrics data
const generateComplianceMetricsData = () => ({
  overallScore: 94.2,
  frameworks: {
    gdpr: { score: 96.8, trend: 'up', change: 2.1 },
    sox: { score: 92.4, trend: 'stable', change: 0.3 },
    iso27001: { score: 89.7, trend: 'up', change: 4.2 },
    ccpa: { score: 94.1, trend: 'down', change: -1.2 },
    hipaa: { score: 91.3, trend: 'up', change: 1.8 }
  },
  policyAdherence: {
    total: 247,
    compliant: 231,
    nonCompliant: 12,
    underReview: 4,
    adherenceRate: 93.5
  },
  violations: {
    total: 18,
    resolved: 14,
    pending: 4,
    critical: 2,
    high: 6,
    medium: 8,
    low: 2
  },
  remediation: {
    averageTime: 4.2, // days
    onTime: 82.3, // percentage
    overdue: 3
  }
});

// Generate comprehensive risk overview data
const generateRiskOverviewData = () => ({
  totalRisks: 47,
  activeRisks: 32,
  resolvedRisks: 15,
  risksByLevel: {
    critical: 3,
    high: 8,
    medium: 18,
    low: 18
  },
  risksByCategory: {
    operational: 12,
    financial: 8,
    compliance: 11,
    technology: 10,
    strategic: 6
  },
  mitigationProgress: {
    total: 156,
    completed: 128,
    inProgress: 23,
    planned: 5,
    completionRate: 82.1
  },
  riskHeatMap: [
    { likelihood: 1, impact: 1, count: 2, level: 'low' },
    { likelihood: 1, impact: 2, count: 3, level: 'low' },
    { likelihood: 1, impact: 3, count: 1, level: 'low' },
    { likelihood: 2, impact: 1, count: 4, level: 'low' },
    { likelihood: 2, impact: 2, count: 6, level: 'medium' },
    { likelihood: 2, impact: 3, count: 5, level: 'medium' },
    { likelihood: 2, impact: 4, count: 3, level: 'medium' },
    { likelihood: 3, impact: 2, count: 4, level: 'medium' },
    { likelihood: 3, impact: 3, count: 7, level: 'high' },
    { likelihood: 3, impact: 4, count: 4, level: 'high' },
    { likelihood: 4, impact: 3, count: 3, level: 'high' },
    { likelihood: 4, impact: 4, count: 2, level: 'critical' },
    { likelihood: 5, impact: 4, count: 1, level: 'critical' },
    { likelihood: 5, impact: 5, count: 2, level: 'critical' }
  ],
  trends: {
    thisMonth: 32,
    lastMonth: 29,
    change: 10.3,
    trend: 'up'
  }
});

// Generate breach detection and monitoring data
const generateBreachMonitoringData = () => ({
  systemHealth: {
    overall: 98.7,
    dataLossPrevention: 99.2,
    accessMonitoring: 98.1,
    networkSecurity: 99.5,
    endpointProtection: 97.8
  },
  realTimeAlerts: {
    active: 3,
    resolved: 47,
    falsePositives: 12,
    criticalAlerts: 1
  },
  monitoringMetrics: {
    dataFlowsMonitored: 1247,
    accessAttemptsLogged: 45632,
    anomaliesDetected: 23,
    threatsBlocked: 156
  },
  complianceStatus: {
    gdprCompliant: true,
    breachNotificationReady: true,
    incidentResponseActive: true,
    dataInventoryComplete: 94.3
  }
});

const EnterpriseDashboard = React.memo(() => {
  const { currentPage, isEnterpriseDashboard } = useNavigation();
  const { fetchData, metrics, isLoading: isComplianceDataLoading } = useCompliance();
  const { mode } = useTheme();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeTab, setActiveTab] = useState<'site_reliability' | 'slo' | 'slo_classic' | 'compliance_metrics' | 'compliance_overview' | 'risk_assessment' | 'security_overview' | 'regulatory_framework' | 'data_protection'>('compliance_metrics');
  const [, setIsComplianceLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // Error handling - must be declared before being used in useEffect
  const { handleError, clearError } = useErrorHandler();

  // Determine if GDPR Command Center and Compliance Overview should be shown
  // These should only appear on the main Enterprise Dashboard view, not in sub-tabs
  const showGDPRCenter = isEnterpriseDashboard && activeTab === 'data_protection';
  const showComplianceOverview = isEnterpriseDashboard && activeTab === 'compliance_overview';

  console.log('🚀 EnterpriseDashboard component mounting/rendering', {
    currentPage,
    isEnterpriseDashboard,
    activeTab,
    showGDPRCenter,
    showComplianceOverview
  });

  // Global error handler for unhandled promise rejections and errors
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      console.error('Unhandled promise rejection in EnterpriseDashboard:', event.reason);
      event.preventDefault(); // Prevent the default browser behavior
      // Report to error handler if available
      if (handleError) {
        handleError(new Error(`Unhandled promise rejection: ${event.reason}`), 'unhandled-promise');
      }
    };

    const handleGlobalError = (event: ErrorEvent) => {
      console.error('Global error in EnterpriseDashboard:', event.error);
      // Don't handle errors that are already handled by React error boundaries
      if (event.error && event.error.name !== 'ChunkLoadError' && handleError) {
        handleError(event.error, 'global-error');
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleGlobalError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleGlobalError);
    };
  }, [handleError]);

  // Interactive handlers for compliance overview
  const handleComplianceCardClick = (cardType: 'compliant' | 'non_compliant' | 'pending') => {
    console.log(`Clicked on ${cardType} compliance card`);
    // In a real application, this could:
    // - Navigate to a detailed view
    // - Open a modal with more information
    // - Filter the data to show only items of this type
    // - Show a drill-down dashboard

    // For now, we'll just log and potentially show a notification
    if (cardType === 'compliant') {
      console.log('Showing compliant policies details...');
    } else if (cardType === 'non_compliant') {
      console.log('Showing non-compliant policies that need attention...');
    } else {
      console.log('Showing policies pending review...');
    }
  };

  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefreshData = async () => {
    try {
      setIsRefreshing(true);
      console.log('Refreshing compliance data...');
      await fetchData();
      console.log('Compliance data refreshed successfully');
    } catch (error) {
      console.error('Failed to refresh compliance data:', error);
      handleError(error as Error, 'compliance-data-refresh');
    } finally {
      setIsRefreshing(false);
    }
  };

  // GDPR dummy data state - Use lazy initialization to avoid blocking render
  const [dataSubjectRequests] = useState<DataSubjectRequest[]>(() => generateDataSubjectRequests());
  const [securityIncidents] = useState<SecurityIncident[]>(() => generateSecurityIncidents());
  const [consentCategories] = useState<ConsentCategory[]>(() => generateConsentCategories());
  const [impactAssessments] = useState<ImpactAssessment[]>(() => generateImpactAssessments());

  // Additional comprehensive data for enhanced dashboard functionality
  const [auditTrail] = useState(() => generateAuditTrail());
  const [complianceMetricsData] = useState(() => generateComplianceMetricsData());
  const [riskOverviewData] = useState(() => generateRiskOverviewData());
  const [breachMonitoringData] = useState(() => generateBreachMonitoringData());

  // Log comprehensive data loading for verification
  console.log('📊 Comprehensive Dashboard Data Loaded:', {
    dataSubjectRequests: dataSubjectRequests.length,
    securityIncidents: securityIncidents.length,
    consentCategories: consentCategories.length,
    impactAssessments: impactAssessments.length,
    auditTrail: auditTrail.length,
    complianceMetrics: complianceMetricsData.overallScore,
    riskOverview: riskOverviewData.totalRisks,
    breachMonitoring: breachMonitoringData.systemHealth.overall
  });

  // Compliance Dashboard Chart Data - Memoized to prevent recalculation
  const lineChartData = useMemo(() => ({
    labels: ['Nov-24', 'Dec-24', 'Jan-25', 'Feb-25', 'Mar-25', 'Apr-25', 'May-25', 'Jun-25'],
    datasets: [
      {
        label: 'Non-Compliant',
        data: [0.2, 1.8, 1.5, 1.0, 0.2, 1.8, 1.7, 2.8],
        borderColor: '#ef4444',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Pending Review',
        data: [1.8, 1.2, 0.9, 1.5, 2.0, 1.5, 0.5, 1.6],
        borderColor: '#f59e0b',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
      {
        label: 'Compliant',
        data: [0.2, 0.5, 0.5, 0.5, 2.4, 1.6, 1.1, 0.9],
        borderColor: '#10b981',
        backgroundColor: 'transparent',
        tension: 0,
        borderWidth: 1,
        pointRadius: 0,
      },
    ],
  }), []);

  // Chart options for compliance trends - Memoized to prevent recalculation
  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      mode: 'index' as const,
      intersect: false,
    },
    plugins: {
      legend: {
        display: false,
      },
      title: {
        display: true,
        text: 'Compliance Trends',
        align: 'center' as const,
        font: {
          size: 16,
          family: 'Arial, sans-serif',
          weight: 'normal' as const,
        },
        padding: {
          bottom: 30,
        },
        color: mode === 'dark' ? '#F8FAFC' : '#111827',
      },
    },
    scales: {
      y: {
        min: 0,
        max: 4,
        ticks: {
          stepSize: 1,
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 8,
        },
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          drawBorder: false,
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        border: {
          display: false,
        },
        title: {
          display: true,
          text: 'Consent Rate',
          font: {
            size: 12,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: { top: 0, bottom: 10 },
        },
      },
      x: {
        grid: {
          color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#E5E7EB',
          borderDash: [2, 2],
          lineWidth: 0.5,
        },
        ticks: {
          font: {
            size: 11,
            family: 'Arial, sans-serif',
            weight: 'normal' as const,
          },
          color: mode === 'dark' ? '#E2E8F0' : '#4B5563',
          padding: 5,
        },
        border: {
          display: false,
        },
      },
    },
  }), [mode]);

  // Optimized time updates - only update when component is visible
  useEffect(() => {
    let timer: NodeJS.Timeout;

    const updateTime = () => {
      try {
        if (document.visibilityState === 'visible') {
          setCurrentTime(new Date());
        }
      } catch (error) {
        handleError(error as Error, 'time-update');
      }
    };

    timer = setInterval(updateTime, 1000);

    const handleVisibilityChange = () => {
      try {
        if (document.visibilityState === 'visible') {
          setCurrentTime(new Date());
        }
      } catch (error) {
        handleError(error as Error, 'visibility-change');
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      try {
        clearInterval(timer);
        document.removeEventListener('visibilitychange', handleVisibilityChange);
      } catch (error) {
        console.error('Error in cleanup:', error);
      }
    };
  }, [handleError]);

  // Compliance data loading effect
  useEffect(() => {
    let mounted = true;

    const loadComplianceData = async () => {
      if (!isInitialLoad) return;

      try {
        setIsComplianceLoading(true);
        await fetchData();
      } catch (error) {
        console.error('Error loading compliance data:', error);
        handleError(error as Error, 'compliance-data-loading');
      } finally {
        if (mounted) {
          setIsComplianceLoading(false);
          setIsInitialLoad(false);
        }
      }
    };

    loadComplianceData();
    return () => {
      mounted = false;
    };
  }, [fetchData, isInitialLoad, handleError]);

  // Initialize all services asynchronously to prevent blocking
  useEffect(() => {
    const initializeServices = async () => {
      try {
        console.log('🚀 Initializing services...');

        // Initialize services in parallel for better performance
        const serviceInitializations = [
          { name: 'RiskAssessmentService', init: () => RiskAssessmentService.initialize() },
          { name: 'RegulatoryFrameworkService', init: () => RegulatoryFrameworkService.initialize() },
          { name: 'BranchDetectionService', init: () => BranchDetectionService.initialize() },
          { name: 'DataSubjectRequestService', init: () => DataSubjectRequestService.initialize() }
        ];

        // Use Promise.allSettled to initialize all services without blocking on failures
        const results = await Promise.allSettled(
          serviceInitializations.map(async ({ name, init }) => {
            try {
              await Promise.resolve(init());
              console.log(`✅ ${name} initialized`);
              return { name, success: true };
            } catch (error) {
              console.error(`❌ Error initializing ${name}:`, error);
              handleError(error as Error, `service-initialization-${name}`);
              return { name, success: false, error };
            }
          })
        );

        const successCount = results.filter(r => r.status === 'fulfilled' && r.value.success).length;
        console.log(`🎉 Service initialization complete: ${successCount}/${serviceInitializations.length} services initialized`);

        // If no services initialized successfully, this might be a critical error
        if (successCount === 0) {
          handleError(new Error('All service initializations failed'), 'critical-service-initialization');
        }
      } catch (error) {
        console.error('Critical error during service initialization:', error);
        handleError(error as Error, 'service-initialization-critical');
      }
    };

    initializeServices();
  }, [handleError]);

  const handleTabClick = (tabName: 'site_reliability' | 'slo' | 'slo_classic' | 'compliance_metrics' | 'compliance_overview' | 'risk_assessment' | 'security_overview' | 'regulatory_framework' | 'data_protection') => {
    try {
      setActiveTab(tabName);
      clearError(); // Clear any previous errors when switching tabs
    } catch (error) {
      handleError(error as Error, 'tab-navigation');
    }
  };



  // Chart data processing removed - using direct data from useCompliance hook



  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Chart data logging removed for performance

  return (
    <div className="flex-1 p-8 bg-background min-h-screen">
      {/* Premium Enterprise Header with Advanced Branding */}
      <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-surface via-card to-surface'} rounded-2xl p-8 mb-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'} overflow-hidden`}>
        {/* Premium Background Pattern - Only in light mode */}
        {mode === 'light' && (
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20"></div>
            <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-radial from-primary/10 to-transparent"></div>
          </div>
        )}

        {/* Premium Content */}
        <div className="relative z-10">
          <div className="flex justify-between items-start mb-6">
            <div className="flex-1">
              <div className="flex items-center gap-4 mb-4">
                <div className="relative">
                  <div className={`w-4 h-4 ${mode === 'dark' ? 'bg-text-secondary' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse shadow-lg shadow-primary/50'}`}></div>
                  {mode === 'light' && (
                    <div className="absolute inset-0 w-4 h-4 bg-primary rounded-full animate-ping opacity-20"></div>
                  )}
                </div>
                <div className="flex items-center gap-2">
                  <h2 className={`${mode === 'dark' ? 'text-text-secondary' : 'text-primary'} text-sm font-bold tracking-wider uppercase`}>Enterprise Compliance Platform</h2>
                  <div className={`px-3 py-1 ${mode === 'dark' ? 'bg-card border border-border text-text-secondary' : 'bg-primary/20 text-primary border border-primary/30'} text-xs font-bold rounded-full`}>
                    Premium
                  </div>
                </div>
              </div>

              <h1 className={`text-5xl font-bold ${mode === 'dark' ? 'text-text' : 'bg-gradient-to-r from-text via-primary to-secondary bg-clip-text text-transparent'} mb-4 leading-tight`}>
                Compliance Command Center
              </h1>

              <div className="flex items-center gap-6 text-sm">
                <div className="flex items-center gap-2">
                  <Calendar className="w-4 h-4 text-text-secondary" />
                  <span className="text-text-secondary">{formatDate(currentTime)}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="w-4 h-4 text-text-secondary" />
                  <span className="text-text-secondary">{formatTime(currentTime)}</span>
                </div>
                <div className={`flex items-center gap-2 px-3 py-1 bg-surface border border-border rounded-lg`}>
                  <div className={`w-2 h-2 bg-text-secondary rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                  <span className="text-text-secondary font-semibold">Real-time Monitoring</span>
                </div>
              </div>
            </div>

            {/* Premium Metrics Dashboard */}
            <div className="flex items-center gap-8">
              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Overall Score</div>
                <div className="relative">
                  <div className={`text-4xl font-bold ${mode === 'dark' ? 'text-text' : 'text-primary'} mb-1`}>98.5%</div>
                  {mode === 'light' && (
                    <div className="absolute -inset-2 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-lg -z-10"></div>
                  )}
                </div>
                <div className={`flex items-center justify-center gap-1 text-xs text-text-secondary`}>
                  <TrendingUp className="w-3 h-3" />
                  <span>+2.3% this month</span>
                </div>
              </div>

              <div className="w-px h-16 bg-border"></div>

              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Active Frameworks</div>
                <div className="text-2xl font-bold text-text mb-1">12</div>
                <div className="text-xs text-text-secondary">Compliance Standards</div>
              </div>

              <div className="w-px h-16 bg-border"></div>

              <div className="text-center">
                <div className="text-xs text-text-secondary uppercase tracking-wider mb-2">Risk Level</div>
                <div className="text-2xl font-bold text-text mb-1">Low</div>
                <div className="text-xs text-text-secondary">3 Open Issues</div>
              </div>
            </div>
          </div>

          {/* Premium Action Bar */}
          <div className="flex items-center justify-between pt-6 border-t border-border/50">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Users className="w-4 h-4" />
                <span>Enterprise License</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <Shield className="w-4 h-4" />
                <span>SOC 2 Certified</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-text-secondary">
                <CheckCircle className="w-4 h-4" />
                <span>ISO 27001 Compliant</span>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <button className={`flex items-center gap-2 px-4 py-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:shadow-lg hover:scale-105'}`}>
                <HelpCircle className="w-4 h-4" />
                <span className="text-sm font-semibold">Help Center</span>
              </button>
              <button className={`flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:shadow-lg hover:scale-105'}`}>
                <Download className="w-4 h-4" />
                <span className="text-sm font-semibold">Export Dashboard</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className={`bg-surface rounded-xl ${mode === 'dark' ? '' : 'shadow-2xl'} border border-border overflow-hidden`}>
        {/* Premium Enterprise Navigation with Advanced Animations */}
        <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-r from-surface via-card to-surface'} border-b border-border ${mode === 'dark' ? '' : 'shadow-lg'} overflow-hidden`}>
          {/* Premium Navigation Background - Only in light mode */}
          {mode === 'light' && (
            <div className="absolute inset-0 opacity-3">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/5 via-transparent to-secondary/5"></div>
            </div>
          )}

          <div className="relative z-10 px-8 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h3 className="text-3xl font-bold text-text mb-3">
                  Compliance Operations Center
                </h3>
                <p className="text-text-secondary text-base">Advanced enterprise compliance monitoring and management platform</p>
              </div>

              <div className="flex items-center gap-6">
                <div className="flex items-center gap-3 px-4 py-3 bg-surface border border-border rounded-xl">
                  <div className="relative">
                    <div className={`w-3 h-3 bg-text-secondary rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    {mode === 'light' && (
                      <div className="absolute inset-0 w-3 h-3 bg-primary rounded-full animate-ping opacity-20"></div>
                    )}
                  </div>
                  <span className="text-sm font-bold text-text-secondary">All Systems Operational</span>
                </div>

                <div className="text-right">
                  <div className="text-xs text-text-secondary uppercase tracking-wider mb-1">Platform Status</div>
                  <div className="flex items-center gap-2">
                    <CheckCircle className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-bold text-text-secondary">Enterprise Ready</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Premium Navigation Grid with Advanced Interactions */}
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Premium GDPR & Data Protection - FLAGSHIP FEATURE */}
              <div className="space-y-6 lg:col-span-2">
                <div className="relative">
                  {/* Premium Header with Gradient Background - Only in light mode */}
                  {mode === 'light' && (
                    <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent rounded-2xl"></div>
                  )}
                  <div className="relative p-6 rounded-2xl border border-border">
                    <div className="flex items-center gap-4 mb-4">
                      <div className="relative">
                        <div className="p-3 bg-surface rounded-xl">
                          <Database className="w-8 h-8 text-text-secondary" />
                        </div>
                        <div className="absolute -top-1 -right-1 w-4 h-4 bg-text-secondary rounded-full flex items-center justify-center">
                          <span className="text-xs font-bold text-background">1</span>
                        </div>
                      </div>
                      <div className="flex-1">
                        <h4 className="text-2xl font-bold text-text">
                          GDPR Data Protection Suite
                        </h4>
                        <p className="text-text-secondary">Advanced privacy management and compliance automation</p>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="px-4 py-2 bg-surface border border-border text-text-secondary text-sm font-bold rounded-full">
                          FLAGSHIP
                        </div>
                        <div className="px-3 py-1 bg-surface border border-border text-text-secondary text-xs font-bold rounded-full">
                          AI-POWERED
                        </div>
                      </div>
                    </div>

                    {/* Premium Feature Highlights */}
                    <div className="grid grid-cols-2 gap-4 mb-6">
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <CheckCircle className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Automated Data Mapping</div>
                          <div className="text-xs text-text-secondary">AI-powered discovery</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <Shield className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Breach Detection</div>
                          <div className="text-xs text-text-secondary">Real-time monitoring</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <Users className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Consent Management</div>
                          <div className="text-xs text-text-secondary">Granular controls</div>
                        </div>
                      </div>
                      <div className="flex items-center gap-3 p-3 bg-surface/50 rounded-lg">
                        <FileText className="w-5 h-5 text-text-secondary" />
                        <div>
                          <div className="text-sm font-semibold text-text">Impact Assessments</div>
                          <div className="text-xs text-text-secondary">Automated PIAs</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Data Protection Tab Button */}
                <button
                  onClick={() => handleTabClick('data_protection')}
                  className={`group relative w-full p-6 rounded-lg text-left transition-all duration-300 ${activeTab === 'data_protection'
                    ? 'bg-surface border border-border text-text'
                    : 'bg-card border border-border text-text hover:bg-surface'
                    }`}
                >

                  <div className="relative z-10">
                    <div className="flex items-center gap-4 mb-4">
                      <div className={`p-3 rounded-lg transition-all duration-300 ${activeTab === 'data_protection'
                        ? 'bg-text/10 text-text'
                        : 'bg-text/5 text-text group-hover:bg-text/10'
                      }`}>
                        <Database className="w-6 h-6" />
                      </div>

                      <div className="flex-1">
                        <h3 className="text-xl font-semibold mb-1 text-text">
                          GDPR Compliance Center
                        </h3>
                        <p className="text-text-secondary text-sm">
                          Enterprise-grade data protection with AI-powered automation, real-time monitoring, and comprehensive compliance reporting.
                        </p>
                      </div>

                      {activeTab === 'data_protection' && (
                        <div className="w-2 h-2 bg-text/40 rounded-full"></div>
                      )}
                    </div>

                    {/* Feature Tags */}
                    <div className="flex flex-wrap gap-2">
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        GDPR Article 30 Compliant
                      </span>
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        <TrendingUp className="w-3 h-3" />
                        Real-time Analytics
                      </span>
                      <span className="inline-flex items-center gap-2 px-3 py-1 bg-text/5 text-text-secondary text-xs rounded border border-border">
                        <Shield className="w-3 h-3" />
                        Enterprise Security
                      </span>
                    </div>
                  </div>
                </button>
              </div>

              {/* Compliance & Risk Management */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-6">
                  <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                    <Scale className={`w-5 h-5 ${mode === 'dark' ? 'text-text-secondary' : 'text-secondary'}`} />
                  </div>
                  <div>
                    <h4 className="text-base font-bold text-text">Compliance & Risk</h4>
                    <p className="text-sm text-text-secondary">Regulatory oversight</p>
                  </div>
                </div>

                <button
                  onClick={() => handleTabClick('compliance_overview')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'compliance_overview'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'compliance_overview'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <BarChart3 className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Compliance Overview</span>
                      <span className="text-xs text-text-secondary">Comprehensive Dashboard</span>
                    </div>
                    {activeTab === 'compliance_overview' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('compliance_metrics')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'compliance_metrics'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'compliance_metrics'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <BarChart3 className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Compliance Metrics</span>
                      <span className="text-xs text-text-secondary">Interactive Analytics</span>
                    </div>
                    {activeTab === 'compliance_metrics' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('risk_assessment')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'risk_assessment'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'risk_assessment'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <AlertTriangle className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Risk Assessment</span>
                      <span className="text-xs text-text-secondary">Risk & Audit Trail</span>
                    </div>
                    {activeTab === 'risk_assessment' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('regulatory_framework')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'regulatory_framework'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-secondary/20 via-secondary/15 to-secondary/10 border-2 border-secondary/40 text-text shadow-xl shadow-secondary/20 ring-1 ring-secondary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-secondary/40 hover:shadow-xl hover:shadow-secondary/10 hover:ring-1 hover:ring-secondary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'regulatory_framework'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-secondary/20 text-secondary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-secondary/15 group-hover:text-secondary'
                    }`}>
                      <Scale className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Regulatory Framework</span>
                      <span className="text-xs text-text-secondary">Multi-Framework</span>
                    </div>
                    {activeTab === 'regulatory_framework' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-secondary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>
              </div>

              {/* Security & Infrastructure */}
              <div className="space-y-4">
                <div className="flex items-center gap-3 mb-6">
                  <div className="p-2 bg-accent-purple/10 rounded-lg">
                    <Shield className="w-5 h-5 text-accent-purple" />
                  </div>
                  <div>
                    <h4 className="text-base font-bold text-text">Security & Infrastructure</h4>
                    <p className="text-sm text-text-secondary">Threat monitoring & reliability</p>
                  </div>
                </div>

                <button
                  onClick={() => handleTabClick('security_overview')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'security_overview'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-accent-purple/20 via-accent-purple/15 to-accent-purple/10 border-2 border-accent-purple/40 text-text shadow-xl shadow-accent-purple/20 ring-1 ring-accent-purple/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-accent-purple/40 hover:shadow-xl hover:shadow-accent-purple/10 hover:ring-1 hover:ring-accent-purple/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'security_overview'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-accent-purple/20 text-accent-purple'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-accent-purple/15 group-hover:text-accent-purple'
                    }`}>
                      <Shield className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Security Overview</span>
                      <span className="text-xs text-text-secondary">Threat Detection & Response</span>
                    </div>
                    {activeTab === 'security_overview' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-accent-purple'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('site_reliability')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'site_reliability'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'site_reliability'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Shield className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">Site Reliability</span>
                      <span className="text-xs text-text-secondary">Guardian Dashboard</span>
                    </div>
                    {activeTab === 'site_reliability' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('slo')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'slo'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'slo'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Target className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">SLO Dashboard</span>
                      <span className="text-xs text-text-secondary">Service Level Objectives</span>
                    </div>
                    {activeTab === 'slo' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>

                <button
                  onClick={() => handleTabClick('slo_classic')}
                  className={`group w-full p-4 rounded-xl text-left transition-all duration-300 ${mode === 'dark' ? '' : 'transform hover:scale-[1.02] hover:-translate-y-0.5'} ${activeTab === 'slo_classic'
                    ? mode === 'dark'
                      ? 'bg-card border-2 border-text/20 text-text'
                      : 'bg-gradient-to-br from-primary/20 via-primary/15 to-primary/10 border-2 border-primary/40 text-text shadow-xl shadow-primary/20 ring-1 ring-primary/20'
                    : mode === 'dark'
                      ? 'bg-card border border-border text-text hover:bg-surface'
                      : 'bg-card border border-border text-text hover:bg-surface hover:border-primary/40 hover:shadow-xl hover:shadow-primary/10 hover:ring-1 hover:ring-primary/10'
                    }`}
                >
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`p-2 rounded-lg transition-all duration-300 ${activeTab === 'slo_classic'
                      ? mode === 'dark' ? 'bg-text/10 text-text' : 'bg-primary/20 text-primary'
                      : mode === 'dark' ? 'bg-text/5 text-text group-hover:bg-text/10' : 'bg-text/5 text-text group-hover:bg-primary/15 group-hover:text-primary'
                    }`}>
                      <Archive className="w-5 h-5" />
                    </div>
                    <div className="flex-1">
                      <span className="text-base font-bold block">SLO Classic</span>
                      <span className="text-xs text-text-secondary">Legacy SLA Management</span>
                    </div>
                    {activeTab === 'slo_classic' && (
                      <div className={`w-2 h-2 ${mode === 'dark' ? 'bg-text/40' : 'bg-primary'} rounded-full ${mode === 'dark' ? '' : 'animate-pulse'}`}></div>
                    )}
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Professional GDPR Dashboard Content */}
        <div className="bg-background min-h-[700px] p-8 rounded-b-xl relative overflow-hidden">
          {/* Subtle background pattern for enterprise feel */}
          <div className="absolute inset-0 opacity-5">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 via-transparent to-secondary/10"></div>
          </div>

          {/* Tab-Specific Content */}
          <div className="relative z-10">
            {activeTab === 'data_protection' && (
              <div className="space-y-8">
                {/* Data Protection Tab Content - GDPR Command Center is now in main dashboard */}
                <div className={`bg-card rounded-xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="text-center">
                    <div className="mb-4">
                      <Shield className="w-16 h-16 text-primary mx-auto mb-4" />
                    </div>
                    <h3 className="text-2xl font-bold text-text mb-2">GDPR Compliance Center</h3>
                    <p className="text-text-secondary mb-6">
                      The GDPR Command Center is now integrated into the main dashboard above for better visibility and accessibility.
                      Scroll up to access all GDPR compliance features including data subject requests, consent management, and impact assessments.
                    </p>
                    <div className="flex justify-center">
                      <button
                        onClick={() => {
                          // Scroll to the GDPR Command Center section
                          const gdprSection = document.querySelector('[data-testid="gdpr-command-center"]');
                          if (gdprSection) {
                            gdprSection.scrollIntoView({ behavior: 'smooth', block: 'start' });
                          } else {
                            // Fallback: scroll to top of compliance overview
                            window.scrollTo({ top: 0, behavior: 'smooth' });
                          }
                        }}
                        className="flex items-center gap-2 px-6 py-3 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors duration-200"
                      >
                        <TrendingUp className="w-4 h-4" />
                        Go to GDPR Command Center
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'compliance_metrics' && (
              <div className="space-y-6">
                {/* Compliance Metrics Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-secondary/20 rounded-lg">
                        <BarChart3 className="w-6 h-6 text-secondary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Compliance Metrics & Analytics</h2>
                        <p className="text-text-secondary">Interactive dashboards and KPI tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-colors">
                      <Download className="w-4 h-4" />
                      Export Report
                    </button>
                  </div>
                </div>

                {/* Enhanced Compliance Metrics Component with Fallback - Only visible when Enterprise Dashboard is active */}
                {showComplianceOverview && (
                  <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                    <ErrorBoundary
                      type="section"
                      fallback={
                        <Suspense fallback={<LoadingFallback componentName="Compliance Metrics Fallback" />}>
                          <ComplianceMetricsFallback
                            onRetry={() => window.location.reload()}
                          />
                        </Suspense>
                      }
                      onError={(error, errorInfo) => {
                        console.error('Enhanced Compliance Metrics Error:', error, errorInfo);
                        handleError(error, 'enhanced-compliance-metrics');
                      }}
                    >
                      <Suspense fallback={<LoadingFallback componentName="Compliance Metrics" />}>
                        <EnhancedComplianceMetrics />
                      </Suspense>
                    </ErrorBoundary>
                  </div>
                )}

                {/* Comprehensive Data Summary */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-card rounded-lg p-6 border border-border">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-text">Data Subject Requests</h3>
                      <Users className="w-5 h-5 text-primary" />
                    </div>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-text">{dataSubjectRequests.length}</div>
                      <div className="text-sm text-text-secondary">
                        {dataSubjectRequests.filter(r => r.status === 'pending').length} pending, {' '}
                        {dataSubjectRequests.filter(r => r.status === 'in_progress').length} in progress
                      </div>
                    </div>
                  </div>

                  <div className="bg-card rounded-lg p-6 border border-border">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-text">Security Incidents</h3>
                      <Shield className="w-5 h-5 text-red-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-text">{securityIncidents.length}</div>
                      <div className="text-sm text-text-secondary">
                        {securityIncidents.filter(i => i.status === 'resolved').length} resolved, {' '}
                        {securityIncidents.filter(i => i.severity === 'critical').length} critical
                      </div>
                    </div>
                  </div>

                  <div className="bg-card rounded-lg p-6 border border-border">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-text">Risk Assessments</h3>
                      <AlertTriangle className="w-5 h-5 text-orange-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-text">{riskOverviewData.totalRisks}</div>
                      <div className="text-sm text-text-secondary">
                        {riskOverviewData.risksByLevel.critical} critical, {' '}
                        {riskOverviewData.risksByLevel.high} high risk
                      </div>
                    </div>
                  </div>

                  <div className="bg-card rounded-lg p-6 border border-border">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="text-lg font-semibold text-text">Compliance Score</h3>
                      <TrendingUp className="w-5 h-5 text-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="text-3xl font-bold text-text">{complianceMetricsData.overallScore}%</div>
                      <div className="text-sm text-text-secondary">
                        {complianceMetricsData.policyAdherence.adherenceRate}% policy adherence
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'risk_assessment' && (
              <div className="space-y-6">
                {/* Risk Assessment Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-accent-purple/20 rounded-lg">
                        <AlertTriangle className="w-6 h-6 text-accent-purple" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Risk Assessment & Management</h2>
                        <p className="text-text-secondary">Risk matrices, assessments, and mitigation tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-accent-purple hover:bg-accent-purple-hover text-white rounded-lg transition-colors">
                      <AlertTriangle className="w-4 h-4" />
                      New Assessment
                    </button>
                  </div>
                </div>

                {/* Risk Assessment Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallbackTitle="Risk Assessment Unavailable"
                    fallbackMessage="The risk assessment dashboard is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                    onError={(error, errorInfo) => {
                      console.error('Risk Assessment Dashboard Error:', error, errorInfo);
                      handleError(error, 'risk-assessment-dashboard');
                    }}
                  >
                    <Suspense fallback={<LoadingFallback componentName="Risk Assessment" />}>
                      <RiskAssessmentDashboard />
                    </Suspense>
                  </ErrorBoundary>
                </div>

                {/* Risk Overview Summary */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="bg-card rounded-lg p-6 border border-border">
                    <h3 className="text-lg font-semibold text-text mb-4">Risk Distribution</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Critical</span>
                        <span className="text-sm font-medium text-red-600">{riskOverviewData.risksByLevel.critical}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">High</span>
                        <span className="text-sm font-medium text-orange-600">{riskOverviewData.risksByLevel.high}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Medium</span>
                        <span className="text-sm font-medium text-yellow-600">{riskOverviewData.risksByLevel.medium}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Low</span>
                        <span className="text-sm font-medium text-green-600">{riskOverviewData.risksByLevel.low}</span>
                      </div>
                    </div>
                  </div>

                  <div className="bg-card rounded-lg p-6 border border-border">
                    <h3 className="text-lg font-semibold text-text mb-4">Mitigation Progress</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Completed</span>
                        <span className="text-sm font-medium text-green-600">{riskOverviewData.mitigationProgress.completed}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">In Progress</span>
                        <span className="text-sm font-medium text-blue-600">{riskOverviewData.mitigationProgress.inProgress}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Planned</span>
                        <span className="text-sm font-medium text-gray-600">{riskOverviewData.mitigationProgress.planned}</span>
                      </div>
                      <div className="pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-text">Completion Rate</span>
                          <span className="text-sm font-bold text-primary">{riskOverviewData.mitigationProgress.completionRate}%</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-card rounded-lg p-6 border border-border">
                    <h3 className="text-lg font-semibold text-text mb-4">Risk Trends</h3>
                    <div className="space-y-3">
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">This Month</span>
                        <span className="text-sm font-medium text-text">{riskOverviewData.trends.thisMonth}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-text-secondary">Last Month</span>
                        <span className="text-sm font-medium text-text">{riskOverviewData.trends.lastMonth}</span>
                      </div>
                      <div className="pt-2 border-t border-border">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium text-text">Change</span>
                          <span className={`text-sm font-bold ${riskOverviewData.trends.trend === 'up' ? 'text-red-600' : 'text-green-600'}`}>
                            {riskOverviewData.trends.trend === 'up' ? '+' : '-'}{Math.abs(riskOverviewData.trends.change)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'regulatory_framework' && (
              <div className="space-y-6">
                {/* Regulatory Framework Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Scale className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Regulatory Framework Management</h2>
                        <p className="text-text-secondary">Multi-framework compliance and regulatory change tracking</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <Scale className="w-4 h-4" />
                      Add Framework
                    </button>
                  </div>
                </div>

                {/* Regulatory Framework Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallbackTitle="Regulatory Framework Unavailable"
                    fallbackMessage="The regulatory framework dashboard is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                    onError={(error, errorInfo) => {
                      console.error('Regulatory Framework Dashboard Error:', error, errorInfo);
                      handleError(error, 'regulatory-framework-dashboard');
                    }}
                  >
                    <Suspense fallback={<LoadingFallback componentName="Regulatory Framework" />}>
                      <RegulatoryFrameworkDashboard />
                    </Suspense>
                  </ErrorBoundary>
                </div>
              </div>
            )}

            {activeTab === 'security_overview' && (
              <div className="space-y-6">
                {/* Security Overview Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-accent-purple/20 rounded-lg">
                        <Shield className="w-6 h-6 text-accent-purple" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Security Overview & Threat Management</h2>
                        <p className="text-text-secondary">Threat detection, incident response, and security controls</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-accent-purple hover:bg-accent-purple-hover text-white rounded-lg transition-colors">
                      <Shield className="w-4 h-4" />
                      Security Scan
                    </button>
                  </div>
                </div>

                {/* Security Overview Dashboard Component with Enhanced Error Handling */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallback={
                      <div className="p-8 text-center">
                        <div className="flex flex-col items-center gap-4">
                          <AlertTriangle className="w-12 h-12 text-yellow-500" />
                          <h3 className="text-lg font-semibold text-text">Security Overview Temporarily Unavailable</h3>
                          <p className="text-text-secondary max-w-md">
                            The security overview dashboard is currently experiencing issues.
                            This may be due to a temporary loading problem.
                          </p>
                          <button
                            onClick={() => window.location.reload()}
                            className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-lg transition-colors"
                          >
                            <RefreshCw className="w-4 h-4" />
                            Retry Loading
                          </button>
                        </div>
                      </div>
                    }
                    onError={(error, errorInfo) => {
                      console.error('Security Overview Dashboard Error:', error, errorInfo);
                      console.error('Error details:', {
                        message: error.message,
                        stack: error.stack,
                        componentStack: errorInfo.componentStack
                      });
                      handleError(error, 'security-overview-dashboard');
                    }}
                  >
                    <Suspense
                      fallback={
                        <div className="flex items-center justify-center py-12">
                          <div className="text-center">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4 mx-auto"></div>
                            <div className="text-text-secondary text-lg font-medium mb-2">Loading Security Overview...</div>
                            <div className="text-text-secondary text-sm">Initializing security monitoring dashboard</div>
                          </div>
                        </div>
                      }
                    >
                      <SecurityOverviewDashboard />
                    </Suspense>
                  </ErrorBoundary>
                </div>
              </div>
            )}

            {activeTab === 'site_reliability' && (
              <div className="space-y-6">
                {/* Site Reliability Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Shield className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Site Reliability Guardian</h2>
                        <p className="text-text-secondary">Infrastructure monitoring and system health management</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <TrendingUp className="w-4 h-4" />
                      View Metrics
                    </button>
                  </div>
                </div>

                {/* Site Reliability Guardian Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallbackTitle="Site Reliability Guardian Unavailable"
                    fallbackMessage="The site reliability monitoring system is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                    onError={(error, errorInfo) => {
                      console.error('Site Reliability Guardian Error:', error, errorInfo);
                      handleError(error, 'site-reliability-guardian');
                    }}
                  >
                    <Suspense fallback={<LoadingFallback componentName="Site Reliability" />}>
                      <SiteReliabilityGuardian />
                    </Suspense>
                  </ErrorBoundary>
                </div>
              </div>
            )}

            {activeTab === 'slo' && (
              <div className="space-y-6">
                {/* SLO Dashboard Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-primary/20 rounded-lg">
                        <Target className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">Service Level Objectives Dashboard</h2>
                        <p className="text-text-secondary">Performance targets and availability metrics</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors">
                      <Target className="w-4 h-4" />
                      New SLO
                    </button>
                  </div>
                </div>

                {/* SLO Dashboard Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallbackTitle="SLO Dashboard Unavailable"
                    fallbackMessage="The Service Level Objectives dashboard is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                    onError={(error, errorInfo) => {
                      console.error('SLO Dashboard Error:', error, errorInfo);
                      handleError(error, 'slo-dashboard');
                    }}
                  >
                    <Suspense fallback={<LoadingFallback componentName="SLO Dashboard" />}>
                      <ServiceLevelObjectivesDashboard />
                    </Suspense>
                  </ErrorBoundary>
                </div>
              </div>
            )}

            {activeTab === 'slo_classic' && (
              <div className="space-y-6">
                {/* SLO Classic Header */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className="p-3 bg-secondary/20 rounded-lg">
                        <Archive className="w-6 h-6 text-secondary" />
                      </div>
                      <div>
                        <h2 className="text-2xl font-bold text-text">SLO Classic Management</h2>
                        <p className="text-text-secondary">Legacy SLA management and historical performance data</p>
                      </div>
                    </div>
                    <button className="flex items-center gap-2 px-4 py-2 bg-secondary hover:bg-secondary-hover text-white rounded-lg transition-colors">
                      <Archive className="w-4 h-4" />
                      Legacy Reports
                    </button>
                  </div>
                </div>

                {/* SLO Classic Component */}
                <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                  <ErrorBoundary
                    type="section"
                    fallbackTitle="SLO Classic Unavailable"
                    fallbackMessage="The SLO Classic management system is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                    onError={(error, errorInfo) => {
                      console.error('SLO Classic Error:', error, errorInfo);
                      handleError(error, 'slo-classic');
                    }}
                  >
                    <Suspense fallback={<LoadingFallback componentName="SLO Classic" />}>
                      <ServiceLevelObjectivesClassic />
                    </Suspense>
                  </ErrorBoundary>
                </div>
              </div>
            )}
          </div>

          {/* Compliance Dashboard Section - Only visible when Compliance Overview tab is active */}
          {showComplianceOverview && (
          <div className="mt-12 space-y-8">
            {/* Compliance Overview Header */}
            <div className={`relative ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-8 border border-border ${mode === 'dark' ? '' : 'shadow-2xl'} overflow-hidden`}>
              {/* Premium Background Effects - Only in light mode */}
              {mode === 'light' && (
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute inset-0 bg-gradient-to-br from-secondary/20 via-transparent to-primary/20"></div>
                  <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-radial from-secondary/15 to-transparent"></div>
                </div>
              )}

              <div className="relative z-10">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center gap-4">
                    <div className="relative">
                      <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-secondary/20 to-secondary/10'} rounded-2xl ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                        <BarChart3 className="w-10 h-10 text-secondary" />
                      </div>
                      <div className={`absolute -top-2 -right-2 w-6 h-6 bg-secondary rounded-full flex items-center justify-center ${mode === 'dark' ? '' : 'animate-bounce'}`}>
                        <CheckCircle className="w-4 h-4 text-white" />
                      </div>
                    </div>
                    <div>
                      <h2 className={`text-4xl font-bold ${mode === 'dark' ? 'text-text' : 'bg-gradient-to-r from-text via-secondary to-primary bg-clip-text text-transparent'} mb-2`}>
                        Compliance Overview
                      </h2>
                      <p className="text-text-secondary text-lg">Comprehensive compliance analytics and policy management platform with GDPR Command Center</p>
                    </div>
                  </div>

                  {/* Action buttons */}
                  <div className="flex items-center gap-3">
                    <button
                      onClick={handleRefreshData}
                      disabled={isRefreshing || isComplianceDataLoading}
                      className="flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md disabled:hover:shadow-sm"
                      aria-label="Refresh compliance data"
                    >
                      <svg
                        className={`w-4 h-4 ${isRefreshing || isComplianceDataLoading ? 'animate-spin' : ''}`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                      <span className="text-sm font-medium">
                        {isRefreshing || isComplianceDataLoading ? 'Refreshing...' : 'Refresh'}
                      </span>
                    </button>
                    <button
                      onClick={async () => {
                        try {
                          console.log('Exporting compliance report...');
                          // In a real application, this would call an export service
                          // For now, we'll simulate the export process
                          const reportData = {
                            timestamp: new Date().toISOString(),
                            overallCompliance: metrics?.compliantPercentage || 75,
                            compliantPolicies: metrics?.compliantPercentage || 75,
                            nonCompliantPolicies: metrics?.nonCompliantPercentage || 15,
                            pendingReview: metrics?.pendingPercentage || 10,
                            exportedBy: 'Current User'
                          };

                          const dataStr = JSON.stringify(reportData, null, 2);
                          const dataBlob = new Blob([dataStr], { type: 'application/json' });
                          const url = URL.createObjectURL(dataBlob);
                          const link = document.createElement('a');
                          link.href = url;
                          link.download = `compliance-report-${new Date().toISOString().split('T')[0]}.json`;
                          link.click();
                          URL.revokeObjectURL(url);

                          console.log('Compliance report exported successfully');
                        } catch (error) {
                          console.error('Failed to export compliance report:', error);
                          handleError(error as Error, 'compliance-export');
                        }
                      }}
                      className="flex items-center gap-2 px-4 py-2 bg-card hover:bg-surface text-text border border-border rounded-lg transition-colors duration-200 shadow-sm hover:shadow-md"
                      aria-label="Export compliance report"
                    >
                      <Download className="w-4 h-4" />
                      <span className="text-sm font-medium">Export</span>
                    </button>
                  </div>

                  <div className="text-center">
                    {isComplianceDataLoading ? (
                      <div className="animate-pulse">
                        <div className="h-16 bg-surface rounded-lg w-32 mx-auto mb-4"></div>
                        <div className="h-4 bg-surface rounded w-40 mx-auto mb-2"></div>
                        <div className="h-4 bg-surface rounded w-28 mx-auto"></div>
                      </div>
                    ) : (
                      <>
                        <div className="relative mb-4">
                          <div className="text-6xl font-bold text-secondary">{metrics?.compliantPercentage || 75}%</div>
                          {mode === 'light' && (
                            <div className="absolute -inset-4 bg-gradient-to-r from-secondary/20 to-primary/20 rounded-2xl -z-10"></div>
                          )}
                        </div>
                        <div className="text-text-secondary text-sm mb-2">Overall Compliance Score</div>
                        <div className="flex items-center justify-center gap-2 text-secondary">
                          <TrendingUp className="w-4 h-4" />
                          <span className="text-sm font-semibold">+2.3% this month</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Compliance Metrics Overview Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
              <div
                className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}
                onClick={() => handleComplianceCardClick('compliant')}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleComplianceCardClick('compliant');
                  }
                }}
                aria-label="View compliant policies details"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Compliant</h3>
                    <p className="text-sm text-text-secondary">Total compliant policies</p>
                  </div>
                  <div className="p-2 rounded-full bg-green-50 dark:bg-green-900/30 group-hover:bg-green-100 dark:group-hover:bg-green-900/50 transition-colors">
                    <svg className="w-6 h-6 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  {isComplianceDataLoading ? (
                    <div className="animate-pulse">
                      <div className="h-8 bg-surface rounded w-16 mb-1"></div>
                      <div className="h-3 bg-surface rounded w-20"></div>
                    </div>
                  ) : (
                    <>
                      <p className="text-3xl font-bold text-green-500">{metrics?.compliantPercentage || 75}%</p>
                      <p className="text-sm text-text-secondary mb-1">of total policies</p>
                    </>
                  )}
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-green-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.compliantPercentage || 75}%` }}
                  />
                </div>
              </div>

              <div
                className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}
                onClick={() => handleComplianceCardClick('non_compliant')}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleComplianceCardClick('non_compliant');
                  }
                }}
                aria-label="View non-compliant policies that need attention"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Non-Compliant</h3>
                    <p className="text-sm text-text-secondary">Policies requiring attention</p>
                  </div>
                  <div className="p-2 rounded-full bg-red-50 dark:bg-red-900/30 group-hover:bg-red-100 dark:group-hover:bg-red-900/50 transition-colors">
                    <svg className="w-6 h-6 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  {isComplianceDataLoading ? (
                    <div className="animate-pulse">
                      <div className="h-8 bg-surface rounded w-16 mb-1"></div>
                      <div className="h-3 bg-surface rounded w-20"></div>
                    </div>
                  ) : (
                    <>
                      <p className="text-3xl font-bold text-red-500">{metrics?.nonCompliantPercentage || 15}%</p>
                      <p className="text-sm text-text-secondary mb-1">of total policies</p>
                    </>
                  )}
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-red-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.nonCompliantPercentage || 15}%` }}
                  />
                </div>
              </div>

              <div
                className={`bg-card rounded-lg ${mode === 'dark' ? '' : 'shadow hover:shadow-lg'} p-6 transition-all duration-300 cursor-pointer group`}
                onClick={() => handleComplianceCardClick('pending')}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handleComplianceCardClick('pending');
                  }
                }}
                aria-label="View policies pending review"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-text mb-1">Pending Review</h3>
                    <p className="text-sm text-text-secondary">Awaiting assessment</p>
                  </div>
                  <div className="p-2 rounded-full bg-orange-50 dark:bg-orange-900/30 group-hover:bg-orange-100 dark:group-hover:bg-orange-900/50 transition-colors">
                    <svg className="w-6 h-6 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div className="flex items-end gap-2">
                  {isComplianceDataLoading ? (
                    <div className="animate-pulse">
                      <div className="h-8 bg-surface rounded w-16 mb-1"></div>
                      <div className="h-3 bg-surface rounded w-20"></div>
                    </div>
                  ) : (
                    <>
                      <p className="text-3xl font-bold text-orange-500">{metrics?.pendingPercentage || 10}%</p>
                      <p className="text-sm text-text-secondary mb-1">of total policies</p>
                    </>
                  )}
                </div>
                <div className="mt-4 h-1 bg-surface rounded-full">
                  <div
                    className="h-full bg-orange-500 rounded-full transition-all duration-500"
                    style={{ width: `${metrics?.pendingPercentage || 10}%` }}
                  />
                </div>
              </div>
            </div>

            {/* GDPR Command Center - Only visible when Enterprise Dashboard is active */}
            {showGDPRCenter && (
              <div className="mb-8" data-testid="gdpr-command-center">
                <ErrorBoundary
                  type="section"
                  fallbackTitle="GDPR Command Center Unavailable"
                  fallbackMessage="The GDPR Command Center is temporarily unavailable. Please try refreshing or contact support if the issue persists."
                  onError={(error, errorInfo) => {
                    console.error('GDPR Command Center Error:', error, errorInfo);
                    handleError(error, 'gdpr-command-center');
                  }}
                >
                  <Suspense fallback={<LoadingFallback componentName="GDPR Command Center" />}>
                    <GDPRCommandCenter />
                  </Suspense>
                </ErrorBoundary>
              </div>
            )}

            {/* Policy Management and Violations Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                <ComplianceByPolicy />
              </div>
              <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                <ViolationsSummary />
              </div>
            </div>

            {/* Charts Section */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
              <div className="bg-card rounded-lg shadow p-6">
                <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
                <ErrorBoundary fallback={
                  <div className="flex items-center justify-center h-96">
                    <p className="text-text-secondary">Chart failed to load</p>
                  </div>
                }>
                  <div style={{
                    height: '400px',
                    position: 'relative',
                    marginLeft: '40px',
                    marginRight: '20px'
                  }}>
                    <Line
                      data={lineChartData}
                      options={chartOptions}
                    />
                  </div>
                </ErrorBoundary>

                {/* Chart Legend */}
                <div className="flex justify-center gap-8 mt-6">
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-green-500"></div>
                    <span className="text-[11px] text-text-secondary">Compliant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-red-500"></div>
                    <span className="text-[11px] text-text-secondary">Non-Compliant</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-6 h-[1px] bg-yellow-500"></div>
                    <span className="text-[11px] text-text-secondary">Pending Review</span>
                  </div>
                </div>
              </div>
              <div className={`bg-card rounded-xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-lg'}`}>
                <ErrorBoundary>
                  <Suspense fallback={<LoadingFallback />}>
                    <PolicyDistributionChart />
                  </Suspense>
                </ErrorBoundary>
              </div>
            </div>

            {/* Analytics Section */}
            <div className="bg-card rounded-lg shadow p-6">
              <ErrorBoundary>
                <Suspense fallback={<LoadingFallback />}>
                  <Analytics />
                </Suspense>
              </ErrorBoundary>
            </div>
          </div>
          )}
        </div>
      </div>
    </div>
  );
});

EnterpriseDashboard.displayName = 'EnterpriseDashboard';

// Wrapped component with top-level error boundary
const EnterpriseDashboardWithErrorBoundary = () => {
  return (
    <ErrorBoundary
      type="page"
      fallbackTitle="Enterprise Dashboard Unavailable"
      fallbackMessage="The Enterprise Dashboard is temporarily unavailable due to a system error. Please refresh the page or contact support if the issue persists."
      onError={(error, errorInfo) => {
        console.error('Enterprise Dashboard Critical Error:', error, errorInfo);
        // In production, this would be sent to error monitoring service
        if (process.env.NODE_ENV === 'production') {
          // Send to error reporting service
          console.log('Sending error to monitoring service:', { error, errorInfo });
        }
      }}
      onRetry={() => {
        // Refresh the entire page as a last resort
        window.location.reload();
      }}
    >
      <EnterpriseDashboard />
    </ErrorBoundary>
  );
};

export default EnterpriseDashboardWithErrorBoundary;
