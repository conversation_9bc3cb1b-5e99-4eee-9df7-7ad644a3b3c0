import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/Modal';
import { FormInput, FormSelect, FormTextarea } from '../ui/FormInput';
import { Button } from '../ui/Button';
import { createValidator, validateRequired } from '../../utils/formValidation';
import { RegulatoryFramework, RegulationType, JurisdictionType } from '../../types/gdprTypes';


interface RegulatoryFrameworkModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (framework: Partial<RegulatoryFramework>) => Promise<void>;
  framework?: RegulatoryFramework | null;
  mode: 'create' | 'edit';
}

const regulationTypeOptions = [
  { value: 'gdpr', label: 'GDPR - General Data Protection Regulation' },
  { value: 'dpdp', label: 'DPDP - Digital Personal Data Protection' },
  { value: 'ccpa', label: 'CCPA - California Consumer Privacy Act' },
  { value: 'cpra', label: 'CPRA - California Privacy Rights Act' },
  { value: 'sox', label: 'SOX - Sarbanes-Oxley Act' },
  { value: 'hipaa', label: 'HIPAA - Health Insurance Portability Act' },
  { value: 'pci_dss', label: 'PCI DSS - Payment Card Industry Data Security Standard' },
  { value: 'iso_27001', label: 'ISO 27001 - Information Security Management' },
  { value: 'nist', label: 'NIST - National Institute of Standards and Technology' },
  { value: 'appi', label: 'APPI - Act on Protection of Personal Information (Japan)' },
  { value: 'lgpd', label: 'LGPD - Lei Geral de Proteção de Dados (Brazil)' },
  { value: 'pipeda', label: 'PIPEDA - Personal Information Protection Act (Canada)' },
  { value: 'other', label: 'Other' }
];

const jurisdictionOptions = [
  { value: 'eu', label: 'European Union' },
  { value: 'india', label: 'India' },
  { value: 'us', label: 'United States' },
  { value: 'california', label: 'California' },
  { value: 'uk', label: 'United Kingdom' },
  { value: 'canada', label: 'Canada' },
  { value: 'brazil', label: 'Brazil' },
  { value: 'japan', label: 'Japan' },
  { value: 'australia', label: 'Australia' },
  { value: 'singapore', label: 'Singapore' },
  { value: 'global', label: 'Global' },
  { value: 'other', label: 'Other' }
];

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'active', label: 'Active' },
  { value: 'deprecated', label: 'Deprecated' },
  { value: 'pending', label: 'Pending' }
];

export const RegulatoryFrameworkModal: React.FC<RegulatoryFrameworkModalProps> = ({
  isOpen,
  onClose,
  onSave,
  framework,
  mode
}) => {
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  
  const [formData, setFormData] = useState({
    name: '',
    type: 'other' as RegulationType,
    jurisdiction: 'other' as JurisdictionType,
    description: '',
    version: '1.0',
    effectiveDate: '',
    status: 'draft' as 'draft' | 'active' | 'deprecated' | 'pending',
    owner: '',
    nextReviewDate: '',
    applicableEntities: [] as string[],
    tags: [] as string[]
  });

  const validator = createValidator({
    name: { ...validateRequired(), minLength: 5, maxLength: 200 },
    description: { ...validateRequired(), minLength: 20, maxLength: 1000 },
    type: { ...validateRequired() },
    jurisdiction: { ...validateRequired() },
    version: { ...validateRequired() },
    effectiveDate: { ...validateRequired() },
    owner: { ...validateRequired() },
    nextReviewDate: { ...validateRequired() }
  });

  useEffect(() => {
    if (framework && mode === 'edit') {
      setFormData({
        name: framework.name,
        type: framework.type,
        jurisdiction: framework.jurisdiction,
        description: framework.description,
        version: framework.version,
        effectiveDate: framework.effectiveDate.toISOString().split('T')[0],
        status: framework.status,
        owner: framework.owner,
        nextReviewDate: framework.nextReviewDate.toISOString().split('T')[0],
        applicableEntities: framework.applicableEntities,
        tags: framework.tags
      });
    } else {
      // Reset form for create mode
      const today = new Date().toISOString().split('T')[0];
      const nextYear = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      setFormData({
        name: '',
        type: 'other',
        jurisdiction: 'other',
        description: '',
        version: '1.0',
        effectiveDate: today,
        status: 'draft',
        owner: '',
        nextReviewDate: nextYear,
        applicableEntities: [],
        tags: []
      });
    }
    setErrors({});
  }, [framework, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const handleArrayInputChange = (field: 'applicableEntities' | 'tags', value: string) => {
    if (value.trim()) {
      const items = value.split(',').map(item => item.trim()).filter(item => item);
      setFormData(prev => ({ ...prev, [field]: items }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validator.validate(formData);
    if (!validation.isValid) {
      const fieldErrors: any = {};
      validation.errors.forEach(error => {
        fieldErrors[error.field] = error.message;
      });
      setErrors(fieldErrors);
      return;
    }

    setLoading(true);
    try {
      const frameworkData: Partial<RegulatoryFramework> = {
        ...formData,
        effectiveDate: new Date(formData.effectiveDate),
        nextReviewDate: new Date(formData.nextReviewDate),
        ...(mode === 'edit' && framework ? { id: framework.id } : {})
      };

      await onSave(frameworkData);
      onClose();
    } catch (error) {
      console.error('Error saving regulatory framework:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldError = (field: string) => errors[field] || null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create Regulatory Framework' : 'Edit Regulatory Framework'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <FormInput
              label="Framework Name"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              error={getFieldError('name')}
              placeholder="Enter the regulatory framework name"
              required
            />
          </div>

          <FormSelect
            label="Regulation Type"
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            options={regulationTypeOptions}
            error={getFieldError('type')}
            required
          />

          <FormSelect
            label="Jurisdiction"
            value={formData.jurisdiction}
            onChange={(e) => handleInputChange('jurisdiction', e.target.value)}
            options={jurisdictionOptions}
            error={getFieldError('jurisdiction')}
            required
          />

          <FormInput
            label="Version"
            value={formData.version}
            onChange={(e) => handleInputChange('version', e.target.value)}
            error={getFieldError('version')}
            placeholder="e.g. 1.0, 2018.1"
            required
          />

          <FormSelect
            label="Status"
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            options={statusOptions}
            error={getFieldError('status')}
            required
          />

          <FormInput
            label="Effective Date"
            type="date"
            value={formData.effectiveDate}
            onChange={(e) => handleInputChange('effectiveDate', e.target.value)}
            error={getFieldError('effectiveDate')}
            required
          />

          <FormInput
            label="Next Review Date"
            type="date"
            value={formData.nextReviewDate}
            onChange={(e) => handleInputChange('nextReviewDate', e.target.value)}
            error={getFieldError('nextReviewDate')}
            required
          />

          <div className="md:col-span-2">
            <FormInput
              label="Framework Owner"
              value={formData.owner}
              onChange={(e) => handleInputChange('owner', e.target.value)}
              error={getFieldError('owner')}
              placeholder="e.g. <EMAIL>"
              required
            />
          </div>
        </div>

        <FormTextarea
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          error={getFieldError('description')}
          placeholder="Provide a detailed description of the regulatory framework..."
          rows={4}
          required
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormInput
            label="Applicable Entities (comma-separated)"
            value={formData.applicableEntities.join(', ')}
            onChange={(e) => handleArrayInputChange('applicableEntities', e.target.value)}
            placeholder="e.g. All subsidiaries, Data processing operations"
          />

          <FormInput
            label="Tags (comma-separated)"
            value={formData.tags.join(', ')}
            onChange={(e) => handleArrayInputChange('tags', e.target.value)}
            placeholder="e.g. privacy, security, mandatory"
          />
        </div>

        <div className="flex items-center gap-3 justify-end pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create Framework' : 'Update Framework'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
