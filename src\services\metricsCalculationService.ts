import { Department, EnhancedPolicy, EnhancedComplianceMetrics } from '../types/compliance';

export interface MetricCalculationOptions {
  includeHistorical?: boolean;
  timeRange?: 'week' | 'month' | 'quarter' | 'year';
  departmentFilter?: string[];
  policyFilter?: string[];
}

export interface RealTimeMetrics {
  timestamp: Date;
  overallScore: number;
  departmentScores: { [departmentId: string]: number };
  policyCompliance: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  trendsData: {
    compliance: number[];
    risks: number[];
    assessments: number[];
    dates: string[];
  };
  alerts: MetricAlert[];
}

export interface MetricAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  departmentId?: string;
  policyId?: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  actionRequired: boolean;
}

export interface ComplianceBreakdown {
  byDepartment: { [departmentId: string]: ComplianceDepartmentMetrics };
  byPolicy: { [policyId: string]: PolicyComplianceMetrics };
  byRisk: RiskBreakdown;
  trends: TrendAnalysis;
}

export interface ComplianceDepartmentMetrics {
  name: string;
  score: number;
  trend: 'improving' | 'declining' | 'stable';
  assessments: {
    total: number;
    completed: number;
    pending: number;
    overdue: number;
  };
  policies: {
    total: number;
    compliant: number;
    nonCompliant: number;
  };
  risks: {
    total: number;
    byLevel: { [level: string]: number };
  };
  lastUpdated: Date;
}

export interface PolicyComplianceMetrics {
  name: string;
  complianceRate: number;
  trend: 'improving' | 'declining' | 'stable';
  violations: number;
  trainingCompletion: number;
  acknowledgmentRate: number;
  applicableDepartments: number;
  lastViolation: Date | null;
}

export interface RiskBreakdown {
  total: number;
  byLevel: {
    low: number;
    medium: number;
    high: number;
    critical: number;
  };
  byCategory: { [category: string]: number };
  mitigated: number;
  open: number;
  overdue: number;
}

export interface TrendAnalysis {
  period: 'week' | 'month' | 'quarter' | 'year';
  dataPoints: number;
  compliance: {
    current: number;
    previous: number;
    change: number;
    trend: 'improving' | 'declining' | 'stable';
  };
  risks: {
    current: number;
    previous: number;
    change: number;
    trend: 'improving' | 'declining' | 'stable';
  };
  assessments: {
    completionRate: number;
    previousRate: number;
    change: number;
    trend: 'improving' | 'declining' | 'stable';
  };
}

export class MetricsCalculationService {
  private static alertThresholds = {
    complianceScore: { warning: 80, critical: 70 },
    policyCompliance: { warning: 85, critical: 75 },
    assessmentCompletion: { warning: 90, critical: 80 },
    overdueAssessments: { warning: 5, critical: 10 }
  };

  static calculateRealTimeMetrics(
    departments: Department[],
    policies: EnhancedPolicy[],
    options: MetricCalculationOptions = {}
  ): RealTimeMetrics {
    const timestamp = new Date();
    
    // Calculate overall compliance score
    const overallScore = this.calculateOverallScore(departments, policies);
    
    // Calculate department scores
    const departmentScores = this.calculateDepartmentScores(departments);
    
    // Calculate policy compliance
    const policyCompliance = this.calculatePolicyCompliance(policies);
    
    // Determine risk level
    const riskLevel = this.determineRiskLevel(overallScore, departments);
    
    // Generate trends data
    const trendsData = this.generateTrendsData(options.timeRange || 'month');
    
    // Generate alerts
    const alerts = this.generateAlerts(departments, policies, overallScore);

    return {
      timestamp,
      overallScore,
      departmentScores,
      policyCompliance,
      riskLevel,
      trendsData,
      alerts
    };
  }

  static calculateComplianceBreakdown(
    departments: Department[],
    policies: EnhancedPolicy[],
    options: MetricCalculationOptions = {}
  ): ComplianceBreakdown {
    const byDepartment = this.calculateDepartmentBreakdown(departments, policies);
    const byPolicy = this.calculatePolicyBreakdown(policies);
    const byRisk = this.calculateRiskBreakdown(departments);
    const trends = this.calculateTrendAnalysis(departments, policies, options.timeRange || 'month');

    return {
      byDepartment,
      byPolicy,
      byRisk,
      trends
    };
  }

  private static calculateOverallScore(departments: Department[], policies: EnhancedPolicy[]): number {
    if (departments.length === 0) return 0;
    
    const departmentScore = departments.reduce((sum, dept) => sum + dept.complianceScore, 0) / departments.length;
    const policyScore = this.calculatePolicyCompliance(policies);
    
    // Weighted average: 60% department compliance, 40% policy compliance
    return Math.round(departmentScore * 0.6 + policyScore * 0.4);
  }

  private static calculateDepartmentScores(departments: Department[]): { [departmentId: string]: number } {
    const scores: { [departmentId: string]: number } = {};
    
    departments.forEach(dept => {
      scores[dept.id] = dept.complianceScore;
    });
    
    return scores;
  }

  private static calculatePolicyCompliance(policies: EnhancedPolicy[]): number {
    if (policies.length === 0) return 0;
    
    const activePolicies = policies.filter(p => p.status === 'active');
    if (activePolicies.length === 0) return 0;
    
    const totalCompliance = activePolicies.reduce((sum, policy) => sum + policy.metrics.complianceRate, 0);
    return Math.round(totalCompliance / activePolicies.length);
  }

  private static determineRiskLevel(
    overallScore: number, 
    departments: Department[]
  ): 'low' | 'medium' | 'high' | 'critical' {
    const criticalDepts = departments.filter(d => d.riskLevel === 'critical').length;
    const highRiskDepts = departments.filter(d => d.riskLevel === 'high').length;
    
    if (overallScore < 70 || criticalDepts > 0) return 'critical';
    if (overallScore < 80 || highRiskDepts > 2) return 'high';
    if (overallScore < 90 || highRiskDepts > 0) return 'medium';
    return 'low';
  }

  private static generateTrendsData(timeRange: 'week' | 'month' | 'quarter' | 'year') {
    const periods = timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : timeRange === 'quarter' ? 90 : 365;
    const dataPoints = Math.min(periods, 12); // Limit to 12 data points for visualization
    
    const dates: string[] = [];
    const compliance: number[] = [];
    const risks: number[] = [];
    const assessments: number[] = [];
    
    for (let i = dataPoints - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - (i * Math.floor(periods / dataPoints)));
      dates.push(date.toISOString().slice(0, 10));
      
      // Generate realistic trend data with some variation
      const baseCompliance = 85;
      const baseRisk = 15;
      const baseAssessment = 90;
      
      compliance.push(Math.max(70, Math.min(100, baseCompliance + (Math.random() - 0.5) * 10)));
      risks.push(Math.max(0, Math.min(50, baseRisk + (Math.random() - 0.5) * 8)));
      assessments.push(Math.max(75, Math.min(100, baseAssessment + (Math.random() - 0.5) * 12)));
    }
    
    return { compliance, risks, assessments, dates };
  }

  private static generateAlerts(
    departments: Department[],
    policies: EnhancedPolicy[],
    overallScore: number
  ): MetricAlert[] {
    const alerts: MetricAlert[] = [];
    const now = new Date();

    // Overall compliance alerts
    if (overallScore < this.alertThresholds.complianceScore.critical) {
      alerts.push({
        id: `alert-${Date.now()}-1`,
        type: 'error',
        title: 'Critical Compliance Score',
        message: `Overall compliance score (${overallScore}%) is below critical threshold`,
        timestamp: now,
        severity: 'critical',
        actionRequired: true
      });
    } else if (overallScore < this.alertThresholds.complianceScore.warning) {
      alerts.push({
        id: `alert-${Date.now()}-2`,
        type: 'warning',
        title: 'Low Compliance Score',
        message: `Overall compliance score (${overallScore}%) needs attention`,
        timestamp: now,
        severity: 'medium',
        actionRequired: true
      });
    }

    // Department-specific alerts
    departments.forEach(dept => {
      if (dept.riskLevel === 'critical') {
        alerts.push({
          id: `alert-${Date.now()}-dept-${dept.id}`,
          type: 'error',
          title: 'Critical Department Risk',
          message: `${dept.name} has critical risk level`,
          timestamp: now,
          departmentId: dept.id,
          severity: 'critical',
          actionRequired: true
        });
      }

      if (dept.complianceMetrics.overdueAssessments > this.alertThresholds.overdueAssessments.warning) {
        alerts.push({
          id: `alert-${Date.now()}-overdue-${dept.id}`,
          type: 'warning',
          title: 'Overdue Assessments',
          message: `${dept.name} has ${dept.complianceMetrics.overdueAssessments} overdue assessments`,
          timestamp: now,
          departmentId: dept.id,
          severity: 'medium',
          actionRequired: true
        });
      }
    });

    // Policy-specific alerts
    policies.forEach(policy => {
      if (policy.status === 'active' && policy.metrics.complianceRate < this.alertThresholds.policyCompliance.critical) {
        alerts.push({
          id: `alert-${Date.now()}-policy-${policy.id}`,
          type: 'error',
          title: 'Policy Non-Compliance',
          message: `${policy.name} compliance rate (${policy.metrics.complianceRate}%) is critically low`,
          timestamp: now,
          policyId: policy.id,
          severity: 'high',
          actionRequired: true
        });
      }

      if (policy.nextReview < now) {
        alerts.push({
          id: `alert-${Date.now()}-review-${policy.id}`,
          type: 'warning',
          title: 'Policy Review Overdue',
          message: `${policy.name} review is overdue`,
          timestamp: now,
          policyId: policy.id,
          severity: 'medium',
          actionRequired: true
        });
      }
    });

    return alerts.sort((a, b) => {
      const severityOrder = { critical: 4, high: 3, medium: 2, low: 1 };
      return severityOrder[b.severity] - severityOrder[a.severity];
    });
  }

  private static calculateDepartmentBreakdown(
    departments: Department[],
    policies: EnhancedPolicy[]
  ): { [departmentId: string]: ComplianceDepartmentMetrics } {
    const breakdown: { [departmentId: string]: ComplianceDepartmentMetrics } = {};

    departments.forEach(dept => {
      const deptPolicies = policies.filter(p => p.applicableDepartments.includes(dept.id));
      const compliantPolicies = deptPolicies.filter(p => p.metrics.complianceRate >= 90);

      breakdown[dept.id] = {
        name: dept.name,
        score: dept.complianceScore,
        trend: Math.random() > 0.5 ? 'improving' : 'stable',
        assessments: {
          total: dept.complianceMetrics.completedAssessments + dept.complianceMetrics.pendingAssessments + dept.complianceMetrics.overdueAssessments,
          completed: dept.complianceMetrics.completedAssessments,
          pending: dept.complianceMetrics.pendingAssessments,
          overdue: dept.complianceMetrics.overdueAssessments
        },
        policies: {
          total: deptPolicies.length,
          compliant: compliantPolicies.length,
          nonCompliant: deptPolicies.length - compliantPolicies.length
        },
        risks: {
          total: 1, // Simplified for this example
          byLevel: {
            [dept.riskLevel]: 1
          }
        },
        lastUpdated: new Date()
      };
    });

    return breakdown;
  }

  private static calculatePolicyBreakdown(policies: EnhancedPolicy[]): { [policyId: string]: PolicyComplianceMetrics } {
    const breakdown: { [policyId: string]: PolicyComplianceMetrics } = {};

    policies.forEach(policy => {
      breakdown[policy.id] = {
        name: policy.name,
        complianceRate: policy.metrics.complianceRate,
        trend: Math.random() > 0.5 ? 'improving' : 'stable',
        violations: policy.metrics.violationCount,
        trainingCompletion: policy.metrics.trainingCompletion,
        acknowledgmentRate: policy.metrics.acknowledgmentRate,
        applicableDepartments: policy.applicableDepartments.length,
        lastViolation: policy.metrics.lastViolation
      };
    });

    return breakdown;
  }

  private static calculateRiskBreakdown(departments: Department[]): RiskBreakdown {
    const riskCounts = { low: 0, medium: 0, high: 0, critical: 0 };
    
    departments.forEach(dept => {
      riskCounts[dept.riskLevel]++;
    });

    return {
      total: departments.length,
      byLevel: riskCounts,
      byCategory: {
        'Data Privacy': Math.floor(departments.length * 0.3),
        'Security': Math.floor(departments.length * 0.25),
        'Operational': Math.floor(departments.length * 0.25),
        'Compliance': Math.floor(departments.length * 0.2)
      },
      mitigated: Math.floor(departments.length * 0.7),
      open: Math.floor(departments.length * 0.3),
      overdue: Math.floor(departments.length * 0.1)
    };
  }

  private static calculateTrendAnalysis(
    departments: Department[],
    policies: EnhancedPolicy[],
    timeRange: 'week' | 'month' | 'quarter' | 'year'
  ): TrendAnalysis {
    const currentCompliance = this.calculateOverallScore(departments, policies);
    const currentRisks = departments.filter(d => d.riskLevel === 'high' || d.riskLevel === 'critical').length;
    const currentAssessmentRate = departments.reduce((sum, dept) => {
      const total = dept.complianceMetrics.completedAssessments + dept.complianceMetrics.pendingAssessments + dept.complianceMetrics.overdueAssessments;
      return sum + (total > 0 ? dept.complianceMetrics.completedAssessments / total : 0);
    }, 0) / departments.length * 100;

    // Simulate previous period data
    const previousCompliance = currentCompliance + (Math.random() - 0.5) * 10;
    const previousRisks = currentRisks + Math.floor((Math.random() - 0.5) * 4);
    const previousAssessmentRate = currentAssessmentRate + (Math.random() - 0.5) * 15;

    return {
      period: timeRange,
      dataPoints: timeRange === 'week' ? 7 : timeRange === 'month' ? 30 : timeRange === 'quarter' ? 90 : 365,
      compliance: {
        current: currentCompliance,
        previous: Math.max(0, Math.min(100, previousCompliance)),
        change: currentCompliance - previousCompliance,
        trend: currentCompliance > previousCompliance ? 'improving' : currentCompliance < previousCompliance ? 'declining' : 'stable'
      },
      risks: {
        current: currentRisks,
        previous: Math.max(0, previousRisks),
        change: currentRisks - previousRisks,
        trend: currentRisks < previousRisks ? 'improving' : currentRisks > previousRisks ? 'declining' : 'stable'
      },
      assessments: {
        completionRate: currentAssessmentRate,
        previousRate: Math.max(0, Math.min(100, previousAssessmentRate)),
        change: currentAssessmentRate - previousAssessmentRate,
        trend: currentAssessmentRate > previousAssessmentRate ? 'improving' : currentAssessmentRate < previousAssessmentRate ? 'declining' : 'stable'
      }
    };
  }
}
