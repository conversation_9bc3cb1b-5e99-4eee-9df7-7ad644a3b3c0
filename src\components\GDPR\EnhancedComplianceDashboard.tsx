import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { EnhancedComplianceService } from '../../services/enhancedComplianceService';
import { Department, EnhancedPolicy, EnhancedComplianceMetrics } from '../../types/compliance';
import { ErrorBoundary } from '../UI/ErrorBoundary';
import { EnhancedErrorBoundary, useErrorHandler } from '../UI/EnhancedErrorBoundary';
import {
  DataLoadingState,
  ErrorState,
  EmptyState,
  LoadingOverlay,
  TableLoadingState
} from '../UI/EnhancedLoadingStates';
import { PolicyManagementModal } from './PolicyManagementModal';
import { MetricsVisualization } from './MetricsVisualization';
import { ValidationService } from '../../utils/validation';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  FileText,
  Users,
  Target,
  Settings,
  Edit,
  Plus,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  BarChart3,
  Activity,
  Zap,
  AlertCircle,
  Building,
  BookOpen,
  RefreshCw
} from 'lucide-react';

interface EnhancedComplianceDashboardProps {
  className?: string;
}

interface LoadingState {
  departments: boolean;
  policies: boolean;
  metrics: boolean;
}

interface ErrorState {
  departments: string | null;
  policies: string | null;
  metrics: string | null;
}

export const EnhancedComplianceDashboard: React.FC<EnhancedComplianceDashboardProps> = ({
  className = ''
}) => {
  const { mode } = useTheme();
  const { reportError } = useErrorHandler();
  
  // State management
  const [departments, setDepartments] = useState<Department[]>([]);
  const [policies, setPolicies] = useState<EnhancedPolicy[]>([]);
  const [metrics, setMetrics] = useState<EnhancedComplianceMetrics | null>(null);
  const [selectedDepartment, setSelectedDepartment] = useState<string>('all');
  const [selectedPolicyCategory, setSelectedPolicyCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState<'overview' | 'departments' | 'policies' | 'metrics'>('overview');

  // Policy management modal state
  const [policyModalOpen, setPolicyModalOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<EnhancedPolicy | null>(null);
  const [policyModalMode, setPolicyModalMode] = useState<'create' | 'edit' | 'view'>('view');
  
  // Loading and error states
  const [loading, setLoading] = useState<LoadingState>({
    departments: true,
    policies: true,
    metrics: true
  });
  
  const [errors, setErrors] = useState<ErrorState>({
    departments: null,
    policies: null,
    metrics: null
  });

  // Data loading functions with enhanced error handling
  const loadDepartments = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, departments: true }));
      setErrors(prev => ({ ...prev, departments: null }));

      const departmentData = await EnhancedComplianceService.getAllDepartments();

      // Validate data structure
      if (!Array.isArray(departmentData)) {
        throw new Error('Invalid department data format received');
      }

      setDepartments(departmentData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Error loading departments:', error);
      reportError(error as Error, 'loadDepartments');

      setErrors(prev => ({
        ...prev,
        departments: `Failed to load departments: ${errorMessage}`
      }));
    } finally {
      setLoading(prev => ({ ...prev, departments: false }));
    }
  }, [reportError]);

  const loadPolicies = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, policies: true }));
      setErrors(prev => ({ ...prev, policies: null }));

      const policyData = await EnhancedComplianceService.getAllPolicies();

      // Validate data structure
      if (!Array.isArray(policyData)) {
        throw new Error('Invalid policy data format received');
      }

      setPolicies(policyData);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Error loading policies:', error);
      reportError(error as Error, 'loadPolicies');

      setErrors(prev => ({
        ...prev,
        policies: `Failed to load policies: ${errorMessage}`
      }));
    } finally {
      setLoading(prev => ({ ...prev, policies: false }));
    }
  }, [reportError]);

  const loadMetrics = useCallback(async () => {
    try {
      setLoading(prev => ({ ...prev, metrics: true }));
      setErrors(prev => ({ ...prev, metrics: null }));
      
      const metricsData = await EnhancedComplianceService.getEnhancedMetrics();
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading metrics:', error);
      setErrors(prev => ({ 
        ...prev, 
        metrics: 'Failed to load metrics. Please try again.' 
      }));
    } finally {
      setLoading(prev => ({ ...prev, metrics: false }));
    }
  }, []);

  // Refresh all data
  const refreshAllData = useCallback(async () => {
    await Promise.all([loadDepartments(), loadPolicies(), loadMetrics()]);
  }, [loadDepartments, loadPolicies, loadMetrics]);

  // Policy management functions
  const handleCreatePolicy = () => {
    setSelectedPolicy(null);
    setPolicyModalMode('create');
    setPolicyModalOpen(true);
  };

  const handleEditPolicy = (policy: EnhancedPolicy) => {
    setSelectedPolicy(policy);
    setPolicyModalMode('edit');
    setPolicyModalOpen(true);
  };

  const handleViewPolicy = (policy: EnhancedPolicy) => {
    setSelectedPolicy(policy);
    setPolicyModalMode('view');
    setPolicyModalOpen(true);
  };

  const handleSavePolicy = async (policy: EnhancedPolicy) => {
    try {
      if (policyModalMode === 'create') {
        // Add new policy to the list
        setPolicies(prev => [...prev, policy]);
      } else if (policyModalMode === 'edit') {
        // Update existing policy
        setPolicies(prev => prev.map(p => p.id === policy.id ? policy : p));
      }

      // Refresh metrics after policy changes
      await loadMetrics();
    } catch (error) {
      console.error('Error saving policy:', error);
    }
  };

  const handleUpdatePolicyStatus = async (policyId: string, status: EnhancedPolicy['status']) => {
    try {
      const updatedPolicy = await EnhancedComplianceService.updatePolicyStatus(policyId, status);
      if (updatedPolicy) {
        setPolicies(prev => prev.map(p => p.id === policyId ? updatedPolicy : p));
        await loadMetrics(); // Refresh metrics
      }
    } catch (error) {
      console.error('Error updating policy status:', error);
    }
  };

  // Initial data loading
  useEffect(() => {
    refreshAllData();
  }, [refreshAllData]);

  // Filtered data with memoization for performance
  const filteredDepartments = useMemo(() => {
    if (!departments.length) return [];
    
    return departments.filter(dept => {
      const matchesSearch = dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           dept.description.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesSearch;
    });
  }, [departments, searchTerm]);

  const filteredPolicies = useMemo(() => {
    if (!policies.length) return [];
    
    return policies.filter(policy => {
      const matchesSearch = policy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           policy.description.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesCategory = selectedPolicyCategory === 'all' || 
                             policy.category === selectedPolicyCategory;
      const matchesDepartment = selectedDepartment === 'all' || 
                               policy.applicableDepartments.includes(selectedDepartment);
      
      return matchesSearch && matchesCategory && matchesDepartment;
    });
  }, [policies, searchTerm, selectedPolicyCategory, selectedDepartment]);

  // Risk level color mapping
  const getRiskColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'low': return 'text-green-600 bg-green-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'critical': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Status color mapping
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'draft': return 'text-blue-600 bg-blue-100';
      case 'under_review': return 'text-yellow-600 bg-yellow-100';
      case 'deprecated': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Enhanced validation for search input
  const handleSearchChange = useCallback((value: string) => {
    // Sanitize input to prevent XSS
    const sanitizedValue = ValidationService.sanitizeInput(value);
    setSearchTerm(sanitizedValue);
  }, []);

  // Network error detection
  const isNetworkError = (error: string) => {
    return error.toLowerCase().includes('network') ||
           error.toLowerCase().includes('fetch') ||
           error.toLowerCase().includes('connection');
  };

  // Tab navigation
  const tabs = [
    { id: 'overview', label: 'Overview', icon: BarChart3 },
    { id: 'departments', label: 'Departments', icon: Building },
    { id: 'policies', label: 'Policies', icon: BookOpen },
    { id: 'metrics', label: 'Metrics', icon: Activity }
  ];

  return (
    <EnhancedErrorBoundary level="page" showDetails={true} onError={reportError}>
      <div className={`min-h-screen bg-background ${className}`}>
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-text mb-2">
                Enhanced Compliance Dashboard
              </h1>
              <p className="text-text-secondary">
                Comprehensive compliance monitoring and management system
              </p>
            </div>
            
            <div className="flex items-center space-x-4 mt-4 lg:mt-0">
              {activeTab === 'policies' && (
                <button
                  onClick={handleCreatePolicy}
                  className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                >
                  <Plus className="w-4 h-4" />
                  <span>New Policy</span>
                </button>
              )}
              <button
                onClick={refreshAllData}
                className="flex items-center space-x-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
                disabled={Object.values(loading).some(Boolean)}
              >
                <RefreshCw className={`w-4 h-4 ${Object.values(loading).some(Boolean) ? 'animate-spin' : ''}`} />
                <span>Refresh</span>
              </button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-surface rounded-lg p-1 mb-8 border border-border">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary text-white'
                      : 'text-text hover:text-primary hover:bg-surface-hover'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col lg:flex-row lg:items-center lg:space-x-4 mb-8">
            <div className="relative flex-1 mb-4 lg:mb-0">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search departments, policies, or metrics..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                maxLength={100}
              />
            </div>
            
            {(activeTab === 'policies' || activeTab === 'departments') && (
              <div className="flex space-x-4">
                <select
                  value={selectedDepartment}
                  onChange={(e) => setSelectedDepartment(e.target.value)}
                  className="px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                >
                  <option value="all">All Departments</option>
                  {departments.map(dept => (
                    <option key={dept.id} value={dept.id}>{dept.name}</option>
                  ))}
                </select>
                
                {activeTab === 'policies' && (
                  <select
                    value={selectedPolicyCategory}
                    onChange={(e) => setSelectedPolicyCategory(e.target.value)}
                    className="px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
                  >
                    <option value="all">All Categories</option>
                    <option value="privacy">Privacy</option>
                    <option value="security">Security</option>
                    <option value="data_governance">Data Governance</option>
                    <option value="compliance">Compliance</option>
                    <option value="operational">Operational</option>
                  </select>
                )}
              </div>
            )}
          </div>

          {/* Content based on active tab */}
          {activeTab === 'overview' && (
            <div className="space-y-8">
              {/* Overview Metrics */}
              {metrics && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  <div className="bg-surface border border-border rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Shield className="w-6 h-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-text-secondary">Overall Score</h3>
                        <div className="text-2xl font-bold text-text">{metrics.overall.score}%</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {metrics.overall.trend === 'improving' ? (
                        <TrendingUp className="w-4 h-4 text-green-500" />
                      ) : metrics.overall.trend === 'declining' ? (
                        <TrendingDown className="w-4 h-4 text-red-500" />
                      ) : (
                        <Activity className="w-4 h-4 text-gray-500" />
                      )}
                      <span className="text-sm text-text-secondary capitalize">{metrics.overall.trend}</span>
                    </div>
                  </div>

                  <div className="bg-surface border border-border rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-green-100 rounded-lg">
                        <FileText className="w-6 h-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-text-secondary">Policy Compliance</h3>
                        <div className="text-2xl font-bold text-text">{metrics.policies.complianceRate}%</div>
                      </div>
                    </div>
                    <div className="text-sm text-text-secondary">
                      {metrics.policies.compliant}/{metrics.policies.total} policies compliant
                    </div>
                  </div>

                  <div className="bg-surface border border-border rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Target className="w-6 h-6 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-text-secondary">Assessment Rate</h3>
                        <div className="text-2xl font-bold text-text">{metrics.assessments.completionRate}%</div>
                      </div>
                    </div>
                    <div className="text-sm text-text-secondary">
                      {metrics.assessments.completed}/{metrics.assessments.total} completed
                    </div>
                  </div>

                  <div className="bg-surface border border-border rounded-lg p-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className="p-2 bg-orange-100 rounded-lg">
                        <AlertTriangle className="w-6 h-6 text-orange-600" />
                      </div>
                      <div>
                        <h3 className="text-sm font-medium text-text-secondary">Open Risks</h3>
                        <div className="text-2xl font-bold text-text">{metrics.risks.open}</div>
                      </div>
                    </div>
                    <div className="text-sm text-text-secondary">
                      {metrics.risks.critical} critical, {metrics.risks.high} high
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              <div className="bg-surface border border-border rounded-lg p-6">
                <h3 className="text-lg font-semibold text-text mb-4">Quick Actions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <button
                    onClick={handleCreatePolicy}
                    className="flex items-center space-x-3 p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors"
                  >
                    <Plus className="w-5 h-5 text-primary" />
                    <span className="text-text">Create Policy</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('departments')}
                    className="flex items-center space-x-3 p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors"
                  >
                    <Building className="w-5 h-5 text-primary" />
                    <span className="text-text">View Departments</span>
                  </button>

                  <button
                    onClick={() => setActiveTab('metrics')}
                    className="flex items-center space-x-3 p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors"
                  >
                    <BarChart3 className="w-5 h-5 text-primary" />
                    <span className="text-text">View Metrics</span>
                  </button>

                  <button
                    onClick={refreshAllData}
                    className="flex items-center space-x-3 p-4 border border-border rounded-lg hover:bg-surface-hover transition-colors"
                  >
                    <RefreshCw className="w-5 h-5 text-primary" />
                    <span className="text-text">Refresh Data</span>
                  </button>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'departments' && (
            <EnhancedErrorBoundary level="section" showDetails={false}>
              <div className="space-y-6">
                {errors.departments ? (
                  <ErrorState
                    title="Department Loading Error"
                    message={errors.departments}
                    onRetry={loadDepartments}
                  />
                ) : loading.departments ? (
                  <DataLoadingState type="departments" />
                ) : filteredDepartments.length === 0 ? (
                  <EmptyState
                    title="No Departments Found"
                    message={searchTerm ? `No departments match "${searchTerm}"` : "No departments available"}
                    action={{
                      label: "Clear Search",
                      onClick: () => setSearchTerm('')
                    }}
                    icon={<Building className="w-12 h-12 text-text-secondary" />}
                  />
                ) : (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {filteredDepartments.map(department => (
                    <div key={department.id} className="bg-surface border border-border rounded-lg p-6 hover:shadow-lg transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold text-text mb-1">{department.name}</h3>
                          <p className="text-sm text-text-secondary">{department.description}</p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskColor(department.riskLevel)}`}>
                          {department.riskLevel.toUpperCase()}
                        </span>
                      </div>
                      
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-text-secondary">Compliance Score</span>
                          <span className="text-lg font-semibold text-primary">{department.complianceScore}%</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-text-secondary">Employees</span>
                          <span className="text-sm font-medium text-text">{department.employeeCount}</span>
                        </div>
                        
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-text-secondary">Head</span>
                          <span className="text-sm font-medium text-text">{department.head}</span>
                        </div>
                        
                        <div className="pt-3 border-t border-border">
                          <div className="grid grid-cols-2 gap-4 text-center">
                            <div>
                              <div className="text-lg font-semibold text-text">{department.complianceMetrics.completedAssessments}</div>
                              <div className="text-xs text-text-secondary">Completed</div>
                            </div>
                            <div>
                              <div className="text-lg font-semibold text-text">{department.complianceMetrics.pendingAssessments}</div>
                              <div className="text-xs text-text-secondary">Pending</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
            </EnhancedErrorBoundary>
          )}

          {activeTab === 'policies' && (
            <EnhancedErrorBoundary level="section" showDetails={false}>
              <div className="space-y-6">
                {errors.policies ? (
                  <ErrorState
                    title="Policy Loading Error"
                    message={errors.policies}
                    onRetry={loadPolicies}
                  />
                ) : loading.policies ? (
                  <DataLoadingState type="policies" />
                ) : filteredPolicies.length === 0 ? (
                  <EmptyState
                    title="No Policies Found"
                    message={searchTerm ? `No policies match "${searchTerm}"` : "No policies available"}
                    action={{
                      label: "Create New Policy",
                      onClick: handleCreatePolicy
                    }}
                    icon={<FileText className="w-12 h-12 text-text-secondary" />}
                  />
                ) : (
                <div className="space-y-4">
                  {filteredPolicies.map(policy => (
                    <div key={policy.id} className="bg-surface border border-border rounded-lg p-6 hover:shadow-lg transition-shadow">
                      <div className="flex items-start justify-between mb-4">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="text-lg font-semibold text-text">{policy.name}</h3>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(policy.status)}`}>
                              {policy.status.replace('_', ' ').toUpperCase()}
                            </span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              policy.priority === 'critical' ? 'text-red-600 bg-red-100' :
                              policy.priority === 'high' ? 'text-orange-600 bg-orange-100' :
                              'text-blue-600 bg-blue-100'
                            }`}>
                              {policy.priority.toUpperCase()}
                            </span>
                          </div>
                          <p className="text-sm text-text-secondary mb-3">{policy.description}</p>

                          <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-text-secondary">Version:</span>
                              <span className="ml-2 font-medium text-text">{policy.version}</span>
                            </div>
                            <div>
                              <span className="text-text-secondary">Owner:</span>
                              <span className="ml-2 font-medium text-text">{policy.owner}</span>
                            </div>
                            <div>
                              <span className="text-text-secondary">Category:</span>
                              <span className="ml-2 font-medium text-text capitalize">{policy.category.replace('_', ' ')}</span>
                            </div>
                            <div>
                              <span className="text-text-secondary">Compliance:</span>
                              <span className="ml-2 font-medium text-primary">{policy.metrics.complianceRate}%</span>
                            </div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-2">
                          <button
                            onClick={() => handleViewPolicy(policy)}
                            className="p-2 text-text-secondary hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors"
                            title="View Policy"
                          >
                            <FileText className="w-4 h-4" />
                          </button>
                          <button
                            onClick={() => handleEditPolicy(policy)}
                            className="p-2 text-text-secondary hover:text-primary hover:bg-surface-hover rounded-lg transition-colors"
                            title="Edit Policy"
                          >
                            <Edit className="w-4 h-4" />
                          </button>
                          <button
                            className="p-2 text-text-secondary hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors"
                            title="Download Policy"
                          >
                            <Download className="w-4 h-4" />
                          </button>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 pt-4 border-t border-border">
                        <div className="text-center">
                          <div className="text-lg font-semibold text-text">{policy.metrics.complianceRate}%</div>
                          <div className="text-xs text-text-secondary">Compliance Rate</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-text">{policy.metrics.trainingCompletion}%</div>
                          <div className="text-xs text-text-secondary">Training Complete</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-text">{policy.metrics.acknowledgmentRate}%</div>
                          <div className="text-xs text-text-secondary">Acknowledged</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-semibold text-text">{policy.metrics.violationCount}</div>
                          <div className="text-xs text-text-secondary">Violations</div>
                        </div>
                      </div>

                      {policy.applicableDepartments.length > 0 && (
                        <div className="mt-4 pt-4 border-t border-border">
                          <div className="text-sm text-text-secondary mb-2">Applicable Departments:</div>
                          <div className="flex flex-wrap gap-2">
                            {policy.applicableDepartments.map(deptId => {
                              const dept = departments.find(d => d.id === deptId);
                              return dept ? (
                                <span key={deptId} className="px-2 py-1 bg-primary/10 text-primary rounded text-xs">
                                  {dept.name}
                                </span>
                              ) : null;
                            })}
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
            </EnhancedErrorBoundary>
          )}

          {activeTab === 'metrics' && (
            <EnhancedErrorBoundary level="section" showDetails={false}>
              <div className="space-y-6">
                {errors.metrics ? (
                  <ErrorState
                    title="Metrics Loading Error"
                    message={errors.metrics}
                    onRetry={loadMetrics}
                  />
                ) : loading.metrics ? (
                  <DataLoadingState type="metrics" />
                ) : (
                  <MetricsVisualization
                    departments={departments}
                    policies={policies}
                    refreshInterval={30000}
                  />
                )}
              </div>
            </EnhancedErrorBoundary>
          )}
        </div>

        {/* Policy Management Modal */}
        <PolicyManagementModal
          isOpen={policyModalOpen}
          onClose={() => setPolicyModalOpen(false)}
          policy={selectedPolicy}
          departments={departments}
          onSave={handleSavePolicy}
          mode={policyModalMode}
        />
      </div>
    </ErrorBoundary>
  );
};
