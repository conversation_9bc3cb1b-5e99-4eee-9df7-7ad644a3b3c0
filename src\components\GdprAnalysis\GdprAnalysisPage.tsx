import React, { useState, useRef } from 'react';
import { Upload, FileText, Send, Download, AlertCircle, CheckCircle, XCircle, Clock } from 'lucide-react';
import { toast } from 'react-toastify';
import { gdprApiService, AnalysisResponse, ComplianceResult } from '../../services/gdprApiService';
import { ErrorBoundary } from '../ui/ErrorBoundary';

const GdprAnalysisPage: React.FC = () => {
  const [analysisMode, setAnalysisMode] = useState<'text' | 'file'>('text');
  const [policyText, setPolicyText] = useState('');
  const [policyName, setPolicyName] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<AnalysisResponse | null>(null);
  const [apiHealth, setApiHealth] = useState<'checking' | 'healthy' | 'error'>('checking');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Check API health on component mount
  React.useEffect(() => {
    checkApiHealth();
  }, []);

  const checkApiHealth = async () => {
    try {
      await gdprApiService.checkHealth();
      setApiHealth('healthy');
    } catch (error) {
      setApiHealth('error');
      console.error('API health check failed:', error);
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'text/plain', 'text/markdown'];
      const allowedExtensions = ['.pdf', '.txt', '.md'];
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
      
      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        toast.error('Please select a PDF, TXT, or MD file');
        return;
      }
      
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        toast.error('File size must be less than 10MB');
        return;
      }
      
      setSelectedFile(file);
      if (!policyName) {
        setPolicyName(file.name.replace(/\.[^/.]+$/, '')); // Remove extension
      }
    }
  };

  const analyzeText = async () => {
    if (!policyText.trim()) {
      toast.error('Please enter a privacy policy text');
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await gdprApiService.analyzeText(policyText, policyName || 'Unnamed Policy');
      setAnalysisResult(result);
      toast.success('Analysis completed successfully!');
    } catch (error) {
      console.error('Analysis error:', error);
      toast.error(error instanceof Error ? error.message : 'Analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const analyzeFile = async () => {
    if (!selectedFile) {
      toast.error('Please select a file');
      return;
    }

    setIsAnalyzing(true);
    try {
      const result = await gdprApiService.analyzeFile(selectedFile, policyName);
      setAnalysisResult(result);
      toast.success('File analysis completed successfully!');
    } catch (error) {
      console.error('File analysis error:', error);
      toast.error(error instanceof Error ? error.message : 'File analysis failed');
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'compliant':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'partially compliant':
        return <Clock className="w-5 h-5 text-yellow-500" />;
      case 'non-compliant':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <AlertCircle className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'compliant':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'partially compliant':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'non-compliant':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const downloadAnalysisReport = () => {
    if (!analysisResult) return;
    
    const reportData = {
      analysis_summary: {
        policy_name: analysisResult.policy_name,
        analysis_date: analysisResult.timestamp,
        compliance_score: analysisResult.compliance_score,
        compliance_level: analysisResult.compliance_level,
        total_requirements: analysisResult.total_requirements,
        status_breakdown: analysisResult.status_breakdown
      },
      detailed_results: analysisResult.compliance_results,
      detailed_analysis: analysisResult.detailed_analysis
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `gdpr_analysis_${analysisResult.analysis_id}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">GDPR Analysis Error</h2>
            <p className="text-text-secondary">Something went wrong loading the GDPR analysis page. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className="min-h-screen bg-background text-text p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-2">GDPR Compliance Analysis</h1>
          <p className="text-text-secondary">
            Analyze privacy policies for GDPR compliance using AI-powered analysis
          </p>
          
          {/* API Status */}
          <div className="mt-4 flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              apiHealth === 'healthy' ? 'bg-green-500' : 
              apiHealth === 'error' ? 'bg-red-500' : 'bg-yellow-500'
            }`}></div>
            <span className="text-sm text-text-secondary">
              API Status: {apiHealth === 'healthy' ? 'Connected' : 
                          apiHealth === 'error' ? 'Disconnected (Please start the API server)' : 'Checking...'}
            </span>
            <button 
              onClick={checkApiHealth}
              className="text-xs text-primary hover:underline"
            >
              Refresh
            </button>
          </div>
        </div>

        {/* Analysis Mode Selection */}
        <div className="bg-surface border border-border rounded-lg p-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Analysis Method</h2>
          
          <div className="flex space-x-4 mb-6">
            <button
              onClick={() => setAnalysisMode('text')}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                analysisMode === 'text'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-surface text-text border-border hover:bg-primary/10'
              }`}
            >
              <FileText className="w-4 h-4 inline mr-2" />
              Text Input
            </button>
            <button
              onClick={() => setAnalysisMode('file')}
              className={`px-4 py-2 rounded-lg border transition-colors ${
                analysisMode === 'file'
                  ? 'bg-primary text-white border-primary'
                  : 'bg-surface text-text border-border hover:bg-primary/10'
              }`}
            >
              <Upload className="w-4 h-4 inline mr-2" />
              File Upload
            </button>
          </div>

          {/* Policy Name Input */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-text mb-2">
              Policy Name (Optional)
            </label>
            <input
              type="text"
              value={policyName}
              onChange={(e) => setPolicyName(e.target.value)}
              placeholder="Enter a name for this policy analysis"
              className="w-full px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
            />
          </div>

          {/* Text Input Mode */}
          {analysisMode === 'text' && (
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Privacy Policy Text
              </label>
              <textarea
                value={policyText}
                onChange={(e) => setPolicyText(e.target.value)}
                placeholder="Paste your privacy policy text here..."
                className="w-full h-64 px-3 py-2 border border-border rounded-lg bg-background text-text focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-vertical"
              />
              <div className="mt-4">
                <button
                  onClick={analyzeText}
                  disabled={isAnalyzing || apiHealth !== 'healthy'}
                  className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {isAnalyzing ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Analyzing...
                    </>
                  ) : (
                    <>
                      <Send className="w-4 h-4 mr-2" />
                      Analyze Policy
                    </>
                  )}
                </button>
              </div>
            </div>
          )}

          {/* File Upload Mode */}
          {analysisMode === 'file' && (
            <div>
              <label className="block text-sm font-medium text-text mb-2">
                Upload Policy File
              </label>
              <div
                onClick={() => fileInputRef.current?.click()}
                className="border-2 border-dashed border-border rounded-lg p-8 text-center cursor-pointer hover:border-primary transition-colors"
              >
                <Upload className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                <p className="text-text-secondary">
                  {selectedFile ? selectedFile.name : 'Click to select or drag and drop a file'}
                </p>
                <p className="text-sm text-text-tertiary mt-2">
                  Supports PDF, TXT, and MD files (max 10MB)
                </p>
              </div>
              <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.txt,.md"
                onChange={handleFileSelect}
                className="hidden"
              />
              
              {selectedFile && (
                <div className="mt-4">
                  <button
                    onClick={analyzeFile}
                    disabled={isAnalyzing || apiHealth !== 'healthy'}
                    className="px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                  >
                    {isAnalyzing ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Analyzing File...
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Analyze File
                      </>
                    )}
                  </button>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Analysis Results */}
        {analysisResult && (
          <div className="space-y-6">
            {/* Summary Card */}
            <div className="bg-surface border border-border rounded-lg p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h2 className="text-xl font-semibold">Analysis Summary</h2>
                  <p className="text-text-secondary">Policy: {analysisResult.policy_name}</p>
                  <p className="text-sm text-text-tertiary">
                    Analyzed on: {new Date(analysisResult.timestamp).toLocaleString()}
                  </p>
                </div>
                <button
                  onClick={downloadAnalysisReport}
                  className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors flex items-center"
                >
                  <Download className="w-4 h-4 mr-2" />
                  Download Report
                </button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-background border border-border rounded-lg p-4">
                  <h3 className="text-sm font-medium text-text-secondary">Compliance Score</h3>
                  <p className="text-2xl font-bold text-primary">{analysisResult.compliance_score}%</p>
                </div>
                <div className="bg-background border border-border rounded-lg p-4">
                  <h3 className="text-sm font-medium text-text-secondary">Compliance Level</h3>
                  <p className="text-lg font-medium text-text">{analysisResult.compliance_level}</p>
                </div>
                <div className="bg-background border border-border rounded-lg p-4">
                  <h3 className="text-sm font-medium text-text-secondary">Total Requirements</h3>
                  <p className="text-2xl font-bold text-text">{analysisResult.total_requirements}</p>
                </div>
              </div>

              {/* Status Breakdown */}
              <div className="mt-4">
                <h3 className="text-sm font-medium text-text-secondary mb-2">Status Breakdown</h3>
                <div className="flex flex-wrap gap-2">
                  {Object.entries(analysisResult.status_breakdown).map(([status, count]) => (
                    <span
                      key={status}
                      className={`px-3 py-1 rounded-full text-sm border ${getStatusColor(status)}`}
                    >
                      {status}: {count}
                    </span>
                  ))}
                </div>
              </div>
            </div>

            {/* Detailed Results */}
            <div className="bg-surface border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Detailed Compliance Results</h2>
              
              <div className="space-y-4">
                {analysisResult.compliance_results.map((result, index) => (
                  <div key={index} className="border border-border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-medium text-text">{result.gdpr_requirement}</h3>
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(result.compliance_status)}
                        <span className={`px-2 py-1 rounded text-xs border ${getStatusColor(result.compliance_status)}`}>
                          {result.compliance_status}
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium text-text-secondary mb-1">Company Policy</h4>
                        <p className="text-sm text-text">{result.company_policy}</p>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium text-text-secondary mb-1">Notes & Gaps</h4>
                        <p className="text-sm text-text">{result.notes_gaps}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Detailed Analysis Text */}
            <div className="bg-surface border border-border rounded-lg p-6">
              <h2 className="text-xl font-semibold mb-4">Detailed Analysis</h2>
              <div className="prose prose-sm max-w-none text-text">
                <pre className="whitespace-pre-wrap font-sans text-sm text-text">
                  {analysisResult.detailed_analysis}
                </pre>
              </div>
            </div>
          </div>
        )}

        {/* API Connection Instructions */}
        {apiHealth === 'error' && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 mt-6">
            <h3 className="text-lg font-semibold text-red-800 mb-2">API Server Setup Required</h3>
            <p className="text-red-700 mb-4">
              To use the GDPR analysis feature, you need to set up the FastAPI server:
            </p>
            <div className="bg-red-100 border border-red-300 rounded p-3 mb-4">
              <h4 className="font-semibold text-red-800 mb-2">Step 1: Configure Cohere API Key</h4>
              <p className="font-mono text-sm text-red-800 mb-2">
                1. Get a free API key from <a href="https://cohere.ai" target="_blank" rel="noopener noreferrer" className="underline">cohere.ai</a><br/>
                2. Open the .env file in your project directory<br/>
                3. Replace: <code className="bg-red-200 px-1 rounded">COHERE_API_KEY=your_cohere_api_key_here</code><br/>
                4. Save the file
              </p>
            </div>
            <div className="bg-red-100 border border-red-300 rounded p-3">
              <h4 className="font-semibold text-red-800 mb-2">Step 2: Start the API Server</h4>
              <p className="font-mono text-sm text-red-800">
                1. Open Anaconda Command Prompt<br/>
                2. Navigate to your project directory<br/>
                3. Run: <code className="bg-red-200 px-1 rounded">python gdpr_compliance_fastapi.py</code><br/>
                4. The API should be available at: <code className="bg-red-200 px-1 rounded">http://127.0.0.1:8000</code>
              </p>
            </div>
            <div className="mt-4 p-3 bg-yellow-100 border border-yellow-300 rounded">
              <p className="text-yellow-800 text-sm">
                <strong>Note:</strong> If you see "COHERE_API_KEY not found" in the server logs, 
                make sure you've added your API key to the .env file and restarted the server.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
    </ErrorBoundary>
  );
};

export default GdprAnalysisPage;
