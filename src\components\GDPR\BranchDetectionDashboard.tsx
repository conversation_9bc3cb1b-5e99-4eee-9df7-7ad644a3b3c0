import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { Branch, DataFlow } from '../../types/gdprTypes';
import { BranchDetectionService } from '../../services/branchDetectionService';
import { BranchModal, DataFlowModal } from './BranchModal';
import { ConfirmationModal } from '../ui/Modal';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button, IconButton } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import {
  Globe,
  Building,
  MapPin,
  Shield,
  AlertTriangle,
  CheckCircle,
  TrendingUp,
  TrendingDown,
  Users,
  Database,
  ArrowRight,
  Plus,
  Filter,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  X
} from 'lucide-react';

interface BranchDetectionDashboardProps {
  className?: string;
}

export const BranchDetectionDashboard: React.FC<BranchDetectionDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  const [branches, setBranches] = useState<Branch[]>([]);
  const [dataFlows, setDataFlows] = useState<DataFlow[]>([]);
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [activeView, setActiveView] = useState<'overview' | 'branches' | 'flows' | 'map'>('overview');

  // CRUD Modal States
  const [showBranchModal, setShowBranchModal] = useState(false);
  const [showDataFlowModal, setShowDataFlowModal] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [editingDataFlow, setEditingDataFlow] = useState<DataFlow | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingItem, setDeletingItem] = useState<{ type: 'branch' | 'dataflow'; item: any } | null>(null);

  // Search and Filter States
  const [branchSearchTerm, setBranchSearchTerm] = useState('');
  const [flowSearchTerm, setFlowSearchTerm] = useState('');
  const [riskFilter, setRiskFilter] = useState('all');
  const [regionFilter, setRegionFilter] = useState('all');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      console.log('🔄 Loading branch detection data...');

      // Initialize service first
      BranchDetectionService.initialize();

      const [branchesData, flowsData, metricsData] = await Promise.all([
        BranchDetectionService.getAllBranches(),
        BranchDetectionService.getAllDataFlows(),
        BranchDetectionService.getBranchMetrics()
      ]);

      console.log('✅ Branch detection data loaded:', {
        branches: branchesData?.length || 0,
        flows: flowsData?.length || 0,
        metrics: metricsData
      });

      setBranches(branchesData || []);
      setDataFlows(flowsData || []);
      setMetrics(metricsData || null);
    } catch (error) {
      console.error('❌ Error loading branch data:', error);
      notifications.error('Failed to load branch data');
      // Set empty data to prevent crashes
      setBranches([]);
      setDataFlows([]);
      setMetrics(null);
    } finally {
      setLoading(false);
    }
  };

  // Branch CRUD Operations
  const handleCreateBranch = () => {
    setEditingBranch(null);
    setShowBranchModal(true);
  };

  const handleEditBranch = (branch: Branch) => {
    setEditingBranch(branch);
    setShowBranchModal(true);
  };

  const handleSaveBranch = async (branchData: Partial<Branch>) => {
    try {
      if (editingBranch) {
        // Update existing branch
        const updatedBranch = await BranchDetectionService.updateBranch(editingBranch.id, branchData);
        setBranches(prev => prev.map(b => b.id === editingBranch.id ? updatedBranch : b));
        notifications.success('Branch updated successfully');
      } else {
        // Create new branch
        const newBranch = await BranchDetectionService.createBranch(branchData);
        setBranches(prev => [newBranch, ...prev]);
        notifications.success('Branch created successfully');
      }

      // Refresh metrics
      const updatedMetrics = await BranchDetectionService.getBranchMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error saving branch:', error);
      notifications.error('Failed to save branch');
      throw error;
    }
  };

  const handleDeleteBranch = (branch: Branch) => {
    setDeletingItem({ type: 'branch', item: branch });
    setShowDeleteConfirm(true);
  };

  // Data Flow CRUD Operations
  const handleCreateDataFlow = () => {
    setEditingDataFlow(null);
    setShowDataFlowModal(true);
  };

  const handleEditDataFlow = (dataFlow: DataFlow) => {
    setEditingDataFlow(dataFlow);
    setShowDataFlowModal(true);
  };

  const handleSaveDataFlow = async (dataFlowData: Partial<DataFlow>) => {
    try {
      if (editingDataFlow) {
        // Update existing data flow
        const updatedDataFlow = await BranchDetectionService.updateDataFlow(editingDataFlow.id, dataFlowData);
        setDataFlows(prev => prev.map(df => df.id === editingDataFlow.id ? updatedDataFlow : df));
        notifications.success('Data flow updated successfully');
      } else {
        // Create new data flow
        const newDataFlow = await BranchDetectionService.createDataFlow(dataFlowData);
        setDataFlows(prev => [newDataFlow, ...prev]);
        notifications.success('Data flow created successfully');
      }

      // Refresh metrics
      const updatedMetrics = await BranchDetectionService.getBranchMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error saving data flow:', error);
      notifications.error('Failed to save data flow');
      throw error;
    }
  };

  const handleDeleteDataFlow = (dataFlow: DataFlow) => {
    setDeletingItem({ type: 'dataflow', item: dataFlow });
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!deletingItem) return;

    try {
      if (deletingItem.type === 'branch') {
        await BranchDetectionService.deleteBranch(deletingItem.item.id);
        setBranches(prev => prev.filter(b => b.id !== deletingItem.item.id));
        notifications.success('Branch deleted successfully');
      } else {
        await BranchDetectionService.deleteDataFlow(deletingItem.item.id);
        setDataFlows(prev => prev.filter(df => df.id !== deletingItem.item.id));
        notifications.success('Data flow deleted successfully');
      }

      // Refresh metrics
      const updatedMetrics = await BranchDetectionService.getBranchMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error deleting item:', error);
      notifications.error(`Failed to delete ${deletingItem.type}`);
    } finally {
      setShowDeleteConfirm(false);
      setDeletingItem(null);
    }
  };

  // Filter functions
  const getFilteredBranches = () => {
    let filtered = branches;

    if (branchSearchTerm) {
      filtered = filtered.filter(branch =>
        branch?.name?.toLowerCase()?.includes(branchSearchTerm.toLowerCase()) ||
        branch?.location?.toLowerCase()?.includes(branchSearchTerm.toLowerCase()) ||
        branch?.country?.toLowerCase()?.includes(branchSearchTerm.toLowerCase())
      );
    }

    if (riskFilter !== 'all') {
      filtered = filtered.filter(branch => branch.riskLevel === riskFilter);
    }

    if (regionFilter !== 'all') {
      filtered = filtered.filter(branch => branch.region === regionFilter);
    }

    return filtered;
  };

  const getFilteredDataFlows = () => {
    let filtered = dataFlows;

    if (flowSearchTerm) {
      const fromBranch = branches.find(b => b.id === filtered[0]?.fromBranch);
      const toBranch = branches.find(b => b.id === filtered[0]?.toBranch);

      filtered = filtered.filter(flow => {
        const fromBranchName = branches.find(b => b.id === flow.fromBranch)?.name || '';
        const toBranchName = branches.find(b => b.id === flow.toBranch)?.name || '';

        return fromBranchName?.toLowerCase()?.includes(flowSearchTerm.toLowerCase()) ||
               toBranchName?.toLowerCase()?.includes(flowSearchTerm.toLowerCase()) ||
               flow?.dataType?.toLowerCase()?.includes(flowSearchTerm.toLowerCase()) ||
               flow?.purpose?.toLowerCase()?.includes(flowSearchTerm.toLowerCase());
      });
    }

    return filtered;
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'partially_compliant': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'non_compliant': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'under_review': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getBranchTypeIcon = (type: string) => {
    switch (type) {
      case 'headquarters': return <Building className="w-5 h-5 text-primary" />;
      case 'subsidiary': return <Globe className="w-5 h-5 text-secondary" />;
      case 'branch_office': return <MapPin className="w-5 h-5 text-accent-purple" />;
      case 'data_center': return <Database className="w-5 h-5 text-primary" />;
      case 'service_provider': return <Users className="w-5 h-5 text-secondary" />;
      default: return <Building className="w-5 h-5 text-text-secondary" />;
    }
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-text-secondary">Loading branch data...</span>
      </div>
    );
  }

  return (
    <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Globe className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Branch Detection & Data Flow</h2>
            <p className="text-text-secondary">Monitor organizational branches and cross-border data transfers</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <Button
            onClick={handleCreateDataFlow}
            variant="outline"
            leftIcon={<ArrowRight className="w-4 h-4" />}
          >
            Add Data Flow
          </Button>
          <Button
            onClick={handleCreateBranch}
            leftIcon={<Plus className="w-4 h-4" />}
          >
            Add Branch
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-surface rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview', icon: TrendingUp },
          { id: 'branches', label: 'Branches', icon: Building },
          { id: 'flows', label: 'Data Flows', icon: ArrowRight },
          { id: 'map', label: 'Global Map', icon: Globe }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveView(tab.id as any)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              activeView === tab.id
                ? 'bg-primary text-white'
                : 'text-text-secondary hover:text-text hover:bg-card'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeView === 'overview' && metrics && (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                  <Building className="w-6 h-6 text-primary" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalBranches}</div>
              <div className="text-text-secondary text-sm">Total Branches</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                  <ArrowRight className="w-6 h-6 text-secondary" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.totalDataFlows}</div>
              <div className="text-text-secondary text-sm">Active Data Flows</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-accent-purple/10'} rounded-lg`}>
                  <Globe className="w-6 h-6 text-accent-purple" />
                </div>
                <AlertTriangle className="w-5 h-5 text-orange-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.crossBorderTransfers}</div>
              <div className="text-text-secondary text-sm">Cross-Border Transfers</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-green-500/10'} rounded-lg`}>
                  <Shield className="w-6 h-6 text-green-500" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics.complianceScore.toFixed(1)}%</div>
              <div className="text-text-secondary text-sm">Compliance Score</div>
            </div>
          </div>

          {/* Compliance Status Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Compliance Status Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byComplianceStatus).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getComplianceStatusColor(status)} text-xs font-bold rounded-full uppercase`}>
                        {status ? status.replace('_', ' ') : 'Unknown'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${(count as number / metrics.totalBranches) * 100}%`}}
                        ></div>
                      </div>
                      <span className="text-sm font-semibold text-text w-8">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Risk Level Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics.byRiskLevel).map(([level, count]) => (
                  <div key={level} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getRiskLevelColor(level)} text-xs font-bold rounded-full uppercase`}>
                        {level}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${(count as number / metrics.totalBranches) * 100}%`}}
                        ></div>
                      </div>
                      <span className="text-sm font-semibold text-text w-8">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Branches Tab */}
      {activeView === 'branches' && (
        <div className="space-y-6">
          {/* Enhanced Search and Filter */}
          <div className={`${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-card via-surface to-card'} rounded-2xl p-6 border border-border ${mode === 'dark' ? '' : 'shadow-xl'}`}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-text">Branch Management</h3>
              <Button
                onClick={handleCreateBranch}
                leftIcon={<Plus className="w-4 h-4" />}
                size="sm"
              >
                Add Branch
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <FormInput
                placeholder="Search branches..."
                value={branchSearchTerm}
                onChange={(e) => setBranchSearchTerm(e.target.value)}
                leftIcon={<Search className="w-4 h-4" />}
                rightIcon={branchSearchTerm && (
                  <button onClick={() => setBranchSearchTerm('')}>
                    <X className="w-4 h-4" />
                  </button>
                )}
              />

              <FormSelect
                value={riskFilter}
                onChange={(e) => setRiskFilter(e.target.value)}
                options={[
                  { value: 'all', label: 'All Risk Levels' },
                  { value: 'low', label: 'Low Risk' },
                  { value: 'medium', label: 'Medium Risk' },
                  { value: 'high', label: 'High Risk' },
                  { value: 'critical', label: 'Critical Risk' }
                ]}
              />

              <FormSelect
                value={regionFilter}
                onChange={(e) => setRegionFilter(e.target.value)}
                options={[
                  { value: 'all', label: 'All Regions' },
                  { value: 'europe', label: 'Europe' },
                  { value: 'north_america', label: 'North America' },
                  { value: 'asia_pacific', label: 'Asia Pacific' },
                  { value: 'middle_east', label: 'Middle East' }
                ]}
              />

              <div className="flex items-center gap-2">
                <span className="text-sm text-text-secondary">
                  {getFilteredBranches().length} of {branches.length} branches
                </span>
              </div>
            </div>
          </div>

          {/* Enhanced Branches List */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {getFilteredBranches().map((branch) => (
              <div
                key={branch.id}
                className={`group relative p-6 cursor-pointer ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-primary/30 hover:shadow-lg'} rounded-xl transition-all duration-300`}
                onClick={() => setSelectedBranch(branch)}
              >
                {mode === 'light' && (
                  <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                )}
                
                <div className="relative z-10">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <div className={`p-2 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                        <Building className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <h3 className="text-lg font-semibold text-text">{branch.name}</h3>
                        <p className="text-text-secondary text-sm">{branch?.region?.replace('_', ' ') || 'Unknown'}</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">

                      <IconButton
                        icon={<Edit className="w-4 h-4" />}
                        ariaLabel="Edit branch"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleEditBranch(branch)}
                      />
                      <IconButton
                        icon={<Trash2 className="w-4 h-4" />}
                        ariaLabel="Delete branch"
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBranch(branch)}
                      />
                    </div>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-text-secondary" />
                      <span className="text-text-secondary text-sm">
                        {branch.location}, {branch.country}
                      </span>
                    </div>

                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 text-xs font-bold rounded-full uppercase ${
                        branch.riskLevel === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                        branch.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                        branch.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                        'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                      }`}>
                        {branch.riskLevel} Risk
                      </span>
                      <span className="text-xs text-text-secondary">
                        Contact: {branch.contactPerson}
                      </span>
                    </div>

                    <div className="flex items-center justify-between text-sm text-text-secondary">
                      <span>Compliance Score</span>
                      <span className="font-semibold text-primary">{branch.complianceScore}%</span>
                    </div>

                    <div className="flex items-center gap-2">
                      <div className="flex-1 bg-border rounded-full h-2">
                        <div
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${branch.complianceScore}%`}}
                        ></div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-xs text-text-secondary">
                      <span>Data Flows: {branch.dataFlows?.length || 0}</span>
                      <span>Regulations: {branch.regulations?.length || 0}</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {getFilteredBranches().length === 0 && (
            <div className="text-center py-12">
              <Building className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text mb-2">No branches found</h3>
              <p className="text-text-secondary mb-4">
                {branchSearchTerm || riskFilter !== 'all' || regionFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first branch to get started'
                }
              </p>
              {!branchSearchTerm && riskFilter === 'all' && regionFilter === 'all' && (
                <Button onClick={handleCreateBranch} leftIcon={<Plus className="w-4 h-4" />}>
                  Create First Branch
                </Button>
              )}
            </div>
          )}
        </div>
      )}

      {/* Data Flows Tab */}
      {activeView === 'flows' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <ArrowRight className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Data Flow Visualization</h3>
            <p className="text-text-secondary">Interactive data flow mapping coming soon</p>
          </div>
        </div>
      )}

      {/* Global Map Tab */}
      {activeView === 'map' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <Globe className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Global Branch Map</h3>
            <p className="text-text-secondary">Interactive world map visualization coming soon</p>
          </div>
        </div>
      )}

      {/* Modals */}
      <BranchModal
        isOpen={showBranchModal}
        onClose={() => setShowBranchModal(false)}
        onSave={handleSaveBranch}
        branch={editingBranch}
        mode={editingBranch ? 'edit' : 'create'}
      />

      <DataFlowModal
        isOpen={showDataFlowModal}
        onClose={() => setShowDataFlowModal(false)}
        onSave={handleSaveDataFlow}
        dataFlow={editingDataFlow}
        mode={editingDataFlow ? 'edit' : 'create'}
        branches={branches}
      />

      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
        title={`Delete ${deletingItem?.type === 'branch' ? 'Branch' : 'Data Flow'}`}
        message={`Are you sure you want to delete ${deletingItem?.type === 'branch' ? `the branch "${deletingItem?.item?.name}"` : 'this data flow'}? This action cannot be undone.`}
        confirmText="Delete"
        type="danger"
      />

      {/* Notifications */}
      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
    </div>
  );
};
