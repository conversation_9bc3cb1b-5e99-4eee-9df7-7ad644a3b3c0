/**
 * Integration Tests for Enterprise Dashboard
 * Tests component interactions, navigation flows, and data integration
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import '@testing-library/jest-dom';

// Test providers wrapper
const TestProviders: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <div data-testid="test-providers">
      {children}
    </div>
  );
};

// Mock all external dependencies
jest.mock('../../context/NavigationContext', () => ({
  useNavigation: () => ({
    currentPage: 'enterprise',
    isEnterpriseDashboard: true,
    setCurrentPage: jest.fn(),
  }),
}));

jest.mock('../../context/ThemeContext', () => ({
  useTheme: () => ({
    mode: 'light',
    toggleTheme: jest.fn(),
  }),
}));

jest.mock('../../context/ComplianceContext', () => ({
  useCompliance: () => ({
    fetchData: jest.fn(),
    metrics: {
      totalRequests: 150,
      pendingRequests: 12,
      completedRequests: 138,
      complianceScore: 94
    },
    isLoading: false,
  }),
}));

jest.mock('../../hooks/useAsyncError', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
  }),
}));

// Mock chart libraries
jest.mock('react-chartjs-2', () => ({
  Line: ({ data }: any) => <div data-testid="line-chart" data-chart-type="line" />,
  Bar: ({ data }: any) => <div data-testid="bar-chart" data-chart-type="bar" />,
  Doughnut: ({ data }: any) => <div data-testid="doughnut-chart" data-chart-type="doughnut" />,
  Radar: ({ data }: any) => <div data-testid="radar-chart" data-chart-type="radar" />,
}));

// Mock services with realistic data
const mockSecurityData = {
  overview: {
    systemsMonitored: 42,
    activeThreats: 3,
    securityScore: 95,
    lastUpdate: new Date()
  },
  systems: [
    { id: '1', name: 'Web Server', status: 'healthy', monitoring: { isActive: true } },
    { id: '2', name: 'Database', status: 'warning', monitoring: { isActive: true } }
  ],
  threats: [
    { id: '1', type: 'malware', severity: 'high', status: 'contained' }
  ],
  realTimeUpdates: {
    lastSync: new Date(),
    pendingAlerts: 2,
    isLive: true
  }
};

const mockGDPRData = {
  totalRequests: 150,
  pendingRequests: 12,
  completedRequests: 138,
  complianceScore: 94,
  recentActivities: [
    { id: '1', type: 'data_access', timestamp: new Date(), status: 'completed' }
  ]
};

jest.mock('../Security/services/SecurityDataService', () => ({
  generateSecurityOverviewData: jest.fn(() => mockSecurityData),
}));

jest.mock('../GDPR/services/GDPRService', () => ({
  getDashboardMetrics: jest.fn(() => Promise.resolve(mockGDPRData)),
  getAlerts: jest.fn(() => Promise.resolve([])),
  getActivities: jest.fn(() => Promise.resolve(mockGDPRData.recentActivities)),
}));

jest.mock('../GDPR/services/DataSubjectRequestService', () => ({
  getAllRequests: jest.fn(() => Promise.resolve([])),
}));

jest.mock('../GDPR/services/GDPRIntegrationService', () => ({
  getDashboardData: jest.fn(() => Promise.resolve({})),
}));

jest.mock('../Compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: jest.fn(() => ({
    overview: {
      totalMetrics: 25,
      activeFrameworks: 8,
      complianceScore: 92,
      lastAssessment: new Date()
    },
    metrics: [],
    frameworks: [],
    categoryBreakdown: {},
    realTimeUpdates: {
      lastSync: new Date(),
      pendingUpdates: 0
    }
  })),
}));

// Import components
import EnterpriseDashboard from '../../components/Dashboard/EnterpriseDashboard';

describe('Enterprise Dashboard Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Full Dashboard Integration', () => {
    test('complete dashboard loads with all components', async () => {
      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      // Wait for all components to load
      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Verify main sections are present
      expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      expect(screen.getByTestId('compliance-metrics')).toBeInTheDocument();
    });

    test('navigation between tabs maintains state', async () => {
      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Navigate to Security Overview
      const securityTab = screen.getByRole('button', { name: /security overview/i });
      fireEvent.click(securityTab);

      await waitFor(() => {
        expect(screen.getByTestId('security-overview')).toBeInTheDocument();
        // Compliance components should be hidden
        expect(screen.queryByTestId('gdpr-command-center')).not.toBeInTheDocument();
      });

      // Navigate back to Data Protection
      const dataProtectionTab = screen.getByRole('button', { name: /data protection/i });
      fireEvent.click(dataProtectionTab);

      await waitFor(() => {
        // Compliance components should be visible again
        expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
        expect(screen.getByTestId('compliance-metrics')).toBeInTheDocument();
      });
    });

    test('data flows correctly between components', async () => {
      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Verify data is displayed correctly
      await waitFor(() => {
        // Check if GDPR metrics are displayed
        expect(screen.getByText('150')).toBeInTheDocument(); // Total requests
        expect(screen.getByText('94')).toBeInTheDocument(); // Compliance score
      });

      // Switch to Security tab and verify data
      const securityTab = screen.getByRole('button', { name: /security overview/i });
      fireEvent.click(securityTab);

      await waitFor(() => {
        expect(screen.getByText('42')).toBeInTheDocument(); // Systems monitored
        expect(screen.getByText('95')).toBeInTheDocument(); // Security score
      });
    });

    test('error handling across components', async () => {
      // Mock one service to fail
      const mockGDPRService = require('../GDPR/services/GDPRService');
      mockGDPRService.getDashboardMetrics.mockRejectedValueOnce(new Error('Service unavailable'));

      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // GDPR component should show error, but others should still work
      await waitFor(() => {
        expect(screen.getByText(/gdpr data loading failed/i)).toBeInTheDocument();
        // Compliance metrics should still load
        expect(screen.getByTestId('compliance-metrics')).toBeInTheDocument();
      });
    });

    test('real-time updates work correctly', async () => {
      jest.useFakeTimers();

      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Fast-forward time to trigger real-time updates
      act(() => {
        jest.advanceTimersByTime(16000); // 16 seconds (more than 15s interval)
      });

      // Verify that real-time update functions are called
      await waitFor(() => {
        const securityService = require('../Security/services/SecurityDataService');
        expect(securityService.generateSecurityOverviewData).toHaveBeenCalled();
      });

      jest.useRealTimers();
    });

    test('responsive behavior and accessibility', async () => {
      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Test keyboard navigation
      const firstTab = screen.getByRole('button', { name: /data protection/i });
      firstTab.focus();
      expect(document.activeElement).toBe(firstTab);

      // Test ARIA attributes
      expect(firstTab).toHaveAttribute('role', 'button');
      expect(firstTab).toHaveAttribute('aria-selected');

      // Test responsive grid layouts
      const dashboardContainer = screen.getByTestId('enterprise-dashboard');
      expect(dashboardContainer).toHaveClass('grid');
    });

    test('performance optimization features', async () => {
      const renderSpy = jest.fn();
      
      // Mock React.memo to track re-renders
      const OriginalMemo = React.memo;
      React.memo = jest.fn((component) => {
        return OriginalMemo((props) => {
          renderSpy();
          return component(props);
        });
      });

      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      const initialRenderCount = renderSpy.mock.calls.length;

      // Trigger a state change that shouldn't cause unnecessary re-renders
      const securityTab = screen.getByRole('button', { name: /security overview/i });
      fireEvent.click(securityTab);

      await waitFor(() => {
        expect(screen.getByTestId('security-overview')).toBeInTheDocument();
      });

      // Verify minimal re-renders occurred
      const finalRenderCount = renderSpy.mock.calls.length;
      expect(finalRenderCount - initialRenderCount).toBeLessThan(5);

      // Restore original React.memo
      React.memo = OriginalMemo;
    });

    test('data persistence across navigation', async () => {
      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Load data in GDPR component
      await waitFor(() => {
        expect(screen.getByText('150')).toBeInTheDocument(); // Total requests
      });

      // Navigate away and back
      const securityTab = screen.getByRole('button', { name: /security overview/i });
      fireEvent.click(securityTab);

      await waitFor(() => {
        expect(screen.getByTestId('security-overview')).toBeInTheDocument();
      });

      const dataProtectionTab = screen.getByRole('button', { name: /data protection/i });
      fireEvent.click(dataProtectionTab);

      // Data should still be there (cached)
      await waitFor(() => {
        expect(screen.getByText('150')).toBeInTheDocument(); // Data persisted
      });
    });
  });

  describe('Error Recovery Integration', () => {
    test('component isolation during failures', async () => {
      // Mock multiple service failures
      const mockSecurityService = require('../Security/services/SecurityDataService');
      const mockGDPRService = require('../GDPR/services/GDPRService');
      
      mockSecurityService.generateSecurityOverviewData.mockImplementation(() => {
        throw new Error('Security service down');
      });
      mockGDPRService.getDashboardMetrics.mockRejectedValue(new Error('GDPR service down'));

      await act(async () => {
        render(
          <TestProviders>
            <EnterpriseDashboard />
          </TestProviders>
        );
      });

      await waitFor(() => {
        expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
      });

      // Dashboard should still render with error states
      await waitFor(() => {
        expect(screen.getByText(/gdpr data loading failed/i)).toBeInTheDocument();
      });

      // Switch to security tab - should show error but not crash
      const securityTab = screen.getByRole('button', { name: /security overview/i });
      fireEvent.click(securityTab);

      await waitFor(() => {
        expect(screen.getByText(/failed to load security data/i)).toBeInTheDocument();
      });

      // Compliance metrics should still work
      const dataProtectionTab = screen.getByRole('button', { name: /data protection/i });
      fireEvent.click(dataProtectionTab);

      await waitFor(() => {
        expect(screen.getByTestId('compliance-metrics')).toBeInTheDocument();
      });
    });
  });
});
