/**
 * Comprehensive Test Suite for Enterprise Dashboard Components
 * Tests component rendering, data loading states, user interactions, and error scenarios
 */

import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '@testing-library/react';
import { jest } from '@jest/globals';
import '@testing-library/jest-dom';

// Mock contexts and providers
const mockNavigationContext = {
  currentPage: 'enterprise',
  isEnterpriseDashboard: true,
  setCurrentPage: jest.fn(),
};

const mockThemeContext = {
  mode: 'light',
  toggleTheme: jest.fn(),
};

const mockComplianceContext = {
  fetchData: jest.fn(),
  metrics: null,
  isLoading: false,
};

// Mock components to avoid complex dependencies
jest.mock('../../context/NavigationContext', () => ({
  useNavigation: () => mockNavigationContext,
}));

jest.mock('../../context/ThemeContext', () => ({
  useTheme: () => mockThemeContext,
}));

jest.mock('../../context/ComplianceContext', () => ({
  useCompliance: () => mockComplianceContext,
}));

jest.mock('../../hooks/useAsyncError', () => ({
  useErrorHandler: () => ({
    handleError: jest.fn(),
    clearError: jest.fn(),
  }),
}));

// Mock chart components to avoid canvas issues in tests
jest.mock('react-chartjs-2', () => ({
  Line: ({ data, options }: any) => <div data-testid="line-chart" data-chart-data={JSON.stringify(data)} />,
  Bar: ({ data, options }: any) => <div data-testid="bar-chart" data-chart-data={JSON.stringify(data)} />,
  Doughnut: ({ data, options }: any) => <div data-testid="doughnut-chart" data-chart-data={JSON.stringify(data)} />,
  Radar: ({ data, options }: any) => <div data-testid="radar-chart" data-chart-data={JSON.stringify(data)} />,
}));

// Mock services
jest.mock('../Security/services/SecurityDataService', () => ({
  generateSecurityOverviewData: jest.fn(() => ({
    overview: { systemsMonitored: 42, activeThreats: 3, securityScore: 95 },
    systems: [],
    threats: [],
    realTimeUpdates: { lastSync: new Date(), pendingAlerts: 0 }
  })),
}));

jest.mock('../GDPR/services/GDPRService', () => ({
  getDashboardMetrics: jest.fn(() => Promise.resolve({
    totalRequests: 150,
    pendingRequests: 12,
    completedRequests: 138,
    complianceScore: 94
  })),
  getAlerts: jest.fn(() => Promise.resolve([])),
  getActivities: jest.fn(() => Promise.resolve([])),
}));

// Import components after mocking
import EnterpriseDashboard from '../../components/Dashboard/EnterpriseDashboard';
import { SecurityOverviewDashboardOptimized } from '../../components/Security/SecurityOverviewDashboardOptimized';
import { GDPRCommandCenter } from '../../components/GDPR/GDPRCommandCenter';
import { EnhancedComplianceMetricsOptimized } from '../../components/Compliance/EnhancedComplianceMetricsOptimized';

describe('Enterprise Dashboard Components', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset context values
    mockNavigationContext.currentPage = 'enterprise';
    mockNavigationContext.isEnterpriseDashboard = true;
    mockComplianceContext.isLoading = false;
    mockComplianceContext.metrics = null;
  });

  describe('EnterpriseDashboard', () => {
    test('renders without crashing', async () => {
      await act(async () => {
        render(<EnterpriseDashboard />);
      });
      
      expect(screen.getByTestId('enterprise-dashboard')).toBeInTheDocument();
    });

    test('shows loading state initially', async () => {
      mockComplianceContext.isLoading = true;
      
      await act(async () => {
        render(<EnterpriseDashboard />);
      });
      
      expect(screen.getByText(/loading/i)).toBeInTheDocument();
    });

    test('conditional rendering - compliance components only show on data_protection tab', async () => {
      await act(async () => {
        render(<EnterpriseDashboard />);
      });
      
      // Should show compliance components on data_protection tab (default)
      await waitFor(() => {
        expect(screen.queryByTestId('gdpr-command-center')).toBeInTheDocument();
        expect(screen.queryByTestId('compliance-metrics')).toBeInTheDocument();
      });
      
      // Switch to security tab
      const securityTab = screen.getByText(/security overview/i);
      fireEvent.click(securityTab);
      
      // Compliance components should be hidden
      await waitFor(() => {
        expect(screen.queryByTestId('gdpr-command-center')).not.toBeInTheDocument();
        expect(screen.queryByTestId('compliance-metrics')).not.toBeInTheDocument();
      });
    });

    test('tab navigation works correctly', async () => {
      await act(async () => {
        render(<EnterpriseDashboard />);
      });
      
      const securityTab = screen.getByText(/security overview/i);
      const dataProtectionTab = screen.getByText(/data protection/i);
      
      // Click security tab
      fireEvent.click(securityTab);
      await waitFor(() => {
        expect(screen.getByTestId('security-overview')).toBeInTheDocument();
      });
      
      // Click back to data protection tab
      fireEvent.click(dataProtectionTab);
      await waitFor(() => {
        expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      });
    });

    test('handles error states gracefully', async () => {
      // Mock error in useErrorHandler
      const mockHandleError = jest.fn();
      jest.mocked(require('../../hooks/useAsyncError').useErrorHandler).mockReturnValue({
        handleError: mockHandleError,
        clearError: jest.fn(),
      });
      
      await act(async () => {
        render(<EnterpriseDashboard />);
      });
      
      // Simulate an error
      const errorEvent = new Error('Test error');
      window.dispatchEvent(new ErrorEvent('error', { error: errorEvent }));
      
      await waitFor(() => {
        expect(mockHandleError).toHaveBeenCalledWith(errorEvent, 'global-error');
      });
    });
  });

  describe('SecurityOverviewDashboardOptimized', () => {
    test('renders security metrics correctly', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboardOptimized />);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/systems monitored/i)).toBeInTheDocument();
        expect(screen.getByText('42')).toBeInTheDocument(); // mocked value
      });
    });

    test('shows loading skeleton during data fetch', async () => {
      await act(async () => {
        render(<SecurityOverviewDashboardOptimized />);
      });
      
      expect(screen.getByTestId('loading-skeleton')).toBeInTheDocument();
    });

    test('handles data loading errors with retry functionality', async () => {
      // Mock service to throw error
      const mockService = require('../Security/services/SecurityDataService');
      mockService.generateSecurityOverviewData.mockImplementation(() => {
        throw new Error('Service unavailable');
      });
      
      await act(async () => {
        render(<SecurityOverviewDashboardOptimized />);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/failed to load security data/i)).toBeInTheDocument();
        expect(screen.getByText(/retry/i)).toBeInTheDocument();
      });
      
      // Test retry functionality
      const retryButton = screen.getByText(/retry/i);
      fireEvent.click(retryButton);
      
      expect(mockService.generateSecurityOverviewData).toHaveBeenCalledTimes(2);
    });
  });

  describe('GDPRCommandCenter', () => {
    test('renders GDPR overview correctly', async () => {
      await act(async () => {
        render(<GDPRCommandCenter />);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/gdpr command center/i)).toBeInTheDocument();
      });
    });

    test('handles service failures gracefully', async () => {
      // Mock service failures
      const mockGDPRService = require('../GDPR/services/GDPRService');
      mockGDPRService.getDashboardMetrics.mockRejectedValue(new Error('Service down'));
      
      await act(async () => {
        render(<GDPRCommandCenter />);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/gdpr data loading failed/i)).toBeInTheDocument();
      });
    });

    test('tab navigation within GDPR center works', async () => {
      await act(async () => {
        render(<GDPRCommandCenter />);
      });
      
      const requestsTab = screen.getByText(/requests/i);
      fireEvent.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText(/data subject requests/i)).toBeInTheDocument();
      });
    });
  });

  describe('EnhancedComplianceMetricsOptimized', () => {
    test('renders compliance metrics without errors', async () => {
      await act(async () => {
        render(<EnhancedComplianceMetricsOptimized />);
      });
      
      await waitFor(() => {
        expect(screen.getByTestId('compliance-metrics')).toBeInTheDocument();
      });
    });

    test('shows error state when data generation fails', async () => {
      // Mock data service to fail
      const mockDataService = require('../Compliance/services/ComplianceDataService');
      mockDataService.generateEnhancedComplianceData.mockImplementation(() => {
        throw new Error('Data generation failed');
      });
      
      await act(async () => {
        render(<EnhancedComplianceMetricsOptimized />);
      });
      
      await waitFor(() => {
        expect(screen.getByText(/compliance metrics unavailable/i)).toBeInTheDocument();
      });
    });

    test('visibility controls work correctly', async () => {
      await act(async () => {
        render(<EnhancedComplianceMetricsOptimized />);
      });
      
      await waitFor(() => {
        const visibilityToggle = screen.getByTestId('departments-visibility-toggle');
        fireEvent.click(visibilityToggle);
        
        expect(screen.queryByTestId('departments-section')).not.toBeVisible();
      });
    });
  });

  describe('Error Boundary Integration', () => {
    test('error boundaries catch component failures', async () => {
      // Mock console.error to avoid noise in test output
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation(() => {});
      
      // Create a component that throws an error
      const ThrowError = () => {
        throw new Error('Component error');
      };
      
      const { container } = render(
        <ErrorBoundary fallback={<div>Error caught</div>}>
          <ThrowError />
        </ErrorBoundary>
      );
      
      expect(screen.getByText('Error caught')).toBeInTheDocument();
      
      consoleSpy.mockRestore();
    });
  });
});
