export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  email?: boolean;
  url?: boolean;
  min?: number;
  max?: number;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface FormValidationResult {
  isValid: boolean;
  errors: ValidationError[];
}

export class FormValidator {
  private rules: Record<string, ValidationRule> = {};
  private errors: ValidationError[] = [];

  constructor(rules: Record<string, ValidationRule> = {}) {
    this.rules = rules;
  }

  addRule(field: string, rule: ValidationRule): FormValidator {
    this.rules[field] = rule;
    return this;
  }

  validate(data: Record<string, any>): FormValidationResult {
    this.errors = [];

    for (const [field, rule] of Object.entries(this.rules)) {
      const value = data[field];
      const error = this.validateField(field, value, rule);
      if (error) {
        this.errors.push(error);
      }
    }

    return {
      isValid: this.errors.length === 0,
      errors: this.errors
    };
  }

  private validateField(field: string, value: any, rule: ValidationRule): ValidationError | null {
    // Required validation
    if (rule.required && (value === undefined || value === null || value === '')) {
      return { field, message: `${this.formatFieldName(field)} is required` };
    }

    // Skip other validations if value is empty and not required
    if (!rule.required && (value === undefined || value === null || value === '')) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      // Min length
      if (rule.minLength && value.length < rule.minLength) {
        return { 
          field, 
          message: `${this.formatFieldName(field)} must be at least ${rule.minLength} characters` 
        };
      }

      // Max length
      if (rule.maxLength && value.length > rule.maxLength) {
        return { 
          field, 
          message: `${this.formatFieldName(field)} must be no more than ${rule.maxLength} characters` 
        };
      }

      // Email validation
      if (rule.email && !this.isValidEmail(value)) {
        return { field, message: `${this.formatFieldName(field)} must be a valid email address` };
      }

      // URL validation
      if (rule.url && !this.isValidUrl(value)) {
        return { field, message: `${this.formatFieldName(field)} must be a valid URL` };
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value)) {
        return { field, message: `${this.formatFieldName(field)} format is invalid` };
      }
    }

    // Number validations
    if (typeof value === 'number') {
      // Min value
      if (rule.min !== undefined && value < rule.min) {
        return { 
          field, 
          message: `${this.formatFieldName(field)} must be at least ${rule.min}` 
        };
      }

      // Max value
      if (rule.max !== undefined && value > rule.max) {
        return { 
          field, 
          message: `${this.formatFieldName(field)} must be no more than ${rule.max}` 
        };
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        return { field, message: customError };
      }
    }

    return null;
  }

  private formatFieldName(field: string): string {
    return field
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  private isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }
}

// Utility functions for common validations
export const createValidator = (rules: Record<string, ValidationRule>) => {
  return new FormValidator(rules);
};

export const validateRequired = (message?: string): ValidationRule => ({
  required: true,
  custom: message ? () => message : undefined
});

export const validateEmail = (required = false): ValidationRule => ({
  required,
  email: true
});

export const validateMinLength = (min: number, required = false): ValidationRule => ({
  required,
  minLength: min
});

export const validateMaxLength = (max: number, required = false): ValidationRule => ({
  required,
  maxLength: max
});

export const validateRange = (min: number, max: number, required = false): ValidationRule => ({
  required,
  min,
  max
});

export const validatePattern = (pattern: RegExp, message: string, required = false): ValidationRule => ({
  required,
  pattern,
  custom: (value) => {
    if (value && !pattern.test(value)) {
      return message;
    }
    return null;
  }
});

export const validateCustom = (validator: (value: any) => string | null, required = false): ValidationRule => ({
  required,
  custom: validator
});

// Common validation patterns
export const VALIDATION_PATTERNS = {
  phone: /^[\+]?[1-9][\d]{0,15}$/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s\-_]+$/,
  strongPassword: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
  ipAddress: /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/,
  uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
};

// Form field error display helper
export const getFieldError = (errors: ValidationError[], fieldName: string): string | null => {
  const error = errors.find(e => e.field === fieldName);
  return error ? error.message : null;
};

// Check if field has error
export const hasFieldError = (errors: ValidationError[], fieldName: string): boolean => {
  return errors.some(e => e.field === fieldName);
};
