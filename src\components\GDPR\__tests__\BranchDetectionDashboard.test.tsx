import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test/utils/test-utils';
import { BranchDetectionDashboard } from '../BranchDetectionDashboard';
import * as BranchDetectionServiceModule from '../../../services/branchDetectionService';

// Mock the service
vi.mock('../../../services/branchDetectionService');
const BranchDetectionService = BranchDetectionServiceModule.BranchDetectionService;

const mockBranches = [
  {
    id: '1',
    name: 'London Office',
    location: 'London, UK',
    country: 'United Kingdom',
    complianceScore: 95,
    riskLevel: 'low',
    dataFlows: ['flow-1', 'flow-2'],
    employeeCount: 150,
    dataCategories: ['personal', 'financial'],
    lastAudit: new Date().toISOString()
  },
  {
    id: '2',
    name: 'New York Office',
    location: 'New York, USA',
    country: 'United States',
    complianceScore: 78,
    riskLevel: 'medium',
    dataFlows: ['flow-3'],
    employeeCount: 200,
    dataCategories: ['personal'],
    lastAudit: new Date().toISOString()
  }
];

const mockDataFlows = [
  {
    id: 'flow-1',
    name: 'Customer Data Transfer',
    sourceBranch: '1',
    destinationBranch: '2',
    dataCategories: ['personal', 'financial'],
    transferMechanism: 'Standard Contractual Clauses',
    riskLevel: 'medium',
    status: 'active',
    lastReview: new Date().toISOString()
  }
];

const mockMetrics = {
  totalBranches: 2,
  compliantBranches: 1,
  complianceScore: 86,
  crossBorderTransfers: 1,
  highRiskBranches: 0,
  dataFlowMetrics: {
    totalFlows: 1,
    activeFlows: 1,
    highRiskFlows: 0,
    averageRiskScore: 65
  }
};

describe('BranchDetectionDashboard', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup service mocks
    vi.mocked(BranchDetectionService.getAllBranches).mockResolvedValue(mockBranches);
    vi.mocked(BranchDetectionService.getAllDataFlows).mockResolvedValue(mockDataFlows);
    vi.mocked(BranchDetectionService.getMetrics).mockResolvedValue(mockMetrics);
    vi.mocked(BranchDetectionService.createBranch).mockResolvedValue(mockBranches[0]);
    vi.mocked(BranchDetectionService.updateBranch).mockResolvedValue(mockBranches[0]);
    vi.mocked(BranchDetectionService.deleteBranch).mockResolvedValue(undefined);
  });

  describe('Component Rendering', () => {
    it('renders the dashboard with header and metrics', async () => {
      render(<BranchDetectionDashboard />);
      
      expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      expect(screen.getByText('Monitor organizational branches and cross-border data transfers')).toBeInTheDocument();
      
      await waitFor(() => {
        expect(screen.getByText('2')).toBeInTheDocument(); // Total branches
        expect(screen.getByText('86%')).toBeInTheDocument(); // Compliance score
      });
    });

    it('displays branches in a table format', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
        expect(screen.getByText('New York Office')).toBeInTheDocument();
        expect(screen.getByText('London, UK')).toBeInTheDocument();
        expect(screen.getByText('New York, USA')).toBeInTheDocument();
      });
    });

    it('shows compliance scores with proper styling', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        const highScore = screen.getByText('95%');
        const mediumScore = screen.getByText('78%');
        
        expect(highScore).toBeInTheDocument();
        expect(mediumScore).toBeInTheDocument();
      });
    });

    it('displays risk levels with appropriate indicators', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Low')).toBeInTheDocument();
        expect(screen.getByText('Medium')).toBeInTheDocument();
      });
    });
  });

  describe('CRUD Operations - Branches', () => {
    it('opens create branch modal when Add Branch button is clicked', async () => {
      render(<BranchDetectionDashboard />);
      
      const addButton = screen.getByRole('button', { name: /add branch/i });
      await user.click(addButton);
      
      expect(screen.getByText('Create New Branch')).toBeInTheDocument();
    });

    it('opens edit modal when edit button is clicked', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      expect(screen.getByText('Edit Branch')).toBeInTheDocument();
    });

    it('shows delete confirmation when delete button is clicked', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
    });

    it('creates a new branch successfully', async () => {
      render(<BranchDetectionDashboard />);
      
      const addButton = screen.getByRole('button', { name: /add branch/i });
      await user.click(addButton);
      
      // Fill out the form
      await user.type(screen.getByLabelText(/branch name/i), 'Paris Office');
      await user.type(screen.getByLabelText(/location/i), 'Paris, France');
      await user.selectOptions(screen.getByLabelText(/country/i), 'France');
      
      const saveButton = screen.getByRole('button', { name: /create branch/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(BranchDetectionService.createBranch).toHaveBeenCalled();
      });
    });

    it('updates an existing branch successfully', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      // Update the name
      const nameInput = screen.getByDisplayValue('London Office');
      await user.clear(nameInput);
      await user.type(nameInput, 'London Headquarters');
      
      const saveButton = screen.getByRole('button', { name: /update branch/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(BranchDetectionService.updateBranch).toHaveBeenCalled();
      });
    });

    it('deletes a branch after confirmation', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(BranchDetectionService.deleteBranch).toHaveBeenCalledWith('1');
      });
    });
  });

  describe('Data Flow Management', () => {
    it('displays data flows section', async () => {
      render(<BranchDetectionDashboard />);
      
      // Switch to data flows tab
      const dataFlowsTab = screen.getByText('Data Flows');
      await user.click(dataFlowsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Customer Data Transfer')).toBeInTheDocument();
      });
    });

    it('opens create data flow modal', async () => {
      render(<BranchDetectionDashboard />);
      
      const addDataFlowButton = screen.getByRole('button', { name: /add data flow/i });
      await user.click(addDataFlowButton);
      
      expect(screen.getByText('Create New Data Flow')).toBeInTheDocument();
    });

    it('shows transfer mechanisms and risk levels', async () => {
      render(<BranchDetectionDashboard />);
      
      const dataFlowsTab = screen.getByText('Data Flows');
      await user.click(dataFlowsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Standard Contractual Clauses')).toBeInTheDocument();
        expect(screen.getByText('Medium')).toBeInTheDocument();
      });
    });
  });

  describe('Search and Filtering', () => {
    it('filters branches by search term', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
        expect(screen.getByText('New York Office')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(/search branches/i);
      await user.type(searchInput, 'London');
      
      // Should filter results (implementation depends on component logic)
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });
    });

    it('filters by compliance score', async () => {
      render(<BranchDetectionDashboard />);
      
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      // Select high compliance filter
      const highComplianceFilter = screen.getByLabelText(/high compliance/i);
      await user.click(highComplianceFilter);
      
      // Apply filter
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      // Should show only high compliance branches
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });
    });

    it('filters by risk level', async () => {
      render(<BranchDetectionDashboard />);
      
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      // Select medium risk filter
      const mediumRiskFilter = screen.getByLabelText(/medium risk/i);
      await user.click(mediumRiskFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      await waitFor(() => {
        expect(screen.getByText('New York Office')).toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles service errors gracefully', async () => {
      vi.mocked(BranchDetectionService.getAllBranches).mockRejectedValue(new Error('Service error'));
      
      render(<BranchDetectionDashboard />);
      
      // Should still render without crashing
      expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      
      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });
    });

    it('handles create branch errors', async () => {
      vi.mocked(BranchDetectionService.createBranch).mockRejectedValue(new Error('Create failed'));
      
      render(<BranchDetectionDashboard />);
      
      const addButton = screen.getByRole('button', { name: /add branch/i });
      await user.click(addButton);
      
      // Fill out form and submit
      await user.type(screen.getByLabelText(/branch name/i), 'Test Branch');
      await user.type(screen.getByLabelText(/location/i), 'Test Location');
      
      const saveButton = screen.getByRole('button', { name: /create branch/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to create/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility', () => {
    it('has proper table headers and structure', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: /branch name/i })).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: /location/i })).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: /compliance score/i })).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation for action buttons', async () => {
      render(<BranchDetectionDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('London Office')).toBeInTheDocument();
      });

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      editButtons[0].focus();
      expect(document.activeElement).toBe(editButtons[0]);
    });

    it('has proper ARIA labels for interactive elements', async () => {
      render(<BranchDetectionDashboard />);
      
      const addButton = screen.getByRole('button', { name: /add branch/i });
      expect(addButton).toHaveAttribute('aria-label');
      
      const searchInput = screen.getByRole('textbox', { name: /search/i });
      expect(searchInput).toHaveAttribute('aria-label');
    });
  });
});
