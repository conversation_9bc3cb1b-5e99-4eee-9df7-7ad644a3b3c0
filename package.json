{"name": "praeferre", "productName": "Praeferre", "private": true, "version": "1.0.0", "description": "Praeferre Privacy Engine", "author": "Praeferre Team", "main": "main.js", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "start": "electron .", "package": "electron-packager . Praeferre --platform=win32 --arch=x64 --out=release --overwrite", "make": "npm run build && npm run package", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:all": "npm run test:run && npm run test:e2e"}, "dependencies": {"@headlessui/react": "^1.7.17", "@types/leaflet": "^1.9.18", "axios": "^1.8.4", "chart.js": "^4.4.8", "chartjs-adapter-moment": "^1.0.1", "date-fns": "^4.1.0", "jspdf": "^3.0.1", "leaflet": "^1.9.4", "lucide-react": "^0.303.0", "react": "^18.3.1", "react-chartjs-2": "^5.3.0", "react-device-frame": "^2.1.4", "react-dom": "^18.3.1", "react-feather": "^2.0.10", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-leaflet": "^5.0.0", "react-responsive": "^10.0.1", "react-router-dom": "^7.4.1", "react-sparklines": "^1.7.0", "react-toastify": "^11.0.5"}, "devDependencies": {"@eslint/js": "^9.9.1", "@playwright/test": "^1.54.1", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.18", "cross-env": "^7.0.3", "electron": "^28.1.0", "electron-builder": "^24.9.1", "electron-packager": "^17.1.2", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "jsdom": "^26.1.0", "msw": "^2.8.4", "playwright": "^1.54.1", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^7.0.6", "vitest": "^3.2.4"}, "build": {"appId": "com.praeferre.app", "productName": "Praeferre", "directories": {"output": "release"}, "files": ["dist/**/*", "main.js"], "win": {"target": ["portable"], "icon": "public/thumbnail.png"}, "forceCodeSigning": false, "portable": {"artifactName": "Praeferre.exe"}}, "msw": {"workerDirectory": ["public"]}}