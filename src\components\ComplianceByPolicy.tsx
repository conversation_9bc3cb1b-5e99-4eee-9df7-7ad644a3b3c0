import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { ComplianceErrorBoundary } from './compliance/ComplianceErrorBoundary';
import { AlertTriangle, FileText, CheckCircle, XCircle } from 'lucide-react';
import { useComplianceStandards } from '../context/ComplianceStandardsContext';

interface PolicyData {
  name: string;
  compliant: number;
  nonCompliant: number;
  frameworks: string[]; // Associated compliance frameworks
}

// Type guard for PolicyData
const isPolicyData = (data: any): data is PolicyData => {
  return (
    data &&
    typeof data === 'object' &&
    typeof data.name === 'string' &&
    typeof data.compliant === 'number' &&
    typeof data.nonCompliant === 'number' &&
    Array.isArray(data.frameworks) &&
    data.frameworks.every((f: any) => typeof f === 'string') &&
    data.compliant >= 0 &&
    data.nonCompliant >= 0 &&
    data.compliant <= 100 &&
    data.nonCompliant <= 100
  );
};

// Validate and sanitize policy data
const validatePolicyData = (policies: any[]): PolicyData[] => {
  if (!Array.isArray(policies)) {
    console.warn('ComplianceByPolicy: Invalid policies data, using fallback');
    return [];
  }

  return policies
    .filter(isPolicyData)
    .map(policy => ({
      ...policy,
      compliant: Math.max(0, Math.min(100, policy.compliant)),
      nonCompliant: Math.max(0, Math.min(100, policy.nonCompliant)),
      frameworks: Array.isArray(policy.frameworks) ? policy.frameworks : []
    }));
};

const ComplianceByPolicyContent: React.FC = () => {
  const [allPolicies, setAllPolicies] = useState<PolicyData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { getEnabledStandards } = useComplianceStandards();

  // Filter policies based on enabled compliance standards
  const filteredPolicies = useMemo(() => {
    const enabledStandards = getEnabledStandards();

    // If no standards are enabled, show all policies
    if (enabledStandards.length === 0) {
      return allPolicies;
    }

    // Filter policies that are associated with at least one enabled standard
    return allPolicies.filter(policy =>
      policy.frameworks.some(framework => enabledStandards.includes(framework))
    );
  }, [allPolicies, getEnabledStandards]);

  // Initialize policies with error handling
  useEffect(() => {
    try {
      const rawPolicies = [
        {
          name: 'ISO 27001 Security Policy',
          compliant: 75,
          nonCompliant: 25,
          frameworks: ['ISO 27001']
        },
        {
          name: 'GDPR Data Protection Policy',
          compliant: 80,
          nonCompliant: 20,
          frameworks: ['GDPR']
        },
        {
          name: 'HIPAA Privacy Policy',
          compliant: 85,
          nonCompliant: 15,
          frameworks: ['HIPAA']
        },
        {
          name: 'SOX Financial Controls',
          compliant: 92,
          nonCompliant: 8,
          frameworks: ['SOX']
        },
        {
          name: 'PCI DSS Payment Security',
          compliant: 88,
          nonCompliant: 12,
          frameworks: ['PCI DSS']
        },
        {
          name: 'CCPA Consumer Privacy',
          compliant: 78,
          nonCompliant: 22,
          frameworks: ['CCPA']
        },
        {
          name: 'Multi-Framework Security Policy',
          compliant: 90,
          nonCompliant: 10,
          frameworks: ['ISO 27001', 'NIST']
        },
        {
          name: 'Cross-Compliance Data Policy',
          compliant: 82,
          nonCompliant: 18,
          frameworks: ['GDPR', 'CCPA', 'HIPAA']
        },
      ];

      const validatedPolicies = validatePolicyData(rawPolicies);

      if (validatedPolicies.length === 0) {
        throw new Error('No valid policy data available');
      }

      setAllPolicies(validatedPolicies);
      setError(null);
    } catch (err) {
      console.error('Error initializing policy data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load policy data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handlePolicyClick = useCallback((policy: PolicyData) => {
    try {
      if (!isPolicyData(policy)) {
        throw new Error('Invalid policy data');
      }

      console.log(`Clicked on ${policy.name} policy`);
      // In a real application, this could:
      // - Navigate to policy details
      // - Show policy compliance breakdown
      // - Open policy management interface
      // - Display policy violations and remediation steps
    } catch (err) {
      console.error('Error handling policy click:', err);
      setError('Failed to process policy selection');
    }
  }, []);

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm">
        <div className="animate-pulse">
          <div className="h-6 bg-surface rounded w-1/2 mb-6"></div>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-2">
                <div className="flex justify-between">
                  <div className="h-4 bg-surface rounded w-1/3"></div>
                  <div className="h-4 bg-surface rounded w-12"></div>
                </div>
                <div className="h-2 bg-surface rounded-full"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">Policy Data Unavailable</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // Empty state
  if (filteredPolicies.length === 0) {
    const enabledStandards = getEnabledStandards();
    const isFiltered = enabledStandards.length > 0 && allPolicies.length > 0;

    return (
      <div className="bg-card p-6 rounded-xl shadow-sm">
        <div className="text-center">
          <FileText className="w-8 h-8 text-text-secondary mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">
            {isFiltered ? 'No Matching Policies' : 'No Policies Available'}
          </h3>
          <p className="text-text-secondary">
            {isFiltered
              ? `No policies match the selected compliance standards: ${enabledStandards.join(', ')}`
              : 'No compliance policies are currently configured.'
            }
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm">
      <div className="flex items-center gap-2 mb-6">
        <FileText className="w-5 h-5 text-primary" />
        <h2 className="text-xl font-semibold text-text">Compliance By Policy</h2>
        {getEnabledStandards().length > 0 && (
          <span className="text-xs text-text-secondary ml-auto">
            Filtered by: {getEnabledStandards().join(', ')}
          </span>
        )}
      </div>

      <div className="space-y-4">
        {filteredPolicies.map((policy) => {
          // Additional safety check for each policy
          if (!isPolicyData(policy)) {
            return null;
          }

          const complianceIcon = policy.compliant >= 80 ? CheckCircle :
                                policy.compliant >= 60 ? AlertTriangle : XCircle;
          const IconComponent = complianceIcon;
          const iconColor = policy.compliant >= 80 ? 'text-green-500' :
                           policy.compliant >= 60 ? 'text-yellow-500' : 'text-red-500';

          return (
            <div
              key={policy.name}
              className="space-y-2 p-3 rounded-lg hover:bg-surface transition-colors duration-200 cursor-pointer group border border-transparent hover:border-border"
              onClick={() => handlePolicyClick(policy)}
              role="button"
              tabIndex={0}
              onKeyDown={(e) => {
                try {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    handlePolicyClick(policy);
                  }
                } catch (err) {
                  console.error('Keyboard interaction error:', err);
                }
              }}
              aria-label={`View details for ${policy.name} policy (${policy.compliant}% compliant)`}
            >
              <div className="flex justify-between items-center text-sm text-text">
                <div className="flex items-center gap-2">
                  <IconComponent className={`w-4 h-4 ${iconColor}`} />
                  <span className="group-hover:text-primary transition-colors font-medium">
                    {policy.name}
                  </span>
                </div>
                <span className="font-semibold">{policy.compliant}%</span>
              </div>

              <div className="h-2 bg-surface rounded-full overflow-hidden">
                <div
                  className="h-full bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full transition-all duration-300 group-hover:from-primary group-hover:to-secondary"
                  style={{
                    width: `${Math.max(0, Math.min(100, policy.compliant))}%`,
                    minWidth: policy.compliant > 0 ? '2px' : '0px'
                  }}
                />
              </div>

              <div className="flex justify-between items-center text-xs text-text-secondary">
                <div className="flex flex-col gap-1">
                  <span>Compliant: {policy.compliant}%</span>
                  <span>Non-compliant: {policy.nonCompliant}%</span>
                </div>
                <div className="flex flex-wrap gap-1">
                  {policy.frameworks.map((framework) => (
                    <span
                      key={framework}
                      className="px-2 py-1 bg-primary/10 text-primary rounded-full text-xs"
                    >
                      {framework}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

// Main component with error boundary
export const ComplianceByPolicy: React.FC = () => {
  return (
    <ComplianceErrorBoundary
      componentName="ComplianceByPolicy"
      fallbackType="policy"
    >
      <ComplianceByPolicyContent />
    </ComplianceErrorBoundary>
  );
};

export default ComplianceByPolicy;
