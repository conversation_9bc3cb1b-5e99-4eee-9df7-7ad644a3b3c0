import re
import json
import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from gdpr_config import OUTPUT_CONFIG

def normalize_text(text):
    """Normalize text for consistent processing"""
    normalized = re.sub(r'\s+', ' ', text.strip())
    normalized = ''.join(char for char in normalized if ord(char) >= 32 or char in '\n\t')
    return normalized

def clean_text_content(text):
    """Clean up text content for better readability"""
    if not text or pd.isna(text):
        return "Not specified"
    
    clean_text = str(text)
    
    # Remove JSON artifacts
    patterns = [
        r'",\s*"[^"]*":\s*"[^"]*"', r'\{[^}]*\}', r'\[[^\]]*\]',
        r'"[^"]*":\s*"[^"]*"', r',\s*"notes_gaps":[^}]*',
        r'"gdpr_requirement":[^,]*,?', r'"compliance_status":[^,]*,?',
        r'"company_policy":[^,]*,?', r'\s+', r'\n+'
    ]
    replacements = ['', '', '', '', '', '', '', '', ' ', ' ']
    
    for pattern, replacement in zip(patterns, replacements):
        clean_text = re.sub(pattern, replacement, clean_text)
    
    # Remove markdown formatting
    clean_text = re.sub(r'\*\*([^*]+)\*\*', r'\1', clean_text)
    clean_text = re.sub(r'- \*\*', '• ', clean_text)
    clean_text = re.sub(r'- \*\*Compliance Status\*\*:\s*[^-]*', '', clean_text)
    clean_text = re.sub(r'Compliance Status[^-]*', '', clean_text)
    clean_text = re.sub(r'^\s*,\s*|,\s*$', '', clean_text)
    
    return clean_text.strip()

def save_results(compliance_table, analysis_result, policy_name, analysis_id=None):
    """Save all analysis results to various formats in organized folders"""
    if compliance_table.empty:
        print("No compliance data to save")
        return []
    
    # Create organized folder structure
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    analysis_id = analysis_id or timestamp
    
    # Clean company name for folder creation
    clean_company_name = re.sub(r'[<>:"/\\|?*]', '_', policy_name)
    clean_company_name = re.sub(r'\.pdf$|\.txt$|\.md$', '', clean_company_name, flags=re.IGNORECASE)
    clean_company_name = clean_company_name.strip()
    
    # Create main results directory and company-specific subdirectory
    results_dir = Path("results")
    company_dir = results_dir / clean_company_name
    company_dir.mkdir(parents=True, exist_ok=True)
    
    files_generated = []
    
    try:
        # Define file paths with organized structure
        file_paths = {
            "csv_file": company_dir / f"gdpr_compliance_{analysis_id}_{timestamp}.csv",
            "json_file": company_dir / f"gdpr_compliance_{analysis_id}_{timestamp}.json",
            "raw_json_file": company_dir / f"gdpr_compliance_raw_{analysis_id}_{timestamp}.json",
            "report_file": company_dir / f"gdpr_compliance_report_{analysis_id}_{timestamp}.txt",
            "clean_csv_file": company_dir / f"gdpr_compliance_clean_{analysis_id}_{timestamp}.csv",
            "comprehensive_json_file": company_dir / f"gdpr_compliance_comprehensive_{analysis_id}_{timestamp}.json"
        }
        
        # Save CSV
        compliance_table.to_csv(file_paths["csv_file"], index=False)
        files_generated.append(str(file_paths["csv_file"]))
        print(f"Results saved to: {file_paths['csv_file']}")
        
        # Save basic JSON
        compliance_json = compliance_table.to_dict('records')
        with open(file_paths["json_file"], "w", encoding="utf-8") as f:
            json.dump(compliance_json, f, indent=2, ensure_ascii=False)
        files_generated.append(str(file_paths["json_file"]))
        print(f"JSON results saved to: {file_paths['json_file']}")
        
        # Extract and save raw JSON if available
        json_match = re.search(r'COMPLIANCE_TABLE_JSON:\s*```json\s*(\[.*?\])\s*```', analysis_result, re.DOTALL)
        if not json_match:
            json_match = re.search(r'COMPLIANCE_TABLE_JSON:\s*(\[.*?\])', analysis_result, re.DOTALL)
        
        if json_match:
            try:
                raw_json_data = json.loads(json_match.group(1))
                with open(file_paths["raw_json_file"], "w", encoding="utf-8") as f:
                    json.dump(raw_json_data, f, indent=2, ensure_ascii=False)
                files_generated.append(str(file_paths["raw_json_file"]))
                print(f"Raw JSON saved to: {file_paths['raw_json_file']}")
            except json.JSONDecodeError:
                print("Could not parse raw JSON from analysis results")
        
        # Save detailed report
        with open(file_paths["report_file"], "w", encoding="utf-8") as f:
            f.write(f"GDPR COMPLIANCE ANALYSIS REPORT FOR: {policy_name}\n")
            f.write("="*60 + "\n")
            f.write(f"Analysis ID: {analysis_id}\n")
            f.write(f"Analysis Date: {datetime.now().isoformat()}\n")
            f.write(f"Company/Policy: {policy_name}\n")
            f.write("="*60 + "\n\n")
            f.write("DETAILED ANALYSIS:\n" + "="*20 + "\n")
            f.write(analysis_result)
            f.write("\n\n" + "="*50 + "\n")
            f.write("COMPLIANCE TABLE:\n" + "="*20 + "\n")
            f.write(compliance_table.to_string(index=False))
        files_generated.append(str(file_paths["report_file"]))
        print(f"Report saved to: {file_paths['report_file']}")
        
        # Save clean CSV
        clean_table = compliance_table.copy()
        clean_table['company_policy'] = clean_table['company_policy'].apply(clean_text_content)
        clean_table['notes_gaps'] = clean_table['notes_gaps'].apply(clean_text_content)
        clean_table.to_csv(file_paths["clean_csv_file"], index=False)
        files_generated.append(str(file_paths["clean_csv_file"]))
        print(f"Clean CSV saved to: {file_paths['clean_csv_file']}")
        
        # Save comprehensive JSON with metadata
        status_counts = compliance_table['compliance_status'].value_counts()
        total_requirements = len(compliance_table)
        compliant_count = status_counts.get('Compliant', 0)
        partially_compliant_count = status_counts.get('Partially Compliant', 0)
        compliance_score = ((compliant_count + (partially_compliant_count * 0.5)) / total_requirements) * 100
        
        comprehensive_json = {
            "analysis_metadata": {
                "policy_name": policy_name,
                "analysis_id": analysis_id,
                "analysis_date": datetime.now().isoformat(),
                "total_requirements": total_requirements,
                "compliance_score": round(compliance_score, 1),
                "compliance_level": (
                    "Good compliance level" if compliance_score >= 80 
                    else "Moderate compliance - improvements needed" if compliance_score >= 60 
                    else "Low compliance - significant gaps identified"
                )
            },
            "compliance_summary": {status: int(count) for status, count in status_counts.items()},
            "compliance_details": compliance_table.to_dict('records')
        }
        
        with open(file_paths["comprehensive_json_file"], "w", encoding="utf-8") as f:
            json.dump(comprehensive_json, f, indent=2, ensure_ascii=False)
        files_generated.append(str(file_paths["comprehensive_json_file"]))
        print(f"Comprehensive JSON saved to: {file_paths['comprehensive_json_file']}")
        
        print(f"\nAll results saved in organized structure under: {company_dir}")
        print(f"Total files generated: {len(files_generated)}")
        
        return files_generated
        
    except Exception as e:
        print(f"Error saving results: {str(e)}")
        return files_generated

def display_results(compliance_table):
    """Display compliance results in terminal"""
    if compliance_table.empty:
        print("No compliance data to display")
        return
    
    status_counts = compliance_table['compliance_status'].value_counts()
    total_requirements = len(compliance_table)
    
    print("\n" + "="*60)
    print("COMPLIANCE OVERVIEW")
    print("="*60)
    print(f"Total Requirements Analyzed: {total_requirements}")
    print("\n Compliance Status Breakdown:")
    
    for status, count in status_counts.items():
        percentage = (count / total_requirements) * 100
        print(f"  • {status}: {count} ({percentage:.1f}%)")
    
    # Calculate overall compliance score
    compliant_count = status_counts.get('Compliant', 0)
    partially_compliant_count = status_counts.get('Partially Compliant', 0)
    compliance_score = ((compliant_count + (partially_compliant_count * 0.5)) / total_requirements) * 100
    
    print(f"\n Overall Compliance Score: {compliance_score:.1f}%")
    
    if compliance_score >= 80:
        print(" Good compliance level")
    elif compliance_score >= 60:
        print(" Moderate compliance - improvements needed")
    else:
        print(" Low compliance - significant gaps identified")
    
    # Display compact table
    print("\n" + "="*60)
    print("COMPACT COMPLIANCE TABLE")
    print("="*60)
    print(f"{'Requirement':<25} {'Status':<18} {'Key Issues':<30}")
    print("-" * 73)
    
    for _, row in compliance_table.iterrows():
        req_short = row['gdpr_requirement'][:23] + "..." if len(row['gdpr_requirement']) > 25 else row['gdpr_requirement']
        status = row['compliance_status'][:16] + "..." if len(row['compliance_status']) > 18 else row['compliance_status']
        clean_notes = clean_text_content(row['notes_gaps'])
        key_issues = clean_notes[:28] + "..." if len(clean_notes) > 30 else clean_notes
        print(f"{req_short:<25} {status:<18} {key_issues:<30}")