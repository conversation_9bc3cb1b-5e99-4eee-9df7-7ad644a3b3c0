/**
 * Comprehensive CRUD Operations Service
 * Handles Create, Read, Update, Delete operations for all dashboard components
 */

import { mockDataService, type GDPRRequest, type RiskAssessment, type Violation, type PolicyCompliance } from './mockDataService';

export interface CRUDResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ToastNotification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
}

class CRUDOperationsService {
  private static instance: CRUDOperationsService;
  private toastCallbacks: ((toast: ToastNotification) => void)[] = [];

  public static getInstance(): CRUDOperationsService {
    if (!CRUDOperationsService.instance) {
      CRUDOperationsService.instance = new CRUDOperationsService();
    }
    return CRUDOperationsService.instance;
  }

  // Toast notification system
  public onToast(callback: (toast: ToastNotification) => void): () => void {
    this.toastCallbacks.push(callback);
    return () => {
      const index = this.toastCallbacks.indexOf(callback);
      if (index > -1) {
        this.toastCallbacks.splice(index, 1);
      }
    };
  }

  private showToast(toast: Omit<ToastNotification, 'id'>): void {
    const notification: ToastNotification = {
      ...toast,
      id: `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      duration: toast.duration || 5000,
    };
    
    this.toastCallbacks.forEach(callback => callback(notification));
  }

  // Simulate API delay
  private async simulateDelay(min: number = 300, max: number = 800): Promise<void> {
    const delay = Math.random() * (max - min) + min;
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // GDPR Requests CRUD Operations
  async createGDPRRequest(request: Omit<GDPRRequest, 'id' | 'submittedAt'>): Promise<CRUDResponse<GDPRRequest>> {
    try {
      await this.simulateDelay();
      
      const newRequest: GDPRRequest = {
        ...request,
        id: `dsr-${Date.now()}`,
        submittedAt: new Date(),
      };

      this.showToast({
        type: 'success',
        title: 'GDPR Request Created',
        message: `New ${request.type} request has been successfully created.`,
      });

      return {
        success: true,
        data: newRequest,
        message: 'GDPR request created successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Creation Failed',
        message: 'Failed to create GDPR request. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to create GDPR request',
      };
    }
  }

  async updateGDPRRequest(id: string, updates: Partial<GDPRRequest>): Promise<CRUDResponse<GDPRRequest>> {
    try {
      await this.simulateDelay();
      
      // Simulate finding and updating the request
      const updatedRequest: GDPRRequest = {
        id,
        email: '<EMAIL>',
        type: 'access',
        status: 'in_progress',
        priority: 'medium',
        description: 'Updated request',
        submittedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        assignedTo: 'Officer 1',
        department: 'IT',
        dataCategories: ['Personal Info'],
        estimatedHours: 5,
        ...updates,
      };

      this.showToast({
        type: 'success',
        title: 'Request Updated',
        message: `GDPR request ${id} has been successfully updated.`,
      });

      return {
        success: true,
        data: updatedRequest,
        message: 'GDPR request updated successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update GDPR request. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to update GDPR request',
      };
    }
  }

  async deleteGDPRRequest(id: string): Promise<CRUDResponse<void>> {
    try {
      await this.simulateDelay();
      
      this.showToast({
        type: 'success',
        title: 'Request Deleted',
        message: `GDPR request ${id} has been successfully deleted.`,
      });

      return {
        success: true,
        message: 'GDPR request deleted successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Deletion Failed',
        message: 'Failed to delete GDPR request. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to delete GDPR request',
      };
    }
  }

  // Risk Assessment CRUD Operations
  async createRiskAssessment(assessment: Omit<RiskAssessment, 'id' | 'lastReviewed'>): Promise<CRUDResponse<RiskAssessment>> {
    try {
      await this.simulateDelay();
      
      const newAssessment: RiskAssessment = {
        ...assessment,
        id: `risk-${Date.now()}`,
        lastReviewed: new Date(),
      };

      this.showToast({
        type: 'success',
        title: 'Risk Assessment Created',
        message: `New ${assessment.category} risk assessment has been created.`,
      });

      return {
        success: true,
        data: newAssessment,
        message: 'Risk assessment created successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Creation Failed',
        message: 'Failed to create risk assessment. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to create risk assessment',
      };
    }
  }

  async updateRiskAssessment(id: string, updates: Partial<RiskAssessment>): Promise<CRUDResponse<RiskAssessment>> {
    try {
      await this.simulateDelay();
      
      const updatedAssessment: RiskAssessment = {
        id,
        title: 'Updated Risk Assessment',
        category: 'security',
        level: 'medium',
        probability: 50,
        impact: 60,
        riskScore: 30,
        status: 'assessed',
        owner: 'Risk Manager',
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        mitigationActions: ['Review security controls'],
        lastReviewed: new Date(),
        ...updates,
      };

      this.showToast({
        type: 'success',
        title: 'Assessment Updated',
        message: `Risk assessment ${id} has been successfully updated.`,
      });

      return {
        success: true,
        data: updatedAssessment,
        message: 'Risk assessment updated successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update risk assessment. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to update risk assessment',
      };
    }
  }

  async deleteRiskAssessment(id: string): Promise<CRUDResponse<void>> {
    try {
      await this.simulateDelay();
      
      this.showToast({
        type: 'success',
        title: 'Assessment Deleted',
        message: `Risk assessment ${id} has been successfully deleted.`,
      });

      return {
        success: true,
        message: 'Risk assessment deleted successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Deletion Failed',
        message: 'Failed to delete risk assessment. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to delete risk assessment',
      };
    }
  }

  // Violation CRUD Operations
  async createViolation(violation: Omit<Violation, 'id' | 'detectedAt'>): Promise<CRUDResponse<Violation>> {
    try {
      await this.simulateDelay();
      
      const newViolation: Violation = {
        ...violation,
        id: `viol-${Date.now()}`,
        detectedAt: new Date(),
      };

      this.showToast({
        type: 'warning',
        title: 'Violation Reported',
        message: `New ${violation.severity} severity violation has been reported.`,
      });

      return {
        success: true,
        data: newViolation,
        message: 'Violation created successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Report Failed',
        message: 'Failed to report violation. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to create violation',
      };
    }
  }

  async updateViolation(id: string, updates: Partial<Violation>): Promise<CRUDResponse<Violation>> {
    try {
      await this.simulateDelay();
      
      const updatedViolation: Violation = {
        id,
        title: 'Updated Violation',
        description: 'Updated violation description',
        severity: 'medium',
        status: 'investigating',
        category: 'policy_violation',
        detectedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
        affectedRecords: 100,
        assignedTo: 'Compliance Team',
        department: 'IT',
        remediationSteps: ['Investigate', 'Remediate'],
        ...updates,
      };

      this.showToast({
        type: 'success',
        title: 'Violation Updated',
        message: `Violation ${id} has been successfully updated.`,
      });

      return {
        success: true,
        data: updatedViolation,
        message: 'Violation updated successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Update Failed',
        message: 'Failed to update violation. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to update violation',
      };
    }
  }

  async deleteViolation(id: string): Promise<CRUDResponse<void>> {
    try {
      await this.simulateDelay();
      
      this.showToast({
        type: 'success',
        title: 'Violation Deleted',
        message: `Violation ${id} has been successfully deleted.`,
      });

      return {
        success: true,
        message: 'Violation deleted successfully',
      };
    } catch (error) {
      this.showToast({
        type: 'error',
        title: 'Deletion Failed',
        message: 'Failed to delete violation. Please try again.',
      });

      return {
        success: false,
        error: 'Failed to delete violation',
      };
    }
  }
}

export const crudOperationsService = CRUDOperationsService.getInstance();
