import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle, 
  CheckCircle, 
  RefreshCw,
  Activity,
  Shield,
  FileText,
  Users
} from 'lucide-react';
import { Button } from '../ui/Button';

interface ComplianceMetricsFallbackProps {
  className?: string;
  onRetry?: () => void;
}

interface BasicMetric {
  id: string;
  name: string;
  value: number;
  target: number;
  status: 'good' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  icon: React.ComponentType<any>;
}

export const ComplianceMetricsFallback: React.FC<ComplianceMetricsFallbackProps> = ({ 
  className = '', 
  onRetry 
}) => {
  const { mode } = useTheme();
  const [isLoading, setIsLoading] = useState(false);

  // Basic fallback metrics
  const basicMetrics: BasicMetric[] = [
    {
      id: 'overall-compliance',
      name: 'Overall Compliance',
      value: 92,
      target: 95,
      status: 'warning',
      trend: 'up',
      icon: Shield
    },
    {
      id: 'active-policies',
      name: 'Active Policies',
      value: 24,
      target: 25,
      status: 'good',
      trend: 'stable',
      icon: FileText
    },
    {
      id: 'data-subjects',
      name: 'Data Subjects',
      value: 1247,
      target: 1500,
      status: 'good',
      trend: 'up',
      icon: Users
    },
    {
      id: 'risk-assessments',
      name: 'Risk Assessments',
      value: 18,
      target: 20,
      status: 'warning',
      trend: 'down',
      icon: Activity
    }
  ];

  const handleRetry = async () => {
    setIsLoading(true);
    try {
      if (onRetry) {
        await onRetry();
      } else {
        // Simulate retry delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        window.location.reload();
      }
    } catch (error) {
      console.error('Retry failed:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good':
        return 'text-green-500';
      case 'warning':
        return 'text-yellow-500';
      case 'critical':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good':
        return CheckCircle;
      case 'warning':
      case 'critical':
        return AlertTriangle;
      default:
        return Activity;
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return TrendingUp;
      case 'down':
        return TrendingDown;
      default:
        return Activity;
    }
  };

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <BarChart3 className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Compliance Metrics</h1>
              <p className="text-text-secondary">Basic compliance monitoring dashboard</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
              <div className="w-2 h-2 rounded-full bg-yellow-500" />
              <span className="text-sm text-text-secondary">Fallback Mode</span>
            </div>
            <Button onClick={handleRetry} disabled={isLoading} size="sm">
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Retrying...' : 'Retry'}
            </Button>
          </div>
        </div>

        {/* Notice */}
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
          <div className="flex items-start gap-3">
            <AlertTriangle className="w-5 h-5 text-yellow-500 mt-0.5" />
            <div>
              <h3 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-1">
                Limited Functionality
              </h3>
              <p className="text-sm text-yellow-700 dark:text-yellow-300">
                The full compliance metrics dashboard is temporarily unavailable. 
                This simplified view shows basic metrics only. Please try refreshing to restore full functionality.
              </p>
            </div>
          </div>
        </div>

        {/* Basic Metrics Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {basicMetrics.map((metric) => {
            const StatusIcon = getStatusIcon(metric.status);
            const TrendIcon = getTrendIcon(metric.trend);
            const IconComponent = metric.icon;
            
            return (
              <div key={metric.id} className="bg-card rounded-lg p-4 border border-border">
                <div className="flex items-center justify-between mb-3">
                  <div className="p-2 bg-primary/10 rounded-lg">
                    <IconComponent className="w-5 h-5 text-primary" />
                  </div>
                  <div className="flex items-center gap-1">
                    <StatusIcon className={`w-4 h-4 ${getStatusColor(metric.status)}`} />
                    <TrendIcon className={`w-3 h-3 ${
                      metric.trend === 'up' ? 'text-green-500' : 
                      metric.trend === 'down' ? 'text-red-500' : 
                      'text-gray-500'
                    }`} />
                  </div>
                </div>
                
                <div className="space-y-1">
                  <h3 className="text-sm font-medium text-text-secondary">{metric.name}</h3>
                  <div className="flex items-baseline gap-2">
                    <span className="text-2xl font-bold text-text">{metric.value}</span>
                    <span className="text-sm text-text-secondary">/ {metric.target}</span>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
                    <div 
                      className={`h-2 rounded-full transition-all duration-300 ${
                        metric.status === 'good' ? 'bg-green-500' :
                        metric.status === 'warning' ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${Math.min((metric.value / metric.target) * 100, 100)}%` }}
                    />
                  </div>
                  
                  <div className="text-xs text-text-secondary mt-1">
                    {Math.round((metric.value / metric.target) * 100)}% of target
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Simple Chart Placeholder */}
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
          <div className="h-64 flex items-center justify-center bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-gray-400 mx-auto mb-2" />
              <p className="text-text-secondary">Chart unavailable in fallback mode</p>
              <p className="text-sm text-text-secondary mt-1">Please refresh to view detailed analytics</p>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex items-center justify-center gap-4 pt-4 border-t border-border">
          <Button onClick={handleRetry} disabled={isLoading}>
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Loading...' : 'Reload Full Dashboard'}
          </Button>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Refresh Page
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ComplianceMetricsFallback;
