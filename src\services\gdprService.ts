import { DataSubjectRequestService } from './dataSubjectRequestService';
import { BranchDetectionService } from './branchDetectionService';
import { ConsentManagementService } from './consentManagementService';
import { ImpactAssessmentService } from './impactAssessmentService';
import { SecurityIncident } from '../types/compliance';

export interface GDPRDashboardMetrics {
  dataSubjectRequests: {
    total: number;
    pending: number;
    inProgress: number;
    completed: number;
    overdue: number;
    averageProcessingTime: number;
    completionRate: number;
  };
  branchCompliance: {
    totalBranches: number;
    compliantBranches: number;
    complianceScore: number;
    crossBorderTransfers: number;
    highRiskBranches: number;
  };
  consentManagement: {
    totalUsers: number;
    consentedUsers: number;
    consentRate: number;
    withdrawalRate: number;
    categoryBreakdown: Array<{
      category: string;
      consentRate: number;
    }>;
  };
  impactAssessments: {
    total: number;
    completed: number;
    inProgress: number;
    overdue: number;
    averageRiskScore: number;
    highRiskAssessments: number;
  };
  securityIncidents: {
    total: number;
    open: number;
    resolved: number;
    dataBreaches: number;
    averageResolutionTime: number;
  };
  complianceScore: {
    overall: number;
    trend: 'up' | 'down' | 'stable';
    weeklyChange: number;
    monthlyChange: number;
  };
}

export interface GDPRAlert {
  id: string;
  type: 'warning' | 'error' | 'info' | 'success';
  category: 'data_subject_request' | 'consent' | 'branch_compliance' | 'impact_assessment' | 'security_incident';
  title: string;
  message: string;
  timestamp: Date;
  priority: 'low' | 'medium' | 'high' | 'critical';
  actionRequired: boolean;
  relatedEntityId?: string;
  dismissed: boolean;
}

export interface GDPRActivity {
  id: string;
  type: 'request_created' | 'consent_granted' | 'consent_withdrawn' | 'assessment_completed' | 'incident_reported' | 'compliance_updated';
  title: string;
  description: string;
  timestamp: Date;
  userId: string;
  userName: string;
  entityId?: string;
  entityType?: string;
  metadata?: Record<string, any>;
}

export class GDPRService {
  private static alerts: GDPRAlert[] = [];
  private static activities: GDPRActivity[] = [];
  private static securityIncidents: SecurityIncident[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    
    // Initialize all sub-services
    DataSubjectRequestService.initialize();
    BranchDetectionService.initialize();
    ConsentManagementService.initialize();
    ImpactAssessmentService.initialize();
    
    // Generate mock data
    this.alerts = this.generateMockAlerts();
    this.activities = this.generateMockActivities();
    this.securityIncidents = this.generateMockSecurityIncidents();
    
    this.initialized = true;
  }

  // Dashboard Metrics
  static async getDashboardMetrics(): Promise<GDPRDashboardMetrics> {
    this.initialize();

    try {
      console.log('🔄 Loading GDPR dashboard metrics...');

      // Initialize all dependent services first
      DataSubjectRequestService.initialize();
      BranchDetectionService.initialize();
      ConsentManagementService.initialize();
      ImpactAssessmentService.initialize();

      const [
        dsrMetrics,
        branchMetrics,
        consentMetrics,
        assessmentMetrics
      ] = await Promise.all([
        DataSubjectRequestService.getRequestMetrics(),
        BranchDetectionService.getBranchMetrics(),
        ConsentManagementService.getConsentMetrics(),
        ImpactAssessmentService.getAssessmentMetrics()
      ]);

      console.log('✅ GDPR metrics loaded:', {
        dsrMetrics,
        branchMetrics,
        consentMetrics,
        assessmentMetrics
      });

    // Calculate security incident metrics
    const openIncidents = this.securityIncidents.filter(i => !['resolved', 'closed'].includes(i.status)).length;
    const resolvedIncidents = this.securityIncidents.filter(i => ['resolved', 'closed'].includes(i.status)).length;
    const dataBreaches = this.securityIncidents.filter(i => i.isPersonalDataBreach).length;
    
    const avgResolutionTime = resolvedIncidents > 0 
      ? this.securityIncidents
          .filter(i => i.resolvedAt)
          .reduce((sum, incident) => {
            if (incident.resolvedAt) {
              return sum + (incident.resolvedAt.getTime() - incident.detectedAt.getTime());
            }
            return sum;
          }, 0) / resolvedIncidents / (1000 * 60 * 60 * 24) // Convert to days
      : 0;

    // Calculate overall compliance score
    const dsrScore = dsrMetrics.completionRate;
    const branchScore = branchMetrics.complianceScore;
    const consentScore = consentMetrics.consentRate;
    const assessmentScore = assessmentMetrics.completionRate;
    const securityScore = this.securityIncidents.length > 0 
      ? Math.max(0, 100 - (openIncidents / this.securityIncidents.length) * 100)
      : 100;

    const overallScore = (dsrScore + branchScore + consentScore + assessmentScore + securityScore) / 5;

    return {
      dataSubjectRequests: {
        total: dsrMetrics.total,
        pending: dsrMetrics.byStatus.pending || 0,
        inProgress: dsrMetrics.byStatus.in_progress || 0,
        completed: dsrMetrics.byStatus.completed || 0,
        overdue: dsrMetrics.overdueRequests,
        averageProcessingTime: dsrMetrics.averageProcessingTime,
        completionRate: dsrMetrics.completionRate
      },
      branchCompliance: {
        totalBranches: branchMetrics.totalBranches,
        compliantBranches: branchMetrics.byComplianceStatus.compliant || 0,
        complianceScore: branchMetrics.complianceScore,
        crossBorderTransfers: branchMetrics.crossBorderTransfers,
        highRiskBranches: (branchMetrics.byRiskLevel.high || 0) + (branchMetrics.byRiskLevel.critical || 0)
      },
      consentManagement: {
        totalUsers: consentMetrics.totalUsers,
        consentedUsers: consentMetrics.consentedUsers,
        consentRate: consentMetrics.consentRate,
        withdrawalRate: 15.2, // Mock data - would be calculated from withdrawal history
        categoryBreakdown: consentMetrics.categoryBreakdown.map(cat => ({
          category: cat.categoryName,
          consentRate: cat.consentRate
        }))
      },
      impactAssessments: {
        total: assessmentMetrics.total,
        completed: assessmentMetrics.byStatus.approved || 0,
        inProgress: (assessmentMetrics.byStatus.in_progress || 0) + (assessmentMetrics.byStatus.review || 0),
        overdue: assessmentMetrics.overdueAssessments,
        averageRiskScore: 12, // Mock data - would be calculated from risk assessments
        highRiskAssessments: (assessmentMetrics.byRiskLevel.high || 0) + (assessmentMetrics.byRiskLevel.critical || 0)
      },
      securityIncidents: {
        total: this.securityIncidents.length,
        open: openIncidents,
        resolved: resolvedIncidents,
        dataBreaches: dataBreaches,
        averageResolutionTime: avgResolutionTime
      },
      complianceScore: {
        overall: overallScore,
        trend: 'up', // Mock data - would be calculated from historical data
        weeklyChange: 2.3,
        monthlyChange: 5.7
      }
    };
    } catch (error) {
      console.error('❌ Error loading GDPR dashboard metrics:', error);
      // Return default metrics to prevent crashes
      return {
        dataSubjectRequests: {
          total: 0,
          pending: 0,
          inProgress: 0,
          completed: 0,
          overdue: 0,
          averageProcessingTime: 0,
          completionRate: 0
        },
        branchCompliance: {
          totalBranches: 0,
          compliantBranches: 0,
          complianceScore: 0,
          crossBorderTransfers: 0,
          highRiskBranches: 0
        },
        consentManagement: {
          totalUsers: 0,
          consentedUsers: 0,
          consentRate: 0,
          withdrawalRate: 0,
          categoryBreakdown: []
        },
        impactAssessments: {
          total: 0,
          completed: 0,
          inProgress: 0,
          overdue: 0,
          averageRiskScore: 0,
          highRiskAssessments: 0
        },
        securityIncidents: {
          total: 0,
          open: 0,
          resolved: 0,
          dataBreaches: 0,
          averageResolutionTime: 0
        },
        complianceScore: {
          overall: 0,
          trend: 'stable',
          weeklyChange: 0,
          monthlyChange: 0
        }
      };
    }
  }

  // Alerts Management
  static async getAlerts(filters?: {
    type?: string[];
    category?: string[];
    priority?: string[];
    dismissed?: boolean;
  }): Promise<GDPRAlert[]> {
    this.initialize();
    let filtered = [...this.alerts];

    if (filters?.type?.length) {
      filtered = filtered.filter(alert => filters.type!.includes(alert.type));
    }

    if (filters?.category?.length) {
      filtered = filtered.filter(alert => filters.category!.includes(alert.category));
    }

    if (filters?.priority?.length) {
      filtered = filtered.filter(alert => filters.priority!.includes(alert.priority));
    }

    if (filters?.dismissed !== undefined) {
      filtered = filtered.filter(alert => alert.dismissed === filters.dismissed);
    }

    return filtered.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  static async dismissAlert(alertId: string): Promise<boolean> {
    this.initialize();
    const alert = this.alerts.find(a => a.id === alertId);
    if (!alert) return false;

    alert.dismissed = true;
    return true;
  }

  static async createAlert(alertData: Partial<GDPRAlert>): Promise<GDPRAlert> {
    this.initialize();
    const newAlert: GDPRAlert = {
      id: `alert-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: alertData.type || 'info',
      category: alertData.category || 'data_subject_request',
      title: alertData.title || '',
      message: alertData.message || '',
      timestamp: new Date(),
      priority: alertData.priority || 'medium',
      actionRequired: alertData.actionRequired || false,
      relatedEntityId: alertData.relatedEntityId,
      dismissed: false
    };

    this.alerts.unshift(newAlert);
    return newAlert;
  }

  // Activity Feed
  static async getActivities(limit: number = 50): Promise<GDPRActivity[]> {
    this.initialize();
    return this.activities
      .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
      .slice(0, limit);
  }

  static async addActivity(activityData: Partial<GDPRActivity>): Promise<GDPRActivity> {
    this.initialize();
    const newActivity: GDPRActivity = {
      id: `activity-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      type: activityData.type || 'request_created',
      title: activityData.title || '',
      description: activityData.description || '',
      timestamp: new Date(),
      userId: activityData.userId || 'user-001',
      userName: activityData.userName || 'Current User',
      entityId: activityData.entityId,
      entityType: activityData.entityType,
      metadata: activityData.metadata
    };

    this.activities.unshift(newActivity);
    
    // Keep only the last 1000 activities
    if (this.activities.length > 1000) {
      this.activities = this.activities.slice(0, 1000);
    }

    return newActivity;
  }

  // Security Incidents
  static async getSecurityIncidents(): Promise<SecurityIncident[]> {
    this.initialize();
    return [...this.securityIncidents];
  }

  static async createSecurityIncident(incidentData: Partial<SecurityIncident>): Promise<SecurityIncident> {
    this.initialize();
    const newIncident: SecurityIncident = {
      id: `incident-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: incidentData.title || '',
      description: incidentData.description || '',
      severity: incidentData.severity || 'medium',
      category: incidentData.category || 'data_breach',
      status: 'detected',
      detectedAt: new Date(),
      reportedAt: new Date(),
      affectedUsers: incidentData.affectedUsers || 0,
      affectedRecords: incidentData.affectedRecords || 0,
      dataCategories: incidentData.dataCategories || [],
      assignee: incidentData.assignee || 'Security Team',
      responseTeam: incidentData.responseTeam || ['Security Team'],
      estimatedResolution: incidentData.estimatedResolution || new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      isPersonalDataBreach: incidentData.isPersonalDataBreach || false,
      riskToRights: incidentData.riskToRights || 'medium',
      notificationRequired: incidentData.notificationRequired || false,
      supervisoryAuthorityNotified: false,
      dataSubjectsNotified: false,
      contributingFactors: [],
      evidenceCollected: [],
      immediateActions: [],
      containmentActions: [],
      eradicationActions: [],
      recoveryActions: [],
      preventiveActions: [],
      internalCommunications: [],
      externalCommunications: [],
      lessonsLearned: [],
      processImprovements: [],
      policyUpdates: [],
      trainingNeeds: [],
      estimatedCost: 0,
      businessImpact: '',
      reputationalImpact: '',
      auditTrail: [{
        id: `audit-${Date.now()}`,
        timestamp: new Date(),
        userId: 'system',
        userName: 'System',
        action: 'Incident Created',
        details: `Security incident created: ${incidentData.title}`,
        newValue: 'detected'
      }]
    };

    this.securityIncidents.push(newIncident);

    // Create alert for high severity incidents
    if (newIncident.severity === 'high' || newIncident.severity === 'critical') {
      await this.createAlert({
        type: 'error',
        category: 'security_incident',
        title: `${newIncident.severity.toUpperCase()} Security Incident`,
        message: `${newIncident.title} - Immediate attention required`,
        priority: newIncident.severity === 'critical' ? 'critical' : 'high',
        actionRequired: true,
        relatedEntityId: newIncident.id
      });
    }

    return newIncident;
  }

  // Real-time Updates Simulation
  static async simulateRealTimeUpdates(): Promise<void> {
    // This would typically connect to WebSocket or Server-Sent Events
    // For demo purposes, we'll simulate periodic updates
    
    setInterval(async () => {
      // Randomly create new activities
      if (Math.random() > 0.7) {
        const activities = [
          {
            type: 'request_created' as const,
            title: 'New Data Subject Request',
            description: 'A new erasure request has been submitted',
            userName: 'System'
          },
          {
            type: 'consent_granted' as const,
            title: 'Consent Granted',
            description: 'User granted marketing consent',
            userName: 'System'
          },
          {
            type: 'assessment_completed' as const,
            title: 'DPIA Completed',
            description: 'Customer data processing DPIA has been approved',
            userName: 'Privacy Officer'
          }
        ];

        const randomActivity = activities[Math.floor(Math.random() * activities.length)];
        await this.addActivity(randomActivity);
      }

      // Randomly create alerts
      if (Math.random() > 0.9) {
        const alerts = [
          {
            type: 'warning' as const,
            category: 'data_subject_request' as const,
            title: 'Request Approaching Deadline',
            message: 'Data subject request due in 2 days',
            priority: 'medium' as const
          },
          {
            type: 'info' as const,
            category: 'consent' as const,
            title: 'Consent Rate Update',
            message: 'Marketing consent rate increased by 3%',
            priority: 'low' as const
          }
        ];

        const randomAlert = alerts[Math.floor(Math.random() * alerts.length)];
        await this.createAlert(randomAlert);
      }
    }, 30000); // Update every 30 seconds
  }

  // Private helper methods
  private static generateMockAlerts(): GDPRAlert[] {
    return [
      {
        id: 'alert-001',
        type: 'warning',
        category: 'data_subject_request',
        title: 'Overdue Data Subject Request',
        message: 'Request DSR-001 is 2 days overdue and requires immediate attention',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        priority: 'high',
        actionRequired: true,
        relatedEntityId: 'dsr-001',
        dismissed: false
      },
      {
        id: 'alert-002',
        type: 'error',
        category: 'branch_compliance',
        title: 'Compliance Issue Detected',
        message: 'US branch has failed compliance assessment - immediate review required',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        priority: 'critical',
        actionRequired: true,
        relatedEntityId: 'branch-002',
        dismissed: false
      },
      {
        id: 'alert-003',
        type: 'info',
        category: 'consent',
        title: 'Consent Rate Improvement',
        message: 'Marketing consent rate has increased by 5% this week',
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
        priority: 'low',
        actionRequired: false,
        dismissed: false
      }
    ];
  }

  private static generateMockActivities(): GDPRActivity[] {
    return [
      {
        id: 'activity-001',
        type: 'request_created',
        title: 'New Erasure Request',
        description: 'John Doe submitted a data erasure request',
        timestamp: new Date(Date.now() - 30 * 60 * 1000),
        userId: 'system',
        userName: 'System',
        entityId: 'dsr-001',
        entityType: 'data_subject_request'
      },
      {
        id: 'activity-002',
        type: 'consent_withdrawn',
        title: 'Marketing Consent Withdrawn',
        description: 'Jane Smith withdrew consent for marketing communications',
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
        userId: 'system',
        userName: 'System',
        entityId: 'consent-002',
        entityType: 'consent_record'
      },
      {
        id: 'activity-003',
        type: 'assessment_completed',
        title: 'DPIA Approved',
        description: 'Customer data processing DPIA has been approved by legal team',
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
        userId: 'legal-001',
        userName: 'Legal Counsel',
        entityId: 'ia-001',
        entityType: 'impact_assessment'
      }
    ];
  }

  private static generateMockSecurityIncidents(): SecurityIncident[] {
    return [
      {
        id: 'incident-001',
        title: 'Unauthorized Database Access',
        description: 'Suspicious access patterns detected in customer database',
        severity: 'high',
        category: 'unauthorized_access',
        status: 'investigating',
        detectedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
        reportedAt: new Date(Date.now() - 5 * 60 * 60 * 1000),
        affectedUsers: 1250,
        affectedRecords: 1250,
        dataCategories: ['Personal Information', 'Contact Details'],
        assignee: 'Security Team Lead',
        responseTeam: ['Security Team', 'IT Team', 'Legal Team'],
        estimatedResolution: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
        isPersonalDataBreach: true,
        breachType: 'confidentiality',
        riskToRights: 'high',
        notificationRequired: true,
        notificationDeadline: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
        supervisoryAuthorityNotified: false,
        dataSubjectsNotified: false,
        contributingFactors: ['Weak access controls', 'Insufficient monitoring'],
        evidenceCollected: ['Access logs', 'Network traffic analysis'],
        immediateActions: ['Revoked suspicious access', 'Enhanced monitoring'],
        containmentActions: ['Isolated affected systems'],
        eradicationActions: [],
        recoveryActions: [],
        preventiveActions: ['Implement MFA', 'Update access policies'],
        internalCommunications: [],
        externalCommunications: [],
        lessonsLearned: [],
        processImprovements: [],
        policyUpdates: [],
        trainingNeeds: [],
        estimatedCost: 50000,
        businessImpact: 'Potential customer trust impact',
        reputationalImpact: 'Medium - contained quickly',
        auditTrail: [{
          id: 'audit-incident-001',
          timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
          userId: 'security-001',
          userName: 'Security Analyst',
          action: 'Incident Detected',
          details: 'Suspicious database access patterns detected',
          newValue: 'detected'
        }]
      }
    ];
  }
}
