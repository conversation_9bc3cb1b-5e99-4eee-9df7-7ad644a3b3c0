// Security Overview Types with comprehensive functionality
export interface SecurityThreat {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'detected' | 'investigating' | 'contained' | 'resolved';
  type: 'malware' | 'phishing' | 'ddos' | 'unauthorized_access' | 'data_breach' | 'insider_threat';
  source: string;
  targetSystems: string[];
  detectedAt: Date;
  resolvedAt?: Date;
  assignedTo: string;
  impact: {
    systems: number;
    users: number;
    dataRecords: number;
    estimatedCost: number;
  };
  mitigationSteps: Array<{
    id: string;
    description: string;
    status: 'pending' | 'in_progress' | 'completed';
    assignedTo: string;
    dueDate: Date;
  }>;
  evidence: string[];
  relatedIncidents: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  category: 'security_breach' | 'policy_violation' | 'system_compromise' | 'data_loss' | 'unauthorized_access';
  reportedBy: string;
  assignedTo: string;
  affectedSystems: string[];
  affectedUsers: number;
  dataCompromised: boolean;
  reportedAt: Date;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  timeline: Array<{
    timestamp: Date;
    action: string;
    performedBy: string;
    notes?: string;
  }>;
  attachments: string[];
  lessons: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityMetric {
  id: string;
  name: string;
  category: 'threat_detection' | 'incident_response' | 'vulnerability_management' | 'compliance' | 'awareness';
  currentValue: number;
  targetValue: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  status: 'good' | 'warning' | 'critical';
  lastUpdated: Date;
  historicalData: Array<{
    date: string;
    value: number;
    status: string;
    notes?: string;
  }>;
  drillDownData?: {
    bySystem: Array<{ name: string; value: number; status: string }>;
    byThreatType: Array<{ type: string; count: number; severity: string }>;
    timeBreakdown: Array<{ period: string; value: number; change: number }>;
  };
}

export interface SecuritySystem {
  id: string;
  name: string;
  type: 'endpoint' | 'network' | 'server' | 'database' | 'application' | 'cloud';
  status: 'secure' | 'warning' | 'compromised' | 'offline';
  lastScan: Date;
  vulnerabilities: {
    critical: number;
    high: number;
    medium: number;
    low: number;
  };
  securityScore: number;
  location: string;
  owner: string;
  compliance: {
    framework: string;
    status: 'compliant' | 'non_compliant' | 'partial';
    lastAssessment: Date;
  };
  monitoring: {
    isActive: boolean;
    lastHeartbeat: Date;
    alertsEnabled: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityAlert {
  id: string;
  title: string;
  description: string;
  severity: 'info' | 'low' | 'medium' | 'high' | 'critical';
  status: 'new' | 'acknowledged' | 'investigating' | 'resolved' | 'false_positive';
  source: string;
  category: 'intrusion_detection' | 'malware' | 'anomaly' | 'policy_violation' | 'system_failure';
  affectedSystems: string[];
  triggeredAt: Date;
  acknowledgedAt?: Date;
  resolvedAt?: Date;
  assignedTo?: string;
  actions: Array<{
    id: string;
    type: 'block' | 'quarantine' | 'notify' | 'escalate';
    status: 'pending' | 'completed' | 'failed';
    timestamp: Date;
  }>;
  metadata: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityOverviewData {
  overview: {
    securityScore: number;
    activeThreats: number;
    systemsMonitored: number;
    alertsLast24h: number;
    incidentsThisMonth: number;
    vulnerabilitiesFound: number;
    complianceScore: number;
    lastUpdated: Date;
  };
  threats: SecurityThreat[];
  incidents: SecurityIncident[];
  metrics: SecurityMetric[];
  systems: SecuritySystem[];
  alerts: SecurityAlert[];
  realTimeUpdates: {
    lastSync: Date;
    isLive: boolean;
    pendingAlerts: number;
    systemStatus: 'operational' | 'degraded' | 'outage';
  };
}

export interface SecurityOverviewDashboardProps {
  className?: string;
}
