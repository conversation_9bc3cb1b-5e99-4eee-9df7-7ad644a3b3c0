import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Mock console.error to avoid noise in tests
const originalError = console.error;
beforeEach(() => {
  console.error = vi.fn();
});

afterEach(() => {
  console.error = originalError;
});

// Simple error boundary for testing
class TestErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ComponentType<{ error: Error }> },
  { hasError: boolean; error: Error | null }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.log('Error caught by boundary:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      const Fallback = this.props.fallback;
      if (Fallback && this.state.error) {
        return <Fallback error={this.state.error} />;
      }
      return (
        <div>
          <h2>Something went wrong</h2>
          <p>Error: {this.state.error?.message}</p>
          <button onClick={() => this.setState({ hasError: false, error: null })}>
            Try Again
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}

// Test component that throws errors
const ThrowErrorComponent: React.FC<{ shouldThrow?: boolean }> = ({ shouldThrow = true }) => {
  if (shouldThrow) {
    throw new Error('Test error');
  }
  return <div>Component rendered successfully</div>;
};

describe('Error Boundary Tests', () => {
  const user = userEvent.setup();

  describe('Basic Error Boundary Functionality', () => {
    it('catches and displays render errors', () => {
      render(
        <TestErrorBoundary>
          <ThrowErrorComponent />
        </TestErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });

    it('displays retry button', () => {
      render(
        <TestErrorBoundary>
          <ThrowErrorComponent />
        </TestErrorBoundary>
      );
      
      expect(screen.getByRole('button', { name: /try again/i })).toBeInTheDocument();
    });

    it('recovers when retry is clicked', async () => {
      let shouldThrow = true;
      
      const ConditionalErrorComponent: React.FC = () => {
        if (shouldThrow) {
          throw new Error('Conditional error');
        }
        return <div>Component recovered</div>;
      };
      
      const { rerender } = render(
        <TestErrorBoundary>
          <ConditionalErrorComponent />
        </TestErrorBoundary>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Simulate fixing the error
      shouldThrow = false;
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      await user.click(retryButton);
      
      // Re-render with fixed component
      rerender(
        <TestErrorBoundary>
          <ConditionalErrorComponent />
        </TestErrorBoundary>
      );
      
      expect(screen.getByText('Component recovered')).toBeInTheDocument();
    });

    it('uses custom fallback component', () => {
      const CustomFallback: React.FC<{ error: Error }> = ({ error }) => (
        <div>
          <h2>Custom Error Fallback</h2>
          <p>Error: {error.message}</p>
        </div>
      );
      
      render(
        <TestErrorBoundary fallback={CustomFallback}>
          <ThrowErrorComponent />
        </TestErrorBoundary>
      );
      
      expect(screen.getByText('Custom Error Fallback')).toBeInTheDocument();
      expect(screen.getByText('Error: Test error')).toBeInTheDocument();
    });

    it('renders children when no error occurs', () => {
      render(
        <TestErrorBoundary>
          <ThrowErrorComponent shouldThrow={false} />
        </TestErrorBoundary>
      );
      
      expect(screen.getByText('Component rendered successfully')).toBeInTheDocument();
      expect(screen.queryByText(/something went wrong/i)).not.toBeInTheDocument();
    });
  });

  describe('Error Boundary Integration', () => {
    it('isolates errors to specific components', () => {
      render(
        <div>
          <TestErrorBoundary>
            <ThrowErrorComponent />
          </TestErrorBoundary>
          <div>Other content should still render</div>
        </div>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText('Other content should still render')).toBeInTheDocument();
    });

    it('handles multiple error boundaries', () => {
      render(
        <div>
          <TestErrorBoundary>
            <ThrowErrorComponent />
          </TestErrorBoundary>
          <TestErrorBoundary>
            <div>Working component</div>
          </TestErrorBoundary>
        </div>
      );
      
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText('Working component')).toBeInTheDocument();
    });
  });

  describe('Error Boundary Accessibility', () => {
    it('provides accessible error messages', () => {
      render(
        <TestErrorBoundary>
          <ThrowErrorComponent />
        </TestErrorBoundary>
      );
      
      const errorHeading = screen.getByRole('heading', { level: 2 });
      expect(errorHeading).toHaveTextContent(/something went wrong/i);
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      expect(retryButton).toBeInTheDocument();
    });

    it('supports keyboard navigation', async () => {
      render(
        <TestErrorBoundary>
          <ThrowErrorComponent />
        </TestErrorBoundary>
      );
      
      const retryButton = screen.getByRole('button', { name: /try again/i });
      retryButton.focus();
      expect(document.activeElement).toBe(retryButton);
      
      await user.keyboard('{Enter}');
      // Button should be clickable via keyboard
    });
  });
});
