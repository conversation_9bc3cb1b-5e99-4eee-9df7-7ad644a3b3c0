import React, { useState, lazy, Suspense } from 'react';
import Sidebar from './components/Sidebar';
import { ComplianceProvider } from './context/ComplianceContext';
import { ComplianceRulesProvider } from './context/ComplianceRulesContext';
import { ComplianceStandardsProvider } from './context/ComplianceStandardsContext';
// import { FlyerVerificationProvider } from './context/FlyerVerificationContext';
import ErrorBoundary from './components/ErrorBoundary';
import { HashRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { SubPolicyPage } from './components/SubPolicyPage';
import { AuthProvider } from './context/AuthContext';
import { ThemeProvider, useTheme } from './context/ThemeContext';
import { NavigationProvider } from './context/NavigationContext';
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

// Lazy load components
const Home = lazy(() => import('./components/Home'));
const Dashboard = lazy(() => import('./components/Dashboard/Dashboard'));
const EnterpriseDashboard = lazy(() => import('./components/Dashboard/EnterpriseDashboard'));
const ComplianceDashboard = lazy(() => import('./components/ComplianceDashboard'));
const Configurations = lazy(() => import('./components/Configurations'));
const ConsumerInterface = lazy(() => import('./components/ConsumerInterface'));
const ComplianceReports = lazy(() => import('./components/ComplianceReports'));
const ComplianceRulesDashboard = lazy(() => import('./components/ComplianceRules/ComplianceRulesDashboard'));
const GdprAnalysisPage = lazy(() => import('./components/GdprAnalysis/GdprAnalysisPage'));
const ComplianceFilteringDemo = lazy(() => import('./components/ComplianceFilteringDemo'));

// Privacy Dashboard detail components
const GreenLevelDetails = lazy(() => import('./components/Dashboard/GreenLevelDetails'));
const AmberLevelDetails = lazy(() => import('./components/Dashboard/AmberLevelDetails'));
const RedLevelDetails = lazy(() => import('./components/Dashboard/RedLevelDetails'));
const DataPrivacyAcknowledgement = lazy(() => import('./components/Dashboard/DataPrivacyAcknowledgement'));

// NOC Dashboard and Flight Onboarding removed

// Flyer Verification Dashboard - temporarily hidden
// const FlyerVerificationDashboard = lazy(() => import('./components/FlyerVerification/FlyerVerificationDashboard'));

const LoadingFallback = () => (
  <div className="flex-1 flex items-center justify-center p-8">
    <div className="spinner"></div>
    <span className="sr-only">Loading...</span>
  </div>
);

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  // Map routes to components
  const routeComponents = {
    home: <Home />,
    enterprise: <EnterpriseDashboard />,
    privacy: <Dashboard />,
    'gdpr-analysis': <GdprAnalysisPage />,
    configurations: <Configurations />,
    consumer: <ConsumerInterface />,
    reports: <ComplianceReports />,
    complianceRules: <ComplianceRulesDashboard />,
    'compliance-demo': <ComplianceFilteringDemo />
  };

  return (
    <Router>
      <ThemeProvider>
        <NavigationProvider>
          <AuthProvider>
            <ComplianceProvider>
              <ComplianceRulesProvider>
                <ComplianceStandardsProvider>
                  {/* FlyerVerificationProvider temporarily removed */}
                  {/* <FlyerVerificationProvider> */}
                  <AppContent
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    routeComponents={routeComponents}
                  />
                  {/* </FlyerVerificationProvider> */}
                </ComplianceStandardsProvider>
              </ComplianceRulesProvider>
            </ComplianceProvider>
          </AuthProvider>
        </NavigationProvider>
      </ThemeProvider>
    </Router>
  );
};

// Separate component to access theme context
const AppContent: React.FC<{
  currentPage: string;
  setCurrentPage: (page: string) => void;
  routeComponents: Record<string, React.ReactNode>;
}> = ({ currentPage, setCurrentPage, routeComponents }) => {
  const { mode } = useTheme();

  return (
    <>
      <ToastContainer theme={mode === 'dark' ? 'dark' : 'light'} />
      <div className={`flex min-h-screen bg-background ${mode === 'dark' ? 'dark' : ''}`}>
        <Sidebar onPageChange={setCurrentPage} currentPage={currentPage} />
        <main className="flex-1 overflow-auto bg-background text-text">
          <ErrorBoundary>
            <Suspense fallback={<LoadingFallback />}>
              <Routes>
                <Route
                  path="/"
                  element={routeComponents[currentPage as keyof typeof routeComponents] || <Home />}
                />
                <Route
                  path="/subpolicy/:parentPolicy/:subPolicyName"
                  element={<SubPolicyPage />}
                />
                <Route
                  path="/compliance-rules"
                  element={<ComplianceRulesDashboard />}
                />
                <Route
                  path="/green-level"
                  element={<GreenLevelDetails />}
                />
                <Route
                  path="/amber-level"
                  element={<AmberLevelDetails />}
                />
                <Route
                  path="/red-level"
                  element={<RedLevelDetails />}
                />
                <Route
                  path="/data-privacy-acknowledgement"
                  element={<DataPrivacyAcknowledgement />}
                />
                {/* NOC Dashboard and Flight Onboarding routes removed */}
                {/* Flyer Verification feature temporarily hidden */}
                {/* <Route
                  path="/flyer-verification"
                  element={<FlyerVerificationDashboard />}
                /> */}

                <Route
                  path="*"
                  element={<Navigate to="/" replace />}
                />
              </Routes>
            </Suspense>
          </ErrorBoundary>
        </main>
      </div>
    </>
  );
}

export default App;