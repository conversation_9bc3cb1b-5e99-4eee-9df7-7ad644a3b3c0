/**
 * Toast Notification Component
 * Provides user feedback for CRUD operations and system events
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { CheckCircle, XCircle, AlertTriangle, Info, X } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';
import { crudOperationsService, type ToastNotification } from '../../services/crudOperationsService';

interface ToastProps {
  toast: ToastNotification;
  onClose: (id: string) => void;
}

const Toast: React.FC<ToastProps> = ({ toast, onClose }) => {
  const { mode } = useTheme();
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);

  useEffect(() => {
    // Animate in
    const timer = setTimeout(() => setIsVisible(true), 10);
    
    // Auto-close timer
    const autoCloseTimer = setTimeout(() => {
      handleClose();
    }, toast.duration || 5000);

    return () => {
      clearTimeout(timer);
      clearTimeout(autoCloseTimer);
    };
  }, [toast.duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose(toast.id);
    }, 300);
  };

  const getIcon = () => {
    switch (toast.type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
        return <Info className="w-5 h-5 text-blue-500" />;
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getBackgroundColor = () => {
    const baseClasses = mode === 'dark' ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200';
    switch (toast.type) {
      case 'success':
        return `${baseClasses} border-l-4 border-l-green-500`;
      case 'error':
        return `${baseClasses} border-l-4 border-l-red-500`;
      case 'warning':
        return `${baseClasses} border-l-4 border-l-yellow-500`;
      case 'info':
        return `${baseClasses} border-l-4 border-l-blue-500`;
      default:
        return `${baseClasses} border-l-4 border-l-blue-500`;
    }
  };

  return (
    <div
      className={`
        transform transition-all duration-300 ease-in-out mb-4 max-w-md w-full
        ${isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'}
        ${getBackgroundColor()}
        rounded-lg shadow-lg border p-4
      `}
      role="alert"
      aria-live="polite"
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {getIcon()}
        </div>
        <div className="ml-3 flex-1">
          <h4 className={`text-sm font-semibold ${mode === 'dark' ? 'text-white' : 'text-gray-900'}`}>
            {toast.title}
          </h4>
          <p className={`text-sm mt-1 ${mode === 'dark' ? 'text-gray-300' : 'text-gray-600'}`}>
            {toast.message}
          </p>
        </div>
        <div className="ml-4 flex-shrink-0">
          <button
            onClick={handleClose}
            className={`
              inline-flex rounded-md p-1.5 transition-colors duration-200
              ${mode === 'dark' 
                ? 'text-gray-400 hover:text-gray-300 hover:bg-gray-700' 
                : 'text-gray-400 hover:text-gray-500 hover:bg-gray-100'
              }
            `}
            aria-label="Close notification"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      </div>
    </div>
  );
};

export const ToastContainer: React.FC = () => {
  const [toasts, setToasts] = useState<ToastNotification[]>([]);

  useEffect(() => {
    const unsubscribe = crudOperationsService.onToast((toast) => {
      setToasts(prev => [...prev, toast]);
    });

    return unsubscribe;
  }, []);

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  };

  if (toasts.length === 0) return null;

  return createPortal(
    <div
      className="fixed top-4 right-4 z-50 space-y-2"
      aria-live="polite"
      aria-label="Notifications"
    >
      {toasts.map(toast => (
        <Toast
          key={toast.id}
          toast={toast}
          onClose={removeToast}
        />
      ))}
    </div>,
    document.body
  );
};

// Hook for programmatic toast usage
export const useToast = () => {
  const showToast = (toast: Omit<ToastNotification, 'id'>) => {
    const notification: ToastNotification = {
      ...toast,
      id: `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      duration: toast.duration || 5000,
    };
    
    crudOperationsService.onToast(() => {})(notification);
  };

  return {
    showSuccess: (title: string, message: string, duration?: number) =>
      showToast({ type: 'success', title, message, duration }),
    showError: (title: string, message: string, duration?: number) =>
      showToast({ type: 'error', title, message, duration }),
    showWarning: (title: string, message: string, duration?: number) =>
      showToast({ type: 'warning', title, message, duration }),
    showInfo: (title: string, message: string, duration?: number) =>
      showToast({ type: 'info', title, message, duration }),
  };
};

export default ToastContainer;
