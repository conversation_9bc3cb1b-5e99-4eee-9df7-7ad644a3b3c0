import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { ImpactAssessment, Risk, MitigationMeasure } from '../../types/compliance';
import { ImpactAssessmentService } from '../../services/impactAssessmentService';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  FileText,
  Users,
  Target,
  Settings,
  Edit,
  Plus,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  BarChart3,
  Activity,
  Zap,
  AlertCircle
} from 'lucide-react';

interface ImpactAssessmentDashboardProps {
  className?: string;
}

export const ImpactAssessmentDashboard: React.FC<ImpactAssessmentDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [assessments, setAssessments] = useState<ImpactAssessment[]>([]);
  const [filteredAssessments, setFilteredAssessments] = useState<ImpactAssessment[]>([]);
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'assessments' | 'risks' | 'templates'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAssessment, setSelectedAssessment] = useState<ImpactAssessment | null>(null);

  // Enhanced state for CRUD operations
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showViewModal, setShowViewModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [showApprovalModal, setShowApprovalModal] = useState(false);
  const [editingAssessment, setEditingAssessment] = useState<ImpactAssessment | null>(null);

  // Bulk operations
  const [selectedAssessments, setSelectedAssessments] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [bulkAction, setBulkAction] = useState<'approve' | 'reject' | 'delete' | 'export'>('approve');

  // Filtering and sorting
  const [filters, setFilters] = useState({
    status: [] as string[],
    type: [] as string[],
    riskLevel: [] as string[],
    assignee: [] as string[]
  });
  const [sortBy, setSortBy] = useState<'title' | 'status' | 'riskLevel' | 'createdAt' | 'dueDate'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Form data for creating/editing assessments
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    type: 'dpia' as const,
    priority: 'medium' as const,
    dataCategories: [] as string[],
    processingPurposes: [] as string[],
    legalBasis: '',
    dataSubjects: [] as string[],
    thirdPartySharing: false,
    internationalTransfers: false,
    retentionPeriod: '',
    securityMeasures: [] as string[],
    assignee: '',
    reviewers: [] as string[],
    approvers: [] as string[]
  });

  useEffect(() => {
    loadData();

    // Set up real-time updates
    const interval = setInterval(loadData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    applyFiltersAndSort();
  }, [assessments, searchTerm, filters, sortBy, sortOrder]);

  const loadData = async () => {
    try {
      setLoading(true);
      const [assessmentsData, metricsData] = await Promise.all([
        ImpactAssessmentService.getAllAssessments(),
        ImpactAssessmentService.getAssessmentMetrics()
      ]);
      
      setAssessments(assessmentsData);
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading assessment data:', error);
    } finally {
      setLoading(false);
    }
  };

  // Enhanced filtering and sorting
  const applyFiltersAndSort = () => {
    let filtered = assessments;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(assessment =>
        assessment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        assessment.createdBy.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filters.status.length > 0) {
      filtered = filtered.filter(assessment => filters.status.includes(assessment.status));
    }

    // Apply type filter
    if (filters.type.length > 0) {
      filtered = filtered.filter(assessment => filters.type.includes(assessment.type));
    }

    // Apply risk level filter
    if (filters.riskLevel.length > 0) {
      filtered = filtered.filter(assessment => filters.riskLevel.includes(assessment.riskLevel));
    }

    // Apply assignee filter
    if (filters.assignee.length > 0) {
      filtered = filtered.filter(assessment =>
        assessment.workflow.some(step => filters.assignee.includes(step.assignee))
      );
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue: any, bValue: any;

      switch (sortBy) {
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'status':
          aValue = a.status;
          bValue = b.status;
          break;
        case 'riskLevel':
          const riskOrder = { 'low': 1, 'medium': 2, 'high': 3, 'critical': 4 };
          aValue = riskOrder[a.riskLevel as keyof typeof riskOrder] || 0;
          bValue = riskOrder[b.riskLevel as keyof typeof riskOrder] || 0;
          break;
        case 'createdAt':
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
          break;
        case 'dueDate':
          aValue = a.workflow[0]?.dueDate?.getTime() || 0;
          bValue = b.workflow[0]?.dueDate?.getTime() || 0;
          break;
        default:
          aValue = a.createdAt.getTime();
          bValue = b.createdAt.getTime();
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1;
      } else {
        return aValue < bValue ? 1 : -1;
      }
    });

    setFilteredAssessments(filtered);
  };

  // Enhanced CRUD Operations
  const handleCreateAssessment = async () => {
    try {
      setLoading(true);

      const newAssessment = await ImpactAssessmentService.createAssessment({
        title: formData.title,
        description: formData.description,
        type: formData.type,
        priority: formData.priority,
        dataCategories: formData.dataCategories,
        processingPurposes: formData.processingPurposes,
        legalBasis: formData.legalBasis,
        dataSubjects: formData.dataSubjects,
        thirdPartySharing: formData.thirdPartySharing,
        internationalTransfers: formData.internationalTransfers,
        retentionPeriod: formData.retentionPeriod,
        securityMeasures: formData.securityMeasures
      });

      await loadData();
      setShowCreateModal(false);
      resetForm();

      // Show success notification (assuming notification system exists)
      console.log('Assessment created successfully:', newAssessment.id);

    } catch (error) {
      console.error('Error creating assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateAssessment = async () => {
    if (!editingAssessment) return;

    try {
      setLoading(true);

      const updatedAssessment = await ImpactAssessmentService.updateAssessment(editingAssessment.id, {
        title: formData.title,
        description: formData.description,
        type: formData.type,
        priority: formData.priority,
        dataCategories: formData.dataCategories,
        processingPurposes: formData.processingPurposes,
        legalBasis: formData.legalBasis,
        dataSubjects: formData.dataSubjects,
        thirdPartySharing: formData.thirdPartySharing,
        internationalTransfers: formData.internationalTransfers,
        retentionPeriod: formData.retentionPeriod,
        securityMeasures: formData.securityMeasures
      });

      if (updatedAssessment) {
        await loadData();
        setShowEditModal(false);
        setEditingAssessment(null);
        resetForm();
        console.log('Assessment updated successfully');
      }

    } catch (error) {
      console.error('Error updating assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteAssessment = async () => {
    if (!selectedAssessment) return;

    try {
      setLoading(true);

      const success = await ImpactAssessmentService.deleteAssessment(selectedAssessment.id);

      if (success) {
        await loadData();
        setShowDeleteModal(false);
        setSelectedAssessment(null);
        console.log('Assessment deleted successfully');
      }

    } catch (error) {
      console.error('Error deleting assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'review': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'in_progress': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'draft': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
      case 'rejected': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  // Workflow Management and Approval Processes
  const handleApproveAssessment = async (assessmentId: string, comments?: string) => {
    try {
      setLoading(true);

      const success = await ImpactAssessmentService.approveAssessment(assessmentId, {
        approvedBy: 'Current User', // In real app, get from auth context
        approvedAt: new Date(),
        comments: comments || ''
      });

      if (success) {
        await loadData();
        console.log('Assessment approved successfully');
      }

    } catch (error) {
      console.error('Error approving assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRejectAssessment = async (assessmentId: string, reason: string) => {
    try {
      setLoading(true);

      const success = await ImpactAssessmentService.rejectAssessment(assessmentId, {
        rejectedBy: 'Current User',
        rejectedAt: new Date(),
        reason: reason
      });

      if (success) {
        await loadData();
        console.log('Assessment rejected successfully');
      }

    } catch (error) {
      console.error('Error rejecting assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateWorkflowStep = async (assessmentId: string, stepId: string, status: string, notes?: string) => {
    try {
      const success = await ImpactAssessmentService.updateWorkflowStep(assessmentId, stepId, {
        status: status as any,
        completedDate: status === 'completed' ? new Date() : undefined,
        notes: notes
      });

      if (success) {
        await loadData();
        console.log('Workflow step updated successfully');
      }

    } catch (error) {
      console.error('Error updating workflow step:', error);
    }
  };

  // Bulk Operations
  const handleSelectAssessment = (assessmentId: string) => {
    setSelectedAssessments(prev =>
      prev.includes(assessmentId)
        ? prev.filter(id => id !== assessmentId)
        : [...prev, assessmentId]
    );
  };

  const handleSelectAll = () => {
    if (selectedAssessments.length === filteredAssessments.length) {
      setSelectedAssessments([]);
    } else {
      setSelectedAssessments(filteredAssessments.map(assessment => assessment.id));
    }
  };

  const handleBulkAction = async () => {
    if (selectedAssessments.length === 0) return;

    try {
      setLoading(true);

      switch (bulkAction) {
        case 'approve':
          for (const assessmentId of selectedAssessments) {
            await ImpactAssessmentService.approveAssessment(assessmentId, {
              approvedBy: 'Current User',
              approvedAt: new Date(),
              comments: 'Bulk approval'
            });
          }
          console.log(`Bulk approved ${selectedAssessments.length} assessments`);
          break;

        case 'reject':
          for (const assessmentId of selectedAssessments) {
            await ImpactAssessmentService.rejectAssessment(assessmentId, {
              rejectedBy: 'Current User',
              rejectedAt: new Date(),
              reason: 'Bulk rejection'
            });
          }
          console.log(`Bulk rejected ${selectedAssessments.length} assessments`);
          break;

        case 'delete':
          for (const assessmentId of selectedAssessments) {
            await ImpactAssessmentService.deleteAssessment(assessmentId);
          }
          console.log(`Bulk deleted ${selectedAssessments.length} assessments`);
          break;

        case 'export':
          const exportData = assessments.filter(assessment => selectedAssessments.includes(assessment.id));
          const dataStr = JSON.stringify(exportData, null, 2);
          const dataBlob = new Blob([dataStr], { type: 'application/json' });
          const url = URL.createObjectURL(dataBlob);
          const link = document.createElement('a');
          link.href = url;
          link.download = `impact-assessments-${new Date().toISOString().split('T')[0]}.json`;
          link.click();
          URL.revokeObjectURL(url);
          console.log(`Exported ${selectedAssessments.length} assessments`);
          break;
      }

      await loadData();
      setSelectedAssessments([]);
      setShowBulkActions(false);

    } catch (error) {
      console.error('Bulk action error:', error);
    } finally {
      setLoading(false);
    }
  };

  // Utility Functions
  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      type: 'dpia',
      priority: 'medium',
      dataCategories: [],
      processingPurposes: [],
      legalBasis: '',
      dataSubjects: [],
      thirdPartySharing: false,
      internationalTransfers: false,
      retentionPeriod: '',
      securityMeasures: [],
      assignee: '',
      reviewers: [],
      approvers: []
    });
  };

  const openCreateModal = () => {
    resetForm();
    setShowCreateModal(true);
  };

  const openEditModal = (assessment: ImpactAssessment) => {
    setEditingAssessment(assessment);
    setFormData({
      title: assessment.title,
      description: assessment.description,
      type: assessment.type,
      priority: assessment.priority,
      dataCategories: assessment.dataCategories,
      processingPurposes: assessment.processingPurposes,
      legalBasis: assessment.legalBasis,
      dataSubjects: assessment.dataSubjects,
      thirdPartySharing: assessment.thirdPartySharing,
      internationalTransfers: assessment.internationalTransfers,
      retentionPeriod: assessment.retentionPeriod,
      securityMeasures: assessment.securityMeasures,
      assignee: assessment.workflow[0]?.assignee || '',
      reviewers: assessment.reviewers.map(r => r.userId),
      approvers: assessment.approvers.map(a => a.userId)
    });
    setShowEditModal(true);
  };

  const openViewModal = (assessment: ImpactAssessment) => {
    setSelectedAssessment(assessment);
    setShowViewModal(true);
  };

  const openDeleteModal = (assessment: ImpactAssessment) => {
    setSelectedAssessment(assessment);
    setShowDeleteModal(true);
  };

  const openApprovalModal = (assessment: ImpactAssessment) => {
    setSelectedAssessment(assessment);
    setShowApprovalModal(true);
  };

  // Export functionality
  const handleExport = async (format: 'json' | 'csv' = 'json') => {
    try {
      const dataToExport = selectedAssessments.length > 0
        ? assessments.filter(assessment => selectedAssessments.includes(assessment.id))
        : filteredAssessments;

      if (format === 'json') {
        const dataStr = JSON.stringify(dataToExport, null, 2);
        const dataBlob = new Blob([dataStr], { type: 'application/json' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `impact-assessments-${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        URL.revokeObjectURL(url);
      } else if (format === 'csv') {
        const headers = ['ID', 'Title', 'Type', 'Status', 'Risk Level', 'Priority', 'Created Date', 'Created By'];
        const csvContent = [
          headers.join(','),
          ...dataToExport.map(assessment => [
            assessment.id,
            `"${assessment.title}"`,
            assessment.type,
            assessment.status,
            assessment.riskLevel,
            assessment.priority,
            assessment.createdAt.toISOString().split('T')[0],
            assessment.createdBy
          ].join(','))
        ].join('\n');

        const dataBlob = new Blob([csvContent], { type: 'text/csv' });
        const url = URL.createObjectURL(dataBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `impact-assessments-${new Date().toISOString().split('T')[0]}.csv`;
        link.click();
        URL.revokeObjectURL(url);
      }

      console.log(`Exported ${dataToExport.length} assessments as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export error:', error);
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'critical': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'DPIA': return <Shield className="w-5 h-5 text-primary" />;
      case 'LIA': return <Target className="w-5 h-5 text-secondary" />;
      case 'TIA': return <Activity className="w-5 h-5 text-accent-purple" />;
      case 'CHIA': return <Users className="w-5 h-5 text-green-500" />;
      default: return <FileText className="w-5 h-5 text-text-secondary" />;
    }
  };

  const formatDate = (date: Date | null | undefined) => {
    if (!date) return 'No date';
    try {
      return date?.toLocaleDateString?.('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      }) || 'Invalid date';
    } catch (error) {
      return 'Invalid date';
    }
  };

  const getDaysUntilDue = (date: Date) => {
    const now = new Date();
    const diffInDays = Math.ceil((date.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    return diffInDays;
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center p-8`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-2 text-text-secondary">Loading assessments...</span>
      </div>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Impact Assessment Error</h2>
            <p className="text-text-secondary">Something went wrong loading the impact assessment dashboard. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Impact Assessments (DPIA)</h2>
            <p className="text-text-secondary">Manage Data Protection Impact Assessments and risk evaluations</p>
          </div>
        </div>
        <div className="flex items-center gap-3">
          <button className={`flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-all duration-300 ${mode === 'dark' ? '' : 'hover:scale-105'}`}>
            <Plus className="w-4 h-4" />
            New Assessment
          </button>
          <button className={`p-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
            <Download className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-surface rounded-lg p-1">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'assessments', label: 'Assessments', icon: Shield },
          { id: 'risks', label: 'Risk Matrix', icon: AlertTriangle },
          { id: 'templates', label: 'Templates', icon: FileText }
        ].map(tab => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as any)}
            className={`flex items-center gap-2 px-4 py-2 rounded-md transition-colors ${
              activeTab === tab.id
                ? 'bg-primary text-white'
                : 'text-text-secondary hover:text-text hover:bg-card'
            }`}
          >
            <tab.icon className="w-4 h-4" />
            {tab.label}
          </button>
        ))}
      </div>

      {/* Overview Tab */}
      {activeTab === 'overview' && metrics && (
        <div className="space-y-6">
          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-lg`}>
                  <Shield className="w-6 h-6 text-primary" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics?.total || 0}</div>
              <div className="text-text-secondary text-sm">Total Assessments</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-green-500/10'} rounded-lg`}>
                  <CheckCircle className="w-6 h-6 text-green-500" />
                </div>
                <TrendingUp className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics?.completionRate?.toFixed?.(1) || '0.0'}%</div>
              <div className="text-text-secondary text-sm">Completion Rate</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-orange-500/10'} rounded-lg`}>
                  <AlertTriangle className="w-6 h-6 text-orange-500" />
                </div>
                <AlertCircle className="w-5 h-5 text-orange-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics?.overdueAssessments || 0}</div>
              <div className="text-text-secondary text-sm">Overdue Assessments</div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} rounded-lg`}>
                  <Clock className="w-6 h-6 text-secondary" />
                </div>
                <TrendingDown className="w-5 h-5 text-green-500" />
              </div>
              <div className="text-3xl font-bold text-text mb-2">{metrics?.averageCompletionTime?.toFixed?.(0) || '0'}</div>
              <div className="text-text-secondary text-sm">Avg. Days to Complete</div>
            </div>
          </div>

          {/* Status and Risk Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Assessment Status</h3>
              <div className="space-y-4">
                {Object.entries(metrics?.byStatus || {}).map(([status, count]) => (
                  <div key={status} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getStatusColor(status)} text-xs font-bold rounded-full uppercase`}>
                        {status ? status?.replace?.('_', ' ') || status : 'Unknown'}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${metrics?.total ? ((count as number / metrics.total) * 100) : 0}%`}}
                        ></div>
                      </div>
                      <span className="text-sm font-semibold text-text w-8">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-xl border border-border`}>
              <h3 className="text-lg font-semibold text-text mb-4">Risk Level Distribution</h3>
              <div className="space-y-4">
                {Object.entries(metrics?.byRiskLevel || {}).map(([level, count]) => (
                  <div key={level} className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <span className={`px-3 py-1 ${getRiskLevelColor(level)} text-xs font-bold rounded-full uppercase`}>
                        {level}
                      </span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-24 bg-border rounded-full h-2">
                        <div 
                          className="bg-primary h-2 rounded-full transition-all duration-500"
                          style={{width: `${metrics?.total ? ((count as number / metrics.total) * 100) : 0}%`}}
                        ></div>
                      </div>
                      <span className="text-sm font-semibold text-text w-8">{count}</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Assessments Tab */}
      {activeTab === 'assessments' && (
        <div className="space-y-6">
          {/* Search and Filter */}
          <div className="flex items-center gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
              <input
                type="text"
                placeholder="Search assessments..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`w-full pl-10 pr-4 py-2 bg-surface border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary`}
              />
            </div>
            <button className={`flex items-center gap-2 px-4 py-2 bg-surface hover:bg-card border border-border text-text rounded-lg transition-colors`}>
              <Filter className="w-4 h-4" />
              Filters
            </button>
          </div>

          {/* Assessments List */}
          <div className="space-y-4">
            {(assessments || [])
              .filter(assessment =>
                !searchTerm ||
                assessment?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
                assessment?.description?.toLowerCase()?.includes(searchTerm.toLowerCase())
              )
              .map((assessment) => (
                <div
                  key={assessment.id}
                  className={`group relative p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface border border-border hover:border-text/20' : 'bg-gradient-to-r from-surface to-card border border-border hover:border-primary/30 hover:shadow-lg'} rounded-xl transition-all duration-300 cursor-pointer`}
                  onClick={() => setSelectedAssessment(assessment)}
                >
                  {mode === 'light' && (
                    <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-transparent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  )}
                  
                  <div className="relative z-10">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-4">
                        <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} rounded-xl`}>
                          {getTypeIcon(assessment?.type)}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-text">{assessment?.title || 'Untitled Assessment'}</h3>
                          <p className="text-text-secondary text-sm">{assessment?.description || 'No description available'}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-3">
                        <span className={`px-3 py-1 ${getStatusColor(assessment.status)} text-xs font-bold rounded-full uppercase`}>
                          {assessment?.status?.replace?.('_', ' ') || assessment?.status || 'Unknown'}
                        </span>
                        <span className={`px-3 py-1 ${getRiskLevelColor(assessment?.riskAssessment?.riskLevel)} text-xs font-bold rounded-full uppercase`}>
                          {assessment?.riskAssessment?.riskLevel || 'Unknown'} Risk
                        </span>
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                      <div className="flex items-center gap-2">
                        <User className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text-secondary">Assignee: {assessment?.assignee || 'Unassigned'}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Calendar className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text-secondary">
                          Due: {assessment?.dueDate ? formatDate(assessment.dueDate) : 'No due date'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text-secondary">
                          {assessment?.dueDate ? (
                            getDaysUntilDue(assessment.dueDate) > 0
                              ? `${getDaysUntilDue(assessment.dueDate)} days left`
                              : `${Math.abs(getDaysUntilDue(assessment.dueDate))} days overdue`
                          ) : 'No due date set'}
                        </span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Zap className="w-4 h-4 text-text-secondary" />
                        <span className="text-sm text-text-secondary">
                          Risk Score: {assessment?.riskAssessment?.overallRiskScore || 'N/A'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="text-sm text-text-secondary">Progress:</span>
                        <div className="w-32 bg-border rounded-full h-2">
                          <div 
                            className="bg-primary h-2 rounded-full transition-all duration-500"
                            style={{width: `${assessment?.completionPercentage || 0}%`}}
                          ></div>
                        </div>
                        <span className="text-sm font-semibold text-primary">{assessment?.completionPercentage || 0}%</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <span className="text-xs text-text-secondary">
                          {assessment?.riskAssessment?.identifiedRisks?.length || 0} risks, {assessment?.mitigationMeasures?.length || 0} measures
                        </span>

                      </div>
                    </div>
                  </div>
                </div>
              ))}
          </div>
        </div>
      )}

      {/* Risk Matrix Tab */}
      {activeTab === 'risks' && (
        <div className="space-y-6">
          <div className="text-center py-12">
            <AlertTriangle className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text mb-2">Risk Matrix Visualization</h3>
            <p className="text-text-secondary">Interactive risk matrix and heat map coming soon</p>
          </div>
        </div>
      )}

      {/* Templates Tab */}
      {activeTab === 'templates' && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              {
                type: 'DPIA',
                title: 'Data Protection Impact Assessment',
                description: 'Standard DPIA template for high-risk processing activities',
                icon: Shield,
                color: 'primary'
              },
              {
                type: 'LIA',
                title: 'Legitimate Interest Assessment',
                description: 'Template for legitimate interest balancing tests',
                icon: Target,
                color: 'secondary'
              },
              {
                type: 'TIA',
                title: 'Transfer Impact Assessment',
                description: 'Assessment template for international data transfers',
                icon: Activity,
                color: 'accent-purple'
              },
              {
                type: 'CHIA',
                title: 'Children Impact Assessment',
                description: 'Specialized template for processing children\'s data',
                icon: Users,
                color: 'green-500'
              }
            ].map((template) => (
              <div
                key={template.type}
                className={`p-6 ${mode === 'dark' ? 'bg-card hover:bg-surface' : 'bg-surface hover:bg-card'} rounded-xl border border-border hover:border-primary/30 transition-all duration-300 cursor-pointer group`}
              >
                <div className="flex items-center gap-4 mb-4">
                  <div className={`p-3 bg-${template.color}/10 rounded-xl`}>
                    <template.icon className={`w-6 h-6 text-${template.color}`} />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-text">{template.title}</h3>
                    <p className="text-text-secondary text-sm">{template.description}</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className={`px-3 py-1 bg-${template.color}/20 text-${template.color} text-xs font-bold rounded-full uppercase`}>
                    {template.type}
                  </span>
                  <button className={`flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-all duration-300 opacity-0 group-hover:opacity-100 ${mode === 'dark' ? '' : 'hover:scale-105'}`}>
                    <Plus className="w-4 h-4" />
                    Use Template
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
      </div>
    </ErrorBoundary>
  );
};

export default ImpactAssessmentDashboard;
