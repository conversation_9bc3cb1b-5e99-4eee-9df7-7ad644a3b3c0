import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test/utils/test-utils';
import { GDPRCommandCenter } from '../GDPRCommandCenter';
import { GDPRService } from '../../../services/gdprService';
import { DataSubjectRequestService } from '../../../services/dataSubjectRequestService';

// Mock services
vi.mock('../../../services/gdprService');
vi.mock('../../../services/dataSubjectRequestService');

const mockMetrics = {
  dataSubjectRequests: {
    total: 150,
    pending: 25,
    inProgress: 30,
    completed: 85,
    overdue: 10,
    averageProcessingTime: 15,
    completionRate: 85
  },
  branchCompliance: {
    totalBranches: 12,
    compliantBranches: 10,
    complianceScore: 83,
    crossBorderTransfers: 5,
    highRiskBranches: 2
  },
  consentManagement: {
    totalUsers: 10000,
    consentedUsers: 8500,
    consentRate: 85,
    withdrawalRate: 5,
    categoryBreakdown: [
      { category: 'Marketing', consentRate: 75 },
      { category: 'Analytics', consentRate: 90 }
    ]
  },
  impactAssessments: {
    total: 45,
    completed: 35,
    inProgress: 8,
    overdue: 2,
    averageRiskScore: 65,
    highRiskAssessments: 5
  }
};

const mockAlerts = [
  {
    id: '1',
    type: 'warning',
    title: 'Overdue Request',
    message: 'Data subject request #123 is overdue',
    timestamp: new Date().toISOString(),
    dismissed: false
  }
];

const mockActivities = [
  {
    id: '1',
    type: 'request_created',
    description: 'New data subject request created',
    timestamp: new Date().toISOString(),
    user: 'John Doe'
  }
];

const mockRequests = [
  {
    id: '1',
    email: '<EMAIL>',
    type: 'access',
    status: 'pending',
    priority: 'medium',
    description: 'Test request',
    createdAt: new Date().toISOString()
  }
];

describe('GDPRCommandCenter', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup service mocks
    vi.mocked(GDPRService.getDashboardMetrics).mockResolvedValue(mockMetrics);
    vi.mocked(GDPRService.getAlerts).mockResolvedValue(mockAlerts);
    vi.mocked(GDPRService.getActivities).mockResolvedValue(mockActivities);
    vi.mocked(DataSubjectRequestService.getAllRequests).mockResolvedValue(mockRequests);
  });

  describe('Component Rendering', () => {
    it('renders the main dashboard with all sections', async () => {
      render(<GDPRCommandCenter />);
      
      // Wait for data to load
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Check navigation tabs
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Data Subject Requests')).toBeInTheDocument();
      expect(screen.getByText('Branch Detection')).toBeInTheDocument();
      expect(screen.getByText('Consent Management')).toBeInTheDocument();
      expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
    });

    it('displays loading state initially', () => {
      render(<GDPRCommandCenter />);
      
      // Should show loading indicators
      expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
    });

    it('displays metrics cards with correct data', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('150')).toBeInTheDocument(); // Total requests
        expect(screen.getByText('25')).toBeInTheDocument(); // Pending requests
        expect(screen.getByText('83%')).toBeInTheDocument(); // Compliance score
      });
    });
  });

  describe('Navigation', () => {
    it('switches between different sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Click on Data Subject Requests tab
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      // Should show requests section
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // Click on Branch Detection tab
      const branchTab = screen.getByText('Branch Detection');
      await user.click(branchTab);
      
      // Should show branch detection section
      await waitFor(() => {
        expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      });
    });

    it('maintains active tab state', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const consentTab = screen.getByText('Consent Management');
      await user.click(consentTab);
      
      // Tab should be active (you might need to check for specific CSS classes)
      expect(consentTab.closest('button')).toHaveClass('bg-primary');
    });
  });

  describe('Data Loading and Error Handling', () => {
    it('handles service errors gracefully', async () => {
      vi.mocked(GDPRService.getDashboardMetrics).mockRejectedValue(new Error('Service error'));
      
      render(<GDPRCommandCenter />);
      
      // Should still render the component without crashing
      expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
    });

    it('refreshes data when refresh button is clicked', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Find and click refresh button
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      await user.click(refreshButton);
      
      // Services should be called again
      expect(GDPRService.getDashboardMetrics).toHaveBeenCalledTimes(2);
    });
  });

  describe('Alerts and Activities', () => {
    it('displays alerts when available', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('Overdue Request')).toBeInTheDocument();
      });
    });

    it('shows activities in the activity feed', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('New data subject request created')).toBeInTheDocument();
      });
    });

    it('allows dismissing alerts', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('Overdue Request')).toBeInTheDocument();
      });

      // Find and click dismiss button (assuming there's one)
      const dismissButtons = screen.getAllByRole('button');
      const dismissButton = dismissButtons.find(btn => 
        btn.getAttribute('aria-label')?.includes('dismiss') || 
        btn.textContent?.includes('×')
      );
      
      if (dismissButton) {
        await user.click(dismissButton);
        // Alert should be removed or marked as dismissed
      }
    });
  });

  describe('Theme Support', () => {
    it('renders correctly in dark mode', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Component should render without errors in dark mode
      // (Theme context will handle the mode switching)
    });

    it('renders correctly in light mode', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Component should render without errors in light mode
    });
  });

  describe('Accessibility', () => {
    it('has proper ARIA labels and roles', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Check for proper button roles
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);

      // Check for proper navigation structure
      const navigation = screen.getByRole('navigation', { hidden: true });
      if (navigation) {
        expect(navigation).toBeInTheDocument();
      }
    });

    it('supports keyboard navigation', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Tab through navigation elements
      const overviewTab = screen.getByText('Overview');
      overviewTab.focus();
      expect(document.activeElement).toBe(overviewTab);
    });
  });
});
