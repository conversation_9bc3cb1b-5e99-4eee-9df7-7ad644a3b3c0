/**
 * Comprehensive Notification Service
 * Handles all notification routing, delivery, and user targeting
 */

export interface NotificationUser {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'compliance_officer' | 'security_officer' | 'data_protection_officer' | 'manager' | 'analyst';
  department: string;
  preferences: {
    inApp: boolean;
    email: boolean;
    sms: boolean;
    push: boolean;
  };
  isActive: boolean;
}

export interface Notification {
  id: string;
  type: 'compliance_violation' | 'gdpr_request' | 'security_alert' | 'risk_assessment' | 'policy_update' | 'system_alert' | 'deadline_reminder';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  details?: any;
  targetUsers: string[];
  targetRoles: string[];
  targetDepartments: string[];
  createdAt: Date;
  scheduledFor?: Date;
  deliveredAt?: Date;
  readBy: string[];
  actionRequired: boolean;
  actionUrl?: string;
  expiresAt?: Date;
  metadata: {
    source: string;
    category: string;
    tags: string[];
  };
}

export interface NotificationRule {
  id: string;
  name: string;
  description: string;
  trigger: {
    type: string;
    conditions: any;
  };
  targets: {
    roles: string[];
    departments: string[];
    specificUsers: string[];
  };
  template: {
    title: string;
    message: string;
  };
  deliveryMethods: ('inApp' | 'email' | 'sms' | 'push')[];
  isActive: boolean;
  priority: number;
}

class NotificationService {
  private static instance: NotificationService;
  private notifications: Notification[] = [];
  private users: NotificationUser[] = [];
  private rules: NotificationRule[] = [];
  private listeners: ((notification: Notification) => void)[] = [];

  private constructor() {
    this.initializeUsers();
    this.initializeRules();
    this.generateSampleNotifications();
  }

  public static getInstance(): NotificationService {
    if (!NotificationService.instance) {
      NotificationService.instance = new NotificationService();
    }
    return NotificationService.instance;
  }

  private initializeUsers(): void {
    this.users = [
      {
        id: 'user-1',
        name: 'John Doe',
        email: '<EMAIL>',
        role: 'admin',
        department: 'IT',
        preferences: { inApp: true, email: true, sms: false, push: true },
        isActive: true
      },
      {
        id: 'user-2',
        name: 'Sarah Johnson',
        email: '<EMAIL>',
        role: 'compliance_officer',
        department: 'Legal',
        preferences: { inApp: true, email: true, sms: true, push: true },
        isActive: true
      },
      {
        id: 'user-3',
        name: 'Michael Chen',
        email: '<EMAIL>',
        role: 'security_officer',
        department: 'Security',
        preferences: { inApp: true, email: true, sms: true, push: true },
        isActive: true
      },
      {
        id: 'user-4',
        name: 'Emma Wilson',
        email: '<EMAIL>',
        role: 'data_protection_officer',
        department: 'Legal',
        preferences: { inApp: true, email: true, sms: false, push: true },
        isActive: true
      },
      {
        id: 'user-5',
        name: 'David Rodriguez',
        email: '<EMAIL>',
        role: 'manager',
        department: 'Operations',
        preferences: { inApp: true, email: true, sms: false, push: false },
        isActive: true
      },
      {
        id: 'user-6',
        name: 'Lisa Thompson',
        email: '<EMAIL>',
        role: 'analyst',
        department: 'Analytics',
        preferences: { inApp: true, email: false, sms: false, push: true },
        isActive: true
      }
    ];
  }

  private initializeRules(): void {
    this.rules = [
      {
        id: 'rule-1',
        name: 'GDPR Request Alert',
        description: 'Notify compliance officers of new GDPR requests',
        trigger: { type: 'gdpr_request_created', conditions: {} },
        targets: { roles: ['compliance_officer', 'data_protection_officer'], departments: [], specificUsers: [] },
        template: {
          title: 'New GDPR Request Received',
          message: 'A new GDPR request has been submitted and requires attention.'
        },
        deliveryMethods: ['inApp', 'email'],
        isActive: true,
        priority: 1
      },
      {
        id: 'rule-2',
        name: 'Critical Security Alert',
        description: 'Notify security team of critical security issues',
        trigger: { type: 'security_violation', conditions: { severity: 'critical' } },
        targets: { roles: ['security_officer', 'admin'], departments: ['Security'], specificUsers: [] },
        template: {
          title: 'Critical Security Alert',
          message: 'A critical security violation has been detected and requires immediate attention.'
        },
        deliveryMethods: ['inApp', 'email', 'sms', 'push'],
        isActive: true,
        priority: 1
      },
      {
        id: 'rule-3',
        name: 'Compliance Violation',
        description: 'Notify compliance team of policy violations',
        trigger: { type: 'compliance_violation', conditions: {} },
        targets: { roles: ['compliance_officer'], departments: ['Legal'], specificUsers: [] },
        template: {
          title: 'Compliance Violation Detected',
          message: 'A compliance violation has been identified and needs review.'
        },
        deliveryMethods: ['inApp', 'email'],
        isActive: true,
        priority: 2
      },
      {
        id: 'rule-4',
        name: 'Risk Assessment Due',
        description: 'Remind users of upcoming risk assessment deadlines',
        trigger: { type: 'risk_assessment_due', conditions: { daysUntilDue: 7 } },
        targets: { roles: ['manager', 'analyst'], departments: [], specificUsers: [] },
        template: {
          title: 'Risk Assessment Due Soon',
          message: 'You have a risk assessment that is due within 7 days.'
        },
        deliveryMethods: ['inApp', 'email'],
        isActive: true,
        priority: 3
      }
    ];
  }

  private generateSampleNotifications(): void {
    const now = new Date();
    const sampleNotifications: Notification[] = [
      {
        id: 'notif-1',
        type: 'gdpr_request',
        severity: 'high',
        title: 'New GDPR Data Access Request',
        message: 'A new data access request has been <NAME_EMAIL> and requires processing within 30 days.',
        targetUsers: ['user-2', 'user-4'],
        targetRoles: ['compliance_officer', 'data_protection_officer'],
        targetDepartments: ['Legal'],
        createdAt: new Date(now.getTime() - 2 * 60 * 60 * 1000), // 2 hours ago
        readBy: [],
        actionRequired: true,
        actionUrl: '/gdpr-analysis',
        expiresAt: new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000), // 30 days
        metadata: {
          source: 'GDPR_SYSTEM',
          category: 'data_request',
          tags: ['gdpr', 'data_access', 'urgent']
        }
      },
      {
        id: 'notif-2',
        type: 'security_alert',
        severity: 'critical',
        title: 'Critical Security Breach Detected',
        message: 'Unauthorized access attempt detected from IP *************. Immediate investigation required.',
        targetUsers: ['user-1', 'user-3'],
        targetRoles: ['admin', 'security_officer'],
        targetDepartments: ['Security', 'IT'],
        createdAt: new Date(now.getTime() - 30 * 60 * 1000), // 30 minutes ago
        readBy: ['user-1'],
        actionRequired: true,
        actionUrl: '/enterprise',
        metadata: {
          source: 'SECURITY_MONITOR',
          category: 'security_breach',
          tags: ['security', 'breach', 'critical', 'investigation']
        }
      },
      {
        id: 'notif-3',
        type: 'compliance_violation',
        severity: 'medium',
        title: 'Data Retention Policy Violation',
        message: 'Data retention policy violation detected in the customer database. 15 records exceed retention period.',
        targetUsers: ['user-2'],
        targetRoles: ['compliance_officer'],
        targetDepartments: ['Legal'],
        createdAt: new Date(now.getTime() - 4 * 60 * 60 * 1000), // 4 hours ago
        readBy: [],
        actionRequired: true,
        actionUrl: '/complianceRules',
        metadata: {
          source: 'COMPLIANCE_MONITOR',
          category: 'policy_violation',
          tags: ['compliance', 'data_retention', 'policy']
        }
      }
    ];

    this.notifications = sampleNotifications;
  }

  // Public methods for notification management
  public createNotification(notification: Omit<Notification, 'id' | 'createdAt' | 'readBy'>): Notification {
    const newNotification: Notification = {
      ...notification,
      id: `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date(),
      readBy: []
    };

    this.notifications.unshift(newNotification);
    this.notifyListeners(newNotification);
    return newNotification;
  }

  public getNotifications(userId?: string, unreadOnly: boolean = false): Notification[] {
    let filtered = this.notifications;

    if (userId) {
      const user = this.users.find(u => u.id === userId);
      if (user) {
        filtered = this.notifications.filter(n => 
          n.targetUsers.includes(userId) || 
          n.targetRoles.includes(user.role) || 
          n.targetDepartments.includes(user.department)
        );
      }
    }

    if (unreadOnly && userId) {
      filtered = filtered.filter(n => !n.readBy.includes(userId));
    }

    return filtered.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  public markAsRead(notificationId: string, userId: string): void {
    const notification = this.notifications.find(n => n.id === notificationId);
    if (notification && !notification.readBy.includes(userId)) {
      notification.readBy.push(userId);
    }
  }

  public getUnreadCount(userId: string): number {
    return this.getNotifications(userId, true).length;
  }

  public subscribeToNotifications(callback: (notification: Notification) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  private notifyListeners(notification: Notification): void {
    this.listeners.forEach(callback => callback(notification));
  }

  public getUsers(): NotificationUser[] {
    return this.users;
  }

  public getRules(): NotificationRule[] {
    return this.rules;
  }

  // Simulate sending notifications based on events
  public triggerNotification(eventType: string, data: any): void {
    const applicableRules = this.rules.filter(rule => 
      rule.isActive && rule.trigger.type === eventType
    );

    applicableRules.forEach(rule => {
      const targetUsers = this.resolveTargetUsers(rule.targets);
      
      this.createNotification({
        type: this.mapEventTypeToNotificationType(eventType),
        severity: this.determineSeverity(eventType, data),
        title: this.processTemplate(rule.template.title, data),
        message: this.processTemplate(rule.template.message, data),
        details: data,
        targetUsers: targetUsers.map(u => u.id),
        targetRoles: rule.targets.roles,
        targetDepartments: rule.targets.departments,
        actionRequired: this.requiresAction(eventType),
        actionUrl: this.getActionUrl(eventType),
        metadata: {
          source: 'NOTIFICATION_SYSTEM',
          category: eventType,
          tags: [eventType, ...rule.targets.roles]
        }
      });
    });
  }

  private resolveTargetUsers(targets: { roles: string[]; departments: string[]; specificUsers: string[] }): NotificationUser[] {
    const users = new Set<NotificationUser>();

    // Add specific users
    targets.specificUsers.forEach(userId => {
      const user = this.users.find(u => u.id === userId && u.isActive);
      if (user) users.add(user);
    });

    // Add users by role
    targets.roles.forEach(role => {
      this.users.filter(u => u.role === role && u.isActive).forEach(user => users.add(user));
    });

    // Add users by department
    targets.departments.forEach(dept => {
      this.users.filter(u => u.department === dept && u.isActive).forEach(user => users.add(user));
    });

    return Array.from(users);
  }

  private mapEventTypeToNotificationType(eventType: string): Notification['type'] {
    const mapping: Record<string, Notification['type']> = {
      'gdpr_request_created': 'gdpr_request',
      'security_violation': 'security_alert',
      'compliance_violation': 'compliance_violation',
      'risk_assessment_due': 'risk_assessment',
      'policy_updated': 'policy_update',
      'system_error': 'system_alert',
      'deadline_approaching': 'deadline_reminder'
    };
    return mapping[eventType] || 'system_alert';
  }

  private determineSeverity(eventType: string, data: any): Notification['severity'] {
    if (data?.severity) return data.severity;
    
    const severityMap: Record<string, Notification['severity']> = {
      'security_violation': 'critical',
      'gdpr_request_created': 'high',
      'compliance_violation': 'medium',
      'risk_assessment_due': 'medium',
      'policy_updated': 'low',
      'system_error': 'high',
      'deadline_approaching': 'medium'
    };
    
    return severityMap[eventType] || 'medium';
  }

  private processTemplate(template: string, data: any): string {
    let processed = template;
    Object.keys(data || {}).forEach(key => {
      processed = processed.replace(new RegExp(`{{${key}}}`, 'g'), data[key]);
    });
    return processed;
  }

  private requiresAction(eventType: string): boolean {
    const actionRequired = [
      'gdpr_request_created',
      'security_violation',
      'compliance_violation',
      'risk_assessment_due'
    ];
    return actionRequired.includes(eventType);
  }

  private getActionUrl(eventType: string): string | undefined {
    const urlMap: Record<string, string> = {
      'gdpr_request_created': '/gdpr-analysis',
      'security_violation': '/enterprise',
      'compliance_violation': '/complianceRules',
      'risk_assessment_due': '/enterprise',
      'policy_updated': '/configurations'
    };
    return urlMap[eventType];
  }
}

export const notificationService = NotificationService.getInstance();
