import { 
  SecurityThreat, 
  SecurityIncident, 
  SecurityMetric, 
  SecuritySystem, 
  SecurityAlert, 
  SecurityOverviewData 
} from '../types/SecurityTypes';

// Generate comprehensive security data
export const generateSecurityOverviewData = (): SecurityOverviewData => {
  const threats = generateThreats();
  const incidents = generateIncidents();
  const metrics = generateSecurityMetrics();
  const systems = generateSystems();
  const alerts = generateAlerts();

  return {
    overview: {
      securityScore: Math.floor(Math.random() * 20) + 80,
      activeThreats: threats.filter(t => t.status !== 'resolved').length,
      systemsMonitored: systems.length,
      alertsLast24h: alerts.filter(a => 
        new Date().getTime() - a.triggeredAt.getTime() < 24 * 60 * 60 * 1000
      ).length,
      incidentsThisMonth: incidents.filter(i => 
        new Date().getMonth() === i.reportedAt.getMonth()
      ).length,
      vulnerabilitiesFound: systems.reduce((acc, s) => 
        acc + s.vulnerabilities.critical + s.vulnerabilities.high + 
        s.vulnerabilities.medium + s.vulnerabilities.low, 0
      ),
      complianceScore: Math.floor(Math.random() * 15) + 85,
      lastUpdated: new Date()
    },
    threats,
    incidents,
    metrics,
    systems,
    alerts,
    realTimeUpdates: {
      lastSync: new Date(),
      isLive: true,
      pendingAlerts: Math.floor(Math.random() * 10),
      systemStatus: ['operational', 'degraded', 'outage'][Math.floor(Math.random() * 3)] as any
    }
  };
};

const generateThreats = (): SecurityThreat[] => {
  const threatTitles = [
    'Malware Detection - Endpoint ************',
    'Suspicious Login Activity - Admin Account',
    'DDoS Attack Attempt - Web Server',
    'Phishing Email Campaign Detected',
    'Unauthorized Access Attempt - Database',
    'Ransomware Signature Detected',
    'Data Exfiltration Attempt',
    'Insider Threat Activity',
    'SQL Injection Attack',
    'Brute Force Login Attempts'
  ];

  return threatTitles.map((title, index) => ({
    id: `threat-${index + 1}`,
    title,
    description: `Detailed description for ${title}`,
    severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    status: ['detected', 'investigating', 'contained', 'resolved'][Math.floor(Math.random() * 4)] as any,
    type: ['malware', 'phishing', 'ddos', 'unauthorized_access', 'data_breach', 'insider_threat'][Math.floor(Math.random() * 6)] as any,
    source: `Source ${index + 1}`,
    targetSystems: [`system-${Math.floor(Math.random() * 5) + 1}`],
    detectedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    resolvedAt: Math.random() > 0.4 ? new Date() : undefined,
    assignedTo: `Security Analyst ${index + 1}`,
    impact: {
      systems: Math.floor(Math.random() * 10) + 1,
      users: Math.floor(Math.random() * 1000) + 100,
      dataRecords: Math.floor(Math.random() * 10000) + 1000,
      estimatedCost: Math.floor(Math.random() * 100000) + 10000
    },
    mitigationSteps: generateMitigationSteps(),
    evidence: [`evidence-${index + 1}.log`],
    relatedIncidents: [],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateMitigationSteps = () => {
  const steps = [
    'Isolate affected systems',
    'Update security signatures',
    'Patch vulnerable systems',
    'Reset compromised credentials',
    'Monitor network traffic'
  ];

  return steps.slice(0, Math.floor(Math.random() * 3) + 2).map((description, index) => ({
    id: `step-${index + 1}`,
    description,
    status: ['pending', 'in_progress', 'completed'][Math.floor(Math.random() * 3)] as any,
    assignedTo: `Analyst ${index + 1}`,
    dueDate: new Date(Date.now() + Math.random() * 7 * 24 * 60 * 60 * 1000)
  }));
};

const generateIncidents = (): SecurityIncident[] => {
  const incidentTitles = [
    'Data Breach - Customer Database',
    'Unauthorized Access - Admin Panel',
    'Malware Infection - HR Department',
    'Phishing Attack - Finance Team',
    'System Compromise - Web Server',
    'Policy Violation - Data Transfer',
    'Insider Threat - Privileged User',
    'Network Intrusion Attempt',
    'Ransomware Attack - File Server',
    'Social Engineering Attack'
  ];

  return incidentTitles.map((title, index) => ({
    id: `incident-${index + 1}`,
    title,
    description: `Detailed incident report for ${title}`,
    severity: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
    status: ['open', 'investigating', 'contained', 'resolved', 'closed'][Math.floor(Math.random() * 5)] as any,
    category: ['security_breach', 'policy_violation', 'system_compromise', 'data_loss', 'unauthorized_access'][Math.floor(Math.random() * 5)] as any,
    reportedBy: `Reporter ${index + 1}`,
    assignedTo: `Incident Handler ${index + 1}`,
    affectedSystems: [`system-${Math.floor(Math.random() * 5) + 1}`],
    affectedUsers: Math.floor(Math.random() * 500) + 50,
    dataCompromised: Math.random() > 0.6,
    reportedAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    acknowledgedAt: Math.random() > 0.3 ? new Date() : undefined,
    resolvedAt: Math.random() > 0.5 ? new Date() : undefined,
    timeline: generateIncidentTimeline(),
    attachments: [`incident-${index + 1}-report.pdf`],
    lessons: [`Lesson learned from ${title}`],
    createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateIncidentTimeline = () => {
  const actions = [
    'Incident reported',
    'Initial assessment completed',
    'Containment measures implemented',
    'Investigation started',
    'Resolution implemented'
  ];

  return actions.slice(0, Math.floor(Math.random() * 3) + 2).map((action, index) => ({
    timestamp: new Date(Date.now() - (actions.length - index) * 60 * 60 * 1000),
    action,
    performedBy: `Handler ${index + 1}`,
    notes: Math.random() > 0.7 ? `Additional notes for ${action}` : undefined
  }));
};

const generateSecurityMetrics = (): SecurityMetric[] => {
  const metricNames = [
    'Mean Time to Detection',
    'Mean Time to Response',
    'Security Incident Count',
    'Vulnerability Remediation Rate',
    'Security Training Completion',
    'Patch Compliance Rate',
    'Threat Intelligence Accuracy',
    'Security Tool Effectiveness',
    'Compliance Score',
    'Risk Assessment Coverage'
  ];

  return metricNames.map((name, index) => ({
    id: `security-metric-${index + 1}`,
    name,
    category: ['threat_detection', 'incident_response', 'vulnerability_management', 'compliance', 'awareness'][Math.floor(Math.random() * 5)] as any,
    currentValue: Math.floor(Math.random() * 100) + 1,
    targetValue: Math.floor(Math.random() * 20) + 80,
    unit: ['%', 'hours', 'count', 'score', 'days'][Math.floor(Math.random() * 5)],
    trend: ['up', 'down', 'stable'][Math.floor(Math.random() * 3)] as any,
    status: ['good', 'warning', 'critical'][Math.floor(Math.random() * 3)] as any,
    lastUpdated: new Date(),
    historicalData: generateHistoricalData(),
    drillDownData: {
      bySystem: [
        { name: 'Web Server', value: Math.floor(Math.random() * 100), status: 'good' },
        { name: 'Database', value: Math.floor(Math.random() * 100), status: 'warning' }
      ],
      byThreatType: [
        { type: 'Malware', count: Math.floor(Math.random() * 50), severity: 'high' },
        { type: 'Phishing', count: Math.floor(Math.random() * 30), severity: 'medium' }
      ],
      timeBreakdown: [
        { period: 'Q1', value: Math.floor(Math.random() * 100), change: Math.floor(Math.random() * 20) - 10 }
      ]
    }
  }));
};

const generateHistoricalData = () => {
  return Array.from({ length: 12 }, (_, index) => ({
    date: new Date(Date.now() - (11 - index) * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    value: Math.floor(Math.random() * 100) + 1,
    status: ['good', 'warning', 'critical'][Math.floor(Math.random() * 3)],
    notes: Math.random() > 0.7 ? `Note for month ${index + 1}` : undefined
  }));
};

const generateSystems = (): SecuritySystem[] => {
  const systemNames = [
    'Web Server - Production',
    'Database Server - Primary',
    'Email Server',
    'File Server',
    'Domain Controller',
    'Firewall - Perimeter',
    'VPN Gateway',
    'Backup Server',
    'Application Server',
    'Load Balancer'
  ];

  return systemNames.map((name, index) => ({
    id: `system-${index + 1}`,
    name,
    type: ['endpoint', 'network', 'server', 'database', 'application', 'cloud'][Math.floor(Math.random() * 6)] as any,
    status: ['secure', 'warning', 'compromised', 'offline'][Math.floor(Math.random() * 4)] as any,
    lastScan: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
    vulnerabilities: {
      critical: Math.floor(Math.random() * 5),
      high: Math.floor(Math.random() * 10),
      medium: Math.floor(Math.random() * 20),
      low: Math.floor(Math.random() * 50)
    },
    securityScore: Math.floor(Math.random() * 30) + 70,
    location: `Location ${index + 1}`,
    owner: `Owner ${index + 1}`,
    compliance: {
      framework: ['ISO 27001', 'SOC 2', 'PCI DSS', 'NIST'][Math.floor(Math.random() * 4)],
      status: ['compliant', 'non_compliant', 'partial'][Math.floor(Math.random() * 3)] as any,
      lastAssessment: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000)
    },
    monitoring: {
      isActive: Math.random() > 0.1,
      lastHeartbeat: new Date(Date.now() - Math.random() * 60 * 60 * 1000),
      alertsEnabled: Math.random() > 0.2
    },
    createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};

const generateAlerts = (): SecurityAlert[] => {
  const alertTitles = [
    'Suspicious Network Activity Detected',
    'Failed Login Attempts Exceeded Threshold',
    'Malware Signature Match',
    'Unusual Data Transfer Pattern',
    'Unauthorized Service Access',
    'System Resource Anomaly',
    'Security Policy Violation',
    'Intrusion Detection Alert',
    'Vulnerability Scan Alert',
    'Compliance Check Failed'
  ];

  return alertTitles.map((title, index) => ({
    id: `alert-${index + 1}`,
    title,
    description: `Alert details for ${title}`,
    severity: ['info', 'low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 5)] as any,
    status: ['new', 'acknowledged', 'investigating', 'resolved', 'false_positive'][Math.floor(Math.random() * 5)] as any,
    source: `Security Tool ${index + 1}`,
    category: ['intrusion_detection', 'malware', 'anomaly', 'policy_violation', 'system_failure'][Math.floor(Math.random() * 5)] as any,
    affectedSystems: [`system-${Math.floor(Math.random() * 5) + 1}`],
    triggeredAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    acknowledgedAt: Math.random() > 0.5 ? new Date() : undefined,
    resolvedAt: Math.random() > 0.7 ? new Date() : undefined,
    assignedTo: Math.random() > 0.4 ? `Analyst ${index + 1}` : undefined,
    actions: [
      {
        id: `action-${index + 1}`,
        type: ['block', 'quarantine', 'notify', 'escalate'][Math.floor(Math.random() * 4)] as any,
        status: ['pending', 'completed', 'failed'][Math.floor(Math.random() * 3)] as any,
        timestamp: new Date()
      }
    ],
    metadata: {
      sourceIP: `192.168.1.${Math.floor(Math.random() * 255)}`,
      targetPort: Math.floor(Math.random() * 65535),
      protocol: ['TCP', 'UDP', 'HTTP', 'HTTPS'][Math.floor(Math.random() * 4)]
    },
    createdAt: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
    updatedAt: new Date()
  }));
};
