import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import EnhancedComplianceMetrics from '../../components/compliance/EnhancedComplianceMetricsOptimized';
import SecurityOverviewDashboard from '../../components/Security/SecurityOverviewDashboardOptimized';

// Mock data services
vi.mock('../../components/compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: () => ({
    overview: {
      totalMetrics: 25,
      compliantMetrics: 20,
      warningMetrics: 3,
      criticalMetrics: 2,
      overallScore: 85,
      trendDirection: 'up',
      lastAssessment: new Date(),
      nextAssessment: new Date()
    },
    metrics: [
      {
        id: 'metric-1',
        name: 'Data Retention Policy',
        category: 'privacy',
        status: 'compliant',
        currentValue: 95,
        targetValue: 90,
        trend: 'up',
        lastUpdated: new Date()
      }
    ],
    frameworks: [
      {
        type: 'gdpr',
        name: 'GDPR',
        overallScore: 94.2,
        departments: [
          {
            id: 'dept-1',
            name: 'Human Resources',
            description: 'HR Department',
            complianceScore: 92,
            riskLevel: 'low',
            dataSubjectsCount: 150,
            violations: 0,
            trend: 'up',
            lastAuditDate: new Date(),
            nextAuditDate: new Date(),
            responsibleOfficer: 'John Doe',
            processingActivities: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        policies: [
          {
            id: 'policy-1',
            title: 'GDPR Data Protection Policy',
            description: 'Comprehensive data protection policy',
            category: 'data_protection',
            framework: 'gdpr',
            status: 'active',
            version: '2.1',
            effectiveDate: new Date(),
            reviewDate: new Date(),
            owner: 'Data Protection Officer',
            assignedDepartments: ['dept-1'],
            adherenceRate: 94.2,
            violations: [],
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        timeline: [],
        metrics: {
          totalDepartments: 1,
          compliantDepartments: 1,
          totalPolicies: 1,
          activePolicies: 1,
          totalViolations: 0
        }
      }
    ]
  })
}));

vi.mock('../../components/Security/services/SecurityDataService', () => ({
  generateSecurityOverviewData: () => ({
    overview: {
      activeThreats: 5,
      resolvedIncidents: 12,
      systemsMonitored: 25,
      alertsLast24h: 8
    },
    threats: [
      {
        id: 'threat-1',
        name: 'SQL Injection Attempt',
        type: 'web_attack',
        severity: 'high',
        status: 'investigating',
        detectedAt: new Date(),
        source: 'Web Application Firewall',
        targetAsset: 'Customer Database',
        riskScore: 85,
        assignedTo: 'Security Team'
      }
    ],
    incidents: [
      {
        id: 'incident-1',
        title: 'Data Breach Investigation',
        description: 'Potential unauthorized access detected',
        severity: 'critical',
        status: 'investigating',
        reportedAt: new Date(),
        category: 'data_breach',
        affectedAssets: ['Database Server']
      }
    ],
    systems: [
      {
        id: 'system-1',
        name: 'Production Database',
        type: 'database',
        status: 'secure',
        securityScore: 95,
        monitoring: { isActive: true }
      }
    ],
    alerts: [
      {
        id: 'alert-1',
        title: 'Unusual Login Activity',
        description: 'Multiple failed login attempts detected',
        severity: 'medium',
        status: 'new',
        triggeredAt: new Date(),
        source: 'Authentication System'
      }
    ],
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30000,
      pendingAlerts: 3
    }
  })
}));

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true
});

describe('Dashboard Enhancements Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    mockConfirm.mockReturnValue(true);
  });

  describe('Enhanced Compliance Metrics Complete Workflow', () => {
    it('completes full visibility control workflow', async () => {
      render(<EnhancedComplianceMetrics />);
      
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      });

      // Test departments visibility workflow
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });
      
      // Hide departments
      const deptEyeButton = screen.getByTitle('Hide departments');
      await user.click(deptEyeButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Human Resources')).not.toBeInTheDocument();
      });
      
      // Show departments again
      const showDeptButton = screen.getByTitle('Show departments');
      await user.click(showDeptButton);
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });

      // Test policies visibility workflow
      const policiesTab = screen.getByText('Policies');
      await user.click(policiesTab);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Data Protection Policy')).toBeInTheDocument();
      });
      
      // Hide policies
      const policyEyeButton = screen.getByTitle('Hide policies');
      await user.click(policyEyeButton);
      
      await waitFor(() => {
        expect(screen.queryByText('GDPR Data Protection Policy')).not.toBeInTheDocument();
      });

      // Test metrics visibility workflow
      const metricsTab = screen.getByText('Metrics');
      await user.click(metricsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Retention Policy')).toBeInTheDocument();
      });
      
      // Hide metrics
      const metricsEyeButton = screen.getByTitle('Hide metrics');
      await user.click(metricsEyeButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Data Retention Policy')).not.toBeInTheDocument();
      });
    });

    it('maintains state consistency across tab switches', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Hide departments
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      const deptEyeButton = screen.getByTitle('Hide departments');
      await user.click(deptEyeButton);
      
      // Hide policies
      const policiesTab = screen.getByText('Policies');
      await user.click(policiesTab);
      
      const policyEyeButton = screen.getByTitle('Hide policies');
      await user.click(policyEyeButton);
      
      // Switch back to departments - should still be hidden
      await user.click(departmentsTab);
      
      await waitFor(() => {
        expect(screen.queryByText('Human Resources')).not.toBeInTheDocument();
      });
      
      // Switch back to policies - should still be hidden
      await user.click(policiesTab);
      
      await waitFor(() => {
        expect(screen.queryByText('GDPR Data Protection Policy')).not.toBeInTheDocument();
      });
    });
  });

  describe('Security Overview Dashboard Complete Workflow', () => {
    it('completes full delete workflow for all entity types', async () => {
      render(<SecurityOverviewDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
      });

      // Test threat deletion
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
      });
      
      const threatDeleteButton = screen.getByTitle('Delete Threat');
      await user.click(threatDeleteButton);
      
      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this threat? This action cannot be undone.'
      );
      
      await waitFor(() => {
        expect(screen.queryByText('SQL Injection Attempt')).not.toBeInTheDocument();
      });

      // Test incident deletion
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Breach Investigation')).toBeInTheDocument();
      });
      
      const incidentDeleteButton = screen.getByTitle('Delete Incident');
      await user.click(incidentDeleteButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Data Breach Investigation')).not.toBeInTheDocument();
      });

      // Test system deletion
      const systemsTab = screen.getByText('Systems');
      await user.click(systemsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Production Database')).toBeInTheDocument();
      });
      
      const systemDeleteButton = screen.getByTitle('Delete System');
      await user.click(systemDeleteButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Production Database')).not.toBeInTheDocument();
      });

      // Test alert deletion
      const alertsTab = screen.getByText('Alerts');
      await user.click(alertsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Unusual Login Activity')).toBeInTheDocument();
      });
      
      const alertDeleteButton = screen.getByTitle('Delete Alert');
      await user.click(alertDeleteButton);
      
      await waitFor(() => {
        expect(screen.queryByText('Unusual Login Activity')).not.toBeInTheDocument();
      });
    });

    it('handles cancellation of delete operations', async () => {
      mockConfirm.mockReturnValue(false); // User cancels deletion
      
      render(<SecurityOverviewDashboard />);
      
      // Test threat deletion cancellation
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
      });
      
      const threatDeleteButton = screen.getByTitle('Delete Threat');
      await user.click(threatDeleteButton);
      
      // Threat should still be present
      expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
    });
  });

  describe('Error Boundary Integration', () => {
    it('handles errors gracefully in Enhanced Compliance Metrics', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<EnhancedComplianceMetrics />);
      
      // Perform multiple operations that could potentially cause errors
      const departmentsTab = screen.getByText('Departments');
      await user.click(departmentsTab);
      
      const eyeButton = screen.getByTitle('Hide departments');
      await user.click(eyeButton);
      await user.click(screen.getByTitle('Show departments'));
      
      // Switch tabs rapidly
      const policiesTab = screen.getByText('Policies');
      await user.click(policiesTab);
      await user.click(departmentsTab);
      await user.click(policiesTab);
      
      // No console errors should occur
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('handles errors gracefully in Security Overview Dashboard', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<SecurityOverviewDashboard />);
      
      // Perform multiple delete operations
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      const deleteButton = screen.getByTitle('Delete Threat');
      await user.click(deleteButton);
      
      // Switch tabs rapidly
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      await user.click(threatsTab);
      
      // No console errors should occur
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Responsiveness', () => {
    it('handles rapid user interactions without lag', async () => {
      render(<EnhancedComplianceMetrics />);
      
      // Rapid tab switching and visibility toggling
      const departmentsTab = screen.getByText('Departments');
      const policiesTab = screen.getByText('Policies');
      const metricsTab = screen.getByText('Metrics');
      
      await user.click(departmentsTab);
      await user.click(policiesTab);
      await user.click(metricsTab);
      await user.click(departmentsTab);
      
      const eyeButton = screen.getByTitle('Hide departments');
      await user.click(eyeButton);
      await user.click(screen.getByTitle('Show departments'));
      
      // Component should remain responsive
      expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
    });

    it('handles multiple delete operations efficiently', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Rapid navigation and delete attempts
      const threatsTab = screen.getByText('Threats');
      const incidentsTab = screen.getByText('Incidents');
      
      await user.click(threatsTab);
      await user.click(incidentsTab);
      await user.click(threatsTab);
      
      // Component should remain responsive
      expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
    });
  });
});
