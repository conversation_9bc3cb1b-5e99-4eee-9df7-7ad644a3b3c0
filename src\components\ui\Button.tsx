import React from 'react';
import { useTheme } from '../../context/ThemeContext';

interface ButtonProps {
  children?: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  loadingText?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  className?: string;
}

interface IconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  type?: 'button' | 'submit' | 'reset';
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  ariaLabel: string;
}

export const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  size = 'md',
  disabled = false,
  loading = false,
  loadingText,
  leftIcon,
  rightIcon,
  className = ''
}) => {
  const { mode } = useTheme();

  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  };

  const variantClasses = {
    primary: `bg-primary text-white hover:bg-primary-hover focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    secondary: `bg-secondary text-white hover:bg-secondary-hover focus:ring-secondary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    outline: `border border-border text-text hover:bg-surface focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    ghost: `text-text hover:bg-surface focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    danger: `bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`
  };

  const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={classes}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )}
      {!loading && leftIcon && <span className="mr-2">{leftIcon}</span>}
      {loading ? loadingText || 'Loading...' : children}
      {!loading && rightIcon && <span className="ml-2">{rightIcon}</span>}
    </button>
  );
};

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  onClick,
  type = 'button',
  variant = 'ghost',
  size = 'md',
  disabled = false,
  className = '',
  ariaLabel
}) => {
  const { mode } = useTheme();

  const baseClasses = 'inline-flex items-center justify-center rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
  
  const sizeClasses = {
    sm: 'p-1.5',
    md: 'p-2',
    lg: 'p-3'
  };

  const variantClasses = {
    primary: `bg-primary text-white hover:bg-primary-hover focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    secondary: `bg-secondary text-white hover:bg-secondary-hover focus:ring-secondary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    outline: `border border-border text-text hover:bg-surface focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    ghost: `text-text hover:bg-surface focus:ring-primary ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`,
    danger: `bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`
  };

  const classes = `${baseClasses} ${sizeClasses[size]} ${variantClasses[variant]} ${className}`;

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={classes}
      aria-label={ariaLabel}
    >
      {icon}
    </button>
  );
};
