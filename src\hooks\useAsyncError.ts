import { useState, useCallback, useEffect, useRef } from 'react';
import { 
  AsyncError, 
  AsyncOperationResult, 
  RetryConfig, 
  safeAsync, 
  withRetry,
  createAsyncError 
} from '../utils/asyncErrorHandler';

// Hook for managing async operations with error handling
export const useAsyncOperation = <T>(
  operation: () => Promise<T>,
  operationName: string,
  config: Partial<RetryConfig> = {}
) => {
  const [result, setResult] = useState<AsyncOperationResult<T>>({
    isLoading: false,
    retryCount: 0
  });

  const operationRef = useRef(operation);
  const configRef = useRef(config);

  // Update refs when dependencies change
  useEffect(() => {
    operationRef.current = operation;
    configRef.current = config;
  }, [operation, config]);

  const execute = useCallback(async () => {
    const asyncResult = await safeAsync(
      operationRef.current,
      operationName,
      configRef.current
    );
    setResult(asyncResult);
    return asyncResult;
  }, [operationName]);

  const retry = useCallback(async () => {
    setResult(prev => ({ ...prev, isLoading: true, error: undefined }));
    return execute();
  }, [execute]);

  const reset = useCallback(() => {
    setResult({
      isLoading: false,
      retryCount: 0
    });
  }, []);

  return {
    ...result,
    execute,
    retry,
    reset
  };
};

// Hook for handling multiple async operations
export const useAsyncOperations = () => {
  const [operations, setOperations] = useState<Map<string, AsyncOperationResult<any>>>(new Map());

  const executeOperation = useCallback(async <T>(
    key: string,
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<AsyncOperationResult<T>> => {
    // Set loading state
    setOperations(prev => new Map(prev.set(key, {
      isLoading: true,
      retryCount: 0
    })));

    const result = await safeAsync(operation, key, config);
    
    // Update with result
    setOperations(prev => new Map(prev.set(key, result)));
    
    return result;
  }, []);

  const retryOperation = useCallback(async <T>(
    key: string,
    operation: () => Promise<T>,
    config: Partial<RetryConfig> = {}
  ): Promise<AsyncOperationResult<T>> => {
    const currentOp = operations.get(key);
    if (currentOp) {
      setOperations(prev => new Map(prev.set(key, {
        ...currentOp,
        isLoading: true,
        error: undefined
      })));
    }

    return executeOperation(key, operation, config);
  }, [operations, executeOperation]);

  const getOperation = useCallback((key: string) => {
    return operations.get(key);
  }, [operations]);

  const clearOperation = useCallback((key: string) => {
    setOperations(prev => {
      const newMap = new Map(prev);
      newMap.delete(key);
      return newMap;
    });
  }, []);

  const clearAllOperations = useCallback(() => {
    setOperations(new Map());
  }, []);

  return {
    operations: Object.fromEntries(operations),
    executeOperation,
    retryOperation,
    getOperation,
    clearOperation,
    clearAllOperations
  };
};

// Hook for handling fetch operations specifically
export const useFetch = <T>(
  url: string | null,
  options: RequestInit = {},
  config: Partial<RetryConfig> = {}
) => {
  const [result, setResult] = useState<AsyncOperationResult<T>>({
    isLoading: false,
    retryCount: 0
  });

  const fetchData = useCallback(async (fetchUrl?: string, fetchOptions?: RequestInit) => {
    const targetUrl = fetchUrl || url;
    if (!targetUrl) {
      setResult({
        error: createAsyncError('No URL provided', 'fetch'),
        isLoading: false,
        retryCount: 0
      });
      return;
    }

    const operation = async (): Promise<T> => {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 10000);

      try {
        const response = await fetch(targetUrl, {
          ...options,
          ...fetchOptions,
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
          throw createAsyncError(
            `HTTP ${response.status}: ${response.statusText}`,
            `fetch ${targetUrl}`,
            { status: response.status }
          );
        }

        const data = await response.json();
        return data;
      } catch (error) {
        clearTimeout(timeoutId);
        
        if (error instanceof Error && error.name === 'AbortError') {
          throw createAsyncError(
            'Request timeout',
            `fetch ${targetUrl}`,
            { code: 'TIMEOUT' }
          );
        }
        
        throw error;
      }
    };

    const asyncResult = await safeAsync(operation, `fetch ${targetUrl}`, config);
    setResult(asyncResult);
    return asyncResult;
  }, [url, options, config]);

  const retry = useCallback(() => {
    return fetchData();
  }, [fetchData]);

  const reset = useCallback(() => {
    setResult({
      isLoading: false,
      retryCount: 0
    });
  }, []);

  // Auto-fetch on mount if URL is provided
  useEffect(() => {
    if (url) {
      fetchData();
    }
  }, [url, fetchData]);

  return {
    ...result,
    fetch: fetchData,
    retry,
    reset
  };
};

// Hook for error boundary integration
export const useErrorHandler = () => {
  const [error, setError] = useState<AsyncError | null>(null);

  const handleError = useCallback((error: AsyncError | Error, context?: string) => {
    const asyncError = error instanceof Error 
      ? createAsyncError(error.message, context || 'unknown')
      : error;

    setError(asyncError);
    
    // Log error
    console.error(`Error in ${context || 'unknown context'}:`, asyncError);
    
    // In production, report to error service
    if (process.env.NODE_ENV === 'production') {
      // errorReportingService.captureException(asyncError);
    }
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const throwError = useCallback((error: AsyncError | Error) => {
    // This will be caught by the nearest error boundary
    throw error;
  }, []);

  return {
    error,
    handleError,
    clearError,
    throwError
  };
};

// Hook for graceful degradation
export const useGracefulDegradation = <T>(
  primaryOperation: () => Promise<T>,
  fallbackOperation?: () => Promise<T> | T,
  operationName: string = 'operation'
) => {
  const [result, setResult] = useState<{
    data?: T;
    error?: AsyncError;
    isLoading: boolean;
    usingFallback: boolean;
  }>({
    isLoading: false,
    usingFallback: false
  });

  const execute = useCallback(async () => {
    setResult(prev => ({ ...prev, isLoading: true }));

    try {
      // Try primary operation first
      const data = await withRetry(primaryOperation, operationName);
      setResult({
        data,
        isLoading: false,
        usingFallback: false
      });
    } catch (primaryError) {
      // If primary fails and fallback is available, try fallback
      if (fallbackOperation) {
        try {
          const fallbackData = await fallbackOperation();
          setResult({
            data: fallbackData,
            isLoading: false,
            usingFallback: true
          });
        } catch (fallbackError) {
          // Both failed
          const asyncError = fallbackError instanceof Error 
            ? createAsyncError(fallbackError.message, `${operationName} (fallback)`)
            : fallbackError as AsyncError;
            
          setResult({
            error: asyncError,
            isLoading: false,
            usingFallback: false
          });
        }
      } else {
        // No fallback available
        const asyncError = primaryError instanceof Error 
          ? createAsyncError(primaryError.message, operationName)
          : primaryError as AsyncError;
          
        setResult({
          error: asyncError,
          isLoading: false,
          usingFallback: false
        });
      }
    }
  }, [primaryOperation, fallbackOperation, operationName]);

  const retry = useCallback(() => {
    return execute();
  }, [execute]);

  const reset = useCallback(() => {
    setResult({
      isLoading: false,
      usingFallback: false
    });
  }, []);

  return {
    ...result,
    execute,
    retry,
    reset
  };
};

export default {
  useAsyncOperation,
  useAsyncOperations,
  useFetch,
  useErrorHandler,
  useGracefulDegradation
};
