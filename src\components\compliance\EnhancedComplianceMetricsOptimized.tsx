import React, { useState, useMemo, useEffect, useCallback } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { ErrorBoundary } from '../ui/ErrorBoundary';
import { ChartErrorBoundary } from '../ui/ChartErrorBoundary';
import { Line, Bar, Doughnut, Radar } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  BarChart3, TrendingUp, TrendingDown, AlertTriangle, CheckCircle, XCircle,
  RefreshCw, Download, Settings, Filter, Calendar, Activity, Zap, Target,
  ChevronDown, ChevronRight, PieChart, LineChart, MousePointer,
  Layers, Clock, Users, Building, Plus, Edit, Trash2, Save, X,
  Calendar as CalendarIcon, FileText, AlertCircle, CheckCircle2, Eye, RotateCcw
} from 'lucide-react';

// Import types and services
import { 
  EnhancedComplianceMetricsProps, 
  EnhancedComplianceData, 
  Department, 
  CompliancePolicy, 
  TimelineEvent,
  ComplianceMetric 
} from './types/ComplianceTypes';
import { generateEnhancedComplianceData } from './services/ComplianceDataService';

// Import UI components
import { FormInput, FormSelect } from '../ui/FormInput';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';

// Icon fallback component for error prevention
const IconFallback: React.FC<{ className?: string }> = ({ className = "w-4 h-4" }) => (
  <div className={`${className} bg-gray-300 rounded`} />
);

// Safe icon wrapper to prevent crashes if icons fail to load
const SafeIcon: React.FC<{
  icon: React.ComponentType<any>;
  className?: string;
  fallback?: React.ReactNode;
  [key: string]: any;
}> = ({ icon: Icon, className = "w-4 h-4", fallback, ...props }) => {
  try {
    return <Icon className={className} {...props} />;
  } catch (error) {
    console.warn('Icon failed to render:', error);
    return fallback || <IconFallback className={className} />;
  }
};

// Error boundary component for icon rendering
class IconErrorBoundary extends React.Component<
  { children: React.ReactNode; fallback?: React.ReactNode },
  { hasError: boolean }
> {
  constructor(props: any) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.warn('Icon rendering error caught by boundary:', error);
    return { hasError: true };
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || <IconFallback />;
    }
    return this.props.children;
  }
}

// FormTextarea Component (not available in UI components, so keeping local)
const FormTextarea: React.FC<{
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  rows?: number;
  required?: boolean;
}> = ({ label, value, onChange, placeholder, rows = 3, required = false }) => (
  <div className="mb-4">
    <label className="block text-sm font-medium text-text mb-2">
      {label} {required && <span className="text-red-500">*</span>}
    </label>
    <textarea
      value={value}
      onChange={(e) => onChange(e.target.value)}
      placeholder={placeholder}
      rows={rows}
      className="w-full px-3 py-2 border border-border rounded-lg bg-surface text-text focus:outline-none focus:ring-2 focus:ring-primary resize-vertical"
      required={required}
    />
  </div>
);

// Main Enhanced Compliance Metrics Component
const EnhancedComplianceMetrics: React.FC<EnhancedComplianceMetricsProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [dashboardData, setDashboardData] = useState<EnhancedComplianceData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<'overview' | 'departments' | 'policies' | 'timeline' | 'metrics'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');

  // Add error boundary protection for the entire component
  const [hasRenderError, setHasRenderError] = useState(false);

  // Visibility controls for sections
  const [departmentsVisible, setDepartmentsVisible] = useState(true);
  const [policiesVisible, setPoliciesVisible] = useState(true);
  const [metricsVisible, setMetricsVisible] = useState(true);

  // Visibility toggle functions
  const toggleDepartmentsVisibility = useCallback(() => {
    setDepartmentsVisible(prev => !prev);
  }, []);

  const togglePoliciesVisibility = useCallback(() => {
    setPoliciesVisible(prev => !prev);
  }, []);

  const toggleMetricsVisibility = useCallback(() => {
    setMetricsVisible(prev => !prev);
  }, []);
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  
  // Modal states
  const [isDepartmentModalOpen, setIsDepartmentModalOpen] = useState(false);
  const [isPolicyModalOpen, setIsPolicyModalOpen] = useState(false);
  const [isTimelineModalOpen, setIsTimelineModalOpen] = useState(false);
  const [isMetricModalOpen, setIsMetricModalOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<any>(null);

  // View modal states
  const [isDepartmentViewOpen, setIsDepartmentViewOpen] = useState(false);
  const [isPolicyViewOpen, setIsPolicyViewOpen] = useState(false);
  const [isTimelineViewOpen, setIsTimelineViewOpen] = useState(false);
  const [isMetricViewOpen, setIsMetricViewOpen] = useState(false);
  const [viewingItem, setViewingItem] = useState<any>(null);

  // Form state for editing
  const [formData, setFormData] = useState<any>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);

  const chartTheme = getChartTheme(mode === 'dark');

  // Safe form handlers
  const handleFormChange = useCallback((field: string, value: string) => {
    try {
      setFormData((prev: any) => ({
        ...prev,
        [field]: value
      }));
      setSubmitError(null);
    } catch (error) {
      console.error('Form change error:', error);
      setSubmitError('Error updating form field');
    }
  }, []);

  const handleFormSubmit = useCallback(async (type: 'department' | 'policy' | 'timeline') => {
    if (isSubmitting) return;

    setIsSubmitting(true);
    setSubmitError(null);

    try {
      // Simulate form submission
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reset form and close modal
      setFormData({});
      setEditingItem(null);

      // Close appropriate modal
      switch (type) {
        case 'department':
          setIsDepartmentModalOpen(false);
          break;
        case 'policy':
          setIsPolicyModalOpen(false);
          break;
        case 'timeline':
          setIsTimelineModalOpen(false);
          break;
      }

      // Show success message (you could add a toast notification here)
      console.log(`${type} saved successfully`);

    } catch (error) {
      console.error('Form submission error:', error);
      setSubmitError('Failed to save. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [isSubmitting]);

  const handleModalClose = useCallback((type: 'department' | 'policy' | 'timeline' | 'metric') => {
    try {
      setFormData({});
      setEditingItem(null);
      setViewingItem(null);
      setSubmitError(null);

      // Close appropriate modals
      switch (type) {
        case 'department':
          setIsDepartmentModalOpen(false);
          setIsDepartmentViewOpen(false);
          break;
        case 'policy':
          setIsPolicyModalOpen(false);
          setIsPolicyViewOpen(false);
          break;
        case 'timeline':
          setIsTimelineModalOpen(false);
          setIsTimelineViewOpen(false);
          break;
        case 'metric':
          setIsMetricModalOpen(false);
          setIsMetricViewOpen(false);
          break;
      }
    } catch (error) {
      console.error('Modal close error:', error);
    }
  }, []);

  // Refresh functionality
  const handleRefresh = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const data = generateEnhancedComplianceData();

      if (!data) {
        throw new Error('No compliance data available. Service may be temporarily unavailable.');
      }

      // Enhanced data structure validation
      const requiredFields = ['overview', 'metrics', 'frameworks', 'categoryBreakdown', 'realTimeUpdates'];
      const missingFields = requiredFields.filter(field => !data[field]);

      if (missingFields.length > 0) {
        throw new Error(`Invalid data structure: missing fields ${missingFields.join(', ')}`);
      }

      setDashboardData(data);
      setError(null);

    } catch (err) {
      console.error('Refresh error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh compliance metrics data';
      const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setError(`${errorMessage} (Error ID: ${errorId})`);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Safe data loading with comprehensive error handling
  useEffect(() => {
    let isMounted = true;

    const loadData = async (isRetry = false) => {
      if (!isMounted) return;

      if (!isRetry) {
        setIsLoading(true);
        setError(null);
      }

      try {
        // Simulate network delay with shorter delay for retries
        const delay = isRetry ? 500 : 1200;
        await new Promise(resolve => setTimeout(resolve, delay));

        if (!isMounted) return;

        // Enhanced data generation with error recovery
        let data;
        try {
          console.log('🔄 Enhanced Compliance Metrics: Generating data...');
          data = generateEnhancedComplianceData();
          console.log('✅ Enhanced Compliance Data Generated Successfully:', {
            overview: data?.overview ? 'Present' : 'Missing',
            metricsCount: data?.metrics?.length || 0,
            frameworksCount: data?.frameworks?.length || 0,
            hasRealTimeUpdates: !!data?.realTimeUpdates,
            hasCategoryBreakdown: !!data?.categoryBreakdown
          });
        } catch (dataError) {
          console.error('❌ Enhanced Compliance Metrics: Data generation error:', dataError);
          console.error('❌ Error stack:', dataError.stack);
          throw new Error('Failed to generate compliance data. Please check data service.');
        }

        if (!data) {
          throw new Error('No compliance data available. Service may be temporarily unavailable.');
        }

        // Enhanced data structure validation
        const requiredFields = ['overview', 'metrics', 'frameworks', 'categoryBreakdown', 'realTimeUpdates'];
        const missingFields = requiredFields.filter(field => !data[field]);

        if (missingFields.length > 0) {
          throw new Error(`Invalid data structure: missing fields ${missingFields.join(', ')}`);
        }

        // Validate overview data
        if (!data.overview || typeof data.overview.totalMetrics !== 'number') {
          throw new Error('Invalid overview data structure');
        }

        // Validate metrics array
        if (!Array.isArray(data.metrics)) {
          console.warn('Invalid metrics data, using fallback');
          data.metrics = [];
        }

        if (isMounted) {
          setDashboardData(data);
          setError(null);
          console.log('🎉 Enhanced Compliance Metrics: Data loaded successfully - should display full functionality');
          console.log('🎉 Component state updated - no fallback mode should be shown');
        }

      } catch (err) {
        console.error('Enhanced Compliance Metrics data loading error:', err);

        if (isMounted) {
          const errorMessage = err instanceof Error ? err.message : 'Failed to load compliance metrics data';
          const errorId = `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          setError(`${errorMessage} (Error ID: ${errorId})`);
          setDashboardData(null);
        }
      } finally {
        if (isMounted && !isRetry) {
          setIsLoading(false);
        }
      }
    };

    loadData();

    // Set up safe real-time updates
    const interval = setInterval(() => {
      if (isMounted && dashboardData) {
        try {
          setDashboardData(prev => {
            if (!prev) return prev;

            return {
              ...prev,
              realTimeUpdates: {
                ...prev.realTimeUpdates,
                lastSync: new Date(),
                pendingUpdates: Math.floor(Math.random() * 5)
              }
            };
          });
        } catch (error) {
          console.error('Real-time update error:', error);
        }
      }
    }, 30000);

    return () => {
      isMounted = false;
      clearInterval(interval);
    };
  }, []);

  // Generate overview chart data
  const overviewChartData = useMemo(() => {
    if (!dashboardData || !dashboardData.overview) return null;

    try {
      return {
        labels: ['Compliant', 'Warning', 'Critical'],
        datasets: [{
          data: [
            dashboardData.overview.compliantMetrics || 0,
            dashboardData.overview.warningMetrics || 0,
            dashboardData.overview.criticalMetrics || 0
          ],
          backgroundColor: [
            'rgba(52, 211, 153, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(248, 113, 113, 0.8)'
          ],
          borderColor: [
            'rgb(52, 211, 153)',
            'rgb(251, 191, 36)',
            'rgb(248, 113, 113)'
          ],
          borderWidth: 2,
          hoverOffset: 8
        }]
      };
    } catch (error) {
      console.error('Error generating overview chart data:', error);
      return null;
    }
  }, [dashboardData]);

  // Generate trend chart data
  const trendChartData = useMemo(() => {
    if (!dashboardData?.metrics) return null;

    try {
      const last6Months = Array.from({ length: 6 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - (5 - i));
        return date.toLocaleDateString('en-US', { month: 'short' });
      });

      return {
        labels: last6Months,
        datasets: [{
          label: 'Overall Compliance Score',
          data: last6Months.map(() => Math.floor(Math.random() * 20) + 80),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          tension: 0.4,
          fill: true
        }]
      };
    } catch (error) {
      console.error('Error generating trend chart data:', error);
      return null;
    }
  }, [dashboardData]);

  if (isLoading) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64 rounded-lg" />
            <LoadingSkeleton className="h-64 rounded-lg" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Compliance Metrics Unavailable</h3>
          <p className="text-text-secondary mb-4 max-w-md mx-auto">{error}</p>
          <div className="flex items-center justify-center gap-3">
            <Button onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              {isLoading ? 'Refreshing...' : 'Refresh Data'}
            </Button>
            <Button variant="outline" onClick={() => window.location.reload()}>
              <RotateCcw className="w-4 h-4 mr-2" />
              Reload Page
            </Button>
          </div>
          <div className="mt-4 text-xs text-text-secondary">
            If the problem persists, please contact support.
          </div>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-amber-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">No Data Available</h3>
          <p className="text-text-secondary">Unable to load compliance metrics data.</p>
        </div>
      </div>
    );
  }

  // Add error boundary protection for render
  if (hasRenderError) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Component Error</h3>
          <p className="text-text-secondary mb-4">
            The Enhanced Compliance Metrics component encountered an error during rendering.
          </p>
          <button
            onClick={() => {
              setHasRenderError(false);
              setError(null);
              window.location.reload();
            }}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Reload Component
          </button>
        </div>
      </div>
    );
  }

  try {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 bg-primary/10 rounded-lg">
                <BarChart3 className="w-8 h-8 text-primary" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-text">Enhanced Compliance Metrics</h1>
                <p className="text-text-secondary">Comprehensive compliance monitoring and analytics</p>
              </div>
            </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
              <div className={`w-2 h-2 rounded-full ${dashboardData.realTimeUpdates.isLive ? 'bg-green-500 animate-pulse' : 'bg-gray-400'}`} />
              <span className="text-xs text-text-secondary">
                {dashboardData.realTimeUpdates.isLive ? 'Live' : 'Offline'}
              </span>
            </div>
            <Button onClick={() => window.location.reload()}>
              <RefreshCw className="w-4 h-4 mr-2" />
              Refresh
            </Button>
          </div>
        </div>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Total Metrics</p>
                <p className="text-2xl font-bold text-text">{dashboardData?.overview?.totalMetrics || 0}</p>
                <p className="text-xs text-green-500 flex items-center mt-1">
                  <TrendingUp className="w-3 h-3 mr-1" />
                  +{dashboardData?.overview?.trendsLastMonth?.improvement || 0}% this month
                </p>
              </div>
              <Target className="w-8 h-8 text-blue-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-green-500/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Compliant</p>
                <p className="text-2xl font-bold text-green-500">{dashboardData?.overview?.compliantMetrics || 0}</p>
                <p className="text-xs text-text-secondary">
                  {Math.round(((dashboardData?.overview?.compliantMetrics || 0) / (dashboardData?.overview?.totalMetrics || 1)) * 100)}% of total
                </p>
              </div>
              <CheckCircle className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-amber-500/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Warnings</p>
                <p className="text-2xl font-bold text-amber-500">{dashboardData?.overview?.warningMetrics || 0}</p>
                <p className="text-xs text-text-secondary">Require attention</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-amber-500" />
            </div>
          </div>

          <div className="bg-card rounded-lg p-4 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Overall Score</p>
                <p className="text-2xl font-bold text-text">{dashboardData?.overview?.overallScore || 0}%</p>
                <p className="text-xs text-text-secondary">Last updated: {dashboardData?.overview?.lastUpdated?.toLocaleDateString?.() || 'N/A'}</p>
              </div>
              <Activity className="w-8 h-8 text-primary" />
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: BarChart3 },
              { id: 'departments', label: 'Departments', icon: Building },
              { id: 'policies', label: 'Policies', icon: FileText },
              { id: 'timeline', label: 'Timeline', icon: Calendar },
              { id: 'metrics', label: 'Metrics', icon: Target }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-all duration-200 cursor-pointer ${
                    activeTab === tab.id
                      ? 'border-primary text-primary'
                      : 'border-transparent text-text-secondary hover:text-text hover:border-border hover:bg-surface/50 rounded-t-lg'
                  }`}
                >
                  <IconErrorBoundary>
                    <Icon className="w-4 h-4" />
                  </IconErrorBoundary>
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="space-y-6">
            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">Compliance Status Overview</h3>
                {overviewChartData && (
                  <ChartErrorBoundary
                    chartType="doughnut"
                    chartTitle="Compliance Status Overview"
                    fallbackHeight="h-64"
                  >
                    <div className="h-64 flex items-center justify-center">
                      <Doughnut
                        data={overviewChartData}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              position: 'bottom',
                              labels: {
                                color: chartTheme.textColor,
                                padding: 20
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </ChartErrorBoundary>
                )}
              </div>

              <div className="bg-card rounded-lg p-6 border border-border">
                <h3 className="text-lg font-semibold text-text mb-4">Compliance Trends</h3>
                {trendChartData && (
                  <ChartErrorBoundary
                    chartType="line"
                    chartTitle="Compliance Trends"
                    fallbackHeight="h-64"
                  >
                    <div className="h-64">
                      <Line
                        data={trendChartData}
                        options={{
                          responsive: true,
                          maintainAspectRatio: false,
                          plugins: {
                            legend: {
                              display: false
                            }
                          },
                          scales: {
                            x: {
                              grid: {
                                color: chartTheme.gridColor
                              },
                              ticks: {
                                color: chartTheme.textSecondary
                              }
                            },
                            y: {
                              grid: {
                                color: chartTheme.gridColor
                              },
                              ticks: {
                                color: chartTheme.textSecondary,
                                callback: (value) => `${value}%`
                              }
                            }
                          }
                        }}
                      />
                    </div>
                  </ChartErrorBoundary>
                )}
              </div>
            </div>

            {/* Category Breakdown */}
            <div className="bg-card rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4">Category Breakdown</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {Object.entries(dashboardData.categoryBreakdown).map(([category, data]) => (
                  <div key={category} className="p-4 bg-surface rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-text">{category}</h4>
                      <div className={`flex items-center gap-1 text-xs ${
                        data.trend === 'up' ? 'text-green-500' : 
                        data.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                      }`}>
                        {data.trend === 'up' ? <TrendingUp className="w-3 h-3" /> : 
                         data.trend === 'down' ? <TrendingDown className="w-3 h-3" /> : 
                         <Activity className="w-3 h-3" />}
                        {data.trend}
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-text mb-1">{data.score}%</div>
                    <div className="text-sm text-text-secondary">{data.count} metrics</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Departments Tab */}
        {activeTab === 'departments' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Departments Tab Error"
            fallbackMessage="There was an error loading the departments tab. Please try refreshing the page."
          >
            <div className="space-y-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-text">Departments</h3>
                <button
                  onClick={toggleDepartmentsVisibility}
                  className="p-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
                  title={departmentsVisible ? "Hide departments" : "Show departments"}
                  aria-label={departmentsVisible ? "Hide departments" : "Show departments"}
                >
                  <Eye className={`w-4 h-4 ${departmentsVisible ? 'text-primary' : 'text-text-secondary'}`} />
                </button>
                <FormInput
                  label=""
                  value={searchTerm}
                  onChange={setSearchTerm}
                  placeholder="Search departments..."
                />
                <FormSelect
                  label=""
                  value={filterStatus}
                  onChange={setFilterStatus}
                  options={[
                    { value: 'all', label: 'All Status' },
                    { value: 'low', label: 'Low Risk' },
                    { value: 'medium', label: 'Medium Risk' },
                    { value: 'high', label: 'High Risk' },
                    { value: 'critical', label: 'Critical Risk' }
                  ]}
                />
              </div>
              <Button onClick={() => setIsDepartmentModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Department
              </Button>
            </div>

            {departmentsVisible && (
              <div className="bg-card rounded-lg border border-border overflow-hidden">
                <div className="overflow-x-auto">
                  <table className="w-full">
                  <thead className="bg-surface">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Department
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Compliance Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Risk Level
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Data Subjects
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Last Audit
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {dashboardData?.frameworks?.[0]?.departments
                      ?.filter(dept =>
                        dept?.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                        (filterStatus === 'all' || dept.riskLevel === filterStatus)
                      )
                      ?.map((department) => (
                        <tr key={department.id} className="hover:bg-surface transition-colors duration-150 cursor-pointer">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <Building className="w-5 h-5 text-text-secondary mr-3" />
                              <div>
                                <div className="text-sm font-medium text-text">{department.name}</div>
                                <div className="text-sm text-text-secondary">{department.description}</div>
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="flex items-center">
                              <div className="text-sm font-medium text-text">{department.complianceScore}%</div>
                              <div className={`ml-2 flex items-center text-xs ${
                                department.trend === 'up' ? 'text-green-500' :
                                department.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                              }`}>
                                {department.trend === 'up' ? <TrendingUp className="w-3 h-3" /> :
                                 department.trend === 'down' ? <TrendingDown className="w-3 h-3" /> :
                                 <Activity className="w-3 h-3" />}
                              </div>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              department.riskLevel === 'low' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                              department.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              department.riskLevel === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                              'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                            }`}>
                              {department.riskLevel}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                            {department?.dataSubjectsCount?.toLocaleString?.() || '0'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-text">
                            {department?.lastAuditDate?.toLocaleDateString?.() || 'N/A'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center gap-2">
                              <button
                                onClick={() => {
                                  setViewingItem(department);
                                  setIsDepartmentViewOpen(true);
                                }}
                                className="text-text-secondary hover:text-text transition-colors cursor-pointer"
                                title="View Department Details"
                              >
                                <Eye className="w-4 h-4" />
                              </button>
                              <button
                                onClick={() => {
                                  setEditingItem(department);
                                  setIsDepartmentModalOpen(true);
                                }}
                                className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                                title="Edit Department"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                              <button
                                className="text-red-500 hover:text-red-700 transition-colors cursor-pointer"
                                title="Delete Department"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                  </tbody>
                  </table>
                </div>
              </div>
            )}
            </div>
          </ErrorBoundary>
        )}

        {/* Policies Tab */}
        {activeTab === 'policies' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Policies Tab Error"
            fallbackMessage="There was an error loading the policies tab. Please try refreshing the page."
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h3 className="text-lg font-semibold text-text">Policies</h3>
                  <button
                    onClick={togglePoliciesVisibility}
                    className="p-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
                    title={policiesVisible ? "Hide policies" : "Show policies"}
                    aria-label={policiesVisible ? "Hide policies" : "Show policies"}
                  >
                    <Eye className={`w-4 h-4 ${policiesVisible ? 'text-primary' : 'text-text-secondary'}`} />
                  </button>
                </div>
                <div className="flex items-center gap-4">
                  <FormInput
                    label=""
                    value={searchTerm}
                    onChange={setSearchTerm}
                    placeholder="Search policies..."
                  />
                  <FormSelect
                    label=""
                    value={filterStatus}
                    onChange={setFilterStatus}
                    options={[
                      { value: 'all', label: 'All Status' },
                      { value: 'draft', label: 'Draft' },
                      { value: 'under_review', label: 'Under Review' },
                      { value: 'approved', label: 'Approved' },
                      { value: 'active', label: 'Active' },
                      { value: 'archived', label: 'Archived' }
                    ]}
                  />
                  <Button onClick={() => setIsPolicyModalOpen(true)}>
                    <Plus className="w-4 h-4 mr-2" />
                    Add Policy
                  </Button>
                </div>
              </div>

            {policiesVisible && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dashboardData?.frameworks?.[0]?.policies
                ?.filter(policy =>
                  policy?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                  (filterStatus === 'all' || policy.status === filterStatus)
                )
                ?.map((policy) => (
                  <div key={policy.id} className="bg-card rounded-lg p-6 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        <FileText className="w-6 h-6 text-primary" />
                        <div>
                          <h3 className="font-semibold text-text">{policy.title}</h3>
                          <p className="text-sm text-text-secondary">{policy.version}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => {
                            setViewingItem(policy);
                            setIsPolicyViewOpen(true);
                          }}
                          className="text-text-secondary hover:text-text transition-colors cursor-pointer"
                          title="View Policy Details"
                        >
                          <Eye className="w-4 h-4" />
                        </button>
                        <button
                          onClick={() => {
                            setEditingItem(policy);
                            setIsPolicyModalOpen(true);
                          }}
                          className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                          title="Edit Policy"
                        >
                          <Edit className="w-4 h-4" />
                        </button>
                        <button
                          className="text-red-500 hover:text-red-700 transition-colors cursor-pointer"
                          title="Delete Policy"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    </div>

                    <p className="text-sm text-text-secondary mb-4 line-clamp-2">
                      {policy.description}
                    </p>

                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Status</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          policy.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          policy.status === 'approved' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          policy.status === 'under_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          policy.status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {policy?.status?.replace?.('_', ' ') || 'Unknown'}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Adherence</span>
                        <span className="text-sm font-medium text-text">{policy.adherenceRate}%</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Framework</span>
                        <span className="text-sm font-medium text-text uppercase">{policy.framework}</span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Violations</span>
                        <span className={`text-sm font-medium ${
                          (policy?.violations?.length || 0) === 0 ? 'text-green-500' :
                          (policy?.violations?.length || 0) <= 2 ? 'text-yellow-500' : 'text-red-500'
                        }`}>
                          {policy?.violations?.length || 0}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-sm text-text-secondary">Review Date</span>
                        <span className="text-sm text-text">{policy?.reviewDate?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                    </div>

                    <div className="mt-4 pt-4 border-t border-border">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-text-secondary">Owner: {policy.owner}</span>
                        <button className="text-primary hover:text-primary/80 text-sm">
                          View Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
            </div>
          </ErrorBoundary>
        )}

        {/* Timeline Tab */}
        {activeTab === 'timeline' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Timeline Tab Error"
            fallbackMessage="There was an error loading the timeline tab. Please try refreshing the page."
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <h3 className="text-lg font-semibold text-text">Timeline</h3>
                </div>
                <div className="flex items-center gap-4">
                  <FormInput
                    label=""
                    value={searchTerm}
                    onChange={setSearchTerm}
                    placeholder="Search timeline events..."
                  />
                  <FormSelect
                    label=""
                    value={filterStatus}
                    onChange={setFilterStatus}
                    options={[
                      { value: 'all', label: 'All Status' },
                      { value: 'planned', label: 'Planned' },
                      { value: 'in_progress', label: 'In Progress' },
                      { value: 'completed', label: 'Completed' },
                      { value: 'overdue', label: 'Overdue' },
                      { value: 'cancelled', label: 'Cancelled' }
                    ]}
                  />
                </div>
              </div>

              <Button onClick={() => setIsTimelineModalOpen(true)}>
                <Plus className="w-4 h-4 mr-2" />
                Add Event
              </Button>

              <div className="bg-card rounded-lg p-6 border border-border">
                <div className="space-y-6">
                {dashboardData?.frameworks?.[0]?.timeline
                  ?.filter(event =>
                    event?.title?.toLowerCase()?.includes(searchTerm.toLowerCase()) &&
                    (filterStatus === 'all' || event.status === filterStatus)
                  )
                  ?.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  ?.map((event, index) => (
                    <div
                      key={event.id}
                      className="flex items-start gap-4 cursor-pointer hover:bg-surface/50 rounded-lg p-2 -m-2 transition-colors duration-150"
                      onClick={() => {
                        setViewingItem(event);
                        setIsTimelineViewOpen(true);
                      }}
                    >
                      <div className="flex flex-col items-center">
                        <div className={`w-3 h-3 rounded-full ${
                          event.status === 'completed' ? 'bg-green-500' :
                          event.status === 'in_progress' ? 'bg-blue-500' :
                          event.status === 'overdue' ? 'bg-red-500' :
                          event.status === 'cancelled' ? 'bg-gray-500' :
                          'bg-yellow-500'
                        }`} />
                        {index < (dashboardData?.frameworks?.[0]?.timeline?.length || 0) - 1 && (
                          <div className="w-px h-16 bg-border mt-2" />
                        )}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h4 className="text-sm font-medium text-text">{event.title}</h4>
                            <p className="text-sm text-text-secondary mt-1">{event.description}</p>

                            <div className="flex items-center gap-4 mt-2 text-xs text-text-secondary">
                              <span className="flex items-center gap-1">
                                <CalendarIcon className="w-3 h-3" />
                                {event?.date?.toLocaleDateString?.() || 'N/A'}
                              </span>
                              <span className="flex items-center gap-1">
                                <Users className="w-3 h-3" />
                                {event.assignedTo}
                              </span>
                              <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                                event.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                                event.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                                event.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                                'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                              }`}>
                                {event.priority}
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center gap-2 ml-4" onClick={(e) => e.stopPropagation()}>
                            <button
                              onClick={() => {
                                setEditingItem(event);
                                setIsTimelineModalOpen(true);
                              }}
                              className="text-primary hover:text-primary/80 transition-colors cursor-pointer"
                              title="Edit Event"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              className="text-red-500 hover:text-red-700 transition-colors cursor-pointer"
                              title="Delete Event"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </ErrorBoundary>
        )}

        {/* Metrics Tab */}
        {activeTab === 'metrics' && (
          <ErrorBoundary
            type="section"
            fallbackTitle="Metrics Tab Error"
            fallbackMessage="There was an error loading the metrics tab. Please try refreshing the page."
          >
            <div className="space-y-6">
              <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <h3 className="text-lg font-semibold text-text">Metrics</h3>
                <button
                  onClick={toggleMetricsVisibility}
                  className="p-2 text-text-secondary hover:text-text hover:bg-card rounded-lg transition-colors"
                  title={metricsVisible ? "Hide metrics" : "Show metrics"}
                  aria-label={metricsVisible ? "Hide metrics" : "Show metrics"}
                >
                  <Eye className={`w-4 h-4 ${metricsVisible ? 'text-primary' : 'text-text-secondary'}`} />
                </button>
              </div>
            </div>
            {metricsVisible && (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {dashboardData?.metrics?.map((metric) => (
                <div key={metric.id} className="bg-card rounded-lg p-6 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer">
                  <div className="flex items-start justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-text">{metric.name}</h3>
                      <p className="text-sm text-text-secondary capitalize">{metric.category}</p>
                    </div>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      metric.status === 'compliant' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                      metric.status === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                      'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                    }`}>
                      {metric.status}
                    </span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-text-secondary">Current Value</span>
                      <span className="text-lg font-bold text-text">
                        {metric.currentValue}{metric.unit}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-text-secondary">Target</span>
                      <span className="text-sm text-text">
                        {metric.targetValue}{metric.unit}
                      </span>
                    </div>

                    <div className="w-full bg-surface rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          metric.status === 'compliant' ? 'bg-green-500' :
                          metric.status === 'warning' ? 'bg-yellow-500' :
                          'bg-red-500'
                        }`}
                        style={{
                          width: `${Math.min((metric.currentValue / metric.targetValue) * 100, 100)}%`
                        }}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm text-text-secondary">Trend</span>
                      <div className={`flex items-center gap-1 text-xs ${
                        metric.trend === 'up' ? 'text-green-500' :
                        metric.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                      }`}>
                        {metric.trend === 'up' ? <TrendingUp className="w-3 h-3" /> :
                         metric.trend === 'down' ? <TrendingDown className="w-3 h-3" /> :
                         <Activity className="w-3 h-3" />}
                        {metric.trend}
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="text-xs text-text-secondary">
                        Last updated: {metric?.lastUpdated?.toLocaleDateString?.() || 'N/A'}
                      </div>
                      <button
                        onClick={() => {
                          setViewingItem(metric);
                          setIsMetricViewOpen(true);
                        }}
                        className="text-text-secondary hover:text-text transition-colors cursor-pointer"
                        title="View Metric Details"
                      >
                        <Eye className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              ))}
              </div>
            )}
            </div>
          </ErrorBoundary>
        )}

        {/* View Modals */}
        <Modal
          isOpen={isDepartmentViewOpen}
          onClose={() => {
            setIsDepartmentViewOpen(false);
            setViewingItem(null);
          }}
          title="Department Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Basic Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Name:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Description:</span>
                        <span className="text-sm text-text">{viewingItem?.description || 'No description available'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Responsible Officer:</span>
                        <span className="text-sm text-text">{viewingItem?.responsibleOfficer || 'Not assigned'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Compliance Metrics</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Compliance Score:</span>
                        <span className="text-sm font-bold text-text">{viewingItem.complianceScore}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Risk Level:</span>
                        <span className={`text-sm font-medium ${
                          viewingItem.riskLevel === 'low' ? 'text-green-500' :
                          viewingItem.riskLevel === 'medium' ? 'text-yellow-500' :
                          viewingItem.riskLevel === 'high' ? 'text-orange-500' :
                          'text-red-500'
                        }`}>
                          {viewingItem?.riskLevel?.toUpperCase?.() || 'UNKNOWN'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Data Subjects:</span>
                        <span className="text-sm text-text">{viewingItem?.dataSubjectsCount?.toLocaleString?.() || '0'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Violations:</span>
                        <span className={`text-sm font-medium ${viewingItem.violations === 0 ? 'text-green-500' : 'text-red-500'}`}>
                          {viewingItem.violations}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Audit Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Audit:</span>
                        <span className="text-sm text-text">{viewingItem?.lastAuditDate?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Next Audit:</span>
                        <span className="text-sm text-text">{viewingItem?.nextAuditDate?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Trend:</span>
                        <div className={`flex items-center gap-1 text-xs ${
                          viewingItem.trend === 'up' ? 'text-green-500' :
                          viewingItem.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                        }`}>
                          {viewingItem.trend === 'up' ? <TrendingUp className="w-3 h-3" /> :
                           viewingItem.trend === 'down' ? <TrendingDown className="w-3 h-3" /> :
                           <Activity className="w-3 h-3" />}
                          {viewingItem.trend}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Processing Activities</h4>
                    <div className="bg-surface rounded-lg p-4 max-h-48 overflow-y-auto">
                      <div className="space-y-3">
                        {viewingItem?.processingActivities?.map((activity: any, index: number) => (
                          <div key={index} className="border-b border-border pb-2 last:border-b-0">
                            <div className="text-sm font-medium text-text">{activity.name}</div>
                            <div className="text-xs text-text-secondary mt-1">
                              Purpose: {activity.purpose}
                            </div>
                            <div className="text-xs text-text-secondary">
                              Legal Basis: {activity.legalBasis}
                            </div>
                            <div className="text-xs text-text-secondary">
                              Retention: {activity.retentionPeriod}
                            </div>
                            <div className="flex items-center gap-2 mt-1">
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                activity.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                                activity.status === 'inactive' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                                'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                              }`}>
                                {activity.status}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setIsDepartmentViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isPolicyViewOpen}
          onClose={() => {
            setIsPolicyViewOpen(false);
            setViewingItem(null);
          }}
          title="Policy Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Policy Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Title:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Version:</span>
                        <span className="text-sm text-text">{viewingItem.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Framework:</span>
                        <span className="text-sm font-medium text-text uppercase">{viewingItem?.framework || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Category:</span>
                        <span className="text-sm text-text">{viewingItem?.category?.replace?.('_', ' ') || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Owner:</span>
                        <span className="text-sm text-text">{viewingItem.owner}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Status & Compliance</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'active' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'approved' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          viewingItem.status === 'under_review' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          viewingItem.status === 'draft' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem?.status?.replace?.('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Adherence Rate:</span>
                        <span className="text-sm font-bold text-text">{viewingItem.adherenceRate}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Violations:</span>
                        <span className={`text-sm font-medium ${
                          (viewingItem?.violations?.length || 0) === 0 ? 'text-green-500' :
                          (viewingItem?.violations?.length || 0) <= 2 ? 'text-yellow-500' : 'text-red-500'
                        }`}>
                          {viewingItem?.violations?.length || 0}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Dates</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Effective Date:</span>
                        <span className="text-sm text-text">{viewingItem?.effectiveDate?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Review Date:</span>
                        <span className="text-sm text-text">{viewingItem?.reviewDate?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Created:</span>
                        <span className="text-sm text-text">{viewingItem?.createdAt?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Updated:</span>
                        <span className="text-sm text-text">{viewingItem?.updatedAt?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Assigned Departments</h4>
                    <div className="bg-surface rounded-lg p-4">
                      <div className="flex flex-wrap gap-2">
                        {viewingItem?.assignedDepartments?.map((deptId: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-primary/10 text-primary text-xs rounded-full">
                            Department {deptId?.split?.('-')?.[1] || 'Unknown'}
                          </span>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-text mb-2">Description</h4>
                <div className="bg-surface rounded-lg p-4">
                  <p className="text-sm text-text">{viewingItem.description}</p>
                </div>
              </div>

              {viewingItem?.violations && Array.isArray(viewingItem.violations) && viewingItem.violations.length > 0 && (
                <div>
                  <h4 className="text-sm font-medium text-text mb-2">Recent Violations</h4>
                  <div className="bg-surface rounded-lg p-4 max-h-48 overflow-y-auto">
                    <div className="space-y-3">
                      {viewingItem?.violations?.map((violation: any, index: number) => (
                        <div key={index} className="border-b border-border pb-2 last:border-b-0">
                          <div className="flex items-center justify-between">
                            <div className="text-sm font-medium text-text">{violation.description}</div>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              violation.severity === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                              violation.severity === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                              violation.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                              'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                            }`}>
                              {violation.severity}
                            </span>
                          </div>
                          <div className="text-xs text-text-secondary mt-1">
                            Reported: {violation?.reportedDate?.toLocaleDateString?.() || 'N/A'}
                          </div>
                          <div className="text-xs text-text-secondary">
                            Status: {violation?.status?.replace?.('_', ' ') || 'Unknown'}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setIsPolicyViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>

        {/* Modals for CRUD operations */}
        <Modal
          isOpen={isDepartmentModalOpen}
          onClose={() => {
            setIsDepartmentModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit Department' : 'Add Department'}
        >
          <div className="space-y-4">
            <FormInput
              label="Department Name"
              value={formData.name || editingItem?.name || ''}
              onChange={(value) => handleFormChange('name', value)}
              required
            />
            <FormTextarea
              label="Description"
              value={formData.description || editingItem?.description || ''}
              onChange={(value) => handleFormChange('description', value)}
              required
            />
            <FormSelect
              label="Risk Level"
              value={formData.riskLevel || editingItem?.riskLevel || ''}
              onChange={(value) => handleFormChange('riskLevel', value)}
              options={[
                { value: 'low', label: 'Low' },
                { value: 'medium', label: 'Medium' },
                { value: 'high', label: 'High' },
                { value: 'critical', label: 'Critical' }
              ]}
              required
            />

            {submitError && (
              <div className="p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                <p className="text-sm text-red-700 dark:text-red-300">{submitError}</p>
              </div>
            )}
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => handleModalClose('department')}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                onClick={() => handleFormSubmit('department')}
                disabled={isSubmitting}
              >
                <Save className="w-4 h-4 mr-2" />
                {isSubmitting ? 'Saving...' : 'Save'}
              </Button>
            </div>
          </div>
        </Modal>

        <Modal
          isOpen={isTimelineViewOpen}
          onClose={() => {
            setIsTimelineViewOpen(false);
            setViewingItem(null);
          }}
          title="Timeline Event Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Event Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Title:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.title}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Type:</span>
                        <span className="text-sm text-text">{viewingItem?.type?.replace?.('_', ' ') || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Framework:</span>
                        <span className="text-sm font-medium text-text uppercase">{viewingItem?.framework || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Date:</span>
                        <span className="text-sm text-text">{viewingItem?.date?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Status & Priority</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'completed' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'in_progress' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300' :
                          viewingItem.status === 'overdue' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          viewingItem.status === 'cancelled' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300' :
                          'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                        }`}>
                          {viewingItem?.status?.replace?.('_', ' ') || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Priority:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.priority === 'critical' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' :
                          viewingItem.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300' :
                          viewingItem.priority === 'medium' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300'
                        }`}>
                          {viewingItem.priority}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Department:</span>
                        <span className="text-sm text-text">Department {viewingItem?.department?.split?.('-')?.[1] || 'Unknown'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Assigned To:</span>
                        <span className="text-sm text-text">{viewingItem.assignedTo}</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Timestamps</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Created:</span>
                        <span className="text-sm text-text">{viewingItem?.createdAt?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Updated:</span>
                        <span className="text-sm text-text">{viewingItem?.updatedAt?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  {viewingItem?.relatedEntities && Array.isArray(viewingItem.relatedEntities) && viewingItem.relatedEntities.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Related Entities</h4>
                      <div className="bg-surface rounded-lg p-4">
                        <div className="space-y-2">
                          {viewingItem?.relatedEntities?.map((entity: any, index: number) => (
                            <div key={index} className="flex items-center justify-between">
                              <span className="text-sm text-text">{entity.name}</span>
                              <span className="text-xs text-text-secondary capitalize">{entity.type}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}

                  {viewingItem?.attachments && Array.isArray(viewingItem.attachments) && viewingItem.attachments.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium text-text mb-2">Attachments</h4>
                      <div className="bg-surface rounded-lg p-4">
                        <div className="space-y-2">
                          {viewingItem?.attachments?.map((attachment: string, index: number) => (
                            <div key={index} className="flex items-center gap-2">
                              <FileText className="w-4 h-4 text-text-secondary" />
                              <span className="text-sm text-text">{attachment}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium text-text mb-2">Description</h4>
                <div className="bg-surface rounded-lg p-4">
                  <p className="text-sm text-text">{viewingItem.description}</p>
                </div>
              </div>

              {viewingItem.outcome && (
                <div>
                  <h4 className="text-sm font-medium text-text mb-2">Outcome</h4>
                  <div className="bg-surface rounded-lg p-4">
                    <p className="text-sm text-text">{viewingItem.outcome}</p>
                  </div>
                </div>
              )}

              <div className="flex justify-end pt-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setIsTimelineViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isMetricViewOpen}
          onClose={() => {
            setIsMetricViewOpen(false);
            setViewingItem(null);
          }}
          title="Metric Details"
        >
          {viewingItem && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Metric Information</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Name:</span>
                        <span className="text-sm font-medium text-text">{viewingItem.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Category:</span>
                        <span className="text-sm text-text capitalize">{viewingItem.category}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Unit:</span>
                        <span className="text-sm text-text">{viewingItem.unit}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Last Updated:</span>
                        <span className="text-sm text-text">{viewingItem?.lastUpdated?.toLocaleDateString?.() || 'N/A'}</span>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-text mb-2">Current Performance</h4>
                    <div className="bg-surface rounded-lg p-4 space-y-3">
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Current Value:</span>
                        <span className="text-lg font-bold text-text">
                          {viewingItem.currentValue}{viewingItem.unit}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Target Value:</span>
                        <span className="text-sm text-text">
                          {viewingItem.targetValue}{viewingItem.unit}
                        </span>
                      </div>
                      <div className="w-full bg-surface rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${
                            viewingItem.status === 'compliant' ? 'bg-green-500' :
                            viewingItem.status === 'warning' ? 'bg-yellow-500' :
                            'bg-red-500'
                          }`}
                          style={{
                            width: `${Math.min((viewingItem.currentValue / viewingItem.targetValue) * 100, 100)}%`
                          }}
                        />
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Status:</span>
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          viewingItem.status === 'compliant' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' :
                          viewingItem.status === 'warning' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300' :
                          'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                        }`}>
                          {viewingItem.status}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-sm text-text-secondary">Trend:</span>
                        <div className={`flex items-center gap-1 text-xs ${
                          viewingItem.trend === 'up' ? 'text-green-500' :
                          viewingItem.trend === 'down' ? 'text-red-500' : 'text-gray-500'
                        }`}>
                          {viewingItem.trend === 'up' ? <TrendingUp className="w-3 h-3" /> :
                           viewingItem.trend === 'down' ? <TrendingDown className="w-3 h-3" /> :
                           <Activity className="w-3 h-3" />}
                          {viewingItem.trend}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  {viewingItem.drillDownData && (
                    <>
                      <div>
                        <h4 className="text-sm font-medium text-text mb-2">Department Breakdown</h4>
                        <div className="bg-surface rounded-lg p-4 max-h-32 overflow-y-auto">
                          <div className="space-y-2">
                            {viewingItem?.drillDownData?.departments?.map((dept: any, index: number) => (
                              <div key={index} className="flex items-center justify-between">
                                <span className="text-sm text-text">{dept.name}</span>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium text-text">{dept.value}%</span>
                                  <span className={`w-2 h-2 rounded-full ${
                                    dept.status === 'compliant' ? 'bg-green-500' :
                                    dept.status === 'warning' ? 'bg-yellow-500' :
                                    'bg-red-500'
                                  }`} />
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="text-sm font-medium text-text mb-2">Policy Breakdown</h4>
                        <div className="bg-surface rounded-lg p-4 max-h-32 overflow-y-auto">
                          <div className="space-y-2">
                            {viewingItem?.drillDownData?.policies?.map((policy: any, index: number) => (
                              <div key={index} className="flex items-center justify-between">
                                <span className="text-sm text-text">{policy.name}</span>
                                <div className="flex items-center gap-2">
                                  <span className="text-sm font-medium text-text">{policy.value}%</span>
                                  <span className={`text-xs ${policy.violations > 0 ? 'text-red-500' : 'text-green-500'}`}>
                                    {policy.violations} violations
                                  </span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </div>
                    </>
                  )}
                </div>
              </div>

              <div className="flex justify-end pt-4">
                <Button
                  variant="secondary"
                  onClick={() => {
                    setIsMetricViewOpen(false);
                    setViewingItem(null);
                  }}
                >
                  Close
                </Button>
              </div>
            </div>
          )}
        </Modal>

        <Modal
          isOpen={isPolicyModalOpen}
          onClose={() => {
            setIsPolicyModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit Policy' : 'Add Policy'}
        >
          <div className="space-y-4">
            <FormInput
              label="Policy Title"
              value={editingItem?.title || ''}
              onChange={() => {}}
              required
            />
            <FormTextarea
              label="Description"
              value={editingItem?.description || ''}
              onChange={() => {}}
              required
            />
            <FormSelect
              label="Category"
              value={editingItem?.category || ''}
              onChange={() => {}}
              options={[
                { value: 'data_protection', label: 'Data Protection' },
                { value: 'consent_management', label: 'Consent Management' },
                { value: 'data_retention', label: 'Data Retention' },
                { value: 'breach_response', label: 'Breach Response' },
                { value: 'access_control', label: 'Access Control' },
                { value: 'security', label: 'Security' }
              ]}
              required
            />
            <FormSelect
              label="Framework"
              value={editingItem?.framework || ''}
              onChange={() => {}}
              options={[
                { value: 'gdpr', label: 'GDPR' },
                { value: 'sox', label: 'SOX' },
                { value: 'iso27001', label: 'ISO 27001' },
                { value: 'ccpa', label: 'CCPA' },
                { value: 'hipaa', label: 'HIPAA' }
              ]}
              required
            />
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsPolicyModalOpen(false);
                  setEditingItem(null);
                }}
              >
                Cancel
              </Button>
              <Button onClick={() => {}}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </Modal>

        <Modal
          isOpen={isTimelineModalOpen}
          onClose={() => {
            setIsTimelineModalOpen(false);
            setEditingItem(null);
          }}
          title={editingItem ? 'Edit Timeline Event' : 'Add Timeline Event'}
        >
          <div className="space-y-4">
            <FormInput
              label="Event Title"
              value={editingItem?.title || ''}
              onChange={() => {}}
              required
            />
            <FormTextarea
              label="Description"
              value={editingItem?.description || ''}
              onChange={() => {}}
              required
            />
            <FormSelect
              label="Type"
              value={editingItem?.type || ''}
              onChange={() => {}}
              options={[
                { value: 'policy_implementation', label: 'Policy Implementation' },
                { value: 'audit_activity', label: 'Audit Activity' },
                { value: 'data_subject_request', label: 'Data Subject Request' },
                { value: 'breach_incident', label: 'Breach Incident' },
                { value: 'regulatory_deadline', label: 'Regulatory Deadline' },
                { value: 'compliance_milestone', label: 'Compliance Milestone' }
              ]}
              required
            />
            <FormSelect
              label="Priority"
              value={editingItem?.priority || ''}
              onChange={() => {}}
              options={[
                { value: 'low', label: 'Low' },
                { value: 'medium', label: 'Medium' },
                { value: 'high', label: 'High' },
                { value: 'critical', label: 'Critical' }
              ]}
              required
            />
            <div className="flex justify-end gap-2 pt-4">
              <Button
                variant="secondary"
                onClick={() => {
                  setIsTimelineModalOpen(false);
                  setEditingItem(null);
                }}
              >
                Cancel
              </Button>
              <Button onClick={() => {}}>
                <Save className="w-4 h-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </Modal>
      </div>
    </div>
  );
  } catch (renderError) {
    console.error('Enhanced Compliance Metrics render error:', renderError);
    setHasRenderError(true);

    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <AlertTriangle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Render Error</h3>
          <p className="text-text-secondary mb-4">
            The component failed to render properly. Please try refreshing the page.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Refresh Page
          </button>
        </div>
      </div>
    );
  }
};

export default EnhancedComplianceMetrics;
