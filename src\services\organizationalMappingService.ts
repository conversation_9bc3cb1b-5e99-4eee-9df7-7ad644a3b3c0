import { OrganizationalUnit, DataFlow, OrganizationalMappingMetrics } from '../types/organizationalMapping';

// Enhanced types for organizational mapping
export interface OrganizationalUnit {
  id: string;
  name: string;
  type: 'department' | 'team' | 'business_unit' | 'subsidiary' | 'branch';
  parentId?: string;
  location: string;
  country: string;
  region: string;
  headCount: number;
  complianceScore: number;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  contactPerson: string;
  email: string;
  phone?: string;
  dataProcessingActivities: string[];
  regulations: string[];
  lastAssessment: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface DataFlow {
  id: string;
  fromUnit: string;
  toUnit: string;
  dataType: string;
  volume: number;
  frequency: 'daily' | 'weekly' | 'monthly' | 'on-demand';
  purpose: string;
  legalBasis: string;
  safeguards: string[];
  riskScore: number;
  lastReview: Date;
  isActive: boolean;
}

export interface OrganizationalMappingMetrics {
  totalUnits: number;
  byType: Record<string, number>;
  byRiskLevel: Record<string, number>;
  byRegion: Record<string, number>;
  averageComplianceScore: number;
  totalHeadCount: number;
  totalDataFlows: number;
  highRiskUnits: number;
  lastUpdated: Date;
}

export class OrganizationalMappingService {
  private static units: OrganizationalUnit[] = [];
  private static dataFlows: DataFlow[] = [];
  private static initialized = false;

  static initialize() {
    if (this.initialized) return;
    
    this.units = this.generateMockUnits();
    this.dataFlows = this.generateMockDataFlows();
    this.initialized = true;
  }

  private static generateMockUnits(): OrganizationalUnit[] {
    return [
      {
        id: 'unit-001',
        name: 'Human Resources',
        type: 'department',
        location: 'New York, NY',
        country: 'United States',
        region: 'North America',
        headCount: 25,
        complianceScore: 92,
        riskLevel: 'low',
        contactPerson: 'Sarah Johnson',
        email: '<EMAIL>',
        phone: '******-0123',
        dataProcessingActivities: ['Employee Records', 'Payroll', 'Benefits', 'Performance Reviews'],
        regulations: ['GDPR', 'CCPA', 'SOX', 'HIPAA'],
        lastAssessment: new Date('2024-01-15'),
        createdAt: new Date('2023-01-01'),
        updatedAt: new Date('2024-01-15')
      },
      {
        id: 'unit-002',
        name: 'Marketing Team',
        type: 'team',
        parentId: 'unit-003',
        location: 'London, UK',
        country: 'United Kingdom',
        region: 'Europe',
        headCount: 15,
        complianceScore: 78,
        riskLevel: 'medium',
        contactPerson: 'James Wilson',
        email: '<EMAIL>',
        phone: '+44-20-7946-0958',
        dataProcessingActivities: ['Customer Data', 'Analytics', 'Campaigns', 'Lead Generation'],
        regulations: ['GDPR', 'DPA 2018', 'PECR'],
        lastAssessment: new Date('2024-01-10'),
        createdAt: new Date('2023-02-01'),
        updatedAt: new Date('2024-01-10')
      },
      {
        id: 'unit-003',
        name: 'Sales Division',
        type: 'business_unit',
        location: 'San Francisco, CA',
        country: 'United States',
        region: 'North America',
        headCount: 45,
        complianceScore: 85,
        riskLevel: 'medium',
        contactPerson: 'Michael Chen',
        email: '<EMAIL>',
        phone: '******-555-0199',
        dataProcessingActivities: ['Customer CRM', 'Sales Analytics', 'Contract Management'],
        regulations: ['GDPR', 'CCPA', 'SOX'],
        lastAssessment: new Date('2024-01-20'),
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2024-01-20')
      },
      {
        id: 'unit-004',
        name: 'IT Security',
        type: 'department',
        location: 'Berlin, Germany',
        country: 'Germany',
        region: 'Europe',
        headCount: 12,
        complianceScore: 96,
        riskLevel: 'low',
        contactPerson: 'Anna Mueller',
        email: '<EMAIL>',
        phone: '+49-30-12345678',
        dataProcessingActivities: ['Security Monitoring', 'Access Control', 'Incident Response'],
        regulations: ['GDPR', 'BSI IT-Grundschutz', 'ISO 27001'],
        lastAssessment: new Date('2024-01-25'),
        createdAt: new Date('2023-03-01'),
        updatedAt: new Date('2024-01-25')
      },
      {
        id: 'unit-005',
        name: 'Customer Support',
        type: 'department',
        location: 'Toronto, Canada',
        country: 'Canada',
        region: 'North America',
        headCount: 30,
        complianceScore: 88,
        riskLevel: 'low',
        contactPerson: 'David Thompson',
        email: '<EMAIL>',
        phone: '******-555-0167',
        dataProcessingActivities: ['Customer Inquiries', 'Support Tickets', 'Feedback Collection'],
        regulations: ['GDPR', 'PIPEDA', 'CCPA'],
        lastAssessment: new Date('2024-01-18'),
        createdAt: new Date('2023-02-15'),
        updatedAt: new Date('2024-01-18')
      },
      {
        id: 'unit-006',
        name: 'Data Analytics',
        type: 'team',
        parentId: 'unit-007',
        location: 'Sydney, Australia',
        country: 'Australia',
        region: 'Asia-Pacific',
        headCount: 8,
        complianceScore: 72,
        riskLevel: 'high',
        contactPerson: 'Emma Watson',
        email: '<EMAIL>',
        phone: '+61-2-9876-5432',
        dataProcessingActivities: ['Data Mining', 'Predictive Analytics', 'Customer Profiling'],
        regulations: ['Privacy Act 1988', 'GDPR', 'CCPA'],
        lastAssessment: new Date('2024-01-12'),
        createdAt: new Date('2023-04-01'),
        updatedAt: new Date('2024-01-12')
      },
      {
        id: 'unit-007',
        name: 'Technology Division',
        type: 'business_unit',
        location: 'Tokyo, Japan',
        country: 'Japan',
        region: 'Asia-Pacific',
        headCount: 60,
        complianceScore: 81,
        riskLevel: 'medium',
        contactPerson: 'Hiroshi Tanaka',
        email: '<EMAIL>',
        phone: '+81-3-1234-5678',
        dataProcessingActivities: ['Software Development', 'System Administration', 'Data Processing'],
        regulations: ['APPI', 'GDPR', 'ISO 27001'],
        lastAssessment: new Date('2024-01-22'),
        createdAt: new Date('2023-01-10'),
        updatedAt: new Date('2024-01-22')
      },
      {
        id: 'unit-008',
        name: 'European Subsidiary',
        type: 'subsidiary',
        location: 'Amsterdam, Netherlands',
        country: 'Netherlands',
        region: 'Europe',
        headCount: 120,
        complianceScore: 94,
        riskLevel: 'low',
        contactPerson: 'Sophie van der Berg',
        email: '<EMAIL>',
        phone: '+31-20-123-4567',
        dataProcessingActivities: ['Regional Operations', 'Customer Management', 'Compliance Oversight'],
        regulations: ['GDPR', 'AVG', 'ePrivacy Directive'],
        lastAssessment: new Date('2024-01-28'),
        createdAt: new Date('2023-01-05'),
        updatedAt: new Date('2024-01-28')
      }
    ];
  }

  private static generateMockDataFlows(): DataFlow[] {
    return [
      {
        id: 'flow-001',
        fromUnit: 'unit-001',
        toUnit: 'unit-003',
        dataType: 'Employee Performance Data',
        volume: 1500,
        frequency: 'monthly',
        purpose: 'Performance-based Sales Incentives',
        legalBasis: 'Legitimate Interest',
        safeguards: ['Encryption', 'Access Controls', 'Data Minimization'],
        riskScore: 3,
        lastReview: new Date('2024-01-15'),
        isActive: true
      },
      {
        id: 'flow-002',
        fromUnit: 'unit-002',
        toUnit: 'unit-006',
        dataType: 'Customer Behavioral Data',
        volume: 50000,
        frequency: 'daily',
        purpose: 'Marketing Analytics and Personalization',
        legalBasis: 'Consent',
        safeguards: ['Pseudonymization', 'Encryption', 'Regular Audits'],
        riskScore: 7,
        lastReview: new Date('2024-01-20'),
        isActive: true
      },
      {
        id: 'flow-003',
        fromUnit: 'unit-005',
        toUnit: 'unit-004',
        dataType: 'Security Incident Reports',
        volume: 200,
        frequency: 'on-demand',
        purpose: 'Security Monitoring and Response',
        legalBasis: 'Legitimate Interest',
        safeguards: ['Encryption', 'Access Logging', 'Secure Channels'],
        riskScore: 2,
        lastReview: new Date('2024-01-25'),
        isActive: true
      }
    ];
  }

  // CRUD Operations for Organizational Units
  static async getAllUnits(): Promise<OrganizationalUnit[]> {
    this.initialize();
    return [...this.units];
  }

  static async getUnitById(id: string): Promise<OrganizationalUnit | null> {
    this.initialize();
    return this.units.find(unit => unit.id === id) || null;
  }

  static async createUnit(unitData: Partial<OrganizationalUnit>): Promise<OrganizationalUnit> {
    this.initialize();
    const newUnit: OrganizationalUnit = {
      id: `unit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      name: unitData.name || '',
      type: unitData.type || 'department',
      parentId: unitData.parentId,
      location: unitData.location || '',
      country: unitData.country || '',
      region: unitData.region || '',
      headCount: unitData.headCount || 0,
      complianceScore: unitData.complianceScore || 0,
      riskLevel: unitData.riskLevel || 'medium',
      contactPerson: unitData.contactPerson || '',
      email: unitData.email || '',
      phone: unitData.phone,
      dataProcessingActivities: unitData.dataProcessingActivities || [],
      regulations: unitData.regulations || [],
      lastAssessment: new Date(),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.units.push(newUnit);
    return newUnit;
  }

  static async updateUnit(id: string, unitData: Partial<OrganizationalUnit>): Promise<OrganizationalUnit | null> {
    this.initialize();
    const index = this.units.findIndex(unit => unit.id === id);
    if (index === -1) return null;

    this.units[index] = {
      ...this.units[index],
      ...unitData,
      updatedAt: new Date()
    };

    return this.units[index];
  }

  static async deleteUnit(id: string): Promise<boolean> {
    this.initialize();
    const index = this.units.findIndex(unit => unit.id === id);
    if (index === -1) return false;

    this.units.splice(index, 1);
    return true;
  }

  // Data Flow Operations
  static async getAllDataFlows(): Promise<DataFlow[]> {
    this.initialize();
    return [...this.dataFlows];
  }

  static async getDataFlowsByUnit(unitId: string): Promise<DataFlow[]> {
    this.initialize();
    return this.dataFlows.filter(flow => flow.fromUnit === unitId || flow.toUnit === unitId);
  }

  // Metrics and Analytics
  static async getMetrics(): Promise<OrganizationalMappingMetrics> {
    this.initialize();
    
    const byType = this.units.reduce((acc, unit) => {
      acc[unit.type] = (acc[unit.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byRiskLevel = this.units.reduce((acc, unit) => {
      acc[unit.riskLevel] = (acc[unit.riskLevel] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const byRegion = this.units.reduce((acc, unit) => {
      acc[unit.region] = (acc[unit.region] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const averageComplianceScore = this.units.length > 0 
      ? this.units.reduce((sum, unit) => sum + unit.complianceScore, 0) / this.units.length 
      : 0;

    const totalHeadCount = this.units.reduce((sum, unit) => sum + unit.headCount, 0);
    const highRiskUnits = this.units.filter(unit => unit.riskLevel === 'high' || unit.riskLevel === 'critical').length;

    return {
      totalUnits: this.units.length,
      byType,
      byRiskLevel,
      byRegion,
      averageComplianceScore,
      totalHeadCount,
      totalDataFlows: this.dataFlows.length,
      highRiskUnits,
      lastUpdated: new Date()
    };
  }

  // Hierarchy and Relationships
  static async getUnitHierarchy(): Promise<OrganizationalUnit[]> {
    this.initialize();
    // Return units organized in hierarchical structure
    const rootUnits = this.units.filter(unit => !unit.parentId);
    const childUnits = this.units.filter(unit => unit.parentId);
    
    // Simple hierarchy - in a real implementation, this would be more sophisticated
    return [...rootUnits, ...childUnits];
  }

  // Search and Filtering
  static async searchUnits(query: string): Promise<OrganizationalUnit[]> {
    this.initialize();
    const lowercaseQuery = query.toLowerCase();
    return this.units.filter(unit => 
      unit.name.toLowerCase().includes(lowercaseQuery) ||
      unit.location.toLowerCase().includes(lowercaseQuery) ||
      unit.contactPerson.toLowerCase().includes(lowercaseQuery) ||
      unit.type.toLowerCase().includes(lowercaseQuery)
    );
  }

  static async filterUnits(filters: {
    type?: string[];
    riskLevel?: string[];
    region?: string[];
    complianceScoreMin?: number;
    complianceScoreMax?: number;
  }): Promise<OrganizationalUnit[]> {
    this.initialize();
    return this.units.filter(unit => {
      if (filters.type && filters.type.length > 0 && !filters.type.includes(unit.type)) {
        return false;
      }
      if (filters.riskLevel && filters.riskLevel.length > 0 && !filters.riskLevel.includes(unit.riskLevel)) {
        return false;
      }
      if (filters.region && filters.region.length > 0 && !filters.region.includes(unit.region)) {
        return false;
      }
      if (filters.complianceScoreMin !== undefined && unit.complianceScore < filters.complianceScoreMin) {
        return false;
      }
      if (filters.complianceScoreMax !== undefined && unit.complianceScore > filters.complianceScoreMax) {
        return false;
      }
      return true;
    });
  }
}
