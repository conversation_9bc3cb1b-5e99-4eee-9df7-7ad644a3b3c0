import React, { useState, useEffect } from 'react';
import { Modal } from '../UI/Modal';
import { FormInput, FormSelect, FormTextarea } from '../UI/FormInput';
import { Button } from '../UI/Button';
import { createValidator, validateRequired, validateEmail } from '../../utils/formValidation';
import { DataSubjectRequest, RequestType, RequestStatus, RequestPriority } from '../../types/gdprTypes';
import { useTheme } from '../../context/ThemeContext';

interface DataSubjectRequestModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (request: Partial<DataSubjectRequest>) => Promise<void>;
  request?: DataSubjectRequest | null;
  mode: 'create' | 'edit';
}

const requestTypeOptions = [
  { value: 'access', label: 'Data Access Request' },
  { value: 'rectification', label: 'Data Rectification' },
  { value: 'erasure', label: 'Data Erasure (Right to be Forgotten)' },
  { value: 'portability', label: 'Data Portability' },
  { value: 'restriction', label: 'Restriction of Processing' },
  { value: 'objection', label: 'Objection to Processing' }
];

const statusOptions = [
  { value: 'pending', label: 'Pending' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'cancelled', label: 'Cancelled' }
];

const priorityOptions = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
];

export const DataSubjectRequestModal: React.FC<DataSubjectRequestModalProps> = ({
  isOpen,
  onClose,
  onSave,
  request,
  mode
}) => {
  const { mode: themeMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  
  const [formData, setFormData] = useState({
    email: '',
    type: 'access' as RequestType,
    status: 'pending' as RequestStatus,
    priority: 'medium' as RequestPriority,
    description: '',
    notes: '',
    progress: 0
  });

  const validator = createValidator({
    email: { ...validateEmail(true) },
    type: { ...validateRequired() },
    status: { ...validateRequired() },
    priority: { ...validateRequired() },
    description: { required: true, minLength: 10, maxLength: 1000 }
  });

  useEffect(() => {
    if (request && mode === 'edit') {
      setFormData({
        email: request.email,
        type: request.type,
        status: request.status,
        priority: request.priority,
        description: request.description || '',
        notes: request.notes || '',
        progress: request.progress
      });
    } else {
      // Reset form for create mode
      setFormData({
        email: '',
        type: 'access',
        status: 'pending',
        priority: 'medium',
        description: '',
        notes: '',
        progress: 0
      });
    }
    setErrors({});
  }, [request, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validator.validate(formData);
    if (!validation.isValid) {
      const fieldErrors: any = {};
      validation.errors.forEach(error => {
        fieldErrors[error.field] = error.message;
      });
      setErrors(fieldErrors);
      return;
    }

    setLoading(true);
    try {
      const requestData: Partial<DataSubjectRequest> = {
        ...formData,
        ...(mode === 'edit' && request ? { id: request.id } : {}),
        submittedAt: mode === 'create' ? new Date() : request?.submittedAt,
        dueDate: mode === 'create' ? new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) : request?.dueDate // 30 days from now
      };

      await onSave(requestData);
      onClose();
    } catch (error) {
      console.error('Error saving request:', error);
      // Handle error - could show notification here
    } finally {
      setLoading(false);
    }
  };

  const getFieldError = (field: string) => errors[field] || null;

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create New Data Subject Request' : 'Edit Data Subject Request'}
      size="lg"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormInput
            id="email-address"
            label="Email Address"
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            error={getFieldError('email')}
            placeholder="<EMAIL>"
            required
            disabled={mode === 'edit'} // Don't allow email changes in edit mode
          />

          <FormSelect
            id="request-type"
            label="Request Type"
            value={formData.type}
            onChange={(e) => handleInputChange('type', e.target.value)}
            options={requestTypeOptions}
            error={getFieldError('type')}
            required
          />

          <FormSelect
            id="status"
            label="Status"
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            options={statusOptions}
            error={getFieldError('status')}
            required
          />

          <FormSelect
            id="priority"
            label="Priority"
            value={formData.priority}
            onChange={(e) => handleInputChange('priority', e.target.value)}
            options={priorityOptions}
            error={getFieldError('priority')}
            required
          />
        </div>

        {mode === 'edit' && (
          <div>
            <label htmlFor="progress" className="block text-sm font-medium text-text mb-2">
              Progress: {formData.progress}%
            </label>
            <input
              id="progress"
              type="range"
              min="0"
              max="100"
              value={formData.progress}
              onChange={(e) => handleInputChange('progress', parseInt(e.target.value))}
              className={`
                w-full h-2 rounded-lg appearance-none cursor-pointer
                ${themeMode === 'dark' ? 'bg-surface' : 'bg-surface'}
              `}
              style={{
                background: `linear-gradient(to right, #3b82f6 0%, #3b82f6 ${formData.progress}%, ${themeMode === 'dark' ? '#374151' : '#f3f4f6'} ${formData.progress}%, ${themeMode === 'dark' ? '#374151' : '#f3f4f6'} 100%)`
              }}
            />
          </div>
        )}

        <FormTextarea
          id="description"
          label="Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          error={getFieldError('description')}
          placeholder="Describe the data subject request in detail..."
          rows={4}
          required
        />

        <FormTextarea
          id="notes"
          label="Internal Notes"
          value={formData.notes}
          onChange={(e) => handleInputChange('notes', e.target.value)}
          error={getFieldError('notes')}
          placeholder="Add internal notes for processing this request..."
          rows={3}
          helperText="These notes are for internal use only and will not be visible to the data subject."
        />

        <div className="flex items-center gap-3 justify-end pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create Request' : 'Update Request'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
