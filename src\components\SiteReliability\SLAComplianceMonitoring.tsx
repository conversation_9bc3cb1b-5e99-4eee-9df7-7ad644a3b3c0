import React, { useState, useMemo } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { useSiteReliability } from '../../hooks/useSiteReliability';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Line, Doughnut } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  Activity,
  RefreshCw,
  Download,
  Settings,
  Filter,
  Search
} from 'lucide-react';

interface SLAComplianceMonitoringProps {
  className?: string;
}

export const SLAComplianceMonitoring: React.FC<SLAComplianceMonitoringProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const { 
    dashboardData, 
    slaTargets, 
    isLoading, 
    error, 
    refresh, 
    updateSLATarget,
    exportMetrics,
    generateReport 
  } = useSiteReliability();

  const [selectedCategory, setSelectedCategory] = useState<'all' | 'availability' | 'performance' | 'error_rate' | 'response_time'>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [isExporting, setIsExporting] = useState(false);
  const [isGeneratingReport, setIsGeneratingReport] = useState(false);

  const chartTheme = getChartTheme(mode === 'dark');

  // Filter SLA targets based on category and search
  const filteredSLATargets = useMemo(() => {
    if (!slaTargets) return [];
    
    return slaTargets.filter(target => {
      const matchesCategory = selectedCategory === 'all' || target.category === selectedCategory;
      const matchesSearch = target.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           target.description.toLowerCase().includes(searchTerm.toLowerCase());
      return matchesCategory && matchesSearch;
    });
  }, [slaTargets, selectedCategory, searchTerm]);

  // Calculate summary statistics
  const summaryStats = useMemo(() => {
    if (!slaTargets) return { total: 0, healthy: 0, warning: 0, critical: 0, averageCompliance: 0 };
    
    const total = slaTargets.length;
    const healthy = slaTargets.filter(t => t.status === 'healthy').length;
    const warning = slaTargets.filter(t => t.status === 'warning').length;
    const critical = slaTargets.filter(t => t.status === 'critical').length;
    const averageCompliance = slaTargets.reduce((sum, t) => sum + t.currentPercentage, 0) / total;
    
    return { total, healthy, warning, critical, averageCompliance };
  }, [slaTargets]);

  // Chart data for SLA compliance overview
  const complianceChartData = useMemo(() => {
    if (!slaTargets) return null;
    
    return {
      labels: ['Healthy', 'Warning', 'Critical'],
      datasets: [{
        data: [summaryStats.healthy, summaryStats.warning, summaryStats.critical],
        backgroundColor: [
          'rgba(52, 211, 153, 0.8)', // Green
          'rgba(251, 191, 36, 0.8)',  // Amber
          'rgba(248, 113, 113, 0.8)'  // Red
        ],
        borderColor: [
          'rgb(52, 211, 153)',
          'rgb(251, 191, 36)',
          'rgb(248, 113, 113)'
        ],
        borderWidth: 2,
      }]
    };
  }, [summaryStats]);

  // Chart data for SLA trends
  const trendChartData = useMemo(() => {
    if (!dashboardData?.trends.uptimeTrend) return null;
    
    const last7Days = dashboardData.trends.uptimeTrend.slice(-7);
    
    return {
      labels: last7Days.map(d => new Date(d.date).toLocaleDateString('en-US', { weekday: 'short' })),
      datasets: [{
        label: 'Average SLA Compliance',
        data: last7Days.map(d => d.uptime),
        borderColor: 'rgb(79, 142, 247)',
        backgroundColor: 'rgba(79, 142, 247, 0.1)',
        borderWidth: 2,
        fill: true,
        tension: 0.4,
        pointBackgroundColor: 'rgb(79, 142, 247)',
        pointBorderColor: '#ffffff',
        pointBorderWidth: 2,
        pointRadius: 4,
        pointHoverRadius: 6,
      }]
    };
  }, [dashboardData]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-amber-500" />;
      case 'critical':
        return <XCircle className="w-5 h-5 text-red-500" />;
      default:
        return <Clock className="w-5 h-5 text-gray-500" />;
    }
  };

  const getStatusColorClass = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'warning':
        return 'bg-amber-100 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400';
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const handleExport = async (format: 'csv' | 'pdf' | 'excel') => {
    setIsExporting(true);
    try {
      await exportMetrics(format, '30d');
    } finally {
      setIsExporting(false);
    }
  };

  const handleGenerateReport = async () => {
    setIsGeneratingReport(true);
    try {
      await generateReport('sla');
    } finally {
      setIsGeneratingReport(false);
    }
  };

  if (isLoading && !dashboardData) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <LoadingSkeleton className="h-64" />
            <LoadingSkeleton className="h-64" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load SLA Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={refresh}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors"
          >
            Try Again
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-primary/10 rounded-lg">
            <Shield className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-xl font-semibold text-text">SLA Compliance Monitoring</h2>
            <p className="text-sm text-text-secondary">
              Real-time monitoring of service level agreements and performance targets
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={refresh}
            disabled={isLoading}
            className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
            title="Refresh data"
          >
            <RefreshCw className={`w-5 h-5 ${isLoading ? 'animate-spin' : ''}`} />
          </button>
          
          <div className="relative">
            <button
              onClick={() => handleExport('csv')}
              disabled={isExporting}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-border/50 hover:bg-border text-text rounded-lg transition-colors"
            >
              <Download className="w-4 h-4" />
              {isExporting ? 'Exporting...' : 'Export'}
            </button>
          </div>
          
          <button
            onClick={handleGenerateReport}
            disabled={isGeneratingReport}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
          >
            <Activity className="w-4 h-4" />
            {isGeneratingReport ? 'Generating...' : 'Generate Report'}
          </button>
        </div>
      </div>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Total SLAs</p>
              <p className="text-2xl font-bold text-text">{summaryStats.total}</p>
            </div>
            <Shield className="w-8 h-8 text-primary" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Healthy</p>
              <p className="text-2xl font-bold text-green-500">{summaryStats.healthy}</p>
            </div>
            <CheckCircle className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Warning</p>
              <p className="text-2xl font-bold text-amber-500">{summaryStats.warning}</p>
            </div>
            <AlertTriangle className="w-8 h-8 text-amber-500" />
          </div>
        </div>
        
        <div className="bg-card rounded-lg p-4 border border-border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-text-secondary">Critical</p>
              <p className="text-2xl font-bold text-red-500">{summaryStats.critical}</p>
            </div>
            <XCircle className="w-8 h-8 text-red-500" />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* SLA Status Distribution */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">SLA Status Distribution</h3>
          {complianceChartData && (
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={complianceChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20,
                        usePointStyle: true,
                      }
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  }
                }}
              />
            </div>
          )}
        </div>

        {/* SLA Compliance Trend */}
        <div className="bg-card rounded-lg p-4 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">7-Day Compliance Trend</h3>
          {trendChartData && (
            <div className="h-64">
              <Line
                data={trendChartData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    },
                    tooltip: {
                      backgroundColor: chartTheme.tooltipBg,
                      titleColor: chartTheme.textColor,
                      bodyColor: chartTheme.textColor,
                      borderColor: chartTheme.borderColor,
                      borderWidth: 1,
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor,
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      },
                      min: 95,
                      max: 100
                    }
                  }
                }}
              />
            </div>
          )}
        </div>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
            <input
              type="text"
              placeholder="Search SLA targets..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-background border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
            />
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Filter className="w-4 h-4 text-text-secondary" />
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value as any)}
            className="px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary"
          >
            <option value="all">All Categories</option>
            <option value="availability">Availability</option>
            <option value="performance">Performance</option>
            <option value="error_rate">Error Rate</option>
            <option value="response_time">Response Time</option>
          </select>
        </div>
      </div>

      {/* SLA Targets List */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-text">SLA Targets</h3>

        {filteredSLATargets.length === 0 ? (
          <div className="text-center py-8">
            <Shield className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">No SLA targets found matching your criteria.</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 gap-4">
            {filteredSLATargets.map((target) => (
              <div key={target.id} className="bg-card rounded-lg p-4 border border-border hover:shadow-md transition-all duration-200">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      {getStatusIcon(target.status)}
                      <h4 className="text-lg font-semibold text-text">{target.name}</h4>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColorClass(target.status)}`}>
                        {target.status.charAt(0).toUpperCase() + target.status.slice(1)}
                      </span>
                    </div>

                    <p className="text-sm text-text-secondary mb-3">{target.description}</p>

                    <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                      <div>
                        <p className="text-xs text-text-secondary">Target</p>
                        <p className="text-sm font-semibold text-text">{target.targetPercentage}%</p>
                      </div>
                      <div>
                        <p className="text-xs text-text-secondary">Current</p>
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-semibold text-text">{target.currentPercentage.toFixed(2)}%</p>
                          {target.currentPercentage >= target.targetPercentage ? (
                            <TrendingUp className="w-4 h-4 text-green-500" />
                          ) : (
                            <TrendingDown className="w-4 h-4 text-red-500" />
                          )}
                        </div>
                      </div>
                      <div>
                        <p className="text-xs text-text-secondary">Breaches</p>
                        <p className="text-sm font-semibold text-text">{target.breachCount}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 ml-4">
                    <button
                      onClick={() => updateSLATarget(target.id, { enabled: true })}
                      className="p-2 text-text-secondary hover:text-text hover:bg-border/50 rounded-lg transition-colors"
                      title="Configure SLA"
                    >
                      <Settings className="w-4 h-4" />
                    </button>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="mt-4">
                  <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                    <span>Compliance Progress</span>
                    <span>{target.currentPercentage.toFixed(2)}% of {target.targetPercentage}%</span>
                  </div>
                  <div className="w-full bg-border rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        target.currentPercentage >= target.targetPercentage
                          ? 'bg-green-500'
                          : target.currentPercentage >= target.targetPercentage * 0.9
                          ? 'bg-amber-500'
                          : 'bg-red-500'
                      }`}
                      style={{
                        width: `${Math.min((target.currentPercentage / target.targetPercentage) * 100, 100)}%`
                      }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};
