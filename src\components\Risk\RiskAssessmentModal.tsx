import React, { useState, useEffect } from 'react';
import { Modal } from '../ui/Modal';
import { FormInput, FormSelect, FormTextarea } from '../ui/FormInput';
import { Button } from '../ui/Button';
import { createValidator, validateRequired } from '../../utils/formValidation';
import { RiskAssessment, RiskCategory, RiskLikelihood, RiskImpact } from '../../types/gdprTypes';
import { useTheme } from '../../context/ThemeContext';

interface RiskAssessmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (assessment: Partial<RiskAssessment>) => Promise<void>;
  assessment?: RiskAssessment | null;
  mode: 'create' | 'edit';
}

const categoryOptions = [
  { value: 'operational', label: 'Operational Risk' },
  { value: 'financial', label: 'Financial Risk' },
  { value: 'compliance', label: 'Compliance Risk' },
  { value: 'reputational', label: 'Reputational Risk' },
  { value: 'strategic', label: 'Strategic Risk' },
  { value: 'technology', label: 'Technology Risk' },
  { value: 'legal', label: 'Legal Risk' },
  { value: 'environmental', label: 'Environmental Risk' }
];

const statusOptions = [
  { value: 'draft', label: 'Draft' },
  { value: 'under_review', label: 'Under Review' },
  { value: 'approved', label: 'Approved' },
  { value: 'rejected', label: 'Rejected' },
  { value: 'archived', label: 'Archived' }
];

const likelihoodOptions = [
  { value: '1', label: 'Very Unlikely (1)' },
  { value: '2', label: 'Unlikely (2)' },
  { value: '3', label: 'Possible (3)' },
  { value: '4', label: 'Likely (4)' },
  { value: '5', label: 'Very Likely (5)' }
];

const impactOptions = [
  { value: '1', label: 'Negligible (1)' },
  { value: '2', label: 'Minor (2)' },
  { value: '3', label: 'Moderate (3)' },
  { value: '4', label: 'Major (4)' },
  { value: '5', label: 'Severe (5)' }
];

export const RiskAssessmentModal: React.FC<RiskAssessmentModalProps> = ({
  isOpen,
  onClose,
  onSave,
  assessment,
  mode
}) => {
  const { mode: themeMode } = useTheme();
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<any>({});
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'operational' as RiskCategory,
    likelihood: 1 as RiskLikelihood,
    impact: 1 as RiskImpact,
    status: 'draft' as 'draft' | 'under_review' | 'approved' | 'rejected' | 'archived',
    owner: '',
    assessor: '',
    reviewDate: '',
    nextReviewDate: '',
    tags: [] as string[]
  });

  const validator = createValidator({
    title: { ...validateRequired(), minLength: 5, maxLength: 200 },
    description: { ...validateRequired(), minLength: 20, maxLength: 1000 },
    category: { ...validateRequired() },
    owner: { ...validateRequired() },
    assessor: { ...validateRequired() },
    reviewDate: { ...validateRequired() },
    nextReviewDate: { ...validateRequired() }
  });

  useEffect(() => {
    if (assessment && mode === 'edit') {
      setFormData({
        title: assessment.title,
        description: assessment.description,
        category: assessment.category,
        likelihood: assessment.likelihood,
        impact: assessment.impact,
        status: assessment.status,
        owner: assessment.owner,
        assessor: assessment.assessor,
        reviewDate: assessment.reviewDate.toISOString().split('T')[0],
        nextReviewDate: assessment.nextReviewDate.toISOString().split('T')[0],
        tags: assessment.tags
      });
    } else {
      // Reset form for create mode
      const today = new Date().toISOString().split('T')[0];
      const nextYear = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];
      
      setFormData({
        title: '',
        description: '',
        category: 'operational',
        likelihood: 1,
        impact: 1,
        status: 'draft',
        owner: '',
        assessor: '',
        reviewDate: today,
        nextReviewDate: nextYear,
        tags: []
      });
    }
    setErrors({});
  }, [assessment, mode, isOpen]);

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors((prev: any) => ({ ...prev, [field]: null }));
    }
  };

  const calculateRiskScore = () => {
    return formData.likelihood * formData.impact;
  };

  const getRiskLevel = (score: number) => {
    if (score >= 20) return 'very_high';
    if (score >= 15) return 'high';
    if (score >= 8) return 'medium';
    return 'low';
  };

  const getRiskColor = (level: string) => {
    switch (level) {
      case 'very_high': return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-300';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-300';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validation = validator.validate(formData);
    if (!validation.isValid) {
      const fieldErrors: any = {};
      validation.errors.forEach(error => {
        fieldErrors[error.field] = error.message;
      });
      setErrors(fieldErrors);
      return;
    }

    setLoading(true);
    try {
      const assessmentData: Partial<RiskAssessment> = {
        ...formData,
        reviewDate: new Date(formData.reviewDate),
        nextReviewDate: new Date(formData.nextReviewDate),
        ...(mode === 'edit' && assessment ? { id: assessment.id } : {})
      };

      await onSave(assessmentData);
      onClose();
    } catch (error) {
      console.error('Error saving risk assessment:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFieldError = (field: string) => errors[field] || null;

  const riskScore = calculateRiskScore();
  const riskLevel = getRiskLevel(riskScore);

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={mode === 'create' ? 'Create Risk Assessment' : 'Edit Risk Assessment'}
      size="xl"
    >
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <FormInput
              label="Risk Title"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              error={getFieldError('title')}
              placeholder="Enter a descriptive title for the risk"
              required
            />
          </div>

          <FormSelect
            label="Risk Category"
            value={formData.category}
            onChange={(e) => handleInputChange('category', e.target.value)}
            options={categoryOptions}
            error={getFieldError('category')}
            required
          />

          <FormSelect
            label="Status"
            value={formData.status}
            onChange={(e) => handleInputChange('status', e.target.value)}
            options={statusOptions}
            error={getFieldError('status')}
            required
          />

          <FormInput
            label="Risk Owner"
            value={formData.owner}
            onChange={(e) => handleInputChange('owner', e.target.value)}
            error={getFieldError('owner')}
            placeholder="e.g. <EMAIL>"
            required
          />

          <FormInput
            label="Assessor"
            value={formData.assessor}
            onChange={(e) => handleInputChange('assessor', e.target.value)}
            error={getFieldError('assessor')}
            placeholder="Name of the person conducting the assessment"
            required
          />

          <FormInput
            label="Review Date"
            type="date"
            value={formData.reviewDate}
            onChange={(e) => handleInputChange('reviewDate', e.target.value)}
            error={getFieldError('reviewDate')}
            required
          />

          <FormInput
            label="Next Review Date"
            type="date"
            value={formData.nextReviewDate}
            onChange={(e) => handleInputChange('nextReviewDate', e.target.value)}
            error={getFieldError('nextReviewDate')}
            required
          />
        </div>

        <FormTextarea
          label="Risk Description"
          value={formData.description}
          onChange={(e) => handleInputChange('description', e.target.value)}
          error={getFieldError('description')}
          placeholder="Provide a detailed description of the risk, including potential causes and consequences..."
          rows={4}
          required
        />

        {/* Risk Scoring Section */}
        <div className={`p-6 ${themeMode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
          <h3 className="text-lg font-semibold text-text mb-4">Risk Scoring</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <FormSelect
              label="Likelihood"
              value={formData.likelihood.toString()}
              onChange={(e) => handleInputChange('likelihood', parseInt(e.target.value))}
              options={likelihoodOptions}
              required
            />

            <FormSelect
              label="Impact"
              value={formData.impact.toString()}
              onChange={(e) => handleInputChange('impact', parseInt(e.target.value))}
              options={impactOptions}
              required
            />
          </div>

          <div className="flex items-center justify-between p-4 bg-surface rounded-lg">
            <div>
              <span className="text-sm text-text-secondary">Risk Score:</span>
              <span className="ml-2 text-2xl font-bold text-text">{riskScore}</span>
              <div className="text-xs text-text-secondary mt-1">
                Likelihood {formData.likelihood} × Impact {formData.impact}
              </div>
            </div>
            <div>
              <span className={`px-3 py-1 rounded-full text-sm font-bold uppercase ${getRiskColor(riskLevel)}`}>
                {riskLevel.replace('_', ' ')}
              </span>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-3 justify-end pt-4 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            loading={loading}
            loadingText={mode === 'create' ? 'Creating...' : 'Updating...'}
          >
            {mode === 'create' ? 'Create Assessment' : 'Update Assessment'}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
