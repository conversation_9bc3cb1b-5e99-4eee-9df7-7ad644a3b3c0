import React, { useState, useEffect } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

interface ComponentTestResult {
  name: string;
  status: 'loading' | 'success' | 'error';
  error?: string;
  renderTime?: number;
}

export const ComponentDiagnosticTest: React.FC = () => {
  const [testResults, setTestResults] = useState<ComponentTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runDiagnostics = async () => {
    setIsRunning(true);
    setTestResults([]);

    const tests = [
      {
        name: 'Enhanced Compliance Metrics',
        test: async () => {
          const startTime = Date.now();
          try {
            // Dynamic import test
            const module = await import('../compliance/EnhancedComplianceMetrics');
            const Component = module.default || module.EnhancedComplianceMetrics;
            
            if (!Component) {
              throw new Error('Component not found in module exports');
            }

            // Test component instantiation
            const element = React.createElement(Component, {});
            const renderTime = Date.now() - startTime;
            
            return { status: 'success' as const, renderTime };
          } catch (error) {
            return { 
              status: 'error' as const, 
              error: (error as Error).message,
              renderTime: Date.now() - startTime
            };
          }
        }
      },
      {
        name: 'Security Overview Dashboard',
        test: async () => {
          const startTime = Date.now();
          try {
            // Dynamic import test
            const module = await import('../Security/SecurityOverviewDashboard');
            const Component = module.default || module.SecurityOverviewDashboard;
            
            if (!Component) {
              throw new Error('Component not found in module exports');
            }

            // Test component instantiation
            const element = React.createElement(Component, {});
            const renderTime = Date.now() - startTime;
            
            return { status: 'success' as const, renderTime };
          } catch (error) {
            return { 
              status: 'error' as const, 
              error: (error as Error).message,
              renderTime: Date.now() - startTime
            };
          }
        }
      }
    ];

    const results: ComponentTestResult[] = [];

    for (const test of tests) {
      // Set loading state
      results.push({ name: test.name, status: 'loading' });
      setTestResults([...results]);

      try {
        const result = await test.test();
        results[results.length - 1] = {
          name: test.name,
          ...result
        };
        setTestResults([...results]);
      } catch (error) {
        results[results.length - 1] = {
          name: test.name,
          status: 'error',
          error: (error as Error).message
        };
        setTestResults([...results]);
      }

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    setIsRunning(false);
  };

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Component Diagnostic Test</h1>
          <p className="text-text-secondary mb-4">
            This diagnostic test checks if components can be imported and instantiated without errors.
          </p>
          
          <button
            onClick={runDiagnostics}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning 
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed' 
                : 'bg-primary text-white hover:bg-primary/90'
            }`}
          >
            {isRunning ? 'Running Diagnostics...' : 'Run Diagnostic Tests'}
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-text">Diagnostic Results</h2>
            
            {testResults.map((result) => (
              <div 
                key={result.name}
                className={`p-4 rounded-lg border ${
                  result.status === 'success' 
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                    : result.status === 'error'
                    ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                    : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-text">{result.name}</h3>
                  <div className="flex items-center gap-2">
                    {result.renderTime && (
                      <span className="text-xs text-text-secondary">
                        {result.renderTime}ms
                      </span>
                    )}
                    <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                      result.status === 'success'
                        ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                        : result.status === 'error'
                        ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                        : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                    }`}>
                      {result.status.toUpperCase()}
                    </div>
                  </div>
                </div>
                
                {result.error && (
                  <div className="mt-2 p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                    <p className="text-sm text-red-700 dark:text-red-300 font-mono">
                      Error: {result.error}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="mt-8 space-y-6">
          <h2 className="text-xl font-semibold text-text">Live Render Tests</h2>
          <p className="text-text-secondary">
            These tests attempt to actually render the components to verify they work in practice.
          </p>
          
          <div className="space-y-6">
            <div className="bg-surface rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4">Enhanced Compliance Metrics</h3>
              <ErrorBoundary
                type="component"
                fallbackTitle="Enhanced Compliance Metrics Failed"
                fallbackMessage="The Enhanced Compliance Metrics component failed to render."
                onError={(error, errorInfo) => {
                  console.error('Enhanced Compliance Metrics Render Error:', error, errorInfo);
                }}
              >
                <React.Suspense fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2 text-text-secondary">Loading Enhanced Compliance Metrics...</span>
                  </div>
                }>
                  <ComponentRenderer componentPath="../compliance/EnhancedComplianceMetrics" />
                </React.Suspense>
              </ErrorBoundary>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4">Security Overview Dashboard</h3>
              <ErrorBoundary
                type="component"
                fallbackTitle="Security Overview Dashboard Failed"
                fallbackMessage="The Security Overview Dashboard component failed to render."
                onError={(error, errorInfo) => {
                  console.error('Security Overview Dashboard Render Error:', error, errorInfo);
                }}
              >
                <React.Suspense fallback={
                  <div className="flex items-center justify-center p-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2 text-text-secondary">Loading Security Overview Dashboard...</span>
                  </div>
                }>
                  <ComponentRenderer componentPath="../Security/SecurityOverviewDashboard" />
                </React.Suspense>
              </ErrorBoundary>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Helper component to dynamically render components
const ComponentRenderer: React.FC<{ componentPath: string }> = ({ componentPath }) => {
  const [Component, setComponent] = useState<React.ComponentType | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadComponent = async () => {
      try {
        const module = await import(componentPath);
        const LoadedComponent = module.default || module[Object.keys(module)[0]];
        setComponent(() => LoadedComponent);
      } catch (err) {
        setError((err as Error).message);
      }
    };

    loadComponent();
  }, [componentPath]);

  if (error) {
    return <div className="text-red-500 p-4">Failed to load component: {error}</div>;
  }

  if (!Component) {
    return <div className="text-text-secondary p-4">Loading component...</div>;
  }

  return (
    <div className="max-h-96 overflow-hidden">
      <Component />
    </div>
  );
};

export default ComponentDiagnosticTest;
