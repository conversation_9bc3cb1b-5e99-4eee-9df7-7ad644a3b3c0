import React from 'react';
import { useComplianceStandards } from '../context/ComplianceStandardsContext';
import { ComplianceByPolicy } from './ComplianceByPolicy';

const ComplianceFilteringDemo: React.FC = () => {
  const { 
    complianceStandards, 
    toggleStandard, 
    getEnabledStandards,
    isLoading 
  } = useComplianceStandards();

  if (isLoading) {
    return <div className="p-6">Loading compliance standards...</div>;
  }

  return (
    <div className="p-6 space-y-6">
      <div className="bg-card rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-4">Compliance Standards Filter Demo</h2>
        <p className="text-text-secondary mb-4">
          Toggle compliance standards below to see how the "Compliance by Policy" section filters policies.
        </p>
        
        {/* Compliance Standards Controls */}
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
          {Object.entries(complianceStandards).map(([key, enabled]) => {
            const displayNames: Record<string, string> = {
              gdprEnabled: 'GDPR',
              dpdpEnabled: 'DPDP',
              hipaaEnabled: 'HIPAA',
              sox: 'SOX',
              pci: 'PCI DSS',
              iso27001: 'ISO 27001',
              ccpa: 'CCPA',
              nist: 'NIST',
              fedramp: 'FedRAMP',
            };

            return (
              <label key={key} className="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={enabled}
                  onChange={(e) => toggleStandard(key as keyof typeof complianceStandards, e.target.checked)}
                  className="rounded border-border text-primary focus:ring-primary/50"
                />
                <span className="text-sm font-medium">{displayNames[key] || key}</span>
              </label>
            );
          })}
        </div>

        {/* Current Filter Status */}
        <div className="bg-surface rounded-lg p-4">
          <h3 className="font-medium mb-2">Current Filter Status:</h3>
          <p className="text-sm text-text-secondary">
            {getEnabledStandards().length > 0 
              ? `Filtering by: ${getEnabledStandards().join(', ')}`
              : 'No standards selected - showing all policies'
            }
          </p>
        </div>
      </div>

      {/* Compliance by Policy Component */}
      <ComplianceByPolicy />
    </div>
  );
};

export default ComplianceFilteringDemo;
