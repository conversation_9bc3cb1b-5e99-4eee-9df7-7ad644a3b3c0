import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { LoadingSkeleton } from '../Dashboard/LoadingSkeleton';
import { Bar, Doughnut, Line } from 'react-chartjs-2';
import { getChartTheme } from '../../utils/chartOptimizations';
import {
  Shield,
  AlertTriangle,
  RefreshCw,
  Server,
  Bell,
  Activity,
  TrendingUp,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface SecurityOverviewDashboardProps {
  className?: string;
}

const SecurityOverviewDashboard: React.FC<SecurityOverviewDashboardProps> = ({ className = '' }) => {
  const { mode } = useTheme();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const chartTheme = getChartTheme(mode === 'dark');

  useEffect(() => {
    const loadData = async () => {
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));
        setIsLoading(false);
      } catch (err) {
        setError('Failed to load security data');
        setIsLoading(false);
      }
    };
    loadData();
  }, []);

  const threatData = {
    labels: ['Detected', 'Investigating', 'Contained', 'Resolved'],
    datasets: [{
      data: [12, 8, 5, 25],
      backgroundColor: [
        'rgba(248, 113, 113, 0.8)',
        'rgba(251, 191, 36, 0.8)',
        'rgba(59, 130, 246, 0.8)',
        'rgba(52, 211, 153, 0.8)'
      ],
      borderColor: [
        'rgb(248, 113, 113)',
        'rgb(251, 191, 36)',
        'rgb(59, 130, 246)',
        'rgb(52, 211, 153)'
      ],
      borderWidth: 2
    }]
  };

  const securityTrendData = {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
    datasets: [{
      label: 'Security Score',
      data: [88, 85, 90, 87, 92, 89],
      borderColor: 'rgb(34, 197, 94)',
      backgroundColor: 'rgba(34, 197, 94, 0.1)',
      tension: 0.4
    }]
  };

  if (isLoading) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <LoadingSkeleton className="h-8 w-64" />
            <LoadingSkeleton className="h-10 w-32" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="bg-card rounded-lg p-4">
                <LoadingSkeleton className="h-4 w-20 mb-2" />
                <LoadingSkeleton className="h-8 w-16 mb-1" />
                <LoadingSkeleton className="h-3 w-24" />
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`bg-surface rounded-lg p-6 ${className}`}>
        <div className="text-center py-8">
          <XCircle className="w-12 h-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-text mb-2">Failed to Load Security Data</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-surface rounded-lg p-6 ${className}`}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="p-3 bg-primary/10 rounded-lg">
              <Shield className="w-8 h-8 text-primary" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-text">Security Overview Dashboard</h1>
              <p className="text-text-secondary">Real-time security monitoring and threat intelligence</p>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2 px-3 py-2 bg-card rounded-lg border border-border">
              <div className="w-2 h-2 rounded-full bg-green-500 animate-pulse" />
              <span className="text-xs text-text-secondary">Live</span>
            </div>
            <button className="flex items-center gap-2 px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors">
              <RefreshCw className="w-4 h-4" />
              Refresh
            </button>
          </div>
        </div>

        {/* Overview Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Security Score</p>
                <p className="text-2xl font-bold text-text">89.2</p>
              </div>
              <Shield className="w-8 h-8 text-primary" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Active Threats</p>
                <p className="text-2xl font-bold text-red-500">12</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Systems Monitored</p>
                <p className="text-2xl font-bold text-blue-500">156</p>
              </div>
              <Server className="w-8 h-8 text-blue-500" />
            </div>
          </div>
          
          <div className="bg-card rounded-lg p-4 border border-border">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Alerts (24h)</p>
                <p className="text-2xl font-bold text-amber-500">47</p>
              </div>
              <Bell className="w-8 h-8 text-amber-500" />
            </div>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="bg-card rounded-lg p-6 border border-border">
            <h3 className="text-lg font-semibold text-text mb-4">Threat Status Distribution</h3>
            <div className="h-64 flex items-center justify-center">
              <Doughnut
                data={threatData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      position: 'bottom',
                      labels: {
                        color: chartTheme.textColor,
                        padding: 20
                      }
                    }
                  }
                }}
              />
            </div>
          </div>

          <div className="bg-card rounded-lg p-6 border border-border">
            <h3 className="text-lg font-semibold text-text mb-4">Security Score Trend</h3>
            <div className="h-64">
              <Line
                data={securityTrendData}
                options={{
                  responsive: true,
                  maintainAspectRatio: false,
                  plugins: {
                    legend: {
                      display: false
                    }
                  },
                  scales: {
                    x: {
                      grid: {
                        color: chartTheme.gridColor
                      },
                      ticks: {
                        color: chartTheme.textSecondary
                      }
                    },
                    y: {
                      grid: {
                        color: chartTheme.gridColor
                      },
                      ticks: {
                        color: chartTheme.textSecondary,
                        callback: (value) => `${value}%`
                      }
                    }
                  }
                }}
              />
            </div>
          </div>
        </div>

        {/* Recent Threats */}
        <div className="bg-card rounded-lg p-6 border border-border">
          <h3 className="text-lg font-semibold text-text mb-4">Recent Security Threats</h3>
          <div className="space-y-3">
            {[
              { name: 'Malware Detection - Endpoint 192.168.1.45', severity: 'high', status: 'investigating', time: '2 hours ago' },
              { name: 'Suspicious Login Activity - Admin Account', severity: 'critical', status: 'contained', time: '4 hours ago' },
              { name: 'DDoS Attack Attempt - Web Server', severity: 'medium', status: 'resolved', time: '6 hours ago' },
              { name: 'Phishing Email Campaign Detected', severity: 'high', status: 'investigating', time: '8 hours ago' },
              { name: 'Unauthorized Access Attempt - Database', severity: 'critical', status: 'resolved', time: '12 hours ago' }
            ].map((threat, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-surface rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    threat.severity === 'critical' ? 'bg-red-500' :
                    threat.severity === 'high' ? 'bg-orange-500' :
                    'bg-yellow-500'
                  }`} />
                  <div>
                    <p className="font-medium text-text">{threat.name}</p>
                    <p className="text-sm text-text-secondary">{threat.time}</p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                    threat.status === 'resolved' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                      : threat.status === 'contained'
                      ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300'
                  }`}>
                    {threat.status.charAt(0).toUpperCase() + threat.status.slice(1)}
                  </div>

                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SecurityOverviewDashboard;
