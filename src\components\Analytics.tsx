import React, { useEffect, useRef, useMemo, useState, useCallback } from 'react';
import { Chart } from 'chart.js';
import { optimizedChartDefaults } from '../utils/chartOptimizations';
import { useTheme } from '../context/ThemeContext';
import { ComplianceErrorBoundary } from './compliance/ComplianceErrorBoundary';
import { TrendingUp, AlertTriangle, BarChart3 } from 'lucide-react';

// Type guard for chart data validation
const isValidChartData = (data: any): boolean => {
  return (
    data &&
    Array.isArray(data.labels) &&
    Array.isArray(data.datasets) &&
    data.datasets.every((dataset: any) =>
      dataset &&
      typeof dataset.label === 'string' &&
      Array.isArray(dataset.data) &&
      dataset.data.every((value: any) => typeof value === 'number' && !isNaN(value))
    )
  );
};

// Validate and sanitize chart data
const validateChartData = (data: any) => {
  if (!isValidChartData(data)) {
    console.warn('Analytics: Invalid chart data, using fallback');
    return {
      labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'],
      datasets: [
        {
          label: 'Europe',
          data: [34, 43, 66, 69, 58, 40, 78, 13],
          borderColor: '#10b981',
          backgroundColor: 'transparent',
          borderWidth: 2,
        },
        {
          label: 'UK',
          data: [21, 26, 29, 32, 47, 46, 50, 90],
          borderColor: '#ef4444',
          backgroundColor: 'transparent',
          borderWidth: 2,
        },
        {
          label: 'America',
          data: [19, 24, 28, 26, 38, 31, 19, 52],
          borderColor: '#f59e0b',
          backgroundColor: 'transparent',
          borderWidth: 2,
        }
      ]
    };
  }
  return data;
};

const AnalyticsContent: React.FC = () => {
  const { mode } = useTheme();
  const chartRef = useRef<HTMLCanvasElement>(null);
  const chartInstance = useRef<Chart | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chartData, setChartData] = useState<any>(null);

  // Initialize chart data with error handling
  useEffect(() => {
    try {
      const rawData = {
        labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'],
        datasets: [
          {
            label: 'Europe',
            data: [34, 43, 66, 69, 58, 40, 78, 13],
            borderColor: '#10b981', // Professional green for compliant
            backgroundColor: 'transparent',
            borderWidth: 2,
          },
          {
            label: 'UK',
            data: [21, 26, 29, 32, 47, 46, 50, 90],
            borderColor: '#ef4444', // Professional red for non-compliant
            backgroundColor: 'transparent',
            borderWidth: 2,
          },
          {
            label: 'America',
            data: [19, 24, 28, 26, 38, 31, 19, 52],
            borderColor: '#f59e0b', // Professional amber for pending
            backgroundColor: 'transparent',
            borderWidth: 2,
          }
        ]
      };

      const validatedData = validateChartData(rawData);
      setChartData(validatedData);
      setError(null);
    } catch (err) {
      console.error('Error initializing analytics data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const chartConfig = useMemo(() => {
    if (!chartData) return null;

    try {
      return {
        type: 'line' as const,
        data: chartData,
        options: {
          ...optimizedChartDefaults,
          plugins: {
            legend: { display: false },
            tooltip: {
              enabled: true,
              mode: 'index' as const,
              intersect: false,
              backgroundColor: mode === 'dark' ? '#1f2937' : '#ffffff',
              titleColor: mode === 'dark' ? '#f9fafb' : '#111827',
              bodyColor: mode === 'dark' ? '#e5e7eb' : '#374151',
              borderColor: mode === 'dark' ? '#374151' : '#e5e7eb',
              borderWidth: 1,
              padding: 12,
              usePointStyle: true,
              callbacks: {
                label: (context: any) => {
                  try {
                    return `${context.dataset.label}: ${context.parsed.y} Compliant`;
                  } catch (err) {
                    console.error('Error in tooltip callback:', err);
                    return 'Data unavailable';
                  }
                }
              }
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              title: {
                display: true,
                text: 'Compliance Rate',
                color: mode === 'dark' ? '#e2e8f0' : '#4b5563',
                font: {
                  size: 12,
                  weight: '500' as const
                }
              },
              grid: {
                color: mode === 'dark' ? 'rgba(255, 255, 255, 0.1)' : '#f1f5f9',
                borderDash: [2, 2]
              },
              ticks: {
                color: mode === 'dark' ? '#9ca3af' : '#6b7280',
                font: {
                  size: 11
                }
              }
            },
            x: {
              grid: {
                display: false
              },
              ticks: {
                color: mode === 'dark' ? '#9ca3af' : '#6b7280',
                font: {
                  size: 11
                }
              }
            }
          }
        }
      };
    } catch (err) {
      console.error('Error creating chart configuration:', err);
      return null;
    }
  }, [chartData, mode]);

  // Chart creation with comprehensive error handling
  useEffect(() => {
    if (!chartRef.current || !chartConfig || isLoading || error) {
      return;
    }

    try {
      // Cleanup existing chart instance
      if (chartInstance.current) {
        chartInstance.current.destroy();
        chartInstance.current = null;
      }

      const ctx = chartRef.current.getContext('2d');
      if (!ctx) {
        throw new Error('Failed to get 2D context from canvas');
      }

      // Configure canvas for better rendering
      ctx.imageSmoothingEnabled = true;
      ctx.imageSmoothingQuality = 'high';

      // Create new chart instance with error handling
      chartInstance.current = new Chart(ctx, {
        ...chartConfig,
        options: {
          ...chartConfig.options,
          animation: {
            duration: 1000,
            easing: 'easeInOutQuart' as const,
            onComplete: () => {
              console.log('Analytics chart animation completed');
            }
          },
          onHover: (event, elements) => {
            try {
              if (chartRef.current) {
                chartRef.current.style.cursor = elements.length > 0 ? 'pointer' : 'default';
              }
            } catch (err) {
              console.error('Error in chart hover handler:', err);
            }
          }
        }
      });

      console.log('Analytics chart created successfully');
    } catch (err) {
      console.error('Error creating analytics chart:', err);
      setError(err instanceof Error ? err.message : 'Failed to create chart');
    }

    // Cleanup function
    return () => {
      try {
        if (chartInstance.current) {
          chartInstance.current.destroy();
          chartInstance.current = null;
        }
      } catch (err) {
        console.error('Error cleaning up chart:', err);
      }
    };
  }, [chartConfig, isLoading, error]);

  // Loading state
  if (isLoading) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
        <div className="animate-pulse">
          <div className="mb-6">
            <div className="h-6 bg-surface rounded w-1/3 mb-2"></div>
            <div className="h-4 bg-surface rounded w-1/2"></div>
          </div>
          <div className="h-[400px] bg-surface rounded"></div>
          <div className="flex justify-center gap-8 mt-6">
            {[1, 2, 3].map((i) => (
              <div key={i} className="flex items-center gap-2">
                <div className="w-6 h-[2px] bg-surface rounded-full"></div>
                <div className="h-3 bg-surface rounded w-12"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
        <div className="text-center">
          <AlertTriangle className="w-8 h-8 text-red-500 mx-auto mb-3" />
          <h3 className="text-lg font-semibold text-text mb-2">Analytics Unavailable</h3>
          <p className="text-text-secondary mb-4">{error}</p>
          <button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              // Retry initialization
              setTimeout(() => {
                try {
                  const rawData = {
                    labels: ['2018', '2019', '2020', '2021', '2022', '2023', '2024', '2025'],
                    datasets: [
                      {
                        label: 'Europe',
                        data: [34, 43, 66, 69, 58, 40, 78, 13],
                        borderColor: '#10b981',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                      },
                      {
                        label: 'UK',
                        data: [21, 26, 29, 32, 47, 46, 50, 90],
                        borderColor: '#ef4444',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                      },
                      {
                        label: 'America',
                        data: [19, 24, 28, 26, 38, 31, 19, 52],
                        borderColor: '#f59e0b',
                        backgroundColor: 'transparent',
                        borderWidth: 2,
                      }
                    ]
                  };
                  const validatedData = validateChartData(rawData);
                  setChartData(validatedData);
                  setError(null);
                } catch (err) {
                  setError('Failed to retry loading analytics data');
                } finally {
                  setIsLoading(false);
                }
              }, 100);
            }}
            className="px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-card p-6 rounded-xl shadow-sm border border-border">
      <div className="flex items-center gap-2 mb-6">
        <BarChart3 className="w-5 h-5 text-primary" />
        <div>
          <h3 className="text-lg font-semibold text-text">Compliance Analytics</h3>
          <p className="text-sm text-text-secondary mt-1">Monthly compliance status distribution</p>
        </div>
      </div>

      <div
        className="h-[400px] relative"
        style={{
          contain: 'content',
          willChange: 'transform',
          isolation: 'isolate',
          backfaceVisibility: 'hidden'
        }}
      >
        {chartConfig ? (
          <canvas
            ref={chartRef}
            aria-label="Compliance analytics chart showing monthly compliance status distribution"
            role="img"
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <BarChart3 className="w-12 h-12 text-text-secondary mx-auto mb-2" />
              <p className="text-text-secondary">Chart data is loading...</p>
            </div>
          </div>
        )}
      </div>

      <div className="flex justify-center gap-8 mt-6">
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-green-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">Europe</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-red-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">UK</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-6 h-[2px] bg-amber-500 rounded-full"></div>
          <span className="text-xs text-text-secondary font-medium">America</span>
        </div>
      </div>
    </div>
  );
};

// Main component with error boundary
const Analytics: React.FC = () => {
  return (
    <ComplianceErrorBoundary
      componentName="Analytics"
      fallbackType="analytics"
    >
      <AnalyticsContent />
    </ComplianceErrorBoundary>
  );
};

export default React.memo(Analytics);