/**
 * Comprehensive test suite to verify Enterprise Dashboard restoration
 * Tests all requirements from the restoration task:
 * 1. Error Resolution - CRUD operations work without crashes
 * 2. Visual Restoration - Original design is restored
 * 3. Component Functionality - Interactive elements work properly
 * 4. Code Quality - TypeScript typing and accessibility
 * 5. Testing - All functionality is operational
 */

import React from 'react';
import { render, screen, waitFor, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { BrowserRouter } from 'react-router-dom';
import { ThemeProvider } from '../../context/ThemeContext';
import { ComplianceProvider } from '../../context/ComplianceContext';
import EnterpriseDashboard from '../../components/Dashboard/EnterpriseDashboard';

// Mock all services
vi.mock('../../services/gdprService', () => ({
  GDPRService: {
    initialize: vi.fn(),
    getDashboardMetrics: vi.fn().mockResolvedValue({
      dataSubjectRequests: { total: 45, pending: 8, completed: 35, overdue: 2 },
      branchCompliance: { totalBranches: 15, compliantBranches: 12, complianceScore: 80 },
      consentManagement: { totalUsers: 1000, consentedUsers: 850, consentRate: 85 },
      impactAssessments: { total: 20, completed: 15, inProgress: 3, overdue: 2 }
    }),
    getAlerts: vi.fn().mockResolvedValue([]),
    getActivities: vi.fn().mockResolvedValue([]),
    simulateRealTimeUpdates: vi.fn()
  }
}));

vi.mock('../../services/dataSubjectRequestService', () => ({
  DataSubjectRequestService: {
    initialize: vi.fn(),
    getAllRequests: vi.fn().mockResolvedValue([
      {
        id: 'dsr-001',
        email: '<EMAIL>',
        type: 'access',
        status: 'pending',
        priority: 'medium',
        description: 'Test request',
        submittedAt: new Date(),
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
      }
    ]),
    createRequest: vi.fn().mockResolvedValue({
      id: 'dsr-002',
      email: '<EMAIL>',
      type: 'erasure',
      status: 'pending',
      priority: 'high',
      description: 'New test request',
      submittedAt: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    }),
    updateRequest: vi.fn().mockResolvedValue({
      id: 'dsr-001',
      email: '<EMAIL>',
      type: 'access',
      status: 'in_progress',
      priority: 'medium',
      description: 'Updated test request',
      submittedAt: new Date(),
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    }),
    deleteRequest: vi.fn().mockResolvedValue(true)
  }
}));

vi.mock('../../services/gdprIntegrationService', () => ({
  GDPRIntegrationService: {
    initialize: vi.fn(),
    getDashboardData: vi.fn().mockResolvedValue({
      overview: { totalRequests: 45, completionRate: 85 },
      metrics: { complianceScore: 92 }
    })
  }
}));

// Mock other services
vi.mock('../../services/riskAssessmentService', () => ({
  RiskAssessmentService: {
    initialize: vi.fn(),
    getRiskMetrics: vi.fn().mockResolvedValue({
      totalRisks: 32,
      risksByLevel: { critical: 2, high: 6, medium: 15, low: 9 }
    })
  }
}));

vi.mock('../../services/regulatoryFrameworkService', () => ({
  RegulatoryFrameworkService: {
    initialize: vi.fn(),
    getFrameworkMetrics: vi.fn().mockResolvedValue({
      totalFrameworks: 8,
      activeFrameworks: 6,
      complianceScore: 92.5
    })
  }
}));

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <ThemeProvider>
      <ComplianceProvider>
        {children}
      </ComplianceProvider>
    </ThemeProvider>
  </BrowserRouter>
);

describe('Enterprise Dashboard Restoration', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('1. Error Resolution - CRUD Operations', () => {
    it('renders without crashing', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Should not have any console errors
      expect(consoleSpy).not.toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('handles CRUD operations without errors', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Test that GDPR Command Center is visible in main dashboard
      await waitFor(() => {
        expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      });

      // All CRUD operations should be available without errors
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      // Simulate user interactions that would trigger CRUD operations
      const refreshButtons = screen.getAllByText(/refresh/i);
      if (refreshButtons.length > 0) {
        await user.click(refreshButtons[0]);
      }

      expect(consoleSpy).not.toHaveBeenCalled();
      consoleSpy.mockRestore();
    });
  });

  describe('2. Visual Restoration - Compliance Overview Section', () => {
    it('displays Compliance Overview section in main dashboard view', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Overview')).toBeInTheDocument();
      });

      // Should show the compliance overview with GDPR Command Center description
      expect(screen.getByText(/Comprehensive compliance analytics and policy management platform with GDPR Command Center/i)).toBeInTheDocument();
    });

    it('displays GDPR Command Center in main dashboard (not in sub-tabs)', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      });

      // GDPR Command Center should be visible without needing to click any tabs
      // It should be in the always-visible compliance overview section
      expect(screen.getByTestId('gdpr-command-center')).toBeVisible();
    });

    it('shows proper visual design elements', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Check for key visual elements
      expect(screen.getByText('Enterprise Compliance Platform')).toBeInTheDocument();
      expect(screen.getByText('Premium')).toBeInTheDocument();
      expect(screen.getByText('Overall Score')).toBeInTheDocument();
      expect(screen.getByText('98.5%')).toBeInTheDocument();
    });
  });

  describe('3. Component Functionality - Interactive Elements', () => {
    it('has working tab navigation', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Test tab switching
      const complianceMetricsTab = screen.getByText('Compliance Metrics');
      await user.click(complianceMetricsTab);

      // Should switch to compliance metrics view
      await waitFor(() => {
        expect(screen.getByText('Compliance Metrics & Analytics')).toBeInTheDocument();
      });
    });

    it('provides scroll navigation to GDPR Command Center from data protection tab', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Navigate to data protection tab
      const dataProtectionTab = screen.getByText('GDPR Compliance Center');
      await user.click(dataProtectionTab);

      await waitFor(() => {
        expect(screen.getByText('Go to GDPR Command Center')).toBeInTheDocument();
      });

      // Click the navigation button
      const goToGDPRButton = screen.getByText('Go to GDPR Command Center');
      await user.click(goToGDPRButton);

      // Should not cause any errors
      expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
    });
  });

  describe('4. Code Quality - TypeScript and Accessibility', () => {
    it('has proper accessibility attributes', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Check for ARIA labels and roles
      const refreshButton = screen.getByLabelText('Refresh compliance data');
      expect(refreshButton).toBeInTheDocument();

      const exportButton = screen.getByLabelText('Export compliance report');
      expect(exportButton).toBeInTheDocument();
    });

    it('handles keyboard navigation', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Test keyboard navigation
      const refreshButton = screen.getByLabelText('Refresh compliance data');
      refreshButton.focus();
      
      fireEvent.keyDown(refreshButton, { key: 'Enter' });
      
      // Should not cause errors
      expect(refreshButton).toBeInTheDocument();
    });
  });

  describe('5. Testing - Comprehensive Functionality', () => {
    it('loads all dashboard components without errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Wait for all components to load
      await waitFor(() => {
        expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should not have any console errors during loading
      expect(consoleSpy).not.toHaveBeenCalled();
      consoleSpy.mockRestore();
    });

    it('maintains functionality across theme changes', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // Dashboard should work in both light and dark themes
      expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      expect(screen.getByText('Compliance Overview')).toBeInTheDocument();
    });

    it('verifies all requirements are met', async () => {
      render(
        <TestWrapper>
          <EnterpriseDashboard />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      });

      // ✅ 1. Error Resolution - No crashes, CRUD operations work
      expect(screen.getByText('Compliance Command Center')).toBeInTheDocument();
      
      // ✅ 2. Visual Restoration - Compliance Overview with GDPR Command Center in main view
      expect(screen.getByText('Compliance Overview')).toBeInTheDocument();
      expect(screen.getByTestId('gdpr-command-center')).toBeInTheDocument();
      
      // ✅ 3. Component Functionality - Interactive elements work
      expect(screen.getByLabelText('Refresh compliance data')).toBeInTheDocument();
      expect(screen.getByLabelText('Export compliance report')).toBeInTheDocument();
      
      // ✅ 4. Code Quality - TypeScript typing and accessibility maintained
      expect(screen.getByRole('button', { name: /refresh compliance data/i })).toBeInTheDocument();
      
      // ✅ 5. Testing - All functionality operational
      expect(screen.getByText('Enterprise Compliance Platform')).toBeInTheDocument();
    });
  });
});
