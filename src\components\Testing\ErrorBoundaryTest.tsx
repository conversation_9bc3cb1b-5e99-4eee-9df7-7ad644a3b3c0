import React, { useState } from 'react';
import { ErrorBoundary } from '../UI/ErrorBoundary';
import { AlertTriangle, Bug, Zap, Database, Network } from 'lucide-react';

// Test components that can throw errors
const ThrowErrorComponent: React.FC<{ errorType: string }> = ({ errorType }) => {
  switch (errorType) {
    case 'render':
      throw new Error('Render error: Component failed to render properly');
    case 'async':
      throw new Error('Async error: Failed to load data from API');
    case 'network':
      throw new Error('Network error: Unable to connect to server');
    case 'validation':
      throw new Error('Validation error: Invalid data format received');
    case 'permission':
      throw new Error('Permission error: Insufficient access rights');
    default:
      return <div>No error thrown</div>;
  }
};

const AsyncErrorComponent: React.FC = () => {
  const [shouldThrow, setShouldThrow] = useState(false);

  React.useEffect(() => {
    if (shouldThrow) {
      // Simulate async error
      setTimeout(() => {
        throw new Error('Async error: Failed to fetch user data');
      }, 1000);
    }
  }, [shouldThrow]);

  return (
    <div className="p-4 bg-card rounded-lg border border-border">
      <h3 className="text-lg font-semibold text-text mb-2">Async Error Test</h3>
      <p className="text-text-secondary mb-4">This component will throw an async error after 1 second</p>
      <button
        onClick={() => setShouldThrow(true)}
        className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-lg transition-colors"
      >
        Trigger Async Error
      </button>
    </div>
  );
};

const NetworkErrorComponent: React.FC = () => {
  const [loading, setLoading] = useState(false);

  const simulateNetworkError = async () => {
    setLoading(true);
    try {
      // Simulate network request that fails
      await new Promise((_, reject) => {
        setTimeout(() => {
          reject(new Error('Network error: Failed to connect to API endpoint'));
        }, 2000);
      });
    } catch (error) {
      setLoading(false);
      throw error;
    }
  };

  return (
    <div className="p-4 bg-card rounded-lg border border-border">
      <h3 className="text-lg font-semibold text-text mb-2">Network Error Test</h3>
      <p className="text-text-secondary mb-4">This component will simulate a network error</p>
      <button
        onClick={simulateNetworkError}
        disabled={loading}
        className="px-4 py-2 bg-orange-500 hover:bg-orange-600 text-white rounded-lg transition-colors disabled:opacity-50"
      >
        {loading ? 'Loading...' : 'Trigger Network Error'}
      </button>
    </div>
  );
};

// Main test component
export const ErrorBoundaryTest: React.FC = () => {
  const [errorType, setErrorType] = useState<string>('none');
  const [testSection, setTestSection] = useState<string>('component');

  const errorTypes = [
    { value: 'none', label: 'No Error', icon: '✅' },
    { value: 'render', label: 'Render Error', icon: '🔴' },
    { value: 'async', label: 'Async Error', icon: '⏱️' },
    { value: 'network', label: 'Network Error', icon: '🌐' },
    { value: 'validation', label: 'Validation Error', icon: '⚠️' },
    { value: 'permission', label: 'Permission Error', icon: '🔒' }
  ];

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Error Boundary Testing Suite</h1>
          <p className="text-text-secondary">
            Test different types of errors and verify that error boundaries handle them correctly.
          </p>
        </div>

        {/* Test Controls */}
        <div className="bg-surface rounded-lg p-6 border border-border mb-8">
          <h2 className="text-xl font-semibold text-text mb-4">Test Controls</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-text mb-2">Error Type</label>
              <select
                value={errorType}
                onChange={(e) => setErrorType(e.target.value)}
                className="w-full px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
              >
                {errorTypes.map((type) => (
                  <option key={type.value} value={type.value}>
                    {type.icon} {type.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-text mb-2">Test Section</label>
              <select
                value={testSection}
                onChange={(e) => setTestSection(e.target.value)}
                className="w-full px-3 py-2 bg-background border border-border rounded-lg text-text focus:outline-none focus:ring-2 focus:ring-primary/50"
              >
                <option value="component">Component Level</option>
                <option value="section">Section Level</option>
                <option value="page">Page Level</option>
              </select>
            </div>
          </div>
        </div>

        {/* Test Results */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Component Level Error Boundary */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-text flex items-center gap-2">
              <Bug className="w-5 h-5" />
              Component Level Error Boundary
            </h2>

            <ErrorBoundary
              type="component"
              fallbackTitle="Component Error"
              fallbackMessage="This component encountered an error and has been safely contained."
              onError={(error, errorInfo) => {
                console.log('Component Error Boundary caught:', error, errorInfo);
              }}
            >
              <div className="p-4 bg-card rounded-lg border border-border">
                <h3 className="text-lg font-semibold text-text mb-2">Test Component</h3>
                <p className="text-text-secondary mb-4">
                  Current error type: <strong>{errorType}</strong>
                </p>
                {testSection === 'component' && (
                  <ThrowErrorComponent errorType={errorType} />
                )}
                {testSection !== 'component' && (
                  <div className="text-text-secondary">Component level test inactive</div>
                )}
              </div>
            </ErrorBoundary>

            <ErrorBoundary
              type="component"
              fallbackTitle="Async Component Error"
              fallbackMessage="This async component encountered an error."
            >
              <AsyncErrorComponent />
            </ErrorBoundary>
          </div>

          {/* Section Level Error Boundary */}
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-text flex items-center gap-2">
              <Zap className="w-5 h-5" />
              Section Level Error Boundary
            </h2>

            <ErrorBoundary
              type="section"
              fallbackTitle="Section Unavailable"
              fallbackMessage="This section is temporarily unavailable due to an error."
              onError={(error, errorInfo) => {
                console.log('Section Error Boundary caught:', error, errorInfo);
              }}
            >
              <div className="p-6 bg-card rounded-lg border border-border min-h-[200px]">
                <h3 className="text-lg font-semibold text-text mb-2">Test Section</h3>
                <p className="text-text-secondary mb-4">
                  This section contains multiple components that could fail.
                </p>
                
                {testSection === 'section' && (
                  <div className="space-y-4">
                    <ThrowErrorComponent errorType={errorType} />
                    <div className="p-3 bg-background rounded border border-border">
                      <p className="text-text-secondary text-sm">
                        Other components in this section would continue to work normally.
                      </p>
                    </div>
                  </div>
                )}
                {testSection !== 'section' && (
                  <div className="text-text-secondary">Section level test inactive</div>
                )}
              </div>
            </ErrorBoundary>

            <ErrorBoundary
              type="component"
              fallbackTitle="Network Component Error"
              fallbackMessage="This network component encountered an error."
            >
              <NetworkErrorComponent />
            </ErrorBoundary>
          </div>
        </div>

        {/* Page Level Error Boundary Test */}
        <div className="mt-8">
          <h2 className="text-xl font-semibold text-text flex items-center gap-2 mb-4">
            <AlertTriangle className="w-5 h-5" />
            Page Level Error Boundary Test
          </h2>

          <div className="bg-surface rounded-lg p-6 border border-border">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="text-lg font-semibold text-text">Critical Error Simulation</h3>
                <p className="text-text-secondary">
                  This will test the page-level error boundary (use with caution)
                </p>
              </div>
              <div className="text-right">
                <div className="text-sm text-text-secondary mb-1">Status</div>
                <div className="text-sm font-semibold text-text">
                  {testSection === 'page' ? 'Active' : 'Inactive'}
                </div>
              </div>
            </div>

            {testSection === 'page' && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <AlertTriangle className="w-4 h-4 text-red-500" />
                  <span className="text-sm font-semibold text-red-700 dark:text-red-300">
                    Warning: Page Level Error Test Active
                  </span>
                </div>
                <p className="text-sm text-red-600 dark:text-red-400 mb-3">
                  This will trigger a page-level error that should be caught by the top-level error boundary.
                </p>
                <ThrowErrorComponent errorType={errorType} />
              </div>
            )}
          </div>
        </div>

        {/* Error Boundary Information */}
        <div className="mt-8 bg-surface rounded-lg p-6 border border-border">
          <h2 className="text-xl font-semibold text-text mb-4">Error Boundary Information</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="p-4 bg-card rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <Bug className="w-5 h-5 text-blue-500" />
                <h3 className="font-semibold text-text">Component Level</h3>
              </div>
              <p className="text-sm text-text-secondary">
                Catches errors in individual components. Provides retry functionality and detailed error information.
              </p>
            </div>

            <div className="p-4 bg-card rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <Zap className="w-5 h-5 text-orange-500" />
                <h3 className="font-semibold text-text">Section Level</h3>
              </div>
              <p className="text-sm text-text-secondary">
                Catches errors in dashboard sections. Allows other sections to continue working normally.
              </p>
            </div>

            <div className="p-4 bg-card rounded-lg border border-border">
              <div className="flex items-center gap-2 mb-2">
                <AlertTriangle className="w-5 h-5 text-red-500" />
                <h3 className="font-semibold text-text">Page Level</h3>
              </div>
              <p className="text-sm text-text-secondary">
                Catches critical errors that would crash the entire application. Provides navigation options.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorBoundaryTest;
