import React, { useState } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { DataSubjectRequest, WorkflowStep } from '../../types/compliance';
import { DataSubjectRequestService } from '../../services/dataSubjectRequestService';
import {
  X,
  User,
  Mail,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  MessageSquare,
  History,
  Play,
  Pause,
  Check,
  ArrowRight,
  Paperclip,
  Send
} from 'lucide-react';

interface DataSubjectRequestDetailProps {
  request: DataSubjectRequest;
  onClose: () => void;
  onUpdate: (updatedRequest: DataSubjectRequest) => void;
}

export const DataSubjectRequestDetail: React.FC<DataSubjectRequestDetailProps> = ({
  request,
  onClose,
  onUpdate
}) => {
  const { mode } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'workflow' | 'communications' | 'audit'>('overview');
  const [newMessage, setNewMessage] = useState('');
  const [processing, setProcessing] = useState(false);

  const handleAdvanceWorkflow = async (stepId: string, notes?: string) => {
    try {
      setProcessing(true);
      const updatedRequest = await DataSubjectRequestService.advanceWorkflow(request.id, stepId, notes);
      if (updatedRequest) {
        onUpdate(updatedRequest);
      }
    } catch (error) {
      console.error('Error advancing workflow:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleSendMessage = async () => {
    if (!newMessage.trim()) return;

    try {
      const updatedRequest = await DataSubjectRequestService.addCommunication(request.id, {
        type: 'internal_note',
        direction: 'outbound',
        content: newMessage,
        timestamp: new Date(),
        from: 'Current User',
        to: [request.email]
      });
      
      if (updatedRequest) {
        onUpdate(updatedRequest);
        setNewMessage('');
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      case 'in_progress': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30';
      case 'pending': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'rejected': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'cancelled': return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'text-red-600 bg-red-100 dark:bg-red-900/30';
      case 'high': return 'text-orange-600 bg-orange-100 dark:bg-orange-900/30';
      case 'medium': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/30';
      case 'low': return 'text-green-600 bg-green-100 dark:bg-green-900/30';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const currentStep = request.workflow.steps.find(step => step.status === 'in_progress');

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className={`w-full max-w-6xl max-h-[90vh] ${mode === 'dark' ? 'bg-surface' : 'bg-white'} rounded-2xl shadow-2xl overflow-hidden`}>
        {/* Header */}
        <div className={`p-6 border-b border-border ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-r from-surface to-card'}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className={`p-3 ${mode === 'dark' ? 'bg-surface' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
                <FileText className="w-6 h-6 text-primary" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-text capitalize">
                  {request.type.replace('_', ' ')} Request
                </h2>
                <p className="text-text-secondary">Request ID: {request.id}</p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <span className={`px-3 py-1 ${getStatusColor(request.status)} text-sm font-bold rounded-full uppercase`}>
                {request.status.replace('_', ' ')}
              </span>
              <span className={`px-3 py-1 ${getPriorityColor(request.priority)} text-sm font-bold rounded-full uppercase`}>
                {request.priority}
              </span>
              <button
                onClick={onClose}
                className={`p-2 hover:bg-surface rounded-lg transition-colors`}
              >
                <X className="w-5 h-5 text-text-secondary" />
              </button>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className={`px-6 border-b border-border ${mode === 'dark' ? 'bg-surface' : 'bg-card'}`}>
          <div className="flex space-x-8">
            {[
              { id: 'overview', label: 'Overview', icon: FileText },
              { id: 'workflow', label: 'Workflow', icon: Play },
              { id: 'communications', label: 'Communications', icon: MessageSquare },
              { id: 'audit', label: 'Audit Trail', icon: History }
            ].map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-4 px-2 border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-text-secondary hover:text-text'
                }`}
              >
                <tab.icon className="w-4 h-4" />
                {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Request Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                  <h3 className="text-lg font-semibold text-text mb-4">Request Information</h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <User className="w-4 h-4 text-text-secondary" />
                      <span className="text-text">{request.firstName} {request.lastName}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Mail className="w-4 h-4 text-text-secondary" />
                      <span className="text-text">{request.email}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Calendar className="w-4 h-4 text-text-secondary" />
                      <span className="text-text">Submitted: {formatDate(request.submittedAt)}</span>
                    </div>
                    <div className="flex items-center gap-3">
                      <Clock className="w-4 h-4 text-text-secondary" />
                      <span className="text-text">Due: {formatDate(request.dueDate)}</span>
                    </div>
                  </div>
                </div>

                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                  <h3 className="text-lg font-semibold text-text mb-4">Processing Details</h3>
                  <div className="space-y-3">
                    <div>
                      <span className="text-text-secondary">Assignee:</span>
                      <span className="text-text ml-2">{request.assignee}</span>
                    </div>
                    {request.reviewer && (
                      <div>
                        <span className="text-text-secondary">Reviewer:</span>
                        <span className="text-text ml-2">{request.reviewer}</span>
                      </div>
                    )}
                    <div>
                      <span className="text-text-secondary">Progress:</span>
                      <div className="flex items-center gap-2 mt-1">
                        <div className="flex-1 bg-border rounded-full h-2">
                          <div className="bg-primary h-2 rounded-full transition-all duration-500" style={{width: `${request.progress}%`}}></div>
                        </div>
                        <span className="text-sm font-semibold text-primary">{request.progress}%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                <h3 className="text-lg font-semibold text-text mb-3">Description</h3>
                <p className="text-text-secondary">{request.description}</p>
              </div>

              {/* Data Categories */}
              <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                <h3 className="text-lg font-semibold text-text mb-3">Data Categories</h3>
                <div className="flex flex-wrap gap-2">
                  {request.requestDetails.dataCategories.map((category, index) => (
                    <span
                      key={index}
                      className={`px-3 py-1 ${mode === 'dark' ? 'bg-surface' : 'bg-primary/10'} text-text border border-border rounded-full text-sm`}
                    >
                      {category}
                    </span>
                  ))}
                </div>
              </div>

              {/* Processing Purposes */}
              <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                <h3 className="text-lg font-semibold text-text mb-3">Processing Purposes</h3>
                <div className="flex flex-wrap gap-2">
                  {request.requestDetails.processingPurposes.map((purpose, index) => (
                    <span
                      key={index}
                      className={`px-3 py-1 ${mode === 'dark' ? 'bg-surface' : 'bg-secondary/10'} text-text border border-border rounded-full text-sm`}
                    >
                      {purpose}
                    </span>
                  ))}
                </div>
              </div>

              {/* Attachments */}
              {request.attachments.length > 0 && (
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                  <h3 className="text-lg font-semibold text-text mb-3">Attachments</h3>
                  <div className="space-y-2">
                    {request.attachments.map((attachment) => (
                      <div key={attachment.id} className="flex items-center gap-3 p-2 hover:bg-surface rounded-lg transition-colors">
                        <Paperclip className="w-4 h-4 text-text-secondary" />
                        <span className="text-text">{attachment.name}</span>
                        <span className="text-text-secondary text-sm">
                          ({(attachment.size / 1024).toFixed(1)} KB)
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'workflow' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-text">Workflow Progress</h3>
                <div className="text-sm text-text-secondary">
                  Step {request.workflow.currentStep} of {request.workflow.totalSteps}
                </div>
              </div>

              <div className="space-y-4">
                {request.workflow.steps.map((step, index) => (
                  <div
                    key={step.id}
                    className={`p-4 border rounded-lg transition-all duration-300 ${
                      step.status === 'completed'
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-900/20'
                        : step.status === 'in_progress'
                        ? 'border-blue-200 bg-blue-50 dark:border-blue-800 dark:bg-blue-900/20'
                        : 'border-border bg-surface'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                          step.status === 'completed'
                            ? 'bg-green-500 text-white'
                            : step.status === 'in_progress'
                            ? 'bg-blue-500 text-white'
                            : 'bg-gray-300 text-gray-600'
                        }`}>
                          {step.status === 'completed' ? (
                            <Check className="w-4 h-4" />
                          ) : step.status === 'in_progress' ? (
                            <Play className="w-4 h-4" />
                          ) : (
                            <Pause className="w-4 h-4" />
                          )}
                        </div>
                        <div>
                          <h4 className="font-semibold text-text">{step.name}</h4>
                          <p className="text-sm text-text-secondary">{step.description}</p>
                        </div>
                      </div>
                      
                      {step.status === 'in_progress' && (
                        <button
                          onClick={() => handleAdvanceWorkflow(step.id, 'Step completed')}
                          disabled={processing}
                          className={`flex items-center gap-2 px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors disabled:opacity-50`}
                        >
                          <ArrowRight className="w-4 h-4" />
                          Complete Step
                        </button>
                      )}
                    </div>

                    {step.notes && (
                      <div className="mt-3 p-3 bg-surface rounded-lg">
                        <p className="text-sm text-text-secondary">{step.notes}</p>
                      </div>
                    )}

                    <div className="mt-3 flex items-center gap-4 text-xs text-text-secondary">
                      <span>Assignee: {step.assignee}</span>
                      <span>Est. Duration: {step.estimatedDuration}h</span>
                      {step.startedAt && (
                        <span>Started: {formatDate(step.startedAt)}</span>
                      )}
                      {step.completedAt && (
                        <span>Completed: {formatDate(step.completedAt)}</span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'communications' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-text">Communications</h3>
              </div>

              {/* Add new message */}
              <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                <div className="flex gap-3">
                  <textarea
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    placeholder="Add a note or communication..."
                    className={`flex-1 p-3 bg-surface border border-border rounded-lg text-text placeholder-text-secondary focus:outline-none focus:ring-2 focus:ring-primary/50 focus:border-primary resize-none`}
                    rows={3}
                  />
                  <button
                    onClick={handleSendMessage}
                    disabled={!newMessage.trim()}
                    className={`px-4 py-2 bg-primary hover:bg-primary-hover text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed`}
                  >
                    <Send className="w-4 h-4" />
                  </button>
                </div>
              </div>

              {/* Communications list */}
              <div className="space-y-4">
                {request.communications.length === 0 ? (
                  <div className="text-center py-8">
                    <MessageSquare className="w-12 h-12 text-text-secondary mx-auto mb-4" />
                    <p className="text-text-secondary">No communications yet</p>
                  </div>
                ) : (
                  request.communications.map((comm) => (
                    <div key={comm.id} className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-semibold text-text">{comm.from}</span>
                          <span className={`px-2 py-1 text-xs rounded-full ${
                            comm.direction === 'inbound' 
                              ? 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                              : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          }`}>
                            {comm.direction}
                          </span>
                          <span className={`px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300`}>
                            {comm.type}
                          </span>
                        </div>
                        <span className="text-sm text-text-secondary">{formatDate(comm.timestamp)}</span>
                      </div>
                      {comm.subject && (
                        <h4 className="font-medium text-text mb-2">{comm.subject}</h4>
                      )}
                      <p className="text-text-secondary">{comm.content}</p>
                    </div>
                  ))
                )}
              </div>
            </div>
          )}

          {activeTab === 'audit' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-text">Audit Trail</h3>
              
              <div className="space-y-4">
                {request.auditTrail.map((entry) => (
                  <div key={entry.id} className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg`}>
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <span className="font-semibold text-text">{entry.userName}</span>
                        <span className="text-sm text-text-secondary">({entry.userId})</span>
                      </div>
                      <span className="text-sm text-text-secondary">{formatDate(entry.timestamp)}</span>
                    </div>
                    <div className="flex items-center gap-2 mb-2">
                      <span className={`px-2 py-1 text-xs rounded-full bg-primary/20 text-primary`}>
                        {entry.action}
                      </span>
                    </div>
                    <p className="text-text-secondary">{entry.details}</p>
                    {entry.previousValue && entry.newValue && (
                      <div className="mt-2 text-xs text-text-secondary">
                        <span>Changed from: </span>
                        <code className="bg-red-100 dark:bg-red-900/30 px-1 rounded">{JSON.stringify(entry.previousValue)}</code>
                        <span> to: </span>
                        <code className="bg-green-100 dark:bg-green-900/30 px-1 rounded">{JSON.stringify(entry.newValue)}</code>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
