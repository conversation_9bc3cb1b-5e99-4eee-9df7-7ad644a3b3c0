/**
 * Production-Ready TypeScript Types and Interfaces
 * Comprehensive type definitions for Enterprise Dashboard components
 */

// Base types for all components
export interface BaseComponentProps {
  className?: string;
  'data-testid'?: string;
  'aria-label'?: string;
  'aria-describedby'?: string;
  role?: string;
}

// Performance monitoring types
export interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  mountTime: number;
  updateTime: number;
  memoryUsage?: number;
  errorCount: number;
  retryCount: number;
}

export interface MonitoringHooks {
  onComponentMount: (componentName: string, metrics: Partial<PerformanceMetrics>) => void;
  onComponentUpdate: (componentName: string, metrics: Partial<PerformanceMetrics>) => void;
  onComponentError: (componentName: string, error: Error, metrics: Partial<PerformanceMetrics>) => void;
  onComponentUnmount: (componentName: string, metrics: Partial<PerformanceMetrics>) => void;
}

// Accessibility types
export interface AccessibilityProps {
  'aria-label'?: string;
  'aria-labelledby'?: string;
  'aria-describedby'?: string;
  'aria-expanded'?: boolean;
  'aria-selected'?: boolean;
  'aria-current'?: boolean | 'page' | 'step' | 'location' | 'date' | 'time';
  'aria-hidden'?: boolean;
  'aria-live'?: 'off' | 'polite' | 'assertive';
  'aria-atomic'?: boolean;
  'aria-busy'?: boolean;
  'aria-controls'?: string;
  'aria-owns'?: string;
  role?: string;
  tabIndex?: number;
}

// Enhanced component props with production features
export interface ProductionComponentProps extends BaseComponentProps, AccessibilityProps {
  monitoring?: MonitoringHooks;
  performanceMode?: 'development' | 'production' | 'testing';
  enableAnalytics?: boolean;
  enableErrorReporting?: boolean;
  enablePerformanceMonitoring?: boolean;
  fallbackComponent?: React.ComponentType<any>;
  loadingComponent?: React.ComponentType<any>;
  errorComponent?: React.ComponentType<{ error: Error; retry: () => void }>;
}

// Data loading states
export type LoadingState = 'idle' | 'loading' | 'success' | 'error' | 'retrying';

export interface DataState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
  retryCount: number;
  state: LoadingState;
}

// API response types
export interface ApiResponse<T = any> {
  data: T;
  success: boolean;
  message?: string;
  errors?: string[];
  metadata?: {
    timestamp: string;
    requestId: string;
    version: string;
  };
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Error types
export interface ApplicationError {
  id: string;
  message: string;
  code: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: Date;
  component?: string;
  stack?: string;
  context?: Record<string, any>;
  userId?: string;
  sessionId?: string;
}

export interface ErrorBoundaryInfo {
  componentName: string;
  errorId: string;
  retryCount: number;
  maxRetries: number;
  canRetry: boolean;
  level: 'page' | 'section' | 'component';
}

// Theme and styling types
export interface ThemeColors {
  primary: string;
  secondary: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  background: string;
  surface: string;
  text: string;
  textSecondary: string;
  border: string;
}

export interface ResponsiveBreakpoints {
  xs: number;
  sm: number;
  md: number;
  lg: number;
  xl: number;
  '2xl': number;
}

export interface ThemeConfig {
  mode: 'light' | 'dark' | 'auto';
  colors: ThemeColors;
  breakpoints: ResponsiveBreakpoints;
  spacing: Record<string, string>;
  typography: Record<string, any>;
  animations: Record<string, any>;
}

// Dashboard specific types
export interface DashboardTab {
  id: string;
  name: string;
  label: string;
  icon: React.ComponentType<any>;
  component: React.ComponentType<any>;
  isActive: boolean;
  isDisabled?: boolean;
  badge?: string | number;
  'aria-label'?: string;
}

export interface DashboardSection {
  id: string;
  title: string;
  description?: string;
  component: React.ComponentType<any>;
  isVisible: boolean;
  isLoading: boolean;
  error?: string;
  lastUpdated?: Date;
  refreshInterval?: number;
  priority: 'high' | 'medium' | 'low';
}

export interface DashboardConfig {
  title: string;
  description?: string;
  tabs: DashboardTab[];
  sections: DashboardSection[];
  refreshInterval: number;
  enableRealTimeUpdates: boolean;
  enableNotifications: boolean;
  theme: ThemeConfig;
  accessibility: {
    enableKeyboardNavigation: boolean;
    enableScreenReader: boolean;
    enableHighContrast: boolean;
    enableReducedMotion: boolean;
  };
}

// Chart and visualization types
export interface ChartDataPoint {
  x: string | number | Date;
  y: number;
  label?: string;
  color?: string;
  metadata?: Record<string, any>;
}

export interface ChartConfig {
  type: 'line' | 'bar' | 'doughnut' | 'radar' | 'pie' | 'area';
  data: ChartDataPoint[];
  options: Record<string, any>;
  responsive: boolean;
  maintainAspectRatio: boolean;
  accessibility: {
    description: string;
    summary: string;
  };
}

// Form and input types
export interface FormFieldConfig {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'select' | 'textarea' | 'checkbox' | 'radio';
  required: boolean;
  placeholder?: string;
  helpText?: string;
  validation?: {
    pattern?: RegExp;
    minLength?: number;
    maxLength?: number;
    min?: number;
    max?: number;
    custom?: (value: any) => string | null;
  };
  accessibility: {
    'aria-label': string;
    'aria-describedby'?: string;
    'aria-required': boolean;
    'aria-invalid'?: boolean;
  };
}

// Navigation types
export interface NavigationItem {
  id: string;
  label: string;
  path: string;
  icon: React.ComponentType<any>;
  isActive: boolean;
  isDisabled?: boolean;
  children?: NavigationItem[];
  badge?: string | number;
  'aria-label': string;
  'aria-current'?: boolean | 'page';
}

export interface NavigationConfig {
  items: NavigationItem[];
  isCollapsed: boolean;
  enableKeyboardNavigation: boolean;
  enableTooltips: boolean;
  position: 'left' | 'right' | 'top' | 'bottom';
}

// Security and compliance types
export interface SecurityMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  status: 'healthy' | 'warning' | 'critical';
  trend: 'up' | 'down' | 'stable';
  lastUpdated: Date;
  description: string;
}

export interface ComplianceFramework {
  id: string;
  name: string;
  version: string;
  status: 'compliant' | 'partial' | 'non-compliant';
  score: number;
  requirements: {
    total: number;
    met: number;
    pending: number;
    failed: number;
  };
  lastAssessment: Date;
}

// User and session types
export interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  permissions: string[];
  preferences: {
    theme: 'light' | 'dark' | 'auto';
    language: string;
    timezone: string;
    notifications: boolean;
    accessibility: {
      highContrast: boolean;
      reducedMotion: boolean;
      screenReader: boolean;
    };
  };
}

export interface Session {
  id: string;
  userId: string;
  startTime: Date;
  lastActivity: Date;
  isActive: boolean;
  metadata: {
    userAgent: string;
    ipAddress: string;
    location?: string;
  };
}

// Analytics and tracking types
export interface AnalyticsEvent {
  name: string;
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties?: Record<string, any>;
  timestamp: Date;
  userId?: string;
  sessionId?: string;
}

export interface PerformanceEntry {
  name: string;
  entryType: string;
  startTime: number;
  duration: number;
  metadata?: Record<string, any>;
}

// Export utility types
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

export type ComponentWithProps<T = {}> = React.ComponentType<T & ProductionComponentProps>;
