import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render, renderWithTheme } from '../utils/test-utils';
import { GDPRCommandCenter } from '../../components/GDPR/GDPRCommandCenter';
import { BranchDetectionDashboard } from '../../components/GDPR/BranchDetectionDashboard';
import { ConsentManagementDashboard } from '../../components/GDPR/ConsentManagementDashboard';
import { ImpactAssessmentDashboard } from '../../components/GDPR/ImpactAssessmentDashboard';
import { DataSubjectRequestModal } from '../../components/GDPR/DataSubjectRequestModal';
import { ThemeProvider, useTheme } from '../../context/ThemeContext';

// Mock services
vi.mock('../../services/gdprService');
vi.mock('../../services/dataSubjectRequestService');
vi.mock('../../services/branchDetectionService');
vi.mock('../../services/consentManagementService');
vi.mock('../../services/impactAssessmentService');

// Theme test component
const ThemeTestComponent: React.FC = () => {
  const { mode, toggleMode } = useTheme();
  
  return (
    <div>
      <div data-testid="current-theme">{mode}</div>
      <button onClick={toggleMode} data-testid="theme-toggle">
        Toggle Theme
      </button>
      <div className={`${mode === 'dark' ? 'bg-surface text-text' : 'bg-white text-black'} p-4`}>
        Theme-aware content
      </div>
    </div>
  );
};

const setupMockServices = () => {
  const mockData = {
    metrics: {
      dataSubjectRequests: { total: 100, pending: 20, inProgress: 30, completed: 50, overdue: 5, averageProcessingTime: 15, completionRate: 85 },
      branchCompliance: { totalBranches: 10, compliantBranches: 8, complianceScore: 80, crossBorderTransfers: 3, highRiskBranches: 2 },
      consentManagement: { totalUsers: 5000, consentedUsers: 4000, consentRate: 80, withdrawalRate: 5, categoryBreakdown: [] },
      impactAssessments: { total: 25, completed: 20, inProgress: 3, overdue: 2, averageRiskScore: 65, highRiskAssessments: 5 }
    },
    requests: [{ id: '1', email: '<EMAIL>', type: 'access', status: 'pending', priority: 'high', description: 'Test', createdAt: new Date().toISOString() }],
    branches: [{ id: '1', name: 'Test Branch', location: 'Test Location', country: 'Test Country', complianceScore: 85, riskLevel: 'medium', dataFlows: [] }],
    consentRecords: [{ id: '1', userId: 'user1', email: '<EMAIL>', consentGiven: true, consentDate: new Date().toISOString(), categories: ['marketing'] }],
    assessments: [{ id: '1', title: 'Test Assessment', description: 'Test description', status: 'completed', riskScore: 70, type: 'DPIA', createdAt: new Date().toISOString() }]
  };

  // Setup all service mocks
  vi.doMock('../../services/gdprService', () => ({
    GDPRService: {
      getDashboardMetrics: vi.fn().mockResolvedValue(mockData.metrics),
      getAlerts: vi.fn().mockResolvedValue([]),
      getActivities: vi.fn().mockResolvedValue([])
    }
  }));

  vi.doMock('../../services/dataSubjectRequestService', () => ({
    DataSubjectRequestService: {
      getAllRequests: vi.fn().mockResolvedValue(mockData.requests)
    }
  }));

  vi.doMock('../../services/branchDetectionService', () => ({
    BranchDetectionService: {
      getAllBranches: vi.fn().mockResolvedValue(mockData.branches),
      getMetrics: vi.fn().mockResolvedValue({ totalBranches: 1, compliantBranches: 1, complianceScore: 85, crossBorderTransfers: 0, highRiskBranches: 0 })
    }
  }));

  vi.doMock('../../services/consentManagementService', () => ({
    ConsentManagementService: {
      getAllConsentRecords: vi.fn().mockResolvedValue(mockData.consentRecords),
      getMetrics: vi.fn().mockResolvedValue({ totalUsers: 1, consentedUsers: 1, consentRate: 100, withdrawalRate: 0, categoryBreakdown: [] })
    }
  }));

  vi.doMock('../../services/impactAssessmentService', () => ({
    ImpactAssessmentService: {
      getAllAssessments: vi.fn().mockResolvedValue(mockData.assessments),
      getMetrics: vi.fn().mockResolvedValue({ total: 1, completed: 1, inProgress: 0, overdue: 0, averageRiskScore: 70, highRiskAssessments: 0 })
    }
  }));

  return mockData;
};

describe('Theme Integration Tests', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    setupMockServices();
  });

  describe('Theme Context Integration', () => {
    it('provides theme context to all components', () => {
      render(<ThemeTestComponent />);
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
      expect(screen.getByTestId('theme-toggle')).toBeInTheDocument();
    });

    it('toggles theme across the application', async () => {
      render(<ThemeTestComponent />);
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
      
      const toggleButton = screen.getByTestId('theme-toggle');
      await user.click(toggleButton);
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      
      await user.click(toggleButton);
      expect(screen.getByTestId('current-theme')).toHaveTextContent('light');
    });

    it('persists theme preference', async () => {
      const { rerender } = render(<ThemeTestComponent />);
      
      const toggleButton = screen.getByTestId('theme-toggle');
      await user.click(toggleButton);
      
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
      
      // Simulate component remount
      rerender(<ThemeTestComponent />);
      
      // Theme should persist (in real app, this would be from localStorage)
      expect(screen.getByTestId('current-theme')).toHaveTextContent('dark');
    });
  });

  describe('Dashboard Components Theme Consistency', () => {
    it('applies theme consistently across GDPR Command Center', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Check for theme-aware classes (these would be applied by the theme context)
      const mainContainer = screen.getByText('GDPR Command Center').closest('div');
      expect(mainContainer).toBeInTheDocument();
    });

    it('maintains theme consistency when navigating between sections', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Navigate to different sections and verify theme consistency
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      const branchTab = screen.getByText('Branch Detection');
      await user.click(branchTab);
      
      await waitFor(() => {
        expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      });

      // All sections should maintain consistent theming
      const dashboardContent = screen.getByText('Branch Detection & Data Flow').closest('div');
      expect(dashboardContent).toBeInTheDocument();
    });

    it('applies dark theme correctly to all dashboard components', async () => {
      renderWithTheme(<GDPRCommandCenter />, 'dark');
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Verify dark theme is applied
      const container = screen.getByText('GDPR Command Center').closest('div');
      expect(container).toBeInTheDocument();
    });

    it('applies light theme correctly to all dashboard components', async () => {
      renderWithTheme(<GDPRCommandCenter />, 'light');
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Verify light theme is applied
      const container = screen.getByText('GDPR Command Center').closest('div');
      expect(container).toBeInTheDocument();
    });
  });

  describe('Individual Component Theme Integration', () => {
    it('applies theme consistently to Branch Detection Dashboard', async () => {
      renderWithTheme(<BranchDetectionDashboard />, 'dark');
      
      await waitFor(() => {
        expect(screen.getByText('Branch Detection & Data Flow')).toBeInTheDocument();
      });

      // Component should render without theme-related errors
      expect(screen.getByText('Monitor organizational branches and cross-border data transfers')).toBeInTheDocument();
    });

    it('applies theme consistently to Consent Management Dashboard', async () => {
      renderWithTheme(<ConsentManagementDashboard />, 'dark');
      
      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      });

      expect(screen.getByText('Manage user consents and privacy preferences')).toBeInTheDocument();
    });

    it('applies theme consistently to Impact Assessment Dashboard', async () => {
      renderWithTheme(<ImpactAssessmentDashboard />, 'dark');
      
      await waitFor(() => {
        expect(screen.getByText('Impact Assessments (DPIA)')).toBeInTheDocument();
      });

      expect(screen.getByText('Manage Data Protection Impact Assessments and risk evaluations')).toBeInTheDocument();
    });
  });

  describe('Modal Theme Integration', () => {
    it('applies theme consistently to modals', async () => {
      const mockOnClose = vi.fn();
      const mockOnSave = vi.fn().mockResolvedValue(undefined);

      renderWithTheme(
        <DataSubjectRequestModal
          isOpen={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="create"
        />,
        'dark'
      );

      expect(screen.getByText('Create New Data Subject Request')).toBeInTheDocument();
      
      // Modal should render with dark theme
      const modal = screen.getByRole('dialog');
      expect(modal).toBeInTheDocument();
    });

    it('maintains theme when opening modals from dashboard', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Navigate to requests
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // Open modal
      const createButton = screen.getByRole('button', { name: /create request/i });
      await user.click(createButton);
      
      // Modal should inherit theme from parent
      expect(screen.getByText('Create New Data Subject Request')).toBeInTheDocument();
    });
  });

  describe('Chart and Visualization Theme Integration', () => {
    it('applies theme to chart components', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Charts should be rendered with theme-aware styling
      const charts = screen.getAllByTestId(/chart/);
      expect(charts.length).toBeGreaterThan(0);
    });

    it('updates chart colors when theme changes', async () => {
      const ChartThemeTest: React.FC = () => {
        const { mode, toggleMode } = useTheme();
        
        return (
          <div>
            <button onClick={toggleMode} data-testid="toggle-theme">Toggle</button>
            <div data-testid="line-chart" data-theme={mode}>Chart</div>
          </div>
        );
      };

      render(<ChartThemeTest />);
      
      const chart = screen.getByTestId('line-chart');
      expect(chart).toHaveAttribute('data-theme', 'light');
      
      const toggleButton = screen.getByTestId('toggle-theme');
      await user.click(toggleButton);
      
      expect(chart).toHaveAttribute('data-theme', 'dark');
    });
  });

  describe('Form and Input Theme Integration', () => {
    it('applies theme to form inputs consistently', async () => {
      const mockOnClose = vi.fn();
      const mockOnSave = vi.fn().mockResolvedValue(undefined);

      render(
        <DataSubjectRequestModal
          isOpen={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="create"
        />
      );

      // All form inputs should have consistent theming
      const emailInput = screen.getByLabelText(/email address/i);
      const typeSelect = screen.getByLabelText(/request type/i);
      const descriptionInput = screen.getByLabelText(/description/i);
      
      expect(emailInput).toBeInTheDocument();
      expect(typeSelect).toBeInTheDocument();
      expect(descriptionInput).toBeInTheDocument();
    });

    it('maintains form validation styling across themes', async () => {
      const mockOnClose = vi.fn();
      const mockOnSave = vi.fn().mockResolvedValue(undefined);

      render(
        <DataSubjectRequestModal
          isOpen={true}
          onClose={mockOnClose}
          onSave={mockOnSave}
          mode="create"
        />
      );

      // Submit form without required fields to trigger validation
      const submitButton = screen.getByRole('button', { name: /create request/i });
      await user.click(submitButton);
      
      // Validation errors should be visible regardless of theme
      await waitFor(() => {
        expect(screen.getByText(/email is required/i)).toBeInTheDocument();
      });
    });
  });

  describe('Button and Interactive Element Theme Integration', () => {
    it('applies theme consistently to all button variants', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Navigate to requests to see action buttons
      const requestsTab = screen.getByText('Data Subject Requests');
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });

      // All buttons should have consistent theming
      const buttons = screen.getAllByRole('button');
      expect(buttons.length).toBeGreaterThan(0);
      
      // Buttons should be interactive
      buttons.forEach(button => {
        expect(button).toBeEnabled();
      });
    });

    it('maintains hover and focus states across themes', async () => {
      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      const requestsTab = screen.getByText('Data Subject Requests');
      
      // Focus should work consistently
      requestsTab.focus();
      expect(document.activeElement).toBe(requestsTab);
      
      // Click should work consistently
      await user.click(requestsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Subject Request Management')).toBeInTheDocument();
      });
    });
  });

  describe('Responsive Design Theme Integration', () => {
    it('maintains theme consistency across different screen sizes', async () => {
      // Mock different viewport sizes
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 768, // Tablet size
      });

      render(<GDPRCommandCenter />);
      
      await waitFor(() => {
        expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
      });

      // Component should render correctly at different sizes
      expect(screen.getByText('Overview')).toBeInTheDocument();
      
      // Change to mobile size
      Object.defineProperty(window, 'innerWidth', {
        value: 375,
      });
      
      // Trigger resize event
      fireEvent(window, new Event('resize'));
      
      // Component should still be functional
      expect(screen.getByText('GDPR Command Center')).toBeInTheDocument();
    });
  });
});
