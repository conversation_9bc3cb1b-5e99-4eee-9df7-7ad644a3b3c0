import React, { useEffect, useState } from 'react';
import { Home, LayoutDashboard, Settings, Users, Shield, PieChart, FileText, Search, BarChart3, Activity, AlertTriangle } from 'lucide-react';
import { Link as RouterLink, useLocation, useNavigate } from 'react-router-dom';
import { HiChevronLeft, HiChevronRight } from 'react-icons/hi';
import { Link as CustomLink } from './Link';
import ThemeToggle from './ThemeToggle';
import { useTheme } from '../context/ThemeContext';
import { useNavigation } from '../context/NavigationContext';
import NotificationCenter from './ui/NotificationCenter';

interface SidebarProps {
  onPageChange: (page: string) => void;
  currentPage: string;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPage, onPageChange }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isExpanded, setIsExpanded] = useState(true);
  const [isHovered, setIsHovered] = useState(false);
  const { mode } = useTheme();
  const { setCurrentPage } = useNavigation();

  useEffect(() => {
    if (!currentPage) {
      onPageChange('home');
    }
  }, [currentPage, onPageChange]);

  const toggleSidebar = () => {
    setIsExpanded(!isExpanded);
  };

  const handleLogoClick = () => {
    onPageChange('home');
    setCurrentPage('home');
    navigate('/');
  };

  return (
    <div
      className={`bg-surface min-h-screen flex flex-col shadow-md relative z-10
        transition-all duration-300 ease-in-out ${isExpanded ? 'w-72' : 'w-20'} overflow-hidden
        text-text border-r border-border dark:shadow-none`}
      style={{ willChange: 'width, transform', transformOrigin: 'left' }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Logo section */}
      <div className="flex flex-col border-b border-border">
        <div className="py-4 px-2 flex justify-center items-center">
          <div
            onClick={handleLogoClick}
            className={`cursor-pointer transition-all duration-300 ease-in-out flex justify-center items-center
              ${isExpanded ? 'w-full' : 'w-10 h-10'}`}
            aria-label="Go to home page"
            role="button"
            tabIndex={0}
            onKeyDown={(e) => {
              if (e.key === 'Enter' || e.key === ' ') {
                handleLogoClick();
              }
            }}
          >
            {isExpanded ? (
              <h1 className="text-3xl font-bold transition-all duration-300 transform hover:translate-x-1">
                <span style={{ color: '#A6D933', fontFamily: 'Inter, system-ui, sans-serif' }}>PRAE</span>
                <span className="text-text" style={{ fontFamily: 'Inter, system-ui, sans-serif' }}>FERRE</span>
              </h1>
            ) : (
              <div className="w-8 h-8 relative transform transition-transform duration-300 hover:scale-105 mx-auto">
                {/* Using the existing thumbnail.png image */}
                <img
                  src="/thumbnail.png"
                  alt="Praeferre Logo"
                  className="w-full h-full object-contain drop-shadow-sm"
                  style={{
                    maxWidth: '32px',
                    maxHeight: '32px',
                    filter: mode === 'dark' ? 'brightness(1.1)' : 'none'
                  }}
                  draggable="false"
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex justify-between px-4 pb-4">
          {isExpanded && <ThemeToggle />}
          <button
            onClick={toggleSidebar}
            className={`p-2 rounded-full transition-all duration-250 shadow-sm
              ${isExpanded
                ? 'ml-auto'
                : 'mx-auto'}
              ${isHovered
                ? 'bg-primary/10 hover:bg-primary/20 text-primary'
                : 'hover:bg-primary/10 text-text-secondary hover:text-primary'}`}
            aria-label={isExpanded ? "Collapse sidebar" : "Expand sidebar"}
          >
            {isExpanded ? <HiChevronLeft size={20} /> : <HiChevronRight size={20} />}
          </button>
        </div>
      </div>

      {/* Navigation section */}
      <nav className={`space-y-1 flex-1 pt-4 ${isExpanded ? 'px-4' : 'px-2'}`}>
        <div className={`mb-2 ${isExpanded ? 'px-4' : 'text-center'}`}>
          <p className="text-xs font-medium text-text-tertiary uppercase tracking-wider">
            {isExpanded ? 'Main Navigation' : ''}
          </p>
        </div>
        {[
          { name: 'Home', icon: <Home size={20} />, path: 'home', badge: null },
          { name: 'Enterprise Dashboard', icon: <Shield size={20} />, path: 'enterprise', badge: null },
          { name: 'Privacy Dashboard', icon: <PieChart size={20} />, path: 'privacy', badge: null },
          { name: 'GDPR Analysis', icon: <Search size={20} />, path: 'gdpr-analysis', badge: null },
          { name: 'Compliance Rules', icon: <BarChart3 size={20} />, path: 'complianceRules', badge: null },
          { name: 'Compliance Reports', icon: <FileText size={20} />, path: 'reports', badge: null },
          { name: 'Configurations', icon: <Settings size={20} />, path: 'configurations', badge: null },
          { name: 'Consumer Interface', icon: <Users size={20} />, path: 'consumer', badge: null }
        ].map((item) => {
          // All items use the same navigation pattern
          const isActive = currentPage === item.path;

          return (
            <CustomLink
              key={item.path}
              href="#"
              active={isActive}
              onClick={() => {
                // If we're currently on a route (not home), navigate to home first
                if (location.pathname !== '/') {
                  navigate('/');
                }
                onPageChange(item.path);
                setCurrentPage(item.path);
              }}
              className={`group py-3 px-4 rounded-lg transition-all duration-250 flex items-center
                ${isActive
                  ? 'bg-primary/10 text-primary font-medium'
                  : 'hover:bg-primary/5 text-text hover:text-primary'}
                ${!isExpanded ? 'justify-center w-full mx-auto' : 'justify-between'}`}
            >
              <div className="flex items-center">
                {React.cloneElement(item.icon, {
                  className: `transition-colors duration-250 ${isActive ? 'text-primary' : 'text-text-secondary'}`
                })}
                {isExpanded && (
                  <span className="transition-all duration-250 group-hover:translate-x-1 ml-3 truncate-1">
                    {item.name}
                  </span>
                )}
              </div>
              {isExpanded && item.badge && (
                <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                  item.badge === 'New'
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                    : 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300'
                }`}>
                  {item.badge}
                </span>
              )}
            </CustomLink>
          );
        })}
      </nav>

      {/* Notification Center and Theme Toggle */}
      <div className={`mt-auto pt-4 ${isExpanded ? 'px-4' : 'px-2'}`}>
        <div className={`flex ${isExpanded ? 'justify-between' : 'flex-col gap-2'} items-center mb-4`}>
          <NotificationCenter isExpanded={isExpanded} currentUserId="user-1" />
          {isExpanded && <ThemeToggle />}
        </div>
      </div>

      {/* Profile section */}
      <div className={`border-t border-border ${isExpanded ? 'px-4' : 'px-2'} pt-4`}>
        <div className={`flex items-center p-3 rounded-lg hover:bg-primary/5 transition-all duration-250 cursor-pointer
          ${!isExpanded ? 'justify-center' : ''}`}
          role="button"
          tabIndex={0}
          aria-label="User profile"
        >
          <div className="w-10 h-10 rounded-full bg-gradient-to-br from-primary-light to-primary text-white flex items-center justify-center shadow-md flex-shrink-0">
            <span className="font-medium text-sm">JD</span>
          </div>
          {isExpanded && (
            <div className="ml-3 overflow-hidden">
              <p className="text-sm font-medium text-text truncate-1">John Doe</p>
              <p className="text-xs text-text-secondary truncate-1">Administrator</p>
            </div>
          )}
        </div>
        {!isExpanded && (
          <div className="flex justify-center mt-4 mb-4">
            <ThemeToggle />
          </div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;