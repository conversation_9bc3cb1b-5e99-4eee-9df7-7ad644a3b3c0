/**
 * Performance Monitoring and Optimization Utilities
 * Production-ready performance tracking and optimization tools
 */

import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import { PerformanceMetrics } from '../types/production';

// Performance monitoring class
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, PerformanceMetrics> = new Map();
  private observers: PerformanceObserver[] = [];
  private isEnabled: boolean = process.env.NODE_ENV === 'production';

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  constructor() {
    if (typeof window !== 'undefined' && this.isEnabled) {
      this.initializeObservers();
    }
  }

  private initializeObservers(): void {
    // Observe navigation timing
    if ('PerformanceObserver' in window) {
      const navigationObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry) => {
          this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
        });
      });

      try {
        navigationObserver.observe({ entryTypes: ['navigation'] });
        this.observers.push(navigationObserver);
      } catch (error) {
        console.warn('Navigation timing observer not supported:', error);
      }
    }
  }

  startComponentMeasure(componentName: string): void {
    if (!this.isEnabled) return;
    performance.mark(`${componentName}-start`);
  }

  endComponentMeasure(componentName: string): number {
    if (!this.isEnabled) return 0;

    const endMark = `${componentName}-end`;
    const measureName = `${componentName}-duration`;

    performance.mark(endMark);
    performance.measure(measureName, `${componentName}-start`, endMark);

    const measure = performance.getEntriesByName(measureName)[0];
    const duration = measure ? measure.duration : 0;

    // Clean up marks and measures
    performance.clearMarks(`${componentName}-start`);
    performance.clearMarks(endMark);
    performance.clearMeasures(measureName);

    return duration;
  }

  recordComponentMetrics(componentName: string, metrics: Partial<PerformanceMetrics>): void {
    if (!this.isEnabled) return;

    const existing = this.metrics.get(componentName) || {
      componentName,
      renderTime: 0,
      mountTime: 0,
      updateTime: 0,
      errorCount: 0,
      retryCount: 0,
    };

    const updated = { ...existing, ...metrics };
    this.metrics.set(componentName, updated);
  }

  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = {
      domContentLoaded: entry.domContentLoadedEventEnd - entry.domContentLoadedEventStart,
      loadComplete: entry.loadEventEnd - entry.loadEventStart,
      domInteractive: entry.domInteractive - entry.navigationStart,
      firstByte: entry.responseStart - entry.requestStart,
    };

    console.log('📊 Navigation Metrics:', metrics);
  }

  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers = [];
    this.metrics.clear();
  }
}

export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;

  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
};

export interface ConsentTrend {
  period: string;
  compliant: number;
  nonCompliant: number;
  pending: number;
}

export const analyzeConsentTrends = (data: ConsentTrend[]) => {
  const calculateGrowthRate = (current: number, previous: number): number => {
    return previous === 0 ? 0 : ((current - previous) / previous) * 100;
  };

  return data.reduce((analysis, current, index, array) => {
    const previous = index > 0 ? array[index - 1] : current;
    
    return {
      ...analysis,
      [current.period]: {
        compliantGrowth: calculateGrowthRate(current.compliant, previous.compliant),
        nonCompliantGrowth: calculateGrowthRate(current.nonCompliant, previous.nonCompliant),
        pendingGrowth: calculateGrowthRate(current.pending, previous.pending),
        complianceRate: (current.compliant / (current.compliant + current.nonCompliant + current.pending)) * 100
      }
    };
  }, {} as Record<string, {
    compliantGrowth: number;
    nonCompliantGrowth: number;
    pendingGrowth: number;
    complianceRate: number;
  }>);
};


export interface TrendData {
  month: string;
  compliant: number;    // Green line
  nonCompliant: number; // Red line
  pending: number;      // Yellow line
}

export const generateTrendData = (): TrendData[] => {
  return [
    { month: 'Nov-23', compliant: 0.2, nonCompliant: 0.2, pending: 1.8 },
    { month: 'Dec-23', compliant: 0.5, nonCompliant: 1.8, pending: 1.2 },
    { month: 'Jan-24', compliant: 0.5, nonCompliant: 1.5, pending: 1.0 },
    { month: 'Feb-24', compliant: 0.5, nonCompliant: 1.2, pending: 1.5 },
    { month: 'Mar-24', compliant: 2.4, nonCompliant: 0.2, pending: 2.0 },
    { month: 'Apr-24', compliant: 1.8, nonCompliant: 1.8, pending: 1.5 },
    { month: 'May-24', compliant: 1.2, nonCompliant: 1.7, pending: 0.5 },
    { month: 'Jun-24', compliant: 0.9, nonCompliant: 2.8, pending: 1.6 }
  ];
};

export const getChartConfig = () => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    layout: {
      padding: {
        top: 20,
        bottom: 20
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        max: 4,
        ticks: {
          stepSize: 1,
          font: {
            size: 11,
            family: "'Inter', sans-serif",
            weight: '400'
          },
          color: '#666666',
          padding: 8
        },
        grid: {
          color: '#f1f5f9',
          drawBorder: false,
          lineWidth: 1
        },
        border: {
          display: false
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          font: {
            size: 11,
            family: "'Inter', sans-serif",
            weight: '400'
          },
          color: '#666666',
          padding: 5
        },
        border: {
          display: false
        }
      }
    },
    elements: {
      line: {
        tension: 0.3,
        borderWidth: 1.5,
        fill: false
      },
      point: {
        radius: 0,
        hoverRadius: 6
      }
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        mode: 'index',
        intersect: false,
        backgroundColor: 'white',
        titleColor: '#1f2937',
        bodyColor: '#666666',
        borderColor: '#e5e7eb',
        borderWidth: 1,
        padding: 12,
        titleFont: {
          size: 12,
          weight: '600',
          family: "'Inter', sans-serif"
        },
        bodyFont: {
          size: 11,
          family: "'Inter', sans-serif"
        },
        cornerRadius: 4,
        displayColors: true,
        boxWidth: 8,
        boxHeight: 8,
        usePointStyle: true
      }
    },
    colors: {
      compliant: '#33cc99',    // Green
      nonCompliant: '#ff3333', // Red
      pending: '#ffcc00'       // Yellow
    }
  };
};

export const getChartContainerStyle = () => ({
  height: '400px',
  position: 'relative' as const,
  marginLeft: '40px',
  marginRight: '20px'
});

export const getLegendStyle = () => ({
  wrapper: 'flex justify-center gap-8 mt-6',
  item: 'flex items-center gap-2',
  line: 'w-6 h-[1px]',
  text: 'text-[11px] text-[#666666]'
});