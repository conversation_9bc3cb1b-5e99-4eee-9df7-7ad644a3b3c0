import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, waitFor } from '@testing-library/react';
import { render } from '../utils/test-utils';

// Import all the components that were failing
const EnhancedComplianceMetricsOptimized = React.lazy(() => import('../../components/compliance/EnhancedComplianceMetricsOptimized'));
const OrganizationalMappingDashboard = React.lazy(() => import('../../components/GDPR/OrganizationalMappingDashboard'));
const PoliciesManagementDashboard = React.lazy(() => import('../../components/GDPR/PoliciesManagementDashboard'));
const ConsentManagementDashboard = React.lazy(() => import('../../components/GDPR/ConsentManagementDashboard'));
const ImpactAssessmentDashboard = React.lazy(() => import('../../components/GDPR/ImpactAssessmentDashboard'));

// Mock all the services to prevent actual API calls
vi.mock('../../services/consentManagementService', () => ({
  ConsentManagementService: {
    initialize: vi.fn(),
    getAllConsentRecords: vi.fn().mockResolvedValue([]),
    getAllConsentCategories: vi.fn().mockResolvedValue([]),
    getConsentMetrics: vi.fn().mockResolvedValue({
      totalUsers: 100,
      consentedUsers: 85,
      consentRate: 85,
      trend: 'up',
      weeklyChange: 2.3,
      monthlyChange: 5.7,
      lastUpdated: new Date(),
      categoryBreakdown: []
    }),
    getConsentTrends: vi.fn().mockResolvedValue([])
  }
}));

vi.mock('../../services/impactAssessmentService', () => ({
  ImpactAssessmentService: {
    initialize: vi.fn(),
    getAllAssessments: vi.fn().mockResolvedValue([]),
    getAssessmentMetrics: vi.fn().mockResolvedValue({
      total: 10,
      completed: 7,
      inProgress: 2,
      overdue: 1,
      averageRiskScore: 65,
      highRiskAssessments: 3
    })
  }
}));

vi.mock('../../components/compliance/services/ComplianceDataService', () => ({
  generateEnhancedComplianceData: () => ({
    overview: {
      totalMetrics: 25,
      compliantMetrics: 20,
      warningMetrics: 3,
      criticalMetrics: 2,
      overallScore: 85,
      trendDirection: 'up',
      lastAssessment: new Date(),
      nextAssessment: new Date()
    },
    metrics: [],
    frameworks: [],
    categoryBreakdown: {
      privacy: { score: 90, count: 5, trend: 'up' },
      security: { score: 88, count: 8, trend: 'stable' },
      operational: { score: 85, count: 7, trend: 'up' },
      regulatory: { score: 92, count: 5, trend: 'up' }
    },
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30000,
      pendingUpdates: 0
    }
  })
}));

// Mock chart.js to prevent canvas issues in tests
vi.mock('react-chartjs-2', () => ({
  Line: () => <div data-testid="line-chart">Line Chart</div>,
  Bar: () => <div data-testid="bar-chart">Bar Chart</div>,
  Doughnut: () => <div data-testid="doughnut-chart">Doughnut Chart</div>,
  Radar: () => <div data-testid="radar-chart">Radar Chart</div>
}));

describe('Component Loading Verification', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Enhanced Compliance Metrics Optimized', () => {
    it('loads and renders without errors', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Organizational Mapping Dashboard', () => {
    it('loads and renders without errors', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <OrganizationalMappingDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Policies Management Dashboard', () => {
    it('loads and renders without errors', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <PoliciesManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Consent Management Dashboard', () => {
    it('loads and renders without errors', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ConsentManagementDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('Impact Assessment Dashboard', () => {
    it('loads and renders without errors', async () => {
      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <ImpactAssessmentDashboard />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 5000 });
    });
  });

  describe('All Components Integration', () => {
    it('can load all components simultaneously without conflicts', async () => {
      const TestWrapper = () => (
        <div>
          <React.Suspense fallback={<div>Loading Enhanced Compliance...</div>}>
            <EnhancedComplianceMetricsOptimized />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Organizational Mapping...</div>}>
            <OrganizationalMappingDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Policies Management...</div>}>
            <PoliciesManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Consent Management...</div>}>
            <ConsentManagementDashboard />
          </React.Suspense>
          <React.Suspense fallback={<div>Loading Impact Assessment...</div>}>
            <ImpactAssessmentDashboard />
          </React.Suspense>
        </div>
      );

      render(<TestWrapper />);

      // Wait for all components to load
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
        expect(screen.getByText('Organizational Mapping')).toBeInTheDocument();
        expect(screen.getByText('Policies Management')).toBeInTheDocument();
        expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
        expect(screen.getByText('Impact Assessments')).toBeInTheDocument();
      }, { timeout: 10000 });
    });

    it('handles component loading errors gracefully', async () => {
      // Mock console.error to prevent test noise
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Should not have any console errors
      expect(consoleSpy).not.toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Performance and Memory', () => {
    it('components unmount cleanly without memory leaks', async () => {
      const { unmount } = render(
        <React.Suspense fallback={<div>Loading...</div>}>
          <EnhancedComplianceMetricsOptimized />
        </React.Suspense>
      );

      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Metrics')).toBeInTheDocument();
      });

      // Unmount should not cause errors
      expect(() => unmount()).not.toThrow();
    });
  });
});
