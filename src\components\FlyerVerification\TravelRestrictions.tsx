import React, { useState, useEffect } from 'react';
import { Shield, AlertTriangle, Plus, Edit, Trash2, Save, X, RefreshCw, Globe, Calendar } from 'lucide-react';
import { toast } from 'react-hot-toast';

interface TravelRestriction {
  id: string;
  country: string;
  nationality: string;
  restrictionType: 'banned' | 'visa_required' | 'quarantine' | 'health_check';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  effectiveDate: string;
  expiryDate?: string;
  lastUpdated: string;
  updatedBy: string;
  isActive: boolean;
}

const TravelRestrictions: React.FC = () => {
  const [restrictions, setRestrictions] = useState<TravelRestriction[]>([]);
  const [filteredRestrictions, setFilteredRestrictions] = useState<TravelRestriction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [severityFilter, setSeverityFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date>(new Date());

  const [newRestriction, setNewRestriction] = useState<Partial<TravelRestriction>>({
    country: '',
    nationality: '',
    restrictionType: 'visa_required',
    severity: 'medium',
    description: '',
    effectiveDate: new Date().toISOString().split('T')[0],
    isActive: true
  });

  // Mock data generation
  useEffect(() => {
    const generateMockRestrictions = (): TravelRestriction[] => {
      // Pre-defined restrictions for testing scenarios
      const predefinedRestrictions: TravelRestriction[] = [
        {
          id: 'TR-0001',
          country: 'United States',
          nationality: 'Iranian',
          restrictionType: 'banned',
          severity: 'critical',
          description: 'Executive Order 13769 - Iranian nationals prohibited from US entry due to national security concerns',
          effectiveDate: '2017-01-27',
          lastUpdated: new Date().toISOString(),
          updatedBy: 'Department of Homeland Security',
          isActive: true
        },
        {
          id: 'TR-0002',
          country: 'United States',
          nationality: 'Syrian',
          restrictionType: 'banned',
          severity: 'critical',
          description: 'Presidential Proclamation 9645 - Syrian nationals banned from US travel due to ongoing conflict and security screening challenges',
          effectiveDate: '2017-09-24',
          lastUpdated: new Date().toISOString(),
          updatedBy: 'Department of Homeland Security',
          isActive: true
        },
        {
          id: 'TR-0003',
          country: 'United States',
          nationality: 'North Korean',
          restrictionType: 'banned',
          severity: 'critical',
          description: 'Presidential Proclamation 9645 - North Korean nationals prohibited due to lack of diplomatic relations and security verification challenges',
          effectiveDate: '2017-09-24',
          lastUpdated: new Date().toISOString(),
          updatedBy: 'Department of State',
          isActive: true
        },
        {
          id: 'TR-0004',
          country: 'United States',
          nationality: 'Indian',
          restrictionType: 'visa_required',
          severity: 'medium',
          description: 'Valid visa required for entry - Indian nationals must have appropriate visa for US travel',
          effectiveDate: '2020-01-01',
          lastUpdated: new Date().toISOString(),
          updatedBy: 'System Administrator',
          isActive: true
        }
      ];

      const countries = ['United Kingdom', 'Germany', 'France', 'Australia', 'Canada', 'Japan', 'Singapore'];
      const nationalities = ['Afghan', 'Somali', 'Yemeni', 'Iraqi', 'Libyan'];
      const types: TravelRestriction['restrictionType'][] = ['banned', 'visa_required', 'quarantine', 'health_check'];
      const severities: TravelRestriction['severity'][] = ['low', 'medium', 'high', 'critical'];

      const additionalRestrictions = Array.from({ length: 10 }, (_, i) => {
        const type = types[Math.floor(Math.random() * types.length)];
        const severity = type === 'banned' ? 'critical' : severities[Math.floor(Math.random() * severities.length)];

        return {
          id: `TR-${String(i + 5).padStart(4, '0')}`,
          country: countries[Math.floor(Math.random() * countries.length)],
          nationality: nationalities[Math.floor(Math.random() * nationalities.length)],
          restrictionType: type,
          severity,
          description: getRestrictionDescription(type, severity),
          effectiveDate: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          expiryDate: Math.random() > 0.3 ? new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] : undefined,
          lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          updatedBy: 'System Administrator',
          isActive: Math.random() > 0.1
        };
      });

      return [...predefinedRestrictions, ...additionalRestrictions];
    };

    const getRestrictionDescription = (type: string, severity: string): string => {
      const descriptions = {
        banned: 'Entry completely prohibited due to security concerns',
        visa_required: 'Special visa verification required before travel approval',
        quarantine: 'Mandatory quarantine period upon arrival',
        health_check: 'Additional health screening and documentation required'
      };
      return descriptions[type as keyof typeof descriptions] || 'Travel restriction in effect';
    };

    setTimeout(() => {
      const mockRestrictions = generateMockRestrictions();
      setRestrictions(mockRestrictions);
      setFilteredRestrictions(mockRestrictions);
      setIsLoading(false);
    }, 1000);
  }, []);

  // Filter restrictions
  useEffect(() => {
    let filtered = restrictions;

    if (searchTerm) {
      filtered = filtered.filter(restriction =>
        restriction.country.toLowerCase().includes(searchTerm.toLowerCase()) ||
        restriction.nationality.toLowerCase().includes(searchTerm.toLowerCase()) ||
        restriction.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (severityFilter !== 'all') {
      filtered = filtered.filter(restriction => restriction.severity === severityFilter);
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(restriction => restriction.restrictionType === typeFilter);
    }

    setFilteredRestrictions(filtered);
  }, [restrictions, searchTerm, severityFilter, typeFilter]);

  const getSeverityBadge = (severity: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (severity) {
      case 'low':
        return `${baseClasses} bg-success/10 text-success`;
      case 'medium':
        return `${baseClasses} bg-warning/10 text-warning`;
      case 'high':
        return `${baseClasses} bg-error/10 text-error`;
      case 'critical':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400`;
      default:
        return `${baseClasses} bg-surface text-text-secondary`;
    }
  };

  const getTypeBadge = (type: string) => {
    const baseClasses = "px-2 py-1 text-xs font-medium rounded-full";
    switch (type) {
      case 'banned':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400`;
      case 'visa_required':
        return `${baseClasses} bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400`;
      case 'quarantine':
        return `${baseClasses} bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400`;
      case 'health_check':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400`;
      default:
        return `${baseClasses} bg-surface text-text-secondary`;
    }
  };

  const handleAddRestriction = () => {
    if (!newRestriction.country || !newRestriction.nationality || !newRestriction.description) {
      toast.error('Please fill in all required fields');
      return;
    }

    const restriction: TravelRestriction = {
      id: `TR-${String(restrictions.length + 1).padStart(4, '0')}`,
      country: newRestriction.country!,
      nationality: newRestriction.nationality!,
      restrictionType: newRestriction.restrictionType!,
      severity: newRestriction.severity!,
      description: newRestriction.description!,
      effectiveDate: newRestriction.effectiveDate!,
      expiryDate: newRestriction.expiryDate,
      lastUpdated: new Date().toISOString(),
      updatedBy: 'Current User',
      isActive: newRestriction.isActive!
    };

    setRestrictions(prev => [restriction, ...prev]);
    setNewRestriction({
      country: '',
      nationality: '',
      restrictionType: 'visa_required',
      severity: 'medium',
      description: '',
      effectiveDate: new Date().toISOString().split('T')[0],
      isActive: true
    });
    setIsAddingNew(false);
    toast.success('Travel restriction added successfully');
  };

  const handleDeleteRestriction = (id: string) => {
    setRestrictions(prev => prev.filter(r => r.id !== id));
    toast.success('Travel restriction deleted');
  };

  const handleRefreshData = () => {
    setLastUpdate(new Date());
    toast.success('Travel restrictions data refreshed');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        <span className="ml-3 text-text-secondary">Loading travel restrictions...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-card rounded-lg p-6 border border-border">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h2 className="text-xl font-semibold text-text">Travel Restrictions</h2>
            <p className="text-text-secondary">Manage daily travel policy updates and restrictions</p>
          </div>
          <div className="flex space-x-3">
            <button
              onClick={handleRefreshData}
              className="px-4 py-2 border border-border rounded-lg hover:bg-surface transition-colors flex items-center space-x-2 text-text"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => setIsAddingNew(true)}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Add Restriction</span>
            </button>
          </div>
        </div>

        {/* Last Update Info */}
        <div className="flex items-center space-x-2 text-sm text-text-secondary mb-4">
          <Calendar className="w-4 h-4" />
          <span>Last updated: {lastUpdate.toLocaleString()}</span>
        </div>

        {/* Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="Search by country, nationality, or description..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-surface text-text"
            />
          </div>
          <div className="flex space-x-4">
            <select
              value={severityFilter}
              onChange={(e) => setSeverityFilter(e.target.value)}
              className="px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
            >
              <option value="all">All Severities</option>
              <option value="low">Low</option>
              <option value="medium">Medium</option>
              <option value="high">High</option>
              <option value="critical">Critical</option>
            </select>
            <select
              value={typeFilter}
              onChange={(e) => setTypeFilter(e.target.value)}
              className="px-4 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
            >
              <option value="all">All Types</option>
              <option value="banned">Banned</option>
              <option value="visa_required">Visa Required</option>
              <option value="quarantine">Quarantine</option>
              <option value="health_check">Health Check</option>
            </select>
          </div>
        </div>
      </div>

      {/* Add New Restriction Form */}
      {isAddingNew && (
        <div className="bg-card rounded-lg p-6 border border-border">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-text">Add New Travel Restriction</h3>
            <button
              onClick={() => setIsAddingNew(false)}
              className="text-text-secondary hover:text-text"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-text mb-1">Country</label>
              <input
                type="text"
                value={newRestriction.country || ''}
                onChange={(e) => setNewRestriction(prev => ({ ...prev, country: e.target.value }))}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
                placeholder="e.g., United Kingdom"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">Nationality</label>
              <input
                type="text"
                value={newRestriction.nationality || ''}
                onChange={(e) => setNewRestriction(prev => ({ ...prev, nationality: e.target.value }))}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
                placeholder="e.g., Afghan"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">Restriction Type</label>
              <select
                value={newRestriction.restrictionType || 'visa_required'}
                onChange={(e) => setNewRestriction(prev => ({ ...prev, restrictionType: e.target.value as TravelRestriction['restrictionType'] }))}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
              >
                <option value="visa_required">Visa Required</option>
                <option value="quarantine">Quarantine</option>
                <option value="health_check">Health Check</option>
                <option value="banned">Banned</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-text mb-1">Severity</label>
              <select
                value={newRestriction.severity || 'medium'}
                onChange={(e) => setNewRestriction(prev => ({ ...prev, severity: e.target.value as TravelRestriction['severity'] }))}
                className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="critical">Critical</option>
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-text mb-1">Description</label>
            <textarea
              value={newRestriction.description || ''}
              onChange={(e) => setNewRestriction(prev => ({ ...prev, description: e.target.value }))}
              rows={3}
              className="w-full px-3 py-2 border border-border rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-surface text-text"
              placeholder="Describe the travel restriction..."
            />
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={() => setIsAddingNew(false)}
              className="px-4 py-2 border border-border rounded-lg hover:bg-surface transition-colors text-text"
            >
              Cancel
            </button>
            <button
              onClick={handleAddRestriction}
              className="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-hover transition-colors flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Save Restriction</span>
            </button>
          </div>
        </div>
      )}

      {/* Restrictions Table */}
      <div className="bg-card rounded-lg border border-border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-border">
            <thead className="bg-surface">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Country / Nationality
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Effective Date
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-card divide-y divide-border">
              {filteredRestrictions.map((restriction) => (
                <tr key={restriction.id} className="hover:bg-surface">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-text">{restriction.country}</div>
                      <div className="text-sm text-text-secondary">{restriction.nationality} nationals</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getTypeBadge(restriction.restrictionType)}>
                      {restriction.restrictionType.replace('_', ' ').toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getSeverityBadge(restriction.severity)}>
                      {restriction.severity.toUpperCase()}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-text">{formatDate(restriction.effectiveDate)}</div>
                    {restriction.expiryDate && (
                      <div className="text-sm text-text-secondary">Expires: {formatDate(restriction.expiryDate)}</div>
                    )}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      restriction.isActive 
                        ? 'bg-success/10 text-success' 
                        : 'bg-surface text-text-secondary'
                    }`}>
                      {restriction.isActive ? 'ACTIVE' : 'INACTIVE'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => setEditingId(restriction.id)}
                        className="text-primary hover:text-primary-hover"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteRestriction(restriction.id)}
                        className="text-error hover:text-error/80"
                      >
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredRestrictions.length === 0 && (
          <div className="text-center py-8">
            <Globe className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">No travel restrictions found matching your criteria.</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default TravelRestrictions;
