import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../utils/test-utils';
import SecurityOverviewDashboard from '../../components/Security/SecurityOverviewDashboardOptimized';

// Mock the data service
vi.mock('../../components/Security/services/SecurityDataService', () => ({
  generateSecurityOverviewData: () => ({
    overview: {
      activeThreats: 5,
      resolvedIncidents: 12,
      systemsMonitored: 25,
      alertsLast24h: 8
    },
    threats: [
      {
        id: 'threat-1',
        name: 'SQL Injection Attempt',
        type: 'web_attack',
        severity: 'high',
        status: 'investigating',
        detectedAt: new Date(),
        source: 'Web Application Firewall',
        targetAsset: 'Customer Database',
        riskScore: 85,
        assignedTo: 'Security Team'
      }
    ],
    incidents: [
      {
        id: 'incident-1',
        title: 'Data Breach Investigation',
        description: 'Potential unauthorized access detected',
        severity: 'critical',
        status: 'investigating',
        reportedAt: new Date(),
        category: 'data_breach',
        affectedAssets: ['Database Server']
      }
    ],
    systems: [
      {
        id: 'system-1',
        name: 'Production Database',
        type: 'database',
        status: 'secure',
        securityScore: 95,
        monitoring: { isActive: true }
      }
    ],
    alerts: [
      {
        id: 'alert-1',
        title: 'Unusual Login Activity',
        description: 'Multiple failed login attempts detected',
        severity: 'medium',
        status: 'new',
        triggeredAt: new Date(),
        source: 'Authentication System'
      }
    ],
    realTimeUpdates: {
      isLive: true,
      lastSync: new Date(),
      updateFrequency: 30000,
      pendingAlerts: 3
    }
  })
}));

// Mock window.confirm
const mockConfirm = vi.fn();
Object.defineProperty(window, 'confirm', {
  value: mockConfirm,
  writable: true
});

describe('Security Overview Dashboard Delete Functionality', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    mockConfirm.mockReturnValue(true); // Default to confirming deletions
  });

  it('renders the component without crashing', async () => {
    render(<SecurityOverviewDashboard />);
    
    await waitFor(() => {
      expect(screen.getByText('Security Overview Dashboard')).toBeInTheDocument();
    });
  });

  describe('Threats Delete Functionality', () => {
    it('shows delete button for threats', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to threats tab
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
        expect(screen.getByTitle('Delete Threat')).toBeInTheDocument();
      });
    });

    it('deletes threat when delete button is clicked and confirmed', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to threats tab
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButton = screen.getByTitle('Delete Threat');
      await user.click(deleteButton);
      
      // Confirm deletion was prompted
      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this threat? This action cannot be undone.'
      );
      
      // Threat should be removed
      await waitFor(() => {
        expect(screen.queryByText('SQL Injection Attempt')).not.toBeInTheDocument();
      });
    });

    it('does not delete threat when deletion is cancelled', async () => {
      mockConfirm.mockReturnValue(false); // User cancels deletion
      
      render(<SecurityOverviewDashboard />);
      
      // Navigate to threats tab
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButton = screen.getByTitle('Delete Threat');
      await user.click(deleteButton);
      
      // Threat should still be present
      expect(screen.getByText('SQL Injection Attempt')).toBeInTheDocument();
    });
  });

  describe('Incidents Delete Functionality', () => {
    it('shows delete button for incidents', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to incidents tab
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Breach Investigation')).toBeInTheDocument();
        expect(screen.getByTitle('Delete Incident')).toBeInTheDocument();
      });
    });

    it('deletes incident when delete button is clicked and confirmed', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to incidents tab
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Breach Investigation')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButton = screen.getByTitle('Delete Incident');
      await user.click(deleteButton);
      
      // Confirm deletion was prompted
      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this incident? This action cannot be undone.'
      );
      
      // Incident should be removed
      await waitFor(() => {
        expect(screen.queryByText('Data Breach Investigation')).not.toBeInTheDocument();
      });
    });
  });

  describe('Systems Delete Functionality', () => {
    it('shows delete button for systems', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to systems tab
      const systemsTab = screen.getByText('Systems');
      await user.click(systemsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Production Database')).toBeInTheDocument();
        expect(screen.getByTitle('Delete System')).toBeInTheDocument();
      });
    });

    it('deletes system when delete button is clicked and confirmed', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to systems tab
      const systemsTab = screen.getByText('Systems');
      await user.click(systemsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Production Database')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButton = screen.getByTitle('Delete System');
      await user.click(deleteButton);
      
      // Confirm deletion was prompted
      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this system? This action cannot be undone.'
      );
      
      // System should be removed
      await waitFor(() => {
        expect(screen.queryByText('Production Database')).not.toBeInTheDocument();
      });
    });
  });

  describe('Alerts Delete Functionality', () => {
    it('shows delete button for alerts', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to alerts tab
      const alertsTab = screen.getByText('Alerts');
      await user.click(alertsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Unusual Login Activity')).toBeInTheDocument();
        expect(screen.getByTitle('Delete Alert')).toBeInTheDocument();
      });
    });

    it('deletes alert when delete button is clicked and confirmed', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to alerts tab
      const alertsTab = screen.getByText('Alerts');
      await user.click(alertsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Unusual Login Activity')).toBeInTheDocument();
      });
      
      // Click delete button
      const deleteButton = screen.getByTitle('Delete Alert');
      await user.click(deleteButton);
      
      // Confirm deletion was prompted
      expect(mockConfirm).toHaveBeenCalledWith(
        'Are you sure you want to delete this alert? This action cannot be undone.'
      );
      
      // Alert should be removed
      await waitFor(() => {
        expect(screen.queryByText('Unusual Login Activity')).not.toBeInTheDocument();
      });
    });
  });

  describe('Error Handling', () => {
    it('handles delete operations without console errors', async () => {
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});
      
      render(<SecurityOverviewDashboard />);
      
      // Navigate to threats tab and delete
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      const deleteButton = screen.getByTitle('Delete Threat');
      await user.click(deleteButton);
      
      // No console errors should occur
      expect(consoleSpy).not.toHaveBeenCalled();
      
      consoleSpy.mockRestore();
    });

    it('prevents event propagation when delete button is clicked', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to incidents tab
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Data Breach Investigation')).toBeInTheDocument();
      });
      
      // Click delete button - should not trigger card click
      const deleteButton = screen.getByTitle('Delete Incident');
      await user.click(deleteButton);
      
      // Modal should not open (would happen if card was clicked)
      expect(screen.queryByText('Incident Details')).not.toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('provides proper titles for delete buttons', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Check threats
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      await waitFor(() => {
        expect(screen.getByTitle('Delete Threat')).toBeInTheDocument();
      });
      
      // Check incidents
      const incidentsTab = screen.getByText('Incidents');
      await user.click(incidentsTab);
      
      await waitFor(() => {
        expect(screen.getByTitle('Delete Incident')).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation for delete buttons', async () => {
      render(<SecurityOverviewDashboard />);
      
      // Navigate to threats tab
      const threatsTab = screen.getByText('Threats');
      await user.click(threatsTab);
      
      const deleteButton = screen.getByTitle('Delete Threat');
      
      // Focus and activate with keyboard
      deleteButton.focus();
      fireEvent.keyDown(deleteButton, { key: 'Enter' });
      
      // Confirm dialog should appear
      expect(mockConfirm).toHaveBeenCalled();
    });
  });
});
