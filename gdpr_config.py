# GDPR Requirements Configuration
GDPR_REQUIREMENTS = [
    {
        "name": "Art. 3 - Territorial Scope",
        "search_query": "Article 3 territorial scope applicability EU data subjects European Union",
        "keywords": ["article 3", "territorial scope", "eu data subject", "applicability"],
        "description": "EU data subject coverage"
    },
    {
        "name": "Art. 5-6 - Lawfulness & Transparency", 
        "search_query": "Article 5 6 lawfulness fairness transparency legal basis processing legitimate interest",
        "keywords": ["article 5", "article 6", "lawful basis", "transparency", "fair processing"],
        "description": "Legal basis and transparency"
    },
    {
        "name": "Art. 7 - Consent",
        "search_query": "Article 7 consent mechanisms explicit withdrawable freely given specific informed unambiguous",
        "keywords": ["article 7", "consent", "freely given", "withdrawable"],
        "description": "Valid consent mechanisms"
    },
    {
        "name": "Art. 12-22 - Data Subject Rights",
        "search_query": "Article 15 16 17 18 19 20 21 22 data subject rights access rectification erasure restriction portability objection automated decision",
        "keywords": ["data subject rights", "article 12", "article 15", "access", "rectification", "erasure", "portability"],
        "description": "All individual rights implementation"
    },
    {
        "name": "Art. 25 - Privacy by Design",
        "search_query": "Article 25 data protection by design and by default privacy engineering",
        "keywords": ["article 25", "privacy by design", "data protection by design"],
        "description": "Privacy by design principles"
    },
    {
        "name": "Art. 30 - Records of Processing",
        "search_query": "Article 30 records of processing activities documentation controller processor",
        "keywords": ["article 30", "records of processing", "processing activities"],
        "description": "Documentation requirements"
    },
    {
        "name": "Art. 32 - Security",
        "search_query": "Article 32 security of processing technical organizational measures encryption pseudonymisation",
        "keywords": ["article 32", "security", "technical measures", "organizational measures"],
        "description": "Technical and organizational measures"
    },
    {
        "name": "Art. 33-34 - Breach Notification",
        "search_query": "Article 33 34 personal data breach notification supervisory authority data subject 72 hours",
        "keywords": ["article 33", "article 34", "breach notification", "data breach"],
        "description": "Notification procedures"
    },
    {
        "name": "Art. 37-39 - Data Protection Officer",
        "search_query": "Article 37 38 39 data protection officer DPO appointment designation contact details",
        "keywords": ["article 37", "data protection officer", "dpo"],
        "description": "Data Protection Officer requirements"
    },
    {
        "name": "Art. 44-49 - International Transfers",
        "search_query": "Article 44 45 46 47 48 49 international transfers third countries adequacy decision standard contractual clauses",
        "keywords": ["article 44", "international transfer", "third country", "adequacy"],
        "description": "Transfer safeguards"
    }
]

# Additional search queries for broader coverage
ADDITIONAL_QUERIES = [
    "lawful basis consent contract legal obligation vital interests public task legitimate interest",
    "data minimisation purpose limitation accuracy storage limitation integrity confidentiality accountability",
    "supervisory authority lead authority one-stop-shop cross-border processing",
    "privacy impact assessment DPIA high risk systematic monitoring"
]

# Compliance status patterns for text analysis
COMPLIANCE_PATTERNS = {
    "Compliant": ["compliant", "meets", "satisfies", "adequate", "sufficient"],
    "Partially Compliant": ["partially", "somewhat", "limited", "incomplete"],
    "Non-compliant": ["non-compliant", "fails", "does not meet", "inadequate", "lacking", "missing", "not mentioned", "not addressed", "absent", "no reference"]
}

# Configuration for file outputs
OUTPUT_CONFIG = {
    "csv_file": "gdpr_compliance_results_cohere.csv",
    "json_file": "gdpr_compliance_results_cohere.json", 
    "raw_json_file": "gdpr_compliance_raw_json.json",
    "report_file": "gdpr_compliance_report_cohere.txt",
    "clean_csv_file": "gdpr_compliance_results_clean.csv",
    "comprehensive_json_file": "gdpr_compliance_comprehensive.json"
}
