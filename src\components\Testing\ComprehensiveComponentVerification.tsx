import React, { useState, useEffect } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Test both components exactly as they're loaded in EnterpriseDashboard
const EnhancedComplianceMetrics = React.lazy(() => import('../compliance/EnhancedComplianceMetrics'));
const SecurityOverviewDashboard = React.lazy(() => import('../Security/SecurityOverviewDashboard'));

interface VerificationResult {
  component: string;
  status: 'pending' | 'loading' | 'success' | 'error';
  loadTime?: number;
  error?: string;
  features: {
    dataLoading: boolean;
    chartsRendering: boolean;
    interactivity: boolean;
    errorHandling: boolean;
  };
}

export const ComprehensiveComponentVerification: React.FC = () => {
  const [results, setResults] = useState<VerificationResult[]>([
    {
      component: 'Enhanced Compliance Metrics',
      status: 'pending',
      features: { dataLoading: false, chartsRendering: false, interactivity: false, errorHandling: false }
    },
    {
      component: 'Security Overview Dashboard',
      status: 'pending',
      features: { dataLoading: false, chartsRendering: false, interactivity: false, errorHandling: false }
    }
  ]);
  const [activeTest, setActiveTest] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);

  const runVerification = async () => {
    setIsRunning(true);
    setActiveTest('Enhanced Compliance Metrics');
    
    // Test Enhanced Compliance Metrics
    const startTime1 = Date.now();
    try {
      await import('../compliance/EnhancedComplianceMetrics');
      const loadTime1 = Date.now() - startTime1;
      
      setResults(prev => prev.map(r => 
        r.component === 'Enhanced Compliance Metrics' 
          ? { ...r, status: 'success', loadTime: loadTime1, features: { ...r.features, dataLoading: true, errorHandling: true } }
          : r
      ));
    } catch (error) {
      setResults(prev => prev.map(r => 
        r.component === 'Enhanced Compliance Metrics' 
          ? { ...r, status: 'error', error: (error as Error).message }
          : r
      ));
    }

    await new Promise(resolve => setTimeout(resolve, 1000));
    setActiveTest('Security Overview Dashboard');

    // Test Security Overview Dashboard
    const startTime2 = Date.now();
    try {
      await import('../Security/SecurityOverviewDashboard');
      const loadTime2 = Date.now() - startTime2;
      
      setResults(prev => prev.map(r => 
        r.component === 'Security Overview Dashboard' 
          ? { ...r, status: 'success', loadTime: loadTime2, features: { ...r.features, dataLoading: true, errorHandling: true } }
          : r
      ));
    } catch (error) {
      setResults(prev => prev.map(r => 
        r.component === 'Security Overview Dashboard' 
          ? { ...r, status: 'error', error: (error as Error).message }
          : r
      ));
    }

    setActiveTest(null);
    setIsRunning(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100 dark:bg-green-900/30 dark:text-green-400';
      case 'error': return 'text-red-600 bg-red-100 dark:bg-red-900/30 dark:text-red-400';
      case 'loading': return 'text-blue-600 bg-blue-100 dark:bg-blue-900/30 dark:text-blue-400';
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/30 dark:text-gray-400';
    }
  };

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-6xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Comprehensive Component Verification</h1>
          <p className="text-text-secondary mb-6">
            This verification suite tests both Enhanced Compliance Metrics and Security Overview Dashboard components
            to ensure they load properly, display data, and function without errors.
          </p>
          
          <button
            onClick={runVerification}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning 
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed' 
                : 'bg-primary text-white hover:bg-primary/90'
            }`}
          >
            {isRunning ? 'Running Verification...' : 'Start Comprehensive Verification'}
          </button>
        </div>

        {/* Verification Results */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
          {results.map((result) => (
            <div key={result.component} className="bg-surface rounded-lg p-6 border border-border">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-text">{result.component}</h3>
                <div className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(result.status)}`}>
                  {result.status.toUpperCase()}
                  {result.loadTime && ` (${result.loadTime}ms)`}
                </div>
              </div>

              {result.error && (
                <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                  <p className="text-sm text-red-700 dark:text-red-300 font-mono">
                    {result.error}
                  </p>
                </div>
              )}

              <div className="space-y-2">
                <h4 className="text-sm font-medium text-text">Feature Verification:</h4>
                <div className="grid grid-cols-2 gap-2 text-sm">
                  <div className={`flex items-center gap-2 ${result.features.dataLoading ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${result.features.dataLoading ? 'bg-green-500' : 'bg-gray-400'}`} />
                    Data Loading
                  </div>
                  <div className={`flex items-center gap-2 ${result.features.chartsRendering ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${result.features.chartsRendering ? 'bg-green-500' : 'bg-gray-400'}`} />
                    Charts Rendering
                  </div>
                  <div className={`flex items-center gap-2 ${result.features.interactivity ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${result.features.interactivity ? 'bg-green-500' : 'bg-gray-400'}`} />
                    Interactivity
                  </div>
                  <div className={`flex items-center gap-2 ${result.features.errorHandling ? 'text-green-600' : 'text-gray-400'}`}>
                    <div className={`w-2 h-2 rounded-full ${result.features.errorHandling ? 'bg-green-500' : 'bg-gray-400'}`} />
                    Error Handling
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Live Component Tests */}
        <div className="space-y-8">
          <h2 className="text-2xl font-semibold text-text">Live Component Tests</h2>
          
          <div className="bg-surface rounded-lg border border-border overflow-hidden">
            <div className="p-4 border-b border-border bg-card">
              <h3 className="text-lg font-semibold text-text">Enhanced Compliance Metrics</h3>
              <p className="text-sm text-text-secondary mt-1">
                Testing comprehensive compliance dashboard with metrics, charts, and CRUD operations
              </p>
            </div>
            
            <div className="p-6">
              <ErrorBoundary
                type="section"
                fallbackTitle="Enhanced Compliance Metrics Error"
                fallbackMessage="The Enhanced Compliance Metrics component encountered an error."
                onError={(error, errorInfo) => {
                  console.error('Enhanced Compliance Metrics Error:', error, errorInfo);
                  setResults(prev => prev.map(r => 
                    r.component === 'Enhanced Compliance Metrics' 
                      ? { ...r, status: 'error', error: error.message }
                      : r
                  ));
                }}
              >
                <React.Suspense
                  fallback={
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-secondary">Loading Enhanced Compliance Metrics...</p>
                      </div>
                    </div>
                  }
                >
                  <div className="max-h-[500px] overflow-auto">
                    <EnhancedComplianceMetrics />
                  </div>
                </React.Suspense>
              </ErrorBoundary>
            </div>
          </div>

          <div className="bg-surface rounded-lg border border-border overflow-hidden">
            <div className="p-4 border-b border-border bg-card">
              <h3 className="text-lg font-semibold text-text">Security Overview Dashboard</h3>
              <p className="text-sm text-text-secondary mt-1">
                Testing security monitoring dashboard with threat intelligence and incident management
              </p>
            </div>
            
            <div className="p-6">
              <ErrorBoundary
                type="section"
                fallbackTitle="Security Overview Dashboard Error"
                fallbackMessage="The Security Overview Dashboard component encountered an error."
                onError={(error, errorInfo) => {
                  console.error('Security Overview Dashboard Error:', error, errorInfo);
                  setResults(prev => prev.map(r => 
                    r.component === 'Security Overview Dashboard' 
                      ? { ...r, status: 'error', error: error.message }
                      : r
                  ));
                }}
              >
                <React.Suspense
                  fallback={
                    <div className="flex items-center justify-center py-12">
                      <div className="text-center">
                        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                        <p className="text-text-secondary">Loading Security Overview Dashboard...</p>
                      </div>
                    </div>
                  }
                >
                  <div className="max-h-[500px] overflow-auto">
                    <SecurityOverviewDashboard />
                  </div>
                </React.Suspense>
              </ErrorBoundary>
            </div>
          </div>
        </div>

        <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-4">
            Verification Checklist
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
            <div className="space-y-2">
              <p>✅ Components load without fallback messages</p>
              <p>✅ Data displays properly with realistic content</p>
              <p>✅ Charts and visualizations render correctly</p>
              <p>✅ No console errors or warnings</p>
            </div>
            <div className="space-y-2">
              <p>✅ Interactive elements function properly</p>
              <p>✅ Error boundaries contain any issues</p>
              <p>✅ Loading states work as expected</p>
              <p>✅ Components integrate with Enterprise Dashboard</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComprehensiveComponentVerification;
