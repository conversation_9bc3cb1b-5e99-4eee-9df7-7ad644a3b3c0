// Comprehensive validation utilities for the compliance dashboard

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  email?: boolean;
  url?: boolean;
  numeric?: boolean;
  integer?: boolean;
  min?: number;
  max?: number;
  custom?: (value: any) => string | null;
  dependencies?: string[];
}

export interface ValidationRules {
  [fieldName: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: { [fieldName: string]: string };
  warnings: { [fieldName: string]: string };
}

export interface FormData {
  [key: string]: any;
}

export class ValidationService {
  private static emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  private static urlRegex = /^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$/;
  private static strongPasswordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;

  static validateField(value: any, rule: ValidationRule, fieldName: string, allData?: FormData): string | null {
    // Handle required validation
    if (rule.required && this.isEmpty(value)) {
      return `${this.formatFieldName(fieldName)} is required`;
    }

    // Skip other validations if value is empty and not required
    if (this.isEmpty(value) && !rule.required) {
      return null;
    }

    // String validations
    if (typeof value === 'string') {
      // Length validations
      if (rule.minLength && value.length < rule.minLength) {
        return `${this.formatFieldName(fieldName)} must be at least ${rule.minLength} characters`;
      }

      if (rule.maxLength && value.length > rule.maxLength) {
        return `${this.formatFieldName(fieldName)} must not exceed ${rule.maxLength} characters`;
      }

      // Pattern validation
      if (rule.pattern && !rule.pattern.test(value)) {
        return `${this.formatFieldName(fieldName)} format is invalid`;
      }

      // Email validation
      if (rule.email && !this.emailRegex.test(value)) {
        return 'Please enter a valid email address';
      }

      // URL validation
      if (rule.url && !this.urlRegex.test(value)) {
        return 'Please enter a valid URL';
      }
    }

    // Numeric validations
    if (rule.numeric || rule.integer) {
      const numValue = typeof value === 'string' ? parseFloat(value) : value;
      
      if (isNaN(numValue)) {
        return `${this.formatFieldName(fieldName)} must be a valid number`;
      }

      if (rule.integer && !Number.isInteger(numValue)) {
        return `${this.formatFieldName(fieldName)} must be a whole number`;
      }

      if (rule.min !== undefined && numValue < rule.min) {
        return `${this.formatFieldName(fieldName)} must be at least ${rule.min}`;
      }

      if (rule.max !== undefined && numValue > rule.max) {
        return `${this.formatFieldName(fieldName)} must not exceed ${rule.max}`;
      }
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        return customError;
      }
    }

    return null;
  }

  static validateForm(data: FormData, rules: ValidationRules): ValidationResult {
    const errors: { [fieldName: string]: string } = {};
    const warnings: { [fieldName: string]: string } = {};

    // Validate each field
    Object.entries(rules).forEach(([fieldName, rule]) => {
      const value = data[fieldName];
      const error = this.validateField(value, rule, fieldName, data);
      
      if (error) {
        errors[fieldName] = error;
      }

      // Check for warnings (non-blocking validations)
      const warning = this.checkWarnings(value, rule, fieldName, data);
      if (warning) {
        warnings[fieldName] = warning;
      }
    });

    // Cross-field validations
    this.validateDependencies(data, rules, errors);

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      warnings
    };
  }

  static validateDependencies(data: FormData, rules: ValidationRules, errors: { [fieldName: string]: string }) {
    Object.entries(rules).forEach(([fieldName, rule]) => {
      if (rule.dependencies) {
        rule.dependencies.forEach(depField => {
          const depValue = data[depField];
          const currentValue = data[fieldName];

          // Example: if field A has value, field B is required
          if (!this.isEmpty(currentValue) && this.isEmpty(depValue)) {
            if (!errors[depField]) {
              errors[depField] = `${this.formatFieldName(depField)} is required when ${this.formatFieldName(fieldName)} is provided`;
            }
          }
        });
      }
    });
  }

  static checkWarnings(value: any, rule: ValidationRule, fieldName: string, allData?: FormData): string | null {
    // Password strength warning
    if (fieldName.toLowerCase().includes('password') && typeof value === 'string') {
      if (value.length > 0 && !this.strongPasswordRegex.test(value)) {
        return 'Consider using a stronger password with uppercase, lowercase, numbers, and special characters';
      }
    }

    // Date warnings
    if (fieldName.toLowerCase().includes('date') && value) {
      const date = new Date(value);
      const now = new Date();
      
      if (fieldName.toLowerCase().includes('expiry') && date < now) {
        return 'This date is in the past';
      }
      
      if (fieldName.toLowerCase().includes('review') && date < now) {
        return 'Review date should be in the future';
      }
    }

    return null;
  }

  private static isEmpty(value: any): boolean {
    if (value === null || value === undefined) return true;
    if (typeof value === 'string') return value.trim() === '';
    if (Array.isArray(value)) return value.length === 0;
    if (typeof value === 'object') return Object.keys(value).length === 0;
    return false;
  }

  private static formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .replace(/_/g, ' ');
  }

  // Predefined validation rules for common fields
  static getCommonRules(): { [key: string]: ValidationRule } {
    return {
      email: {
        required: true,
        email: true,
        maxLength: 255
      },
      name: {
        required: true,
        minLength: 2,
        maxLength: 100,
        pattern: /^[a-zA-Z\s'-]+$/
      },
      description: {
        required: true,
        minLength: 10,
        maxLength: 1000
      },
      url: {
        url: true,
        maxLength: 500
      },
      phone: {
        pattern: /^[\+]?[1-9][\d]{0,15}$/
      },
      percentage: {
        numeric: true,
        min: 0,
        max: 100
      },
      positiveInteger: {
        integer: true,
        min: 0
      },
      requiredSelect: {
        required: true,
        custom: (value) => {
          if (value === '' || value === 'select' || value === 'choose') {
            return 'Please make a selection';
          }
          return null;
        }
      }
    };
  }

  // Validation rules specific to compliance forms
  static getComplianceRules(): { [key: string]: ValidationRule } {
    return {
      policyName: {
        required: true,
        minLength: 3,
        maxLength: 200,
        pattern: /^[a-zA-Z0-9\s\-_()]+$/
      },
      policyDescription: {
        required: true,
        minLength: 20,
        maxLength: 2000
      },
      complianceScore: {
        required: true,
        numeric: true,
        min: 0,
        max: 100
      },
      riskLevel: {
        required: true,
        custom: (value) => {
          const validLevels = ['low', 'medium', 'high', 'critical'];
          if (!validLevels.includes(value)) {
            return 'Please select a valid risk level';
          }
          return null;
        }
      },
      effectiveDate: {
        required: true,
        custom: (value) => {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return 'Please enter a valid date';
          }
          return null;
        }
      },
      expiryDate: {
        required: true,
        custom: (value, fieldName, allData) => {
          const date = new Date(value);
          if (isNaN(date.getTime())) {
            return 'Please enter a valid date';
          }
          
          if (allData?.effectiveDate) {
            const effectiveDate = new Date(allData.effectiveDate);
            if (date <= effectiveDate) {
              return 'Expiry date must be after effective date';
            }
          }
          
          return null;
        },
        dependencies: ['effectiveDate']
      },
      departmentSelection: {
        required: true,
        custom: (value) => {
          if (!Array.isArray(value) || value.length === 0) {
            return 'At least one department must be selected';
          }
          return null;
        }
      }
    };
  }

  // Real-time validation for better UX
  static validateFieldRealTime(
    value: any, 
    rule: ValidationRule, 
    fieldName: string, 
    allData?: FormData
  ): { error: string | null; warning: string | null } {
    const error = this.validateField(value, rule, fieldName, allData);
    const warning = this.checkWarnings(value, rule, fieldName, allData);
    
    return { error, warning };
  }

  // Sanitize input to prevent XSS and other attacks
  static sanitizeInput(value: string): string {
    if (typeof value !== 'string') return value;
    
    return value
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+=/gi, '') // Remove event handlers
      .trim();
  }

  // Validate file uploads
  static validateFile(
    file: File,
    options: {
      maxSize?: number; // in bytes
      allowedTypes?: string[];
      allowedExtensions?: string[];
    } = {}
  ): string | null {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = ['image/*', 'application/pdf', 'text/*'],
      allowedExtensions = ['.jpg', '.jpeg', '.png', '.pdf', '.txt', '.doc', '.docx']
    } = options;

    // Check file size
    if (file.size > maxSize) {
      return `File size must not exceed ${Math.round(maxSize / (1024 * 1024))}MB`;
    }

    // Check file type
    const isTypeAllowed = allowedTypes.some(type => {
      if (type.endsWith('/*')) {
        return file.type.startsWith(type.slice(0, -1));
      }
      return file.type === type;
    });

    if (!isTypeAllowed) {
      return `File type ${file.type} is not allowed`;
    }

    // Check file extension
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedExtensions.includes(fileExtension)) {
      return `File extension ${fileExtension} is not allowed`;
    }

    return null;
  }
}
