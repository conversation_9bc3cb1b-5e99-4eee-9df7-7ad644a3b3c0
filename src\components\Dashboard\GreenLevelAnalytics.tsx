import React, { useState, useEffect } from 'react';
import { X, TrendingUp, <PERSON><PERSON><PERSON><PERSON>, Pie<PERSON>hart, Calendar, Download, RefreshCw } from 'lucide-react';
import { Line, Doughnut, Bar } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
} from 'chart.js';
import { useTheme } from '../../context/ThemeContext';
import { toast } from 'react-toastify';

// Register Chart.js components
ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  BarElement
);

interface AnalyticsData {
  trendData: {
    labels: string[];
    datasets: {
      label: string;
      data: number[];
      borderColor: string;
      backgroundColor: string;
    }[];
  };
  categoryBreakdown: {
    labels: string[];
    datasets: {
      data: number[];
      backgroundColor: string[];
      borderColor: string[];
    }[];
  };
  engagementMetrics: {
    conversionRate: number;
    avgResponseTime: string;
    userSatisfaction: number;
    retentionRate: number;
  };
  comparativeData: {
    green: number;
    amber: number;
    red: number;
  };
}

interface GreenLevelAnalyticsProps {
  isOpen: boolean;
  onClose: () => void;
  data: any;
}

const GreenLevelAnalytics: React.FC<GreenLevelAnalyticsProps> = ({ isOpen, onClose, data }) => {
  const { mode } = useTheme();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (isOpen) {
      loadAnalyticsData();
    }
  }, [isOpen]);

  const loadAnalyticsData = async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Generate mock analytics data with enhanced styling
      const mockData: AnalyticsData = {
        trendData: {
          labels: ['January', 'February', 'March', 'April', 'May', 'June'],
          datasets: [{
            label: 'Green Level Consent Rate',
            data: [28.2, 29.1, 30.3, 31.0, 31.8, 31.2],
            borderColor: '#22c55e',
            backgroundColor: (context: any) => {
              const chart = context.chart;
              const {ctx, chartArea} = chart;
              if (!chartArea) return null;

              const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom);
              gradient.addColorStop(0, 'rgba(34, 197, 94, 0.3)');
              gradient.addColorStop(0.5, 'rgba(34, 197, 94, 0.15)');
              gradient.addColorStop(1, 'rgba(34, 197, 94, 0.05)');
              return gradient;
            },
            borderWidth: 3,
            pointBackgroundColor: '#22c55e',
            pointBorderColor: '#ffffff',
            pointBorderWidth: 3,
            pointRadius: 6,
            pointHoverRadius: 8,
            pointHoverBackgroundColor: '#16a34a',
            pointHoverBorderColor: '#ffffff',
            pointHoverBorderWidth: 3,
            fill: true,
            tension: 0.4,
          }]
        },
        categoryBreakdown: {
          labels: ['Data Collection', 'Data Processing', 'Data Sharing', 'Marketing Consent'],
          datasets: [{
            data: [100, 98, 95, 87],
            backgroundColor: [
              '#22c55e',  // Primary green
              '#16a34a',  // Darker green
              '#15803d',  // Even darker green
              '#166534'   // Darkest green
            ],
            borderColor: [
              '#ffffff',
              '#ffffff',
              '#ffffff',
              '#ffffff'
            ],
            borderWidth: 3,
            hoverBackgroundColor: [
              '#16a34a',
              '#15803d',
              '#166534',
              '#14532d'
            ],
            hoverBorderColor: [
              '#ffffff',
              '#ffffff',
              '#ffffff',
              '#ffffff'
            ],
            hoverBorderWidth: 4,
            hoverOffset: 8
          }]
        },
        engagementMetrics: {
          conversionRate: 94.5,
          avgResponseTime: '2.3 min',
          userSatisfaction: 4.7,
          retentionRate: 89.2
        },
        comparativeData: {
          green: 31,
          amber: 58,
          red: 11
        }
      };

      setAnalyticsData(mockData);
    } catch (err) {
      setError('Failed to load analytics data');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportAnalytics = async () => {
    try {
      toast.info('Exporting analytics data...');
      await new Promise(resolve => setTimeout(resolve, 2000));
      toast.success('Analytics data exported successfully!');
    } catch (err) {
      toast.error('Failed to export analytics data');
    }
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    interaction: {
      intersect: false,
      mode: 'index' as const,
    },
    animation: {
      duration: 1500,
      easing: 'easeInOutQuart' as const,
      delay: (context: any) => context.dataIndex * 100,
    },
    elements: {
      point: {
        hoverRadius: 8,
        radius: 6,
      },
      line: {
        borderJoinStyle: 'round' as const,
        borderCapStyle: 'round' as const,
      }
    },
    plugins: {
      legend: {
        position: 'top' as const,
        align: 'start' as const,
        labels: {
          color: mode === 'dark' ? '#f3f4f6' : '#1f2937',
          font: {
            size: 14,
            weight: '600' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 24,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 12,
          boxHeight: 12
        }
      },
      tooltip: {
        enabled: true,
        backgroundColor: mode === 'dark' ? 'rgba(17, 24, 39, 0.98)' : 'rgba(255, 255, 255, 0.98)',
        titleColor: mode === 'dark' ? '#f9fafb' : '#111827',
        bodyColor: mode === 'dark' ? '#e5e7eb' : '#374151',
        borderColor: mode === 'dark' ? '#4b5563' : '#d1d5db',
        borderWidth: 2,
        cornerRadius: 12,
        padding: 16,
        titleFont: {
          size: 15,
          weight: '700' as const,
          family: 'Inter, system-ui, sans-serif'
        },
        bodyFont: {
          size: 14,
          weight: '500' as const,
          family: 'Inter, system-ui, sans-serif'
        },
        displayColors: true,
        boxPadding: 8,
        boxWidth: 12,
        boxHeight: 12,
        caretSize: 8,
        caretPadding: 12,
        titleMarginBottom: 12,
        bodySpacing: 8,
        callbacks: {
          title: function(context: any) {
            return `${context[0].label} 2024`;
          },
          label: function(context: any) {
            return `Compliance Rate: ${context.parsed.y.toFixed(1)}%`;
          },
          afterLabel: function(context: any) {
            const change = context.dataIndex > 0 ?
              (context.parsed.y - context.dataset.data[context.dataIndex - 1]).toFixed(1) : 0;
            return change > 0 ? `↗ +${change}% from previous month` :
                   change < 0 ? `↘ ${change}% from previous month` :
                   '→ No change from previous month';
          }
        }
      }
    },
    scales: {
      x: {
        title: {
          display: true,
          text: 'Time Period',
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          font: {
            size: 13,
            weight: '600' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 16
        },
        ticks: {
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
            weight: '500' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 12,
          maxRotation: 0
        },
        grid: {
          color: mode === 'dark' ? 'rgba(55, 65, 81, 0.2)' : 'rgba(229, 231, 235, 0.6)',
          lineWidth: 1,
          drawOnChartArea: true,
          drawTicks: false
        },
        border: {
          color: mode === 'dark' ? '#4b5563' : '#d1d5db',
          width: 2
        }
      },
      y: {
        beginAtZero: false,
        min: 25,
        max: 35,
        title: {
          display: true,
          text: 'Compliance Rate (%)',
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          font: {
            size: 13,
            weight: '600' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 16
        },
        ticks: {
          color: mode === 'dark' ? '#9ca3af' : '#6b7280',
          font: {
            size: 12,
            weight: '500' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 12,
          stepSize: 1,
          callback: function(value: any) {
            return value.toFixed(0) + '%';
          }
        },
        grid: {
          color: mode === 'dark' ? 'rgba(55, 65, 81, 0.2)' : 'rgba(229, 231, 235, 0.6)',
          lineWidth: 1,
          drawOnChartArea: true,
          drawTicks: false
        },
        border: {
          color: mode === 'dark' ? '#4b5563' : '#d1d5db',
          width: 2
        }
      }
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  };

  const doughnutOptions = {
    responsive: true,
    maintainAspectRatio: false,
    cutout: '70%',
    radius: '90%',
    animation: {
      animateRotate: true,
      animateScale: true,
      duration: 1800,
      easing: 'easeInOutQuart' as const,
      delay: (context: any) => context.dataIndex * 200,
    },
    elements: {
      arc: {
        borderWidth: 4,
        borderJoinStyle: 'round' as const,
        borderAlign: 'inner' as const,
      }
    },
    plugins: {
      legend: {
        position: 'right' as const,
        align: 'center' as const,
        labels: {
          color: mode === 'dark' ? '#f3f4f6' : '#1f2937',
          font: {
            size: 13,
            weight: '600' as const,
            family: 'Inter, system-ui, sans-serif'
          },
          padding: 20,
          usePointStyle: true,
          pointStyle: 'circle',
          boxWidth: 16,
          boxHeight: 16,
          generateLabels: function(chart: any) {
            const data = chart.data;
            if (data.labels.length && data.datasets.length) {
              return data.labels.map((label: string, i: number) => {
                const dataset = data.datasets[0];
                const value = dataset.data[i];
                const total = dataset.data.reduce((a: number, b: number) => a + b, 0);
                const percentage = ((value / total) * 100).toFixed(1);
                return {
                  text: `${label}`,
                  fillStyle: dataset.backgroundColor[i],
                  strokeStyle: dataset.borderColor[i],
                  lineWidth: 3,
                  hidden: false,
                  index: i,
                  fontColor: mode === 'dark' ? '#f3f4f6' : '#1f2937'
                };
              });
            }
            return [];
          }
        }
      },
      tooltip: {
        enabled: true,
        backgroundColor: mode === 'dark' ? 'rgba(17, 24, 39, 0.98)' : 'rgba(255, 255, 255, 0.98)',
        titleColor: mode === 'dark' ? '#f9fafb' : '#111827',
        bodyColor: mode === 'dark' ? '#e5e7eb' : '#374151',
        borderColor: mode === 'dark' ? '#4b5563' : '#d1d5db',
        borderWidth: 2,
        cornerRadius: 12,
        padding: 16,
        titleFont: {
          size: 15,
          weight: '700' as const,
          family: 'Inter, system-ui, sans-serif'
        },
        bodyFont: {
          size: 14,
          weight: '500' as const,
          family: 'Inter, system-ui, sans-serif'
        },
        displayColors: true,
        boxPadding: 8,
        boxWidth: 12,
        boxHeight: 12,
        caretSize: 8,
        caretPadding: 12,
        titleMarginBottom: 12,
        bodySpacing: 8,
        callbacks: {
          title: function(context: any) {
            return context[0].label;
          },
          label: function(context: any) {
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `Compliance Rate: ${value}%`;
          },
          afterLabel: function(context: any) {
            const value = context.parsed;
            const total = context.dataset.data.reduce((a: number, b: number) => a + b, 0);
            const percentage = ((value / total) * 100).toFixed(1);
            return `Share of Total: ${percentage}%`;
          }
        }
      }
    },
    layout: {
      padding: {
        top: 20,
        right: 20,
        bottom: 20,
        left: 20
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-card rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <div>
            <h2 className="text-2xl font-bold text-text">Green Level Analytics</h2>
            <p className="text-text-secondary">Comprehensive compliance analytics and insights</p>
          </div>
          <div className="flex items-center gap-3">
            <button
              onClick={handleExportAnalytics}
              className="flex items-center px-3 py-2 text-sm border border-border rounded-md hover:bg-surface transition-colors text-text"
            >
              <Download className="w-4 h-4 mr-2" />
              Export
            </button>
            <button
              onClick={onClose}
              className="p-2 hover:bg-surface rounded-md transition-colors text-text-secondary hover:text-text"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {isLoading ? (
            <div className="space-y-8 animate-pulse">
              {/* Skeleton for trend chart */}
              <div className="bg-surface rounded-lg p-6">
                <div className="flex items-center mb-4">
                  <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mr-2"></div>
                  <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-48"></div>
                </div>
                <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              </div>

              {/* Skeleton for category breakdown and metrics */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-surface rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mr-2"></div>
                    <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-40"></div>
                  </div>
                  <div className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                </div>

                <div className="bg-surface rounded-lg p-6">
                  <div className="flex items-center mb-4">
                    <div className="w-5 h-5 bg-gray-300 dark:bg-gray-600 rounded mr-2"></div>
                    <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-36"></div>
                  </div>
                  <div className="space-y-4">
                    {[1, 2, 3, 4].map((i) => (
                      <div key={i} className="flex justify-between items-center">
                        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-24"></div>
                        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Skeleton for comparative analytics */}
              <div className="bg-surface rounded-lg p-6">
                <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-44 mb-4"></div>
                <div className="grid grid-cols-3 gap-4">
                  {[1, 2, 3].map((i) => (
                    <div key={i} className="text-center p-4 bg-card rounded-lg">
                      <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-16 mx-auto mb-2"></div>
                      <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-20 mx-auto"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ) : error ? (
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <p className="text-red-500 mb-4">{error}</p>
                <button
                  onClick={loadAnalyticsData}
                  className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-hover transition-colors"
                >
                  Retry
                </button>
              </div>
            </div>
          ) : analyticsData ? (
            <div className="space-y-8">
              {/* Trend Chart */}
              <div className="bg-gradient-to-br from-surface to-surface/80 rounded-xl p-8 border border-border/50 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
                         style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                      <TrendingUp className="w-6 h-6" style={{ color: 'var(--dashboard-green)' }} />
                    </div>
                    <div>
                      <h3 className="text-xl font-bold text-text">Consent Trends Over Time</h3>
                      <p className="text-text-secondary text-sm">Monthly compliance rate progression</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-2xl font-bold" style={{ color: 'var(--dashboard-green)' }}>
                      +2.8%
                    </div>
                    <div className="text-text-secondary text-sm">6-month growth</div>
                  </div>
                </div>
                <div className="h-80 relative">
                  <div className="absolute inset-0 bg-gradient-to-t from-card/50 to-transparent rounded-lg"></div>
                  <Line data={analyticsData.trendData} options={chartOptions} />
                </div>
              </div>

              {/* Category Breakdown and Engagement Metrics */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div className="bg-gradient-to-br from-surface to-surface/80 rounded-xl p-6 border border-border/50 shadow-lg hover:shadow-xl transition-shadow duration-300">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 rounded-xl flex items-center justify-center mr-3"
                         style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                      <PieChart className="w-5 h-5" style={{ color: 'var(--dashboard-green)' }} />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-text">Category Breakdown</h3>
                      <p className="text-text-secondary text-sm">Compliance by data category</p>
                    </div>
                  </div>
                  <div className="h-72 relative">
                    <div className="absolute inset-0 bg-gradient-to-t from-card/30 to-transparent rounded-lg"></div>
                    <Doughnut data={analyticsData.categoryBreakdown} options={doughnutOptions} />
                  </div>
                  <div className="mt-4 grid grid-cols-2 gap-3">
                    <div className="text-center p-3 bg-card rounded-lg border border-border/30">
                      <div className="text-lg font-bold text-text">100%</div>
                      <div className="text-xs text-text-secondary">Data Collection</div>
                    </div>
                    <div className="text-center p-3 bg-card rounded-lg border border-border/30">
                      <div className="text-lg font-bold text-text">87%</div>
                      <div className="text-xs text-text-secondary">Marketing</div>
                    </div>
                  </div>
                </div>

                <div className="bg-surface rounded-lg p-6 border border-border/50 shadow-sm hover:shadow-md transition-shadow duration-300">
                  <div className="flex items-center mb-6">
                    <div className="w-10 h-10 rounded-lg flex items-center justify-center mr-3"
                         style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                      <BarChart3 className="w-5 h-5" style={{ color: 'var(--dashboard-green)' }} />
                    </div>
                    <h3 className="text-lg font-semibold text-text">Engagement Metrics</h3>
                  </div>
                  <div className="grid grid-cols-1 gap-4">
                    <div className="bg-card rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-text-secondary">Conversion Rate</span>
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: 'var(--dashboard-green)' }}></div>
                          <span className="text-lg font-bold text-text">{analyticsData.engagementMetrics.conversionRate}%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{
                            backgroundColor: 'var(--dashboard-green)',
                            width: `${analyticsData.engagementMetrics.conversionRate}%`
                          }}
                        ></div>
                      </div>
                    </div>

                    <div className="bg-card rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors duration-200">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium text-text-secondary">Avg. Response Time</span>
                        <span className="text-lg font-bold text-text">{analyticsData.engagementMetrics.avgResponseTime}</span>
                      </div>
                    </div>

                    <div className="bg-card rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-text-secondary">User Satisfaction</span>
                        <span className="text-lg font-bold text-text">{analyticsData.engagementMetrics.userSatisfaction}/5</span>
                      </div>
                      <div className="flex space-x-1">
                        {[1, 2, 3, 4, 5].map((star) => (
                          <div
                            key={star}
                            className={`w-4 h-4 rounded-sm ${
                              star <= analyticsData.engagementMetrics.userSatisfaction
                                ? 'bg-yellow-400'
                                : 'bg-gray-300 dark:bg-gray-600'
                            }`}
                          ></div>
                        ))}
                      </div>
                    </div>

                    <div className="bg-card rounded-lg p-4 border border-border/30 hover:border-border/60 transition-colors duration-200">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium text-text-secondary">Retention Rate</span>
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full mr-2" style={{ backgroundColor: 'var(--dashboard-green)' }}></div>
                          <span className="text-lg font-bold text-text">{analyticsData.engagementMetrics.retentionRate}%</span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out"
                          style={{
                            backgroundColor: 'var(--dashboard-green)',
                            width: `${analyticsData.engagementMetrics.retentionRate}%`
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Comparative Analytics */}
              <div className="bg-gradient-to-br from-surface to-surface/80 rounded-xl p-8 border border-border/50 shadow-lg hover:shadow-xl transition-shadow duration-300">
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-xl flex items-center justify-center mr-4"
                       style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                    <BarChart3 className="w-6 h-6" style={{ color: 'var(--dashboard-green)' }} />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-text">Comparative Analysis</h3>
                    <p className="text-text-secondary text-sm">Distribution across compliance levels</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border-2 border-green-200 dark:border-green-800 hover:border-green-300 dark:hover:border-green-700 transition-all duration-300 hover:shadow-lg">
                      <div className="absolute top-4 right-4">
                        <div className="w-3 h-3 rounded-full bg-green-500 animate-pulse"></div>
                      </div>
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                           style={{ backgroundColor: 'rgba(34, 197, 94, 0.1)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--dashboard-green)' }}>
                          {analyticsData.comparativeData.green}%
                        </div>
                      </div>
                      <div className="text-text font-semibold mb-2">Green Level</div>
                      <div className="text-text-secondary text-sm">Fully Compliant</div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out bg-green-500"
                          style={{ width: `${analyticsData.comparativeData.green}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border-2 border-yellow-200 dark:border-yellow-800 hover:border-yellow-300 dark:hover:border-yellow-700 transition-all duration-300 hover:shadow-lg">
                      <div className="absolute top-4 right-4">
                        <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
                      </div>
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                           style={{ backgroundColor: 'rgba(245, 158, 11, 0.1)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--dashboard-amber)' }}>
                          {analyticsData.comparativeData.amber}%
                        </div>
                      </div>
                      <div className="text-text font-semibold mb-2">Amber Level</div>
                      <div className="text-text-secondary text-sm">Under Review</div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out bg-yellow-500"
                          style={{ width: `${analyticsData.comparativeData.amber}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  <div className="relative group">
                    <div className="text-center p-6 bg-card rounded-xl border-2 border-red-200 dark:border-red-800 hover:border-red-300 dark:hover:border-red-700 transition-all duration-300 hover:shadow-lg">
                      <div className="absolute top-4 right-4">
                        <div className="w-3 h-3 rounded-full bg-red-500"></div>
                      </div>
                      <div className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                           style={{ backgroundColor: 'rgba(239, 68, 68, 0.1)' }}>
                        <div className="text-2xl font-bold" style={{ color: 'var(--dashboard-red)' }}>
                          {analyticsData.comparativeData.red}%
                        </div>
                      </div>
                      <div className="text-text font-semibold mb-2">Red Level</div>
                      <div className="text-text-secondary text-sm">Non-Compliant</div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-3">
                        <div
                          className="h-2 rounded-full transition-all duration-1000 ease-out bg-red-500"
                          style={{ width: `${analyticsData.comparativeData.red}%` }}
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="mt-6 p-4 bg-card rounded-lg border border-border/30">
                  <div className="text-center">
                    <div className="text-sm text-text-secondary mb-2">Total Data Subjects</div>
                    <div className="text-2xl font-bold text-text">
                      {(analyticsData.comparativeData.green + analyticsData.comparativeData.amber + analyticsData.comparativeData.red).toLocaleString()}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : null}
        </div>
      </div>
    </div>
  );
};

export default GreenLevelAnalytics;
