import React, { useState, useEffect } from 'react';
import { useTheme } from '../../context/ThemeContext';
import { RegulatoryFramework, RegulatoryComplianceMetrics } from '../../types/gdprTypes';
import { RegulatoryFrameworkService } from '../../services/regulatoryFrameworkService';
import { RegulatoryFrameworkModal } from './RegulatoryFrameworkModal';
import { RegulatoryFrameworkViewModal } from './RegulatoryFrameworkViewModal';
import { ConfirmationModal } from '../ui/Modal';
import { NotificationContainer, useNotifications } from '../ui/Notification';
import { Button, IconButton } from '../ui/Button';
import { FormInput, FormSelect } from '../ui/FormInput';
import {
  Scale,
  FileText,
  AlertCircle,
  Calendar,
  Plus,
  Search,
  Filter,
  Edit,
  Trash2,
  CheckSquare,
  AlertTriangle,
  Eye
} from 'lucide-react';

interface RegulatoryFrameworkDashboardProps {
  className?: string;
}

export const RegulatoryFrameworkDashboard: React.FC<RegulatoryFrameworkDashboardProps> = React.memo(({ className = '' }) => {
  const { mode } = useTheme();
  const notifications = useNotifications();

  const [frameworks, setFrameworks] = useState<RegulatoryFramework[]>([]);
  const [metrics, setMetrics] = useState<RegulatoryComplianceMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  // Modal states
  const [showFrameworkModal, setShowFrameworkModal] = useState(false);
  const [editingFramework, setEditingFramework] = useState<RegulatoryFramework | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deletingFramework, setDeletingFramework] = useState<RegulatoryFramework | null>(null);

  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [jurisdictionFilter, setJurisdictionFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([]);

  // View modal states
  const [showViewModal, setShowViewModal] = useState(false);
  const [viewingFramework, setViewingFramework] = useState<RegulatoryFramework | null>(null);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      const [frameworksData, metricsData] = await Promise.all([
        RegulatoryFrameworkService.getAllFrameworks(),
        RegulatoryFrameworkService.getComplianceMetrics()
      ]);

      setFrameworks(frameworksData);
      setMetrics(metricsData);
    } catch (error) {
      console.error('Error loading regulatory framework data:', error);
      notifications.error('Failed to load regulatory framework data');
    } finally {
      setLoading(false);
    }
  };

  // CRUD Operations
  const handleCreateFramework = () => {
    setEditingFramework(null);
    setShowFrameworkModal(true);
  };

  const handleEditFramework = (framework: RegulatoryFramework) => {
    setEditingFramework(framework);
    setShowFrameworkModal(true);
  };

  const handleSaveFramework = async (frameworkData: Partial<RegulatoryFramework>) => {
    try {
      if (editingFramework) {
        // Update existing framework
        const updatedFramework = await RegulatoryFrameworkService.updateFramework(editingFramework.id, frameworkData);
        if (updatedFramework) {
          setFrameworks(prev => prev.map(f => f.id === editingFramework.id ? updatedFramework : f));
          notifications.success('Regulatory framework updated successfully');
        }
      } else {
        // Create new framework
        const newFramework = await RegulatoryFrameworkService.createFramework(frameworkData);
        setFrameworks(prev => [newFramework, ...prev]);
        notifications.success('Regulatory framework created successfully');
      }

      // Refresh metrics
      const updatedMetrics = await RegulatoryFrameworkService.getComplianceMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error saving regulatory framework:', error);
      notifications.error('Failed to save regulatory framework');
      throw error;
    }
  };

  const handleViewFramework = (framework: RegulatoryFramework) => {
    setViewingFramework(framework);
    setShowViewModal(true);
  };

  const handleDeleteFramework = (framework: RegulatoryFramework) => {
    setDeletingFramework(framework);
    setShowDeleteConfirm(true);
  };

  const confirmDelete = async () => {
    if (!deletingFramework) return;

    try {
      await RegulatoryFrameworkService.deleteFramework(deletingFramework?.id);
      setFrameworks(prev => prev.filter(f => f.id !== deletingFramework.id));
      setSelectedFrameworks(prev => prev.filter(id => id !== deletingFramework.id));
      notifications.success('Regulatory framework deleted successfully');

      // Refresh metrics
      const updatedMetrics = await RegulatoryFrameworkService.getComplianceMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error deleting regulatory framework:', error);
      notifications.error('Failed to delete regulatory framework');
    } finally {
      setShowDeleteConfirm(false);
      setDeletingFramework(null);
    }
  };

  // Bulk operations
  const handleBulkDelete = async () => {
    if (selectedFrameworks.length === 0) return;

    try {
      await Promise.all(selectedFrameworks?.map(id => RegulatoryFrameworkService.deleteFramework(id)) || []);
      setFrameworks(prev => prev.filter(f => !selectedFrameworks.includes(f.id)));
      setSelectedFrameworks([]);
      notifications.success(`${selectedFrameworks.length} regulatory frameworks deleted successfully`);

      // Refresh metrics
      const updatedMetrics = await RegulatoryFrameworkService.getComplianceMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error bulk deleting frameworks:', error);
      notifications.error('Failed to delete selected frameworks');
    }
  };

  const handleBulkStatusUpdate = async (status: string) => {
    if (selectedFrameworks.length === 0) return;

    try {
      const updates = selectedFrameworks.map(id =>
        RegulatoryFrameworkService.updateFramework(id, { status: status as any })
      );
      const updatedFrameworks = await Promise.all(updates);

      setFrameworks(prev => prev.map(f => {
        const updated = updatedFrameworks.find(u => u && u.id === f.id);
        return updated || f;
      }));

      setSelectedFrameworks([]);
      notifications.success(`${selectedFrameworks.length} frameworks updated successfully`);

      // Refresh metrics
      const updatedMetrics = await RegulatoryFrameworkService.getComplianceMetrics();
      setMetrics(updatedMetrics);
    } catch (error) {
      console.error('Error bulk updating frameworks:', error);
      notifications.error('Failed to update selected frameworks');
    }
  };

  // Selection handlers
  const handleSelectFramework = (frameworkId: string) => {
    setSelectedFrameworks(prev =>
      prev.includes(frameworkId)
        ? prev.filter(id => id !== frameworkId)
        : [...prev, frameworkId]
    );
  };

  const handleSelectAll = () => {
    const filteredFrameworks = getFilteredFrameworks();
    const allSelected = filteredFrameworks.every(f => selectedFrameworks.includes(f.id));
    
    if (allSelected) {
      setSelectedFrameworks(prev => prev.filter(id => !filteredFrameworks.some(f => f.id === id)));
    } else {
      const newSelections = filteredFrameworks?.map(f => f?.id) || [];
      setSelectedFrameworks(prev => [...new Set([...(prev || []), ...newSelections])]);
    }
  };

  // Filtering
  const getFilteredFrameworks = () => {
    return frameworks.filter(framework => {
      const matchesSearch = !searchTerm ||
        framework?.name?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        framework?.description?.toLowerCase()?.includes(searchTerm.toLowerCase()) ||
        framework?.owner?.toLowerCase()?.includes(searchTerm.toLowerCase());

      const matchesType = typeFilter === 'all' || framework.type === typeFilter;
      const matchesJurisdiction = jurisdictionFilter === 'all' || framework.jurisdiction === jurisdictionFilter;
      const matchesStatus = statusFilter === 'all' || framework.status === statusFilter;

      return matchesSearch && matchesType && matchesJurisdiction && matchesStatus;
    });
  };

  // Utility functions
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'draft': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      case 'deprecated': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'gdpr': return <Scale className="w-4 h-4" />;
      case 'ccpa': case 'cpra': return <FileText className="w-4 h-4" />;
      case 'sox': return <AlertCircle className="w-4 h-4" />;
      default: return <FileText className="w-4 h-4" />;
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(date);
  };

  if (loading) {
    return (
      <div className={`${className} flex items-center justify-center h-64`}>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  const filteredFrameworks = getFilteredFrameworks();
  const allSelected = filteredFrameworks?.length > 0 && filteredFrameworks?.every(f => selectedFrameworks?.includes(f?.id));
  const someSelected = selectedFrameworks.length > 0;

  return (
    <div className={`${className} space-y-6`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <div className={`p-3 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-primary/20 to-primary/10'} rounded-xl`}>
            <Scale className="w-6 h-6 text-primary" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-text">Regulatory Framework Dashboard</h2>
            <p className="text-text-secondary">Manage compliance frameworks and regulatory requirements</p>
          </div>
        </div>
        <Button
          onClick={handleCreateFramework}
          leftIcon={<Plus className="w-4 h-4" />}
        >
          Create Framework
        </Button>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-primary/20 transition-all duration-200`}
            onClick={() => {
              // Reset filters to show all frameworks
              setSearchTerm('');
              setTypeFilter('all');
              setJurisdictionFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Total Frameworks</p>
                <p className="text-2xl font-bold text-text">{metrics.totalFrameworks}</p>
              </div>
              <Scale className="w-8 h-8 text-primary" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-green-500/20 transition-all duration-200`}
            onClick={() => {
              // Show compliance score details
              setSearchTerm('');
              setTypeFilter('all');
              setJurisdictionFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Compliance Score</p>
                <p className="text-2xl font-bold text-green-500">{metrics.overallComplianceScore.toFixed(1)}%</p>
              </div>
              <CheckSquare className="w-8 h-8 text-green-500" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-yellow-500/20 transition-all duration-200`}
            onClick={() => {
              // Filter to show upcoming deadlines
              setSearchTerm('');
              setTypeFilter('all');
              setJurisdictionFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Upcoming Deadlines</p>
                <p className="text-2xl font-bold text-yellow-500">{metrics.upcomingDeadlines}</p>
              </div>
              <Calendar className="w-8 h-8 text-yellow-500" />
            </div>
          </div>

          <div
            className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border cursor-pointer hover:shadow-lg hover:border-red-500/20 transition-all duration-200`}
            onClick={() => {
              // Filter to show overdue items
              setSearchTerm('');
              setTypeFilter('all');
              setJurisdictionFilter('all');
              setStatusFilter('all');
            }}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-text-secondary">Overdue Items</p>
                <p className="text-2xl font-bold text-red-500">{metrics.overdueDeadlines}</p>
              </div>
              <AlertTriangle className="w-8 h-8 text-red-500" />
            </div>
          </div>
        </div>
      )}

      {/* Framework Table */}
      <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
        <div className="flex flex-col lg:flex-row gap-4 mb-6">
          <div className="flex-1 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-text-secondary" />
            <FormInput
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search frameworks..."
              className="pl-10"
            />
          </div>

          <FormSelect
            value={typeFilter}
            onChange={(e) => setTypeFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Types' },
              { value: 'gdpr', label: 'GDPR' },
              { value: 'ccpa', label: 'CCPA' },
              { value: 'cpra', label: 'CPRA' },
              { value: 'sox', label: 'SOX' },
              { value: 'hipaa', label: 'HIPAA' },
              { value: 'pci_dss', label: 'PCI DSS' },
              { value: 'iso_27001', label: 'ISO 27001' },
              { value: 'other', label: 'Other' }
            ]}
          />

          <FormSelect
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            options={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'draft', label: 'Draft' },
              { value: 'deprecated', label: 'Deprecated' },
              { value: 'pending', label: 'Pending' }
            ]}
          />
        </div>

        {/* Framework List */}
        <div className="space-y-4">
          {filteredFrameworks.length === 0 ? (
            <div className="text-center py-12">
              <Scale className="w-12 h-12 text-text-secondary mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-text mb-2">No regulatory frameworks found</h3>
              <p className="text-text-secondary mb-4">
                {searchTerm || typeFilter !== 'all' || statusFilter !== 'all'
                  ? 'Try adjusting your search or filters'
                  : 'Create your first regulatory framework to get started'
                }
              </p>
              {!searchTerm && typeFilter === 'all' && statusFilter === 'all' && (
                <Button onClick={handleCreateFramework} leftIcon={<Plus className="w-4 h-4" />}>
                  Create First Framework
                </Button>
              )}
            </div>
          ) : (
            filteredFrameworks.map((framework) => (
              <div
                key={framework.id}
                className="bg-surface rounded-lg p-4 border border-border hover:shadow-lg hover:border-primary/20 transition-all duration-200 cursor-pointer"
                onClick={() => handleViewFramework(framework)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      {getTypeIcon(framework.type)}
                      <div>
                        <h3 className="font-semibold text-text">{framework?.name || 'Unknown Framework'}</h3>
                        <p className="text-sm text-text-secondary">v{framework.version} • {framework?.jurisdiction?.toUpperCase() || 'UNKNOWN'}</p>
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center gap-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(framework.status)}`}>
                      {framework?.status?.toUpperCase() || 'Unknown'}
                    </span>
                    <div className="text-center">
                      <span className="text-lg font-bold text-text">{framework?.requirements?.length || 0}</span>
                      <div className="text-xs text-text-secondary">Requirements</div>
                    </div>
                    <div className="flex items-center gap-2">
                      <IconButton
                        size="sm"
                        variant="ghost"
                        onClick={() => handleViewFramework(framework)}
                        icon={<Eye className="w-4 h-4" />}
                        ariaLabel="View framework details"
                      />
                      <IconButton
                        size="sm"
                        variant="ghost"
                        onClick={() => handleEditFramework(framework)}
                        icon={<Edit className="w-4 h-4" />}
                        ariaLabel="Edit framework"
                      />
                      <IconButton
                        size="sm"
                        variant="ghost"
                        onClick={() => handleDeleteFramework(framework)}
                        className="text-red-600 hover:text-red-700"
                        icon={<Trash2 className="w-4 h-4" />}
                        ariaLabel="Delete framework"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>

      {/* Modals */}
      <RegulatoryFrameworkModal
        isOpen={showFrameworkModal}
        onClose={() => setShowFrameworkModal(false)}
        onSave={handleSaveFramework}
        framework={editingFramework}
        mode={editingFramework ? 'edit' : 'create'}
      />

      <RegulatoryFrameworkViewModal
        isOpen={showViewModal}
        onClose={() => setShowViewModal(false)}
        framework={viewingFramework}
      />

      <ConfirmationModal
        isOpen={showDeleteConfirm}
        onClose={() => setShowDeleteConfirm(false)}
        onConfirm={confirmDelete}
        title="Delete Regulatory Framework"
        message={`Are you sure you want to delete "${deletingFramework?.name}"? This action cannot be undone and will remove all associated requirements and deadlines.`}
        confirmText="Delete"
        type="danger"
      />

      {/* Notifications */}
      <NotificationContainer
        notifications={notifications.notifications}
        onClose={notifications.removeNotification}
      />
    </div>
  );
});

RegulatoryFrameworkDashboard.displayName = 'RegulatoryFrameworkDashboard';

export default RegulatoryFrameworkDashboard;
