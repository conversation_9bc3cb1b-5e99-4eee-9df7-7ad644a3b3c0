# GDPR Compliance Analysis - Setup Instructions

This document provides instructions for running both the FastAPI backend and the React frontend for the GDPR Compliance Analysis feature.

## Prerequisites

1. **Python Environment**: Anaconda or Miniconda installed
2. **Node.js**: Version 16 or higher
3. **Required Python packages**: Install using the requirements.txt file

## Running the Application

### Step 1: Start the FastAPI Backend

1. Open **Anaconda Command Prompt** (or Anaconda PowerShell Prompt)
2. Navigate to your project directory:
   ```bash
   cd "C:\Users\<USER>\Desktop\python\Office Project\WebSite\compliancee"
   ```
3. Activate your Python environment (if needed):
   ```bash
   conda activate your_environment_name
   ```
4. Install required Python packages:
   ```bash
   pip install -r requirements.txt
   ```
5. Start the FastAPI server:
   ```bash
   python gdpr_compliance_fastapi.py
   ```

The API server will start at: `http://127.0.0.1:8000`

You should see output like:
```
INFO:     Started server process [XXXX]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
```

### Step 2: Start the React Frontend

1. Open a **new** Command Prompt or PowerShell window
2. Navigate to the same project directory:
   ```bash
   cd "C:\Users\<USER>\Desktop\python\Office Project\WebSite\compliancee"
   ```
3. Start the Vite development server:
   ```bash
   npm run dev
   ```

The frontend will start at: `http://localhost:5173`

You should see output like:
```
VITE v5.4.19  ready in 717 ms
➜  Local:   http://localhost:5173/
➜  Network: use --host to expose
```

## Using the GDPR Analysis Feature

1. Open your web browser and go to `http://localhost:5173`
2. In the sidebar, click on **"GDPR Analysis"**
3. Choose your analysis method:
   - **Text Input**: Paste privacy policy text directly
   - **File Upload**: Upload PDF, TXT, or MD files

### Supported File Types
- PDF (.pdf)
- Text files (.txt)
- Markdown files (.md)
- Maximum file size: 10MB

## API Endpoints

The FastAPI backend provides several endpoints:

- `GET /health` - Check API health status
- `POST /analyze/text` - Analyze privacy policy from text
- `POST /analyze/file` - Analyze privacy policy from file
- `GET /requirements` - Get list of GDPR requirements
- `GET /list-companies` - List all analyzed companies
- `GET /list-results/{company_name}` - List results for specific company
- `GET /download/{company_name}/{filename}` - Download analysis files
- `GET /docs` - Interactive API documentation

## Troubleshooting

### API Connection Issues
- Ensure the FastAPI server is running on `http://127.0.0.1:8000`
- Check the API status indicator in the GDPR Analysis page
- Verify CORS settings allow connections from `http://localhost:5173`

### File Upload Issues
- Check file size (must be under 10MB)
- Verify file type is supported (PDF, TXT, MD)
- Ensure the file is not corrupted

### Python Dependencies
If you encounter import errors, install missing packages:
```bash
pip install fastapi uvicorn python-multipart langchain langchain-community langchain-huggingface pandas requests python-dotenv
```

### Environment Variables
Create a `.env` file in the project root with:
```
COHERE_API_KEY=your_cohere_api_key_here
```

## Development Notes

- The frontend runs on port 5173 (Vite default)
- The backend runs on port 8000 (FastAPI default)
- Both servers support hot reload for development
- CORS is configured to allow cross-origin requests between the two servers

## Production Deployment

For production deployment:
1. Build the frontend: `npm run build`
2. Serve the built files using a web server
3. Update CORS settings in the FastAPI to allow production URLs
4. Use a production WSGI server like Gunicorn for the FastAPI backend
