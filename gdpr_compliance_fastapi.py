import os
import pandas as pd
import requests
import json
import re
import random
import numpy as np
import hashlib
from datetime import datetime
from typing import Optional, Dict, Any, List
import tempfile
import shutil
from pathlib import Path

from fastapi import FastAPI, File, UploadFile, HTTPException, BackgroundTasks
from fastapi.responses import JSONResponse, FileResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from contextlib import asynccontextmanager

from dotenv import load_dotenv
from langchain_community.document_loaders import PyPDFLoader
from langchain.text_splitter import RecursiveCharacterTextSplitter
try:
    from langchain_huggingface import HuggingFaceEmbeddings
except ImportError:
    from langchain_community.embeddings import HuggingFaceEmbeddings
from langchain_community.vectorstores import FAISS
from langchain.llms.base import LLM

from gdpr_config import GDPR_REQUIREMENTS, ADDITIONAL_QUERIES, COMPLIANCE_PATTERNS
from gdpr_utils import normalize_text, clean_text_content, save_results, display_results

# Set global seeds for maximum reproducibility
os.environ['PYTHONHASHSEED'] = '0'
random.seed(42)
np.random.seed(42)
load_dotenv()

# Global variables for the RAG model
rag_model = None
gdpr_initialized = False

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan context manager for startup and shutdown events"""
    # Startup
    await initialize_system()
    yield
    # Shutdown (if needed)
    pass

# FastAPI app initialization
app = FastAPI(
    title="GDPR Compliance Analysis API",
    description="AI-powered GDPR compliance analysis using Cohere and RAG technology",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:5173", "http://127.0.0.1:5173"],  # Updated with Vite dev server URL
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models for request/response
class PolicyAnalysisRequest(BaseModel):
    policy_text: str = Field(..., description="Privacy policy text to analyze")
    policy_name: Optional[str] = Field("Unknown Policy", description="Name of the policy")

class ComplianceResult(BaseModel):
    gdpr_requirement: str
    company_policy: str
    compliance_status: str
    notes_gaps: str

class AnalysisResponse(BaseModel):
    success: bool
    analysis_id: str
    policy_name: str
    timestamp: str
    total_requirements: int
    compliance_score: float
    compliance_level: str
    status_breakdown: Dict[str, int]
    detailed_analysis: str
    compliance_results: List[ComplianceResult]
    files_generated: List[str]

class HealthResponse(BaseModel):
    status: str
    message: str
    timestamp: str

class CohereLLM(LLM):
    """Custom LLM wrapper for Cohere API"""
    
    api_key: str
    model: str = "command-r-plus"
    temperature: float = 0
    max_tokens: int = 3000
    
    def __init__(self, api_key: str, **kwargs):
        super().__init__(api_key=api_key, **kwargs)
        self.api_key = api_key
        
    @property
    def _llm_type(self) -> str:
        return "cohere"
    
    def _call(self, prompt: str, stop: Optional[List[str]] = None) -> str:
        """Call the Cohere API with maximum determinism"""
        prompt_hash = hashlib.md5(prompt.encode()).hexdigest()
        deterministic_seed = int(prompt_hash[:8], 16) % 1000000
        
        headers = {
            "accept": "application/json",
            "content-type": "application/json",
            "Authorization": f"Bearer {self.api_key}"
        }
        
        data = {
            "message": prompt,
            "model": self.model,
            "temperature": 0.0,
            "max_tokens": self.max_tokens,
            "k": 0,
            "p": 0.05,
            "frequency_penalty": 0.0,
            "presence_penalty": 0.0,
            "seed": deterministic_seed,
            "stop_sequences": [],
            "return_likelihoods": "NONE",
            "truncate": "END"
        }
        
        try:
            response = requests.post(
                "https://api.cohere.ai/v1/chat",
                headers=headers,
                json=data,
                timeout=120
            )
            response.raise_for_status()
            result = response.json()
            return result["text"]
        except Exception as e:
            raise Exception(f"Cohere API error: {str(e)}")
    
    @property
    def _identifying_params(self) -> dict:
        return {
            "model": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens
        }

class GDPRComplianceRAG:
    """Advanced RAG model for GDPR compliance checking with Cohere"""
    
    def __init__(self, cohere_api_key):
        self.vector_store = None
        self.cohere_api_key = cohere_api_key
        self._analysis_cache = {}
        self.gdpr_requirements_config = GDPR_REQUIREMENTS
        
    def _normalize_text(self, text):
        """Normalize text for consistent processing"""
        normalized = re.sub(r'\s+', ' ', text.strip())
        normalized = ''.join(char for char in normalized if ord(char) >= 32 or char in '\n\t')
        return normalized
        
    def setup_embeddings(self):
        """Initialize embeddings model"""
        return HuggingFaceEmbeddings(
            model_name="sentence-transformers/all-MiniLM-L6-v2",
            model_kwargs={'device': 'cpu'}
        )
    
    def load_gdpr_document(self, pdf_path):
        """Load and process GDPR PDF document"""
        try:
            print(f"Loading GDPR document from: {pdf_path}")
            loader = PyPDFLoader(pdf_path)
            documents = loader.load()
            
            text_splitter = RecursiveCharacterTextSplitter(
                chunk_size=1500,
                chunk_overlap=300,
                separators=["\n\nArticle ", "\n\n", "\n", ".", "!", "?", ",", " ", ""]
            )
            
            texts = text_splitter.split_documents(documents)
            print(f"Successfully loaded {len(texts)} document chunks from GDPR regulation")
            return texts
            
        except Exception as e:
            print(f"Error loading GDPR document: {str(e)}")
            return None
    
    def create_vector_store(self, texts):
        """Create vector store from document chunks"""
        try:
            print("Creating vector store...")
            embeddings = self.setup_embeddings()
            
            random.seed(42)
            np.random.seed(42)
            
            self.vector_store = FAISS.from_documents(texts, embeddings)
            print("Vector store created successfully")
            return True
        except Exception as e:
            print(f"Error creating vector store: {str(e)}")
            return False
    
    def setup_retrieval_chain(self):
        """Setup the retrieval system"""
        try:
            print("Setting up retrieval system...")
            if self.vector_store is None:
                raise Exception("Vector store not initialized")
            
            print("Retrieval system setup complete")
            return True
            
        except Exception as e:
            print(f"Error setting up retrieval system: {str(e)}")
            return False
    
    def analyze_compliance(self, company_policy):
        """Analyze company policy for GDPR compliance"""
        try:
            print("Starting compliance analysis...")
            
            normalized_policy = self._normalize_text(company_policy)
            policy_hash = hashlib.md5(normalized_policy.encode()).hexdigest()
            
            if policy_hash in self._analysis_cache:
                print("Using cached analysis result for consistency...")
                return self._analysis_cache[policy_hash]
            
            # Create targeted queries from configuration
            gdpr_queries = [req["search_query"] for req in self.gdpr_requirements_config]
            gdpr_queries.extend([
                "lawful basis consent contract legal obligation vital interests public task legitimate interest",
                "data minimisation purpose limitation accuracy storage limitation integrity confidentiality accountability",
                "supervisory authority lead authority one-stop-shop cross-border processing",
                "privacy impact assessment DPIA high risk systematic monitoring"
            ])
            
            # Retrieve relevant GDPR context
            print("Retrieving relevant GDPR articles...")
            random.seed(42)
            np.random.seed(42)
            
            retriever = self.vector_store.as_retriever(
                search_type="similarity",
                search_kwargs={"k": 6}
            )
            
            all_relevant_docs = []
            for i, query in enumerate(sorted(gdpr_queries)):
                random.seed(42 + i)
                docs = retriever.get_relevant_documents(query)
                all_relevant_docs.extend(docs)
            
            # Remove duplicates with deterministic ordering
            unique_docs = []
            seen_content = set()
            for doc in sorted(all_relevant_docs, key=lambda x: hashlib.md5(x.page_content.encode()).hexdigest()):
                content_hash = hashlib.md5(doc.page_content.encode()).hexdigest()
                if content_hash not in seen_content:
                    unique_docs.append(doc)
                    seen_content.add(content_hash)
            
            unique_docs = unique_docs[:20]
            gdpr_context = "\n\n---\n\n".join([doc.page_content for doc in unique_docs])
            
            print(f"Retrieved {len(unique_docs)} unique GDPR article sections")
            
            # Build dynamic prompt template
            requirements_text = "\n".join([
                f"   - {req['name']} - {req['description']}" 
                for req in self.gdpr_requirements_config
            ])
            
            prompt_template = f"""You are an expert in data privacy law with access to the full text of the General Data Protection Regulation (GDPR). 
Your task is to analyze the company's Privacy Policy against the GDPR to identify compliance violations and gaps.

RELEVANT GDPR ARTICLES (Retrieved from vector store):
{{context}}

USER QUERY (Contains the Privacy Policy to analyze):
{{question}}

INSTRUCTIONS:
1. **Extract the Privacy Policy**: From the user query above, identify and extract the company's privacy policy text.

2. **Apply Retrieved GDPR Articles**: Use the GDPR articles provided in the context above to evaluate the privacy policy. Pay special attention to:
{requirements_text}

3. **Compliance Analysis**: For each relevant GDPR requirement, assess:
   - What the privacy policy states (quote specific text where possible)
   - Whether it meets the GDPR requirement
   - What gaps or violations exist
   - Recommendations for improvement

4. **Structured Output**: Provide your analysis followed by a JSON table.

DETAILED ANALYSIS:
[Provide comprehensive analysis here]

COMPLIANCE_TABLE_JSON:
[
    {{{{
        "gdpr_requirement": "Article X - Specific Requirement",
        "company_policy": "Direct quote or description of what policy says",
        "compliance_status": "Compliant/Partially Compliant/Non-compliant",
        "notes_gaps": "Specific issues, missing elements, and recommendations"
    }}}}
]

Ensure the JSON is valid and covers all major GDPR requirements found in the retrieved context."""
            
            normalized_context = self._normalize_text(gdpr_context)
            formatted_prompt = prompt_template.format(
                context=normalized_context,
                question=f"Please analyze this company privacy policy for GDPR compliance:\n\n{normalized_policy}"
            )
            
            # Call the LLM
            print("Calling Cohere LLM for analysis...")
            llm = CohereLLM(
                api_key=self.cohere_api_key,
                temperature=0,
                max_tokens=4000
            )
            
            result = llm._call(formatted_prompt)
            print("Compliance analysis completed")
            
            self._analysis_cache[policy_hash] = result
            return result
            
        except Exception as e:
            print(f"Error during compliance analysis: {str(e)}")
            return None
    
    def parse_compliance_table(self, analysis_result):
        """Parse the analysis result to extract table data"""
        try:
            # Look for JSON data in the result
            json_match = re.search(r'COMPLIANCE_TABLE_JSON:\s*```json\s*(\[.*?\])\s*```', analysis_result, re.DOTALL)
            if not json_match:
                json_match = re.search(r'COMPLIANCE_TABLE_JSON:\s*(\[.*?\])', analysis_result, re.DOTALL)
            
            if json_match:
                json_str = json_match.group(1)
                json_str = re.sub(r'[\n\r\t]', ' ', json_str)
                json_str = re.sub(r'\s+', ' ', json_str)
                try:
                    table_data = json.loads(json_str)
                    table_data = sorted(table_data, key=lambda x: x.get('gdpr_requirement', ''))
                    df = pd.DataFrame(table_data)
                    if len(df) > 0:
                        print("Successfully parsed JSON compliance table")
                        return df
                except json.JSONDecodeError as e:
                    print(f"JSON parsing failed: {e}, using fallback...")
            
            # Fallback parsing
            print("Extracting compliance information from analysis text...")
            compliance_items = []
            analysis_lower = analysis_result.lower()
            
            for req_config in self.gdpr_requirements_config:
                req_name = req_config["name"]
                keywords = req_config["keywords"]
                
                mentioned = any(keyword in analysis_lower for keyword in keywords)
                
                if mentioned:
                    policy_text = self._extract_policy_text_for_requirement(analysis_result, keywords)
                    compliance_status = self._determine_compliance_status(analysis_result, keywords)
                    gaps_notes = self._extract_gaps_and_notes(analysis_result, keywords)
                else:
                    policy_text = "Not addressed in the privacy policy"
                    compliance_status = "Non-compliant"
                    gaps_notes = f"Privacy policy does not address {req_name} requirements"
                
                compliance_items.append({
                    "gdpr_requirement": req_name,
                    "company_policy": policy_text,
                    "compliance_status": compliance_status,
                    "notes_gaps": gaps_notes
                })
            
            return pd.DataFrame(compliance_items)
            
        except Exception as e:
            print(f"Error parsing compliance table: {str(e)}")
            return pd.DataFrame([
                {
                    "gdpr_requirement": "Analysis Error",
                    "company_policy": "Could not parse results",
                    "compliance_status": "Error",
                    "notes_gaps": str(e)
                }
            ])

    def _extract_policy_text_for_requirement(self, analysis_result, keywords):
        """Extract relevant policy text for a specific requirement"""
        try:
            sentences = re.split(r'[.!?]+', analysis_result)
            relevant_sentences = []
            
            for sentence in sentences:
                if any(keyword in sentence.lower() for keyword in keywords):
                    relevant_sentences.append(sentence.strip())
            
            if relevant_sentences:
                combined = ". ".join(relevant_sentences[:3])
                return combined[:300] + "..." if len(combined) > 300 else combined
            else:
                return "Policy mentions this requirement but details are unclear"
        except Exception:
            return "Unable to extract specific policy text"

    def _determine_compliance_status(self, analysis_result, keywords):
        """Determine compliance status based on analysis text"""
        try:
            compliance_patterns = {
                "Compliant": ["compliant", "meets", "satisfies", "adequate", "sufficient"],
                "Partially Compliant": ["partially", "somewhat", "limited", "incomplete"],
                "Non-compliant": ["non-compliant", "fails", "does not meet", "inadequate", "lacking", "missing", "not mentioned", "not addressed", "absent", "no reference"],
            }
            
            for sentence in re.split(r'[.!?]+', analysis_result):
                sentence_lower = sentence.lower()
                if any(keyword in sentence_lower for keyword in keywords):
                    for status, patterns in compliance_patterns.items():
                        if any(pattern in sentence_lower for pattern in patterns):
                            return status
            
            return "Partially Compliant"
        except Exception:
            return "Under Review"

    def _extract_gaps_and_notes(self, analysis_result, keywords):
        """Extract gaps and recommendations"""
        try:
            recommendation_sentences = []
            for sentence in re.split(r'[.!?]+', analysis_result):
                sentence_lower = sentence.lower()
                if any(keyword in sentence_lower for keyword in keywords):
                    if any(word in sentence_lower for word in ["recommend", "should", "must", "gap", "missing", "improve"]):
                        recommendation_sentences.append(sentence.strip())
            
            if recommendation_sentences:
                combined = ". ".join(recommendation_sentences[:2])
                return combined[:200] + "..." if len(combined) > 200 else combined
            else:
                return "Requires detailed review for compliance gaps"
        except Exception:
            return "Unable to determine specific gaps"

    def load_policy_from_text(self, policy_text):
        """Load policy from text string"""
        try:
            return policy_text.strip()
        except Exception as e:
            print(f"Error processing policy text: {str(e)}")
            return None

async def initialize_system():
    """Initialize the GDPR compliance system"""
    global rag_model, gdpr_initialized
    
    if gdpr_initialized:
        return True
    
    try:
        cohere_api_key = os.getenv("COHERE_API_KEY")
        if not cohere_api_key:
            raise Exception("COHERE_API_KEY not found in environment variables")
        
        print("Initializing GDPR Compliance RAG...")
        rag_model = GDPRComplianceRAG(cohere_api_key)
        
        gdpr_pdf_path = "GDPR_Regulation_2025.pdf"
        if not os.path.exists(gdpr_pdf_path):
            raise Exception(f"GDPR PDF not found at {gdpr_pdf_path}")
        
        texts = rag_model.load_gdpr_document(gdpr_pdf_path)
        if not texts:
            raise Exception("Failed to load GDPR document")
        
        if not rag_model.create_vector_store(texts):
            raise Exception("Failed to create vector store")
        
        if not rag_model.setup_retrieval_chain():
            raise Exception("Failed to setup retrieval chain")
        
        gdpr_initialized = True
        print("GDPR Compliance system initialized successfully")
        return True
        
    except Exception as e:
        print(f"Error initializing system: {str(e)}")
        return False

# API Endpoints

@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    return HealthResponse(
        status="healthy" if gdpr_initialized else "initializing",
        message="GDPR Compliance API is running",
        timestamp=datetime.now().isoformat()
    )

@app.post("/analyze/text", response_model=AnalysisResponse)
async def analyze_policy_text(request: PolicyAnalysisRequest):
    """Analyze privacy policy from text input"""
    if not gdpr_initialized:
        if not await initialize_system():
            raise HTTPException(status_code=503, detail="System not initialized")
    
    try:
        # Generate analysis ID
        analysis_id = hashlib.md5(f"{request.policy_text[:100]}{datetime.now()}".encode()).hexdigest()[:12]
        
        analysis_result = rag_model.analyze_compliance(request.policy_text)
        if not analysis_result:
            raise HTTPException(status_code=500, detail="Failed to analyze policy")
        
        compliance_table = rag_model.parse_compliance_table(analysis_result)
        
        # Calculate metrics
        if not compliance_table.empty:
            status_counts = compliance_table['compliance_status'].value_counts()
            total_requirements = len(compliance_table)
            compliant_count = status_counts.get('Compliant', 0)
            partially_compliant_count = status_counts.get('Partially Compliant', 0)
            compliance_score = ((compliant_count + (partially_compliant_count * 0.5)) / total_requirements) * 100
            
            compliance_level = (
                "Good compliance level" if compliance_score >= 80 
                else "Moderate compliance - improvements needed" if compliance_score >= 60 
                else "Low compliance - significant gaps identified"
            )
            
            # Convert DataFrame to list of ComplianceResult objects
            compliance_results = []
            for _, row in compliance_table.iterrows():
                compliance_results.append(ComplianceResult(
                    gdpr_requirement=row['gdpr_requirement'],
                    company_policy=clean_text_content(row['company_policy']),
                    compliance_status=row['compliance_status'],
                    notes_gaps=clean_text_content(row['notes_gaps'])
                ))
            
            # Save results to files using the organized structure from utils
            files_generated = save_results(
                compliance_table=compliance_table,
                analysis_result=analysis_result,
                policy_name=request.policy_name,
                analysis_id=analysis_id
            )
            
            return AnalysisResponse(
                success=True,
                analysis_id=analysis_id,
                policy_name=request.policy_name,
                timestamp=datetime.now().isoformat(),
                total_requirements=total_requirements,
                compliance_score=round(compliance_score, 1),
                compliance_level=compliance_level,
                status_breakdown=status_counts.to_dict(),
                detailed_analysis=analysis_result,
                compliance_results=compliance_results,
                files_generated=files_generated
            )
        else:
            raise HTTPException(status_code=500, detail="No compliance data generated")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analyze/file", response_model=AnalysisResponse)
async def analyze_policy_file(file: UploadFile = File(...), policy_name: Optional[str] = None):
    """Analyze privacy policy from uploaded file (PDF or text)"""
    if not gdpr_initialized:
        if not await initialize_system():
            raise HTTPException(status_code=503, detail="System not initialized")
    
    # Validate file type
    allowed_extensions = {'.pdf', '.txt', '.md'}
    file_extension = Path(file.filename).suffix.lower()
    if file_extension not in allowed_extensions:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file type. Allowed: {', '.join(allowed_extensions)}"
        )
    
    try:
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=file_extension) as temp_file:
            shutil.copyfileobj(file.file, temp_file)
            temp_file_path = temp_file.name
        
        try:
            # Load policy from file
            if file_extension == '.pdf':
                from langchain_community.document_loaders import PyPDFLoader
                loader = PyPDFLoader(temp_file_path)
                documents = loader.load()
                policy_text = "\n\n".join([doc.page_content for doc in documents])
            else:
                with open(temp_file_path, 'r', encoding='utf-8') as f:
                    policy_text = f.read()
            
            # Use the text analysis endpoint
            request = PolicyAnalysisRequest(
                policy_text=policy_text,
                policy_name=policy_name or file.filename
            )
            
            return await analyze_policy_text(request)
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"File processing failed: {str(e)}")

@app.get("/download/{company_name}/{filename}")
async def download_file(company_name: str, filename: str):
    """Download generated analysis files from organized folder structure"""
    # Construct the file path within the organized structure
    file_path = Path("results") / company_name / filename
    
    if not file_path.exists():
        raise HTTPException(status_code=404, detail="File not found")
    
    return FileResponse(
        path=str(file_path),
        filename=filename,
        media_type='application/octet-stream'
    )

@app.get("/list-results/{company_name}")
async def list_company_results(company_name: str):
    """List all analysis results for a specific company"""
    company_dir = Path("results") / company_name
    
    if not company_dir.exists():
        raise HTTPException(status_code=404, detail="Company results not found")
    
    files = []
    for file_path in company_dir.iterdir():
        if file_path.is_file():
            files.append({
                "filename": file_path.name,
                "size": file_path.stat().st_size,
                "modified": datetime.fromtimestamp(file_path.stat().st_mtime).isoformat(),
                "download_url": f"/download/{company_name}/{file_path.name}"
            })
    
    return {
        "company_name": company_name,
        "total_files": len(files),
        "files": sorted(files, key=lambda x: x['modified'], reverse=True)
    }

@app.get("/list-companies")
async def list_all_companies():
    """List all companies that have analysis results"""
    results_dir = Path("results")
    
    if not results_dir.exists():
        return {"companies": [], "total_companies": 0}
    
    companies = []
    for company_dir in results_dir.iterdir():
        if company_dir.is_dir():
            file_count = len(list(company_dir.glob("*")))
            if file_count > 0:
                # Get the most recent file modification time
                try:
                    most_recent = max(
                        (f.stat().st_mtime for f in company_dir.iterdir() if f.is_file()),
                        default=0
                    )
                    last_analysis = datetime.fromtimestamp(most_recent).isoformat() if most_recent else None
                except (OSError, ValueError):
                    last_analysis = None
                
                companies.append({
                    "company_name": company_dir.name,
                    "file_count": file_count,
                    "last_analysis": last_analysis,
                    "results_url": f"/list-results/{company_dir.name}"
                })
    
    return {
        "companies": sorted(companies, key=lambda x: x['last_analysis'] or '', reverse=True),
        "total_companies": len(companies)
    }

@app.get("/requirements")
async def get_gdpr_requirements():
    """Get list of GDPR requirements being checked"""
    return {
        "requirements": [
            {
                "name": req["name"],
                "description": req["description"],
                "keywords": req["keywords"]
            }
            for req in GDPR_REQUIREMENTS
        ]
    }

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "GDPR Compliance Analysis API",
        "version": "1.0.0",
        "status": "healthy" if gdpr_initialized else "initializing",
        "endpoints": {
            "analyze_text": "/analyze/text",
            "analyze_file": "/analyze/file", 
            "health": "/health",
            "requirements": "/requirements",
            "download_file": "/download/{company_name}/{filename}",
            "list_company_results": "/list-results/{company_name}",
            "list_all_companies": "/list-companies",
            "docs": "/docs"
        },
        "features": {
            "organized_results": "Results are saved in organized folders by company name",
            "multiple_formats": "CSV, JSON, comprehensive reports generated",
            "file_upload_support": "PDF, TXT, MD files supported"
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="127.0.0.1", port=8000)
