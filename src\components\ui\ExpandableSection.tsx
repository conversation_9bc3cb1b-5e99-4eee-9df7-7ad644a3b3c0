/**
 * Expandable Section Component
 * Provides collapsible sections with additional context and details
 */

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronRight, Info, AlertCircle, CheckCircle, HelpCircle } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';

interface ExpandableSectionProps {
  title: string;
  children: React.ReactNode;
  defaultExpanded?: boolean;
  icon?: 'info' | 'warning' | 'success' | 'help' | React.ReactNode;
  variant?: 'default' | 'card' | 'minimal';
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  animationDuration?: number;
}

export const ExpandableSection: React.FC<ExpandableSectionProps> = ({
  title,
  children,
  defaultExpanded = false,
  icon,
  variant = 'default',
  className = '',
  headerClassName = '',
  contentClassName = '',
  animationDuration = 300
}) => {
  const { mode } = useTheme();
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [contentHeight, setContentHeight] = useState<number>(0);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentRef.current) {
      setContentHeight(contentRef.current.scrollHeight);
    }
  }, [children, isExpanded]);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  const getIcon = () => {
    if (React.isValidElement(icon)) {
      return icon;
    }

    switch (icon) {
      case 'info':
        return <Info className="w-4 h-4 text-blue-500" />;
      case 'warning':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'success':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'help':
        return <HelpCircle className="w-4 h-4 text-text-secondary" />;
      default:
        return null;
    }
  };

  const getVariantClasses = () => {
    switch (variant) {
      case 'card':
        return {
          container: `bg-card border border-border rounded-lg shadow-sm ${mode === 'dark' ? '' : 'shadow-lg'}`,
          header: 'px-4 py-3 border-b border-border',
          content: 'px-4 py-3'
        };
      case 'minimal':
        return {
          container: '',
          header: 'py-2',
          content: 'py-2'
        };
      default:
        return {
          container: `bg-surface border border-border rounded-lg`,
          header: 'px-4 py-3',
          content: 'px-4 py-3'
        };
    }
  };

  const variantClasses = getVariantClasses();

  return (
    <div className={`${variantClasses.container} ${className}`}>
      {/* Header */}
      <button
        onClick={toggleExpanded}
        className={`
          w-full flex items-center justify-between text-left transition-colors duration-200
          hover:bg-surface/50 focus:outline-none focus:ring-2 focus:ring-primary/50 rounded-t-lg
          ${variantClasses.header} ${headerClassName}
        `}
        aria-expanded={isExpanded}
        aria-controls="expandable-content"
      >
        <div className="flex items-center gap-3">
          {getIcon()}
          <h3 className="font-medium text-text">{title}</h3>
        </div>
        <div className="flex items-center gap-2">
          <div className={`transform transition-transform duration-${animationDuration} ${
            isExpanded ? 'rotate-90' : 'rotate-0'
          }`}>
            {isExpanded ? (
              <ChevronDown className="w-4 h-4 text-text-secondary" />
            ) : (
              <ChevronRight className="w-4 h-4 text-text-secondary" />
            )}
          </div>
        </div>
      </button>

      {/* Content */}
      <div
        className="overflow-hidden transition-all duration-300 ease-in-out"
        style={{
          height: isExpanded ? `${contentHeight}px` : '0px'
        }}
      >
        <div
          ref={contentRef}
          id="expandable-content"
          className={`${variantClasses.content} ${contentClassName}`}
        >
          {children}
        </div>
      </div>
    </div>
  );
};

// Pre-configured expandable sections
export const InfoSection: React.FC<Omit<ExpandableSectionProps, 'icon'>> = (props) => (
  <ExpandableSection {...props} icon="info" />
);

export const WarningSection: React.FC<Omit<ExpandableSectionProps, 'icon'>> = (props) => (
  <ExpandableSection {...props} icon="warning" />
);

export const SuccessSection: React.FC<Omit<ExpandableSectionProps, 'icon'>> = (props) => (
  <ExpandableSection {...props} icon="success" />
);

export const HelpSection: React.FC<Omit<ExpandableSectionProps, 'icon'>> = (props) => (
  <ExpandableSection {...props} icon="help" />
);

// Accordion component for multiple expandable sections
interface AccordionProps {
  children: React.ReactElement<ExpandableSectionProps>[];
  allowMultiple?: boolean;
  className?: string;
}

export const Accordion: React.FC<AccordionProps> = ({
  children,
  allowMultiple = false,
  className = ''
}) => {
  const [expandedSections, setExpandedSections] = useState<number[]>([]);

  const handleSectionToggle = (index: number) => {
    if (allowMultiple) {
      setExpandedSections(prev =>
        prev.includes(index)
          ? prev.filter(i => i !== index)
          : [...prev, index]
      );
    } else {
      setExpandedSections(prev =>
        prev.includes(index) ? [] : [index]
      );
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      {React.Children.map(children, (child, index) => {
        if (!React.isValidElement(child)) return child;

        return React.cloneElement(child, {
          ...child.props,
          defaultExpanded: expandedSections.includes(index),
          key: index,
          onClick: () => handleSectionToggle(index)
        });
      })}
    </div>
  );
};

// Hook for managing expandable sections
export const useExpandableSection = (initialExpanded: boolean = false) => {
  const [isExpanded, setIsExpanded] = useState(initialExpanded);

  const toggle = () => setIsExpanded(!isExpanded);
  const expand = () => setIsExpanded(true);
  const collapse = () => setIsExpanded(false);

  return {
    isExpanded,
    toggle,
    expand,
    collapse
  };
};

export default ExpandableSection;
