import React, { useState } from 'react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { RegulatoryFramework } from '../../types/gdprTypes';
import { useTheme } from '../../context/ThemeContext';
import {
  Scale,
  FileText,
  AlertCircle,
  Calendar,
  CheckSquare,
  AlertTriangle,
  User,
  Globe,
  Clock,
  ExternalLink
} from 'lucide-react';

interface RegulatoryFrameworkViewModalProps {
  isOpen: boolean;
  onClose: () => void;
  framework: RegulatoryFramework | null;
}

export const RegulatoryFrameworkViewModal: React.FC<RegulatoryFrameworkViewModalProps> = ({
  isOpen,
  onClose,
  framework
}) => {
  const { mode } = useTheme();
  const [activeTab, setActiveTab] = useState<'overview' | 'requirements' | 'deadlines' | 'penalties'>('overview');

  if (!framework) return null;

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'draft':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'deprecated':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'pending':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getRequirementStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'non_compliant':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'not_started':
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'high':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const tabs = [
    { id: 'overview', label: 'Overview', icon: <Scale className="w-4 h-4" /> },
    { id: 'requirements', label: 'Requirements', icon: <CheckSquare className="w-4 h-4" /> },
    { id: 'deadlines', label: 'Deadlines', icon: <Calendar className="w-4 h-4" /> },
    { id: 'penalties', label: 'Penalties', icon: <AlertTriangle className="w-4 h-4" /> }
  ];

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={framework.name}
      size="2xl"
    >
      <div className="space-y-6">
        {/* Framework Header */}
        <div className={`p-6 ${mode === 'dark' ? 'bg-card' : 'bg-gradient-to-br from-surface to-card'} rounded-xl border border-border`}>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <Scale className="w-8 h-8 text-primary" />
              <div>
                <h2 className="text-xl font-bold text-text">{framework.name}</h2>
                <p className="text-text-secondary">v{framework.version} • {framework.jurisdiction.toUpperCase()}</p>
              </div>
            </div>
            <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(framework.status)}`}>
              {framework.status.toUpperCase()}
            </span>
          </div>
          <p className="text-text-secondary">{framework.description}</p>
        </div>

        {/* Tabs */}
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`flex items-center gap-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                  activeTab === tab.id
                    ? 'border-primary text-primary'
                    : 'border-transparent text-text-secondary hover:text-text hover:border-border'
                }`}
              >
                {tab.icon}
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="min-h-[400px]">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Calendar className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-medium text-text">Effective Date</span>
                  </div>
                  <p className="text-text">{framework.effectiveDate.toLocaleDateString()}</p>
                </div>
                
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Clock className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-medium text-text">Last Updated</span>
                  </div>
                  <p className="text-text">{framework.lastUpdated.toLocaleDateString()}</p>
                </div>

                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <div className="flex items-center gap-2 mb-2">
                    <User className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-medium text-text">Owner</span>
                  </div>
                  <p className="text-text">{framework.owner}</p>
                </div>

                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <div className="flex items-center gap-2 mb-2">
                    <Globe className="w-4 h-4 text-text-secondary" />
                    <span className="text-sm font-medium text-text">Jurisdiction</span>
                  </div>
                  <p className="text-text">{framework.jurisdiction.toUpperCase()}</p>
                </div>
              </div>

              {framework.applicableEntities && framework.applicableEntities.length > 0 && (
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <h3 className="text-lg font-semibold text-text mb-3">Applicable Entities</h3>
                  <div className="flex flex-wrap gap-2">
                    {framework.applicableEntities.map((entity, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-primary/10 text-primary rounded-full text-sm"
                      >
                        {entity}
                      </span>
                    ))}
                  </div>
                </div>
              )}

              {framework.tags && framework.tags.length > 0 && (
                <div className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}>
                  <h3 className="text-lg font-semibold text-text mb-3">Tags</h3>
                  <div className="flex flex-wrap gap-2">
                    {framework.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-secondary/10 text-secondary rounded-full text-sm"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'requirements' && (
            <div className="space-y-4">
              {framework.requirements.map((requirement) => (
                <div
                  key={requirement.id}
                  className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-text mb-1">{requirement.title}</h4>
                      <p className="text-text-secondary text-sm mb-2">{requirement.description}</p>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(requirement.priority)}`}>
                        {requirement.priority.toUpperCase()}
                      </span>
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRequirementStatusColor(requirement.status)}`}>
                        {requirement.status.replace('_', ' ').toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="text-text-secondary">Assigned To:</span>
                      <p className="text-text">{requirement.assignedTo}</p>
                    </div>
                    <div>
                      <span className="text-text-secondary">Due Date:</span>
                      <p className="text-text">{requirement.dueDate.toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="text-text-secondary">Compliance Score:</span>
                      <p className="text-text font-semibold">{requirement.complianceScore}%</p>
                    </div>
                  </div>

                  {requirement.notes && (
                    <div className="mt-3 p-3 bg-background rounded border border-border">
                      <span className="text-text-secondary text-sm">Notes:</span>
                      <p className="text-text text-sm mt-1">{requirement.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {activeTab === 'deadlines' && (
            <div className="space-y-4">
              {framework.complianceDeadlines.map((deadline) => (
                <div
                  key={deadline.id}
                  className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-text mb-1">{deadline.title}</h4>
                      <p className="text-text-secondary text-sm">{deadline.description}</p>
                    </div>
                    <div className="flex items-center gap-2 ml-4">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(deadline.priority)}`}>
                        {deadline.priority.toUpperCase()}
                      </span>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-text-secondary">Due Date:</span>
                      <p className="text-text font-semibold">{deadline.dueDate.toLocaleDateString()}</p>
                    </div>
                    <div>
                      <span className="text-text-secondary">Assigned To:</span>
                      <p className="text-text">{deadline.assignedTo}</p>
                    </div>
                  </div>

                  {deadline.notes && (
                    <div className="mt-3 p-3 bg-background rounded border border-border">
                      <span className="text-text-secondary text-sm">Notes:</span>
                      <p className="text-text text-sm mt-1">{deadline.notes}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}

          {activeTab === 'penalties' && (
            <div className="space-y-4">
              {framework.penalties.map((penalty) => (
                <div
                  key={penalty.id}
                  className={`p-4 ${mode === 'dark' ? 'bg-card' : 'bg-surface'} rounded-lg border border-border`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="text-lg font-semibold text-text mb-1">{penalty.violationType}</h4>
                      <p className="text-text-secondary text-sm">{penalty.description}</p>
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-text-secondary">Minimum Penalty:</span>
                      <p className="text-text font-semibold">
                        {penalty.minPenalty.toLocaleString()} {penalty.currency}
                      </p>
                    </div>
                    <div>
                      <span className="text-text-secondary">Maximum Penalty:</span>
                      <p className="text-text font-semibold">
                        {penalty.maxPenalty.toLocaleString()} {penalty.currency}
                      </p>
                    </div>
                  </div>

                  {penalty.additionalConsequences && penalty.additionalConsequences.length > 0 && (
                    <div className="mt-3">
                      <span className="text-text-secondary text-sm">Additional Consequences:</span>
                      <div className="flex flex-wrap gap-2 mt-1">
                        {penalty.additionalConsequences.map((consequence, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300 rounded text-xs"
                          >
                            {consequence}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex justify-end pt-4 border-t border-border">
          <Button onClick={onClose}>
            Close
          </Button>
        </div>
      </div>
    </Modal>
  );
};
