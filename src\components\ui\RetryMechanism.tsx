/**
 * Retry Mechanism Component
 * Provides retry functionality for failed operations with exponential backoff
 */

import React, { useState, useCallback } from 'react';
import { RefreshCw, AlertTriangle, CheckCircle } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';

interface RetryMechanismProps {
  onRetry: () => Promise<void> | void;
  maxRetries?: number;
  initialDelay?: number;
  backoffMultiplier?: number;
  className?: string;
  children?: React.ReactNode;
  showSuccessMessage?: boolean;
  retryButtonText?: string;
  errorMessage?: string;
}

export const RetryMechanism: React.FC<RetryMechanismProps> = ({
  onRetry,
  maxRetries = 3,
  initialDelay = 1000,
  backoffMultiplier = 2,
  className = '',
  children,
  showSuccessMessage = true,
  retryButtonText = 'Retry',
  errorMessage = 'Operation failed. Please try again.'
}) => {
  const { mode } = useTheme();
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const handleRetry = useCallback(async () => {
    if (isRetrying || retryCount >= maxRetries) {
      return;
    }

    setIsRetrying(true);
    setLastError(null);
    setShowSuccess(false);

    try {
      // Calculate delay with exponential backoff
      const delay = initialDelay * Math.pow(backoffMultiplier, retryCount);
      
      // Show delay to user for longer waits
      if (delay > 2000) {
        console.log(`⏳ Retrying in ${delay / 1000} seconds...`);
      }

      await new Promise(resolve => setTimeout(resolve, delay));
      
      await onRetry();
      
      // Success
      setRetryCount(0);
      setLastError(null);
      
      if (showSuccessMessage) {
        setShowSuccess(true);
        setTimeout(() => setShowSuccess(false), 3000);
      }
      
      console.log('✅ Retry successful');
      
    } catch (error) {
      const newRetryCount = retryCount + 1;
      setRetryCount(newRetryCount);
      
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setLastError(errorMsg);
      
      console.error(`❌ Retry ${newRetryCount} failed:`, errorMsg);
      
      if (newRetryCount >= maxRetries) {
        console.error(`❌ Max retries (${maxRetries}) reached. Giving up.`);
      }
    } finally {
      setIsRetrying(false);
    }
  }, [onRetry, retryCount, maxRetries, initialDelay, backoffMultiplier, showSuccessMessage]);

  const canRetry = retryCount < maxRetries;
  const hasError = lastError !== null;

  return (
    <div className={`${className}`}>
      {children}
      
      {/* Success Message */}
      {showSuccess && (
        <div className={`mt-4 p-3 rounded-lg border ${
          mode === 'dark' 
            ? 'bg-green-900/20 border-green-800 text-green-200' 
            : 'bg-green-50 border-green-200 text-green-800'
        }`}>
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4" />
            <span className="text-sm font-medium">Operation completed successfully!</span>
          </div>
        </div>
      )}

      {/* Error Message */}
      {hasError && (
        <div className={`mt-4 p-3 rounded-lg border ${
          mode === 'dark' 
            ? 'bg-red-900/20 border-red-800 text-red-200' 
            : 'bg-red-50 border-red-200 text-red-800'
        }`}>
          <div className="flex items-start gap-2">
            <AlertTriangle className="w-4 h-4 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm font-medium">{errorMessage}</p>
              {lastError && (
                <p className="text-xs mt-1 opacity-75">{lastError}</p>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Retry Controls */}
      {(hasError || isRetrying) && (
        <div className="mt-4 flex items-center justify-between">
          <div className="text-sm text-text-secondary">
            {isRetrying ? (
              <span>Retrying...</span>
            ) : canRetry ? (
              <span>Attempt {retryCount} of {maxRetries}</span>
            ) : (
              <span>Max retries reached</span>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            {canRetry && (
              <button
                onClick={handleRetry}
                disabled={isRetrying}
                className={`
                  inline-flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg transition-colors
                  ${mode === 'dark'
                    ? 'bg-blue-600 hover:bg-blue-700 disabled:bg-blue-800 text-white'
                    : 'bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white'
                  }
                  disabled:cursor-not-allowed
                `}
              >
                <RefreshCw className={`w-3 h-3 ${isRetrying ? 'animate-spin' : ''}`} />
                {isRetrying ? 'Retrying...' : retryButtonText}
              </button>
            )}
            
            {!canRetry && (
              <button
                onClick={() => {
                  setRetryCount(0);
                  setLastError(null);
                }}
                className={`
                  inline-flex items-center gap-2 px-3 py-1.5 text-sm rounded-lg transition-colors
                  ${mode === 'dark'
                    ? 'bg-gray-600 hover:bg-gray-700 text-white'
                    : 'bg-gray-500 hover:bg-gray-600 text-white'
                  }
                `}
              >
                Reset
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

// Hook for using retry mechanism programmatically
export const useRetryMechanism = (
  operation: () => Promise<void> | void,
  options: {
    maxRetries?: number;
    initialDelay?: number;
    backoffMultiplier?: number;
  } = {}
) => {
  const {
    maxRetries = 3,
    initialDelay = 1000,
    backoffMultiplier = 2
  } = options;

  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);
  const [lastError, setLastError] = useState<string | null>(null);

  const retry = useCallback(async () => {
    if (isRetrying || retryCount >= maxRetries) {
      return { success: false, error: 'Max retries reached or already retrying' };
    }

    setIsRetrying(true);
    setLastError(null);

    try {
      const delay = initialDelay * Math.pow(backoffMultiplier, retryCount);
      await new Promise(resolve => setTimeout(resolve, delay));
      
      await operation();
      
      setRetryCount(0);
      setLastError(null);
      
      return { success: true, error: null };
      
    } catch (error) {
      const newRetryCount = retryCount + 1;
      setRetryCount(newRetryCount);
      
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';
      setLastError(errorMsg);
      
      return { success: false, error: errorMsg };
      
    } finally {
      setIsRetrying(false);
    }
  }, [operation, retryCount, maxRetries, initialDelay, backoffMultiplier, isRetrying]);

  const reset = useCallback(() => {
    setRetryCount(0);
    setLastError(null);
    setIsRetrying(false);
  }, []);

  return {
    retry,
    reset,
    isRetrying,
    retryCount,
    lastError,
    canRetry: retryCount < maxRetries,
    hasError: lastError !== null
  };
};

export default RetryMechanism;
