import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import complianceRulesService, {
  DPDPRule,
  GDPRRule,
  ComplianceEvent,
  RuleEvaluationResult
} from '../services/complianceRulesService';

// Import the configurationService to get settings
import { configurationService } from '../services/api';

// Extended compliance standards interface
export interface ComplianceStandards {
  gdprEnabled: boolean;
  dpdpEnabled: boolean;
  hipaaEnabled: boolean;
  sox: boolean;
  pci: boolean;
  iso27001: boolean;
  ccpa: boolean;
  nist: boolean;
  fedramp: boolean;
}

interface ComplianceRulesContextType {
  dpdpRules: DPDPRule[];
  gdprRules: GDPRRule[];
  dpdpEnabled: boolean;
  gdprEnabled: boolean;
  complianceStandards: ComplianceStandards;
  toggleDPDP: (enabled: boolean) => void;
  toggleGDPR: (enabled: boolean) => void;
  toggleComplianceStandard: (standard: keyof ComplianceStandards, enabled: boolean) => void;
  getEnabledStandards: () => string[];
  evaluateEvent: (event: ComplianceEvent) => Promise<RuleEvaluationResult[]>;
  complianceHistory: RuleEvaluationResult[];
  isLoading: boolean;
  error: string | null;
}

const ComplianceRulesContext = createContext<ComplianceRulesContextType | undefined>(undefined);

export const ComplianceRulesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [dpdpRules, setDpdpRules] = useState<DPDPRule[]>([]);
  const [gdprRules, setGdprRules] = useState<GDPRRule[]>([]);
  const [dpdpEnabled, setDpdpEnabled] = useState(false);
  const [gdprEnabled, setGdprEnabled] = useState(true); // GDPR enabled by default
  const [complianceStandards, setComplianceStandards] = useState<ComplianceStandards>({
    gdprEnabled: true,
    dpdpEnabled: false,
    hipaaEnabled: false,
    sox: false,
    pci: true,
    iso27001: false,
    ccpa: true,
    nist: false,
    fedramp: false,
  });
  const [complianceHistory, setComplianceHistory] = useState<RuleEvaluationResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load rules and settings on component mount
  useEffect(() => {
    const loadRulesAndSettings = async () => {
      setIsLoading(true);
      try {
        // Load rules
        const dpdpRules = complianceRulesService.getDPDPRules();
        const gdprRules = complianceRulesService.getGDPRRules();

        setDpdpRules(dpdpRules);
        setGdprRules(gdprRules);

        // Try to load settings from configuration service
        try {
          const settings = await configurationService.getSettings();
          if (settings && settings.compliance) {
            setGdprEnabled(settings.compliance.gdprEnabled);
            setDpdpEnabled(settings.compliance.dpdpEnabled);

            // Update comprehensive compliance standards
            setComplianceStandards({
              gdprEnabled: settings.compliance.gdprEnabled,
              dpdpEnabled: settings.compliance.dpdpEnabled,
              hipaaEnabled: settings.compliance.hipaaEnabled || false,
              sox: settings.compliance.sox || false,
              pci: settings.compliance.pci || false,
              iso27001: settings.compliance.iso27001 || false,
              ccpa: settings.compliance.ccpa || false,
              nist: settings.compliance.nist || false,
              fedramp: settings.compliance.fedramp || false,
            });
          }
        } catch (settingsErr) {
          console.warn('Could not load compliance settings, using defaults:', settingsErr);
        }

        setError(null);
      } catch (err) {
        setError('Failed to load compliance rules');
        console.error('Error loading compliance rules:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadRulesAndSettings();
  }, []);

  // Toggle DPDP compliance
  const toggleDPDP = async (enabled: boolean) => {
    setDpdpEnabled(enabled);
    setComplianceStandards(prev => ({ ...prev, dpdpEnabled: enabled }));

    // Update configuration settings if possible
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance.dpdpEnabled = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.warn('Could not update DPDP settings:', err);
    }
  };

  // Toggle GDPR compliance
  const toggleGDPR = async (enabled: boolean) => {
    setGdprEnabled(enabled);
    setComplianceStandards(prev => ({ ...prev, gdprEnabled: enabled }));

    // Update configuration settings if possible
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance.gdprEnabled = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.warn('Could not update GDPR settings:', err);
    }
  };

  // Toggle any compliance standard
  const toggleComplianceStandard = async (standard: keyof ComplianceStandards, enabled: boolean) => {
    setComplianceStandards(prev => ({ ...prev, [standard]: enabled }));

    // Sync with individual state variables for backward compatibility
    if (standard === 'gdprEnabled') {
      setGdprEnabled(enabled);
    } else if (standard === 'dpdpEnabled') {
      setDpdpEnabled(enabled);
    }

    // Update configuration settings if possible
    try {
      const settings = await configurationService.getSettings();
      if (settings && settings.compliance) {
        settings.compliance[standard] = enabled;
        await configurationService.updateSettings(settings);
      }
    } catch (err) {
      console.warn(`Could not update ${standard} settings:`, err);
    }
  };

  // Get list of enabled standards
  const getEnabledStandards = (): string[] => {
    return Object.entries(complianceStandards)
      .filter(([_, enabled]) => enabled)
      .map(([standard, _]) => {
        // Convert internal names to display names
        const standardMap: Record<string, string> = {
          gdprEnabled: 'GDPR',
          dpdpEnabled: 'DPDP',
          hipaaEnabled: 'HIPAA',
          sox: 'SOX',
          pci: 'PCI DSS',
          iso27001: 'ISO 27001',
          ccpa: 'CCPA',
          nist: 'NIST',
          fedramp: 'FedRAMP',
        };
        return standardMap[standard] || standard;
      });
  };

  // Evaluate a compliance event
  const evaluateEvent = async (event: ComplianceEvent): Promise<RuleEvaluationResult[]> => {
    setIsLoading(true);
    try {
      // Only evaluate rules for enabled compliance frameworks
      if (!dpdpEnabled && !gdprEnabled) {
        return [];
      }

      // Evaluate the event against applicable rules
      const results = complianceRulesService.evaluateComplianceEvent(event);

      // Log each result
      for (const result of results) {
        await complianceRulesService.logComplianceResult(result);
      }

      // Add to history
      setComplianceHistory(prev => [...results, ...prev].slice(0, 100)); // Keep last 100 events

      return results;
    } catch (err) {
      setError('Failed to evaluate compliance event');
      console.error('Error evaluating compliance event:', err);
      return [];
    } finally {
      setIsLoading(false);
    }
  };

  const contextValue: ComplianceRulesContextType = {
    dpdpRules,
    gdprRules,
    dpdpEnabled,
    gdprEnabled,
    complianceStandards,
    toggleDPDP,
    toggleGDPR,
    toggleComplianceStandard,
    getEnabledStandards,
    evaluateEvent,
    complianceHistory,
    isLoading,
    error
  };

  return (
    <ComplianceRulesContext.Provider value={contextValue}>
      {children}
    </ComplianceRulesContext.Provider>
  );
};

export const useComplianceRules = () => {
  const context = useContext(ComplianceRulesContext);
  if (context === undefined) {
    throw new Error('useComplianceRules must be used within a ComplianceRulesProvider');
  }
  return context;
};
