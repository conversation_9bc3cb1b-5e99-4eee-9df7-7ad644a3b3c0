import React, { useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useTheme } from '../context/ThemeContext';
import { useComplianceRules } from '../context/ComplianceRulesContext';
import { useComplianceStandards } from '../context/ComplianceStandardsContext';
import { ErrorBoundary } from './ui/ErrorBoundary';

// Update the ComplianceSettings interface
interface ComplianceSettings {
  apiConfiguration: {
    endpoint: string;
    apiKey: string;
    timeout: number;
    secretKey: string;
    environment: 'development' | 'staging' | 'production';
    apiVersion: string;
    rateLimits: {
      enabled: boolean;
      requestsPerMinute: number;
    };
  };
  security: {
    mfaEnabled: boolean;
    passwordPolicy: {
      minLength: number;
      requireSpecialChars: boolean;
      requireNumbers: boolean;
      requireUppercase: boolean;
      maxAge: number;
      preventReuse: number;
      preventCommonWords: boolean;
    };
    sessionTimeout: number;
    ipWhitelist: string[];
    failedLoginAttempts: number;
    lockoutDuration: number;
    sslEnabled: boolean;
    encryptionLevel: 'standard' | 'high' | 'military';
    deviceTracking: boolean;
    geofencing: boolean;
    bruteForceProtection: boolean;
    anomalyDetection: boolean;
    ddosProtection: boolean;
  };
  notifications: {
    email: boolean;
    slack: boolean;
    webhook: string;
    frequency: 'realtime' | 'daily' | 'weekly';
  };
  dataRetention: {
    auditLogs: number;
    reports: number;
    backupEnabled: boolean;
    backupFrequency: 'daily' | 'weekly' | 'monthly';
  };
  compliance: {
    gdprEnabled: boolean;
    dpdpEnabled: boolean;
    hipaaEnabled: boolean;
    sox: boolean;
    pci: boolean;
    iso27001: boolean;
    ccpa: boolean;
    nist: boolean;
    fedramp: boolean;
  };
  externalServices: {
    openai: {
      apiKey: string;
      model: string;
      maxTokens: number;
    };
    aws: {
      accessKeyId: string;
      secretAccessKey: string;
      region: string;
    };
    azure: {
      tenantId: string;
      clientId: string;
      clientSecret: string;
    };
  };
  auditSettings: {
    enableAuditLogging: boolean;
    logRetentionDays: number;
    detailedLogging: boolean;
    sensitiveDataMasking: boolean;
  };
  riskManagement: {
    enableRiskAssessment: boolean;
    riskThreshold: number;
    automaticMitigation: boolean;
    notifyOnHighRisk: boolean;
  };
  integrations: {
    jira: {
      enabled: boolean;
      apiKey: string;
      projectKey: string;
    };
  };
}

// Update the defaultSettings
const defaultSettings: ComplianceSettings = {
  apiConfiguration: {
    endpoint: 'https://api.compliance.example.com',
    apiKey: import.meta.env.VITE_API_KEY || '',
    secretKey: import.meta.env.VITE_SECRET_KEY || '',
    timeout: 30,
    environment: 'development',
    apiVersion: 'v1',
    rateLimits: {
      enabled: true,
      requestsPerMinute: 100
    }
  },
  security: {
    mfaEnabled: true,
    passwordPolicy: {
      minLength: 12,
      requireSpecialChars: true,
      requireNumbers: true,
      requireUppercase: true,
      maxAge: 90,
      preventReuse: 5,
      preventCommonWords: true,
    },
    sessionTimeout: 30,
    ipWhitelist: [],
    failedLoginAttempts: 5,
    lockoutDuration: 15,
    sslEnabled: true,
    encryptionLevel: 'high',
    deviceTracking: false,
    geofencing: false,
    bruteForceProtection: true,
    anomalyDetection: false,
    ddosProtection: true,
  },
  notifications: {
    email: true,
    slack: false,
    webhook: '',
    frequency: 'daily',
  },
  dataRetention: {
    auditLogs: 365,
    reports: 90,
    backupEnabled: true,
    backupFrequency: 'daily',
  },
  compliance: {
    gdprEnabled: true,
    dpdpEnabled: false,
    hipaaEnabled: false,
    sox: false,
    pci: true,
    iso27001: false,
    ccpa: true,
    nist: false,
    fedramp: false,
  },
  externalServices: {
    openai: {
      apiKey: import.meta.env.VITE_OPENAI_API_KEY || '',
      model: 'gpt-4',
      maxTokens: 2000
    },
    aws: {
      accessKeyId: import.meta.env.VITE_AWS_ACCESS_KEY_ID || '',
      secretAccessKey: import.meta.env.VITE_AWS_SECRET_ACCESS_KEY || '',
      region: 'us-east-1'
    },
    azure: {
      tenantId: import.meta.env.VITE_AZURE_TENANT_ID || '',
      clientId: import.meta.env.VITE_AZURE_CLIENT_ID || '',
      clientSecret: import.meta.env.VITE_AZURE_CLIENT_SECRET || ''
    }
  },
  auditSettings: {
    enableAuditLogging: true,
    logRetentionDays: 90,
    detailedLogging: true,
    sensitiveDataMasking: true
  },
  riskManagement: {
    enableRiskAssessment: true,
    riskThreshold: 75,
    automaticMitigation: false,
    notifyOnHighRisk: true
  },
  integrations: {
    jira: {
      enabled: false,
      apiKey: '',
      projectKey: ''
    }
  }
};

const Configurations: React.FC = () => {
  const { mode } = useTheme();
  const { gdprEnabled, toggleGDPR, dpdpEnabled, toggleDPDP } = useComplianceRules();
  const { complianceStandards, toggleStandard } = useComplianceStandards();
  const [settings, setSettings] = useState<ComplianceSettings>(defaultSettings);
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    loadSettings();
  }, []);

  // Sync settings with ComplianceRulesContext when they change
  useEffect(() => {
    setSettings(prev => ({
      ...prev,
      compliance: {
        ...prev.compliance,
        gdprEnabled,
        dpdpEnabled
      }
    }));
  }, [gdprEnabled, dpdpEnabled]);

  const loadSettings = async () => {
    try {
      setIsLoading(true);
      // Fetch settings from local storage first
      const savedSettings = localStorage.getItem('complianceSettings');
      if (savedSettings) {
        setSettings(JSON.parse(savedSettings));
      } else {
        setSettings(defaultSettings);
      }
    } catch (error) {
      console.error('Error loading settings:', error);
      toast.error('Failed to load settings');
      setSettings(defaultSettings);
    } finally {
      setIsLoading(false);
    }
  };

  // Special handler for compliance changes that need to sync with ComplianceRulesContext
  const handleComplianceChange = (field: string, value: boolean) => {
    // Update local settings
    setSettings(prev => ({
      ...prev,
      compliance: {
        ...prev.compliance,
        [field]: value
      }
    }));

    // Sync with ComplianceRulesContext
    if (field === 'gdprEnabled') {
      toggleGDPR(value);
    } else if (field === 'dpdpEnabled') {
      toggleDPDP(value);
    }
  };

  // Handler for other compliance standards
  const handleOtherComplianceChange = async (field: string, value: boolean) => {
    // Update local settings
    setSettings(prev => ({
      ...prev,
      compliance: {
        ...prev.compliance,
        [field]: value
      }
    }));

    // Update the comprehensive compliance standards context
    await toggleStandard(field as keyof typeof complianceStandards, value);
  };

  const handleNestedChange = (
    category: keyof ComplianceSettings,
    field: string,
    value: any,
    subField?: string
  ) => {
    setSettings(prev => {
      if (subField) {
        return {
          ...prev,
          [category]: {
            ...prev[category],
            [field]: {
              ...prev[category][field],
              [subField]: value
            }
          }
        };
      }

      if (typeof value === 'object') {
        return {
          ...prev,
          [category]: {
            ...prev[category],
            [field]: value
          }
        };
      }

      return {
        ...prev,
        [category]: {
          ...prev[category],
          [field]: value
        }
      };
    });
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      localStorage.setItem('complianceSettings', JSON.stringify(settings));
      await new Promise(resolve => setTimeout(resolve, 1000));
      toast.success('Settings saved successfully');
    } catch (error) {
      console.error('Error saving settings:', error);
      toast.error('Failed to save settings');
    } finally {
      setIsSaving(false);
    }
  };
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen bg-background">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-text mb-2">Configurations Error</h2>
            <p className="text-text-secondary">Something went wrong loading the configurations page. Please refresh the page.</p>
          </div>
        </div>
      }
    >
      <div className="flex-1 p-8 bg-background">
      <div className="mb-8 flex justify-between items-center">
        <h1 className="text-2xl font-bold text-text">Compliance Configurations</h1>
        <button
          onClick={handleSave}
          disabled={isSaving}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 dark:focus:ring-purple-400 disabled:opacity-50 transition-all duration-200"
        >
          {isSaving ? (
            <>
              <span className="animate-spin -ml-1 mr-3 h-5 w-5 text-white">⚪</span>
              Saving...
            </>
          ) : (
            'Save Changes'
          )}
        </button>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Updated API Configuration Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">API Configuration</h2>
          <div className="space-y-6">
            {/* Basic Settings */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Basic Settings</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text">API Endpoint</label>
                  <input
                    type="text"
                    value={settings.apiConfiguration.endpoint}
                    onChange={(e) => handleNestedChange('apiConfiguration', 'endpoint', e.target.value)}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    placeholder="https://api.example.com/v1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">API Version</label>
                  <input
                    type="text"
                    value={settings.apiConfiguration.apiVersion}
                    onChange={(e) => handleNestedChange('apiConfiguration', 'apiVersion', e.target.value)}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    placeholder="v1"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Environment</label>
                  <select
                    value={settings.apiConfiguration.environment}
                    onChange={(e) => handleNestedChange('apiConfiguration', 'environment', e.target.value)}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                  >
                    <option value="development">Development</option>
                    <option value="staging">Staging</option>
                    <option value="production">Production</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Authentication */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Authentication</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text">API Key</label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="password"
                      value={settings.apiConfiguration.apiKey}
                      onChange={(e) => handleNestedChange('apiConfiguration', 'apiKey', e.target.value)}
                      className="block w-full rounded-md border-border bg-surface text-text focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center">
                      <button
                        type="button"
                        onClick={() => {/* Add show/hide password logic */ }}
                        className="px-3 py-1 text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                      >
                        👁️
                      </button>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Secret Key</label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="password"
                      value={settings.apiConfiguration.secretKey}
                      onChange={(e) => handleNestedChange('apiConfiguration', 'secretKey', e.target.value)}
                      className="block w-full rounded-md border-border bg-surface text-text focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center">
                      <button
                        type="button"
                        onClick={() => {/* Add show/hide password logic */ }}
                        className="px-3 py-1 text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                      >
                        👁️
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Rate Limiting */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Rate Limiting</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.apiConfiguration.rateLimits.enabled}
                    onChange={(e) => handleNestedChange('apiConfiguration', 'rateLimits', { ...settings.apiConfiguration.rateLimits, enabled: e.target.checked })}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                  />
                  <label className="ml-2 block text-sm text-text">Enable Rate Limiting</label>
                </div>
                {settings.apiConfiguration.rateLimits.enabled && (
                  <div>
                    <label className="block text-sm font-medium text-text">Requests per Minute</label>
                    <input
                      type="number"
                      min="1"
                      value={settings.apiConfiguration.rateLimits.requestsPerMinute}
                      onChange={(e) => handleNestedChange('apiConfiguration', 'rateLimits', { ...settings.apiConfiguration.rateLimits, requestsPerMinute: parseInt(e.target.value) })}
                      className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                  </div>
                )}
              </div>
            </div>

            {/* Timeout Settings */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Timeout Settings</h3>
              <div>
                <label className="block text-sm font-medium text-text">Request Timeout (seconds)</label>
                <input
                  type="number"
                  min="1"
                  max="120"
                  value={settings.apiConfiguration.timeout}
                  onChange={(e) => handleNestedChange('apiConfiguration', 'timeout', parseInt(e.target.value))}
                  className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                />
              </div>
            </div>
          </div>
        </section>

        {/* Security Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">Security Settings</h2>
          <div className="space-y-6">
            {/* Authentication Controls */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Authentication & Access</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.security.mfaEnabled}
                    onChange={(e) => handleNestedChange('security', 'mfaEnabled', e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                  />
                  <label className="ml-2 block text-sm text-text">Enable MFA</label>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Failed Login Attempts</label>
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={settings.security.failedLoginAttempts}
                    onChange={(e) => handleNestedChange('security', 'failedLoginAttempts', parseInt(e.target.value))}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Account Lockout Duration (minutes)</label>
                  <input
                    type="number"
                    min="5"
                    value={settings.security.lockoutDuration}
                    onChange={(e) => handleNestedChange('security', 'lockoutDuration', parseInt(e.target.value))}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                  />
                </div>
                <div className="flex items-center">
                  {/* <input
                    type="checkbox"
                    checked={settings.security.deviceTracking}
                    onChange={(e) => handleNestedChange('security', 'deviceTracking', e.target.checked)}
                    className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                  /> */}
                  {/* <label className="ml-2 block text-sm text-gray-900">Enable Device Tracking</label> */}
                </div>
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.security.geofencing}
                    onChange={(e) => handleNestedChange('security', 'geofencing', e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                  />
                  <label className="ml-2 block text-sm text-text">Enable Geofencing</label>
                </div>
              </div>

              {/* Password Policy */}
              <div>
                <h3 className="text-md font-medium text-text mb-2">Password Policy</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text">Minimum Length</label>
                    <input
                      type="number"
                      min="8"
                      value={settings.security.passwordPolicy.minLength}
                      onChange={(e) => handleNestedChange('security', 'passwordPolicy', parseInt(e.target.value), 'minLength')}
                      className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.security.passwordPolicy.requireSpecialChars}
                        onChange={(e) => handleNestedChange('security', 'passwordPolicy', e.target.checked, 'requireSpecialChars')}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                      />
                      <label className="ml-2 block text-sm text-text">Special Characters</label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.security.passwordPolicy.requireNumbers}
                        onChange={(e) => handleNestedChange('security', 'passwordPolicy', e.target.checked, 'requireNumbers')}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                      />
                      <label className="ml-2 block text-sm text-text">Numbers</label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.security.passwordPolicy.requireUppercase}
                        onChange={(e) => handleNestedChange('security', 'passwordPolicy', e.target.checked, 'requireUppercase')}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                      />
                      <label className="ml-2 block text-sm text-text">Uppercase Letters</label>
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        checked={settings.security.passwordPolicy.preventCommonWords}
                        onChange={(e) => handleNestedChange('security', 'passwordPolicy', e.target.checked, 'preventCommonWords')}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                      />
                      <label className="ml-2 block text-sm text-text">Prevent Common Words</label>
                    </div>
                  </div>
                </div>
              </div>

              {/* Advanced Security */}
              <div>
                <h3 className="text-md font-medium text-text mb-2">Advanced Security</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-text">Session Timeout (minutes)</label>
                    <input
                      type="number"
                      min="5"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => handleNestedChange('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text">Encryption Level</label>
                    <select
                      value={settings.security.encryptionLevel}
                      onChange={(e) => handleNestedChange('security', 'encryptionLevel', e.target.value)}
                      className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    >
                      <option value="standard">Standard (128-bit)</option>
                      <option value="high">High (256-bit)</option>
                      {/* <option value="military">Military Grade (384-bit)</option> */}
                    </select>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.bruteForceProtection}
                      onChange={(e) => handleNestedChange('security', 'bruteForceProtection', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                    />
                    <label className="ml-2 block text-sm text-text">Brute Force Protection</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.anomalyDetection}
                      onChange={(e) => handleNestedChange('security', 'anomalyDetection', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                    />
                    <label className="ml-2 block text-sm text-text">Anomaly Detection</label>
                  </div>
                </div>
              </div>

              {/* Network Security */}
              <div>
                <h3 className="text-md font-medium text-text mb-2">Network Security</h3>
                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.sslEnabled}
                      onChange={(e) => handleNestedChange('security', 'sslEnabled', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                    />
                    <label className="ml-2 block text-sm text-text">Force SSL/TLS</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.ddosProtection}
                      onChange={(e) => handleNestedChange('security', 'ddosProtection', e.target.checked)}
                      className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                    />
                    <label className="ml-2 block text-sm text-text">DDoS Protection</label>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text">Allowed IP Addresses</label>
                    <div className="mt-1">
                      <input
                        type="text"
                        placeholder="Enter IP address and press Enter"
                        className="block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all placeholder:text-text-secondary"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            const input = e.target as HTMLInputElement;
                            const newIp = input.value.trim();
                            if (newIp && settings.security.ipWhitelist && !settings.security.ipWhitelist.includes(newIp)) {
                              handleNestedChange('security', 'ipWhitelist', [...(settings.security.ipWhitelist || []), newIp]);
                              input.value = '';
                            }
                          }
                        }}
                      />
                    </div>
                    <div className="mt-2">
                      {settings.security.ipWhitelist?.map((ip, index) => (
                        <div key={index} className="inline-flex items-center bg-purple-50 dark:bg-purple-900/30 rounded-full px-3 py-1 text-sm text-purple-700 dark:text-purple-300 mr-2 mb-2 border border-purple-200 dark:border-purple-700/50">
                          {ip}
                          <button
                            onClick={() => {
                              const newList = settings.security.ipWhitelist.filter((_, i) => i !== index);
                              handleNestedChange('security', 'ipWhitelist', newList);
                            }}
                            className="ml-2 text-purple-500 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300 transition-colors"
                          >
                            ×
                          </button>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced Compliance Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">Compliance Standards</h2>
          <div className="space-y-6">
            {/* Core Compliance Standards */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Core Standards</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={gdprEnabled}
                    onChange={(e) => handleComplianceChange('gdprEnabled', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">GDPR</label>
                    <p className="text-xs text-text-secondary">European Union Privacy</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={dpdpEnabled}
                    onChange={(e) => handleComplianceChange('dpdpEnabled', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">DPDP</label>
                    <p className="text-xs text-text-secondary">Digital Personal Data Protection</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.hipaaEnabled}
                    onChange={(e) => handleOtherComplianceChange('hipaaEnabled', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">HIPAA</label>
                    <p className="text-xs text-text-secondary">Healthcare Privacy</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.pci}
                    onChange={(e) => handleOtherComplianceChange('pci', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">PCI DSS</label>
                    <p className="text-xs text-text-secondary">Payment Card Security</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.sox}
                    onChange={(e) => handleOtherComplianceChange('sox', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">SOX</label>
                    <p className="text-xs text-text-secondary">Financial Reporting</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Standards */}
            <div>
              <h3 className="text-md font-medium text-text mb-2">Additional Standards</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.iso27001}
                    onChange={(e) => handleOtherComplianceChange('iso27001', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">ISO 27001</label>
                    <p className="text-xs text-text-secondary">Information Security</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.ccpa}
                    onChange={(e) => handleOtherComplianceChange('ccpa', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">CCPA</label>
                    <p className="text-xs text-text-secondary">California Privacy</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.nist}
                    onChange={(e) => handleOtherComplianceChange('nist', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">NIST</label>
                    <p className="text-xs text-text-secondary">Cybersecurity Framework</p>
                  </div>
                </div>
                <div className="flex items-center p-3 bg-surface rounded-lg">
                  <input
                    type="checkbox"
                    checked={complianceStandards.fedramp}
                    onChange={(e) => handleOtherComplianceChange('fedramp', e.target.checked)}
                    className="h-4 w-4 text-primary focus:ring-primary border-border rounded"
                  />
                  <div className="ml-3">
                    <label className="block text-sm font-medium text-text">FedRAMP</label>
                    <p className="text-xs text-text-secondary">Federal Cloud Security</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Compliance Status Summary */}
            <div className="mt-4 p-4 bg-surface rounded-lg">
              <h3 className="text-sm font-medium text-text mb-2">Compliance Status Summary</h3>
              <div className="text-sm text-text-secondary">
                Active Standards: {Object.entries(complianceStandards).filter(([_, value]) => value).length}
                <div className="w-full bg-border rounded-full h-2.5 mt-2">
                  <div
                    className="bg-primary h-2.5 rounded-full"
                    style={{ width: `${(Object.entries(settings.compliance).filter(([_, value]) => value).length / Object.keys(settings.compliance).length) * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Enhanced External Services Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">External Services</h2>
          <div className="space-y-6">
            {/* OpenAI Integration */}
            <div className="border-b border-border pb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-text">OpenAI Integration</h3>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 border border-green-200 dark:border-green-700/50">Active</span>
              </div>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-text">API Key</label>
                  <div className="mt-1 relative rounded-md shadow-sm">
                    <input
                      type="password"
                      value={settings.externalServices.openai.apiKey}
                      onChange={(e) => handleNestedChange('externalServices', 'openai', { ...settings.externalServices.openai, apiKey: e.target.value })}
                      className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                    />
                    <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                      <button
                        type="button"
                        className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors"
                        onClick={() => {/* Toggle password visibility */ }}
                      >
                        👁️
                      </button>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Model</label>
                  <select
                    value={settings.externalServices.openai.model}
                    onChange={(e) => handleNestedChange('externalServices', 'openai', { ...settings.externalServices.openai, model: e.target.value })}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                  >
                    <option value="gpt-4">GPT-4 (Recommended)</option>
                    <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                    <option value="gpt-4-32k">GPT-4 32K</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700">Max Tokens</label>
                  <input
                    type="range"
                    min="100"
                    max="4000"
                    step="100"
                    value={settings.externalServices.openai.maxTokens}
                    onChange={(e) => handleNestedChange('externalServices', 'openai', { ...settings.externalServices.openai, maxTokens: parseInt(e.target.value) })}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>100</span>
                    <span>{settings.externalServices.openai.maxTokens}</span>
                    <span>4000</span>
                  </div>
                </div>
              </div>
            </div>

            {/* AWS Integration */}
            <div className="border-b border-border pb-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-text">AWS Integration</h3>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 border border-blue-200 dark:border-blue-700/50">Cloud</span>
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text">Access Key ID</label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="password"
                        value={settings.externalServices.aws.accessKeyId}
                        onChange={(e) => handleNestedChange('externalServices', 'aws', { ...settings.externalServices.aws, accessKeyId: e.target.value })}
                        className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button type="button" className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors">👁️</button>
                      </div>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-text">Secret Access Key</label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="password"
                        value={settings.externalServices.aws.secretAccessKey}
                        onChange={(e) => handleNestedChange('externalServices', 'aws', { ...settings.externalServices.aws, secretAccessKey: e.target.value })}
                        className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button type="button" className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors">👁️</button>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-text">Region</label>
                  <select
                    value={settings.externalServices.aws.region}
                    onChange={(e) => handleNestedChange('externalServices', 'aws', { ...settings.externalServices.aws, region: e.target.value })}
                    className="mt-1 block w-full rounded-md border-border bg-surface text-text focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                  >
                    <option value="us-east-1">US East (N. Virginia)</option>
                    <option value="us-west-2">US West (Oregon)</option>
                    <option value="eu-west-1">EU (Ireland)</option>
                    <option value="ap-southeast-1">Asia Pacific (Singapore)</option>
                  </select>
                </div>
              </div>
            </div>

            {/* Azure Integration */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-md font-medium text-text">Azure Integration</h3>
                <span className="px-2 py-1 text-xs font-medium rounded-full bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300 border border-purple-200 dark:border-purple-700/50">Enterprise</span>
              </div>
              <div className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-text">Tenant ID</label>
                    <div className="mt-1 relative rounded-md shadow-sm">
                      <input
                        type="password"
                        value={settings.externalServices.azure.tenantId}
                        onChange={(e) => handleNestedChange('externalServices', 'azure', { ...settings.externalServices.azure, tenantId: e.target.value })}
                        className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      />
                      <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button type="button" className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors">👁️</button>
                      </div>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="block text-sm font-medium text-text">Client ID</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <input
                          type="password"
                          value={settings.externalServices.azure.clientId}
                          onChange={(e) => handleNestedChange('externalServices', 'azure', { ...settings.externalServices.azure, clientId: e.target.value })}
                          className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <button type="button" className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors">👁️</button>
                        </div>
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-text">Client Secret</label>
                      <div className="mt-1 relative rounded-md shadow-sm">
                        <input
                          type="password"
                          value={settings.externalServices.azure.clientSecret}
                          onChange={(e) => handleNestedChange('externalServices', 'azure', { ...settings.externalServices.azure, clientSecret: e.target.value })}
                          className="block w-full rounded-md border-border bg-surface text-text pr-10 focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                        />
                        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
                          <button type="button" className="text-text-secondary hover:text-purple-600 dark:hover:text-purple-400 transition-colors">👁️</button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* Audit Settings Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">Audit Settings</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.auditSettings.enableAuditLogging}
                onChange={(e) => handleNestedChange('auditSettings', 'enableAuditLogging', e.target.checked)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
              />
              <label className="ml-2 block text-sm text-text">Enable Audit Logging</label>
            </div>
            <div>
              <label className="block text-sm font-medium text-text">Log Retention (Days)</label>
              <input
                type="number"
                value={settings.auditSettings.logRetentionDays}
                onChange={(e) => handleNestedChange('auditSettings', 'logRetentionDays', parseInt(e.target.value))}
                className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
              />
              {validationErrors.logRetention && (
                <p className="mt-1 text-sm text-error">{validationErrors.logRetention}</p>
              )}
            </div>
          </div>
        </section>

        {/* Risk Management Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">Risk Management</h2>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                checked={settings.riskManagement.enableRiskAssessment}
                onChange={(e) => handleNestedChange('riskManagement', 'enableRiskAssessment', e.target.checked)}
                className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
              />
              <label className="ml-2 block text-sm text-text">Enable Risk Assessment</label>
            </div>
            <div>
              <label className="block text-sm font-medium text-text">Risk Threshold (%)</label>
              <input
                type="number"
                min="0"
                max="100"
                value={settings.riskManagement.riskThreshold}
                onChange={(e) => handleNestedChange('riskManagement', 'riskThreshold', parseInt(e.target.value))}
                className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
              />
            </div>
          </div>
        </section>

        {/* Integrations Section */}
        <section className="bg-card rounded-lg shadow p-6">
          <h2 className="text-lg font-medium text-text mb-4">Integrations</h2>
          <div className="space-y-6">
            <div>
              <h3 className="text-md font-medium text-text mb-2">Jira Integration</h3>
              <div className="space-y-4">
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    checked={settings.integrations.jira.enabled}
                    onChange={(e) => handleNestedChange('integrations', 'jira', { ...settings.integrations.jira, enabled: e.target.checked })}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 dark:focus:ring-purple-400 border-border rounded"
                  />
                  <label className="ml-2 block text-sm text-text">Enable Jira Integration</label>
                </div>
                {settings.integrations.jira.enabled && (
                  <>
                    <div>
                      <label className="block text-sm font-medium text-text">API Key</label>
                      <input
                        type="password"
                        value={settings.integrations.jira.apiKey}
                        onChange={(e) => handleNestedChange('integrations', 'jira', { ...settings.integrations.jira, apiKey: e.target.value })}
                        className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-text">Project Key</label>
                      <input
                        type="text"
                        value={settings.integrations.jira.projectKey}
                        onChange={(e) => handleNestedChange('integrations', 'jira', { ...settings.integrations.jira, projectKey: e.target.value })}
                        className="mt-1 block w-full rounded-md border-border bg-surface text-text shadow-sm focus:border-purple-500 dark:focus:border-purple-400 focus:ring-purple-500 dark:focus:ring-purple-400 hover:border-purple-300 dark:hover:border-purple-600 transition-all"
                      />
                    </div>
                  </>
                )}
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
    </ErrorBoundary>
  );
};

export default Configurations;