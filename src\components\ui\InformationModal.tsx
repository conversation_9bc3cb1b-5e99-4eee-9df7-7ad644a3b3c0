/**
 * Information Modal Component
 * Provides detailed information pop-ups for dashboard data items
 */

import React, { useState, useEffect } from 'react';
import { createPortal } from 'react-dom';
import { X, Calendar, User, AlertTriangle, CheckCircle, Clock, FileText, Shield, TrendingUp, BarChart3, Info, Eye, Edit, Trash2 } from 'lucide-react';
import { useTheme } from '../../context/ThemeContext';
import { ExpandableSection, InfoSection, WarningSection } from './ExpandableSection';
import { HelpTooltip, InfoTooltip } from './Tooltip';
import type { GDPRRequest, RiskAssessment, Violation, ComplianceScore } from '../../services/mockDataService';

interface InformationModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  data: any;
  type: 'gdpr_request' | 'risk_assessment' | 'violation' | 'compliance_score' | 'audit_trail' | 'policy' | 'branch';
}

export const InformationModal: React.FC<InformationModalProps> = ({
  isOpen,
  onClose,
  title,
  data,
  type
}) => {
  const { mode } = useTheme();
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      document.body.style.overflow = 'hidden';
    } else {
      setIsVisible(false);
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const formatDate = (date: Date | string) => {
    const d = typeof date === 'string' ? new Date(date) : date;
    return d.toLocaleDateString() + ' ' + d.toLocaleTimeString();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
      case 'resolved':
      case 'compliant':
        return 'text-green-600 bg-green-100';
      case 'pending':
      case 'in_progress':
      case 'investigating':
        return 'text-yellow-600 bg-yellow-100';
      case 'overdue':
      case 'critical':
      case 'high':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'critical':
        return <AlertTriangle className="w-4 h-4 text-red-500" />;
      case 'high':
        return <AlertTriangle className="w-4 h-4 text-orange-500" />;
      case 'medium':
        return <Clock className="w-4 h-4 text-yellow-500" />;
      case 'low':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      default:
        return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  const renderGDPRRequestDetails = (request: GDPRRequest) => (
    <div className="space-y-6">
      {/* Basic Information */}
      <InfoSection title="Basic Information" defaultExpanded={true}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Request ID
              <HelpTooltip content="Unique identifier for this GDPR request" />
            </label>
            <p className="text-text font-mono bg-surface px-2 py-1 rounded">{request.id}</p>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Email Address
              <InfoTooltip content="The data subject's email address who submitted this request" />
            </label>
            <p className="text-text">{request.email}</p>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Request Type
              <HelpTooltip content="The type of GDPR request: access, erasure, rectification, portability, restriction, or objection" />
            </label>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
              {request.type.replace('_', ' ').toUpperCase()}
            </span>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Status
              <InfoTooltip content="Current processing status of the request" />
            </label>
            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
              {request.status.replace('_', ' ').toUpperCase()}
            </span>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Priority Level
              <HelpTooltip content="Priority level determines processing order and urgency" />
            </label>
            <div className="flex items-center gap-2">
              {getPriorityIcon(request.priority)}
              <span className="capitalize font-medium">{request.priority}</span>
            </div>
          </div>
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-1">
              Department
              <InfoTooltip content="The department responsible for processing this request" />
            </label>
            <p className="text-text">{request.department}</p>
          </div>
        </div>
      </InfoSection>

      {/* Request Details */}
      <ExpandableSection title="Request Details" defaultExpanded={true}>
        <div className="space-y-4">
          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
              Description
              <HelpTooltip content="Detailed description of what the data subject is requesting" />
            </label>
            <div className="text-text bg-surface p-4 rounded-lg border border-border">
              {request.description}
            </div>
          </div>

          <div>
            <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
              Data Categories Involved
              <InfoTooltip content="Types of personal data that this request affects" />
            </label>
            <div className="flex flex-wrap gap-2">
              {request.dataCategories.map((category, index) => (
                <span key={index} className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-primary/10 text-primary border border-primary/20">
                  <Shield className="w-3 h-3 mr-1" />
                  {category}
                </span>
              ))}
            </div>
          </div>
        </div>
      </ExpandableSection>

      {/* Timeline Information */}
      <ExpandableSection title="Timeline & Assignment" defaultExpanded={true}>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
                Submitted At
                <InfoTooltip content="When this request was originally submitted" />
              </label>
              <div className="flex items-center gap-2 text-text bg-surface p-2 rounded border">
                <Calendar className="w-4 h-4 text-blue-500" />
                {formatDate(request.submittedAt)}
              </div>
            </div>

            <div>
              <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
                Due Date
                <InfoTooltip content="Legal deadline for completing this request (typically 30 days)" />
              </label>
              <div className="flex items-center gap-2 text-text bg-surface p-2 rounded border">
                <Clock className="w-4 h-4 text-orange-500" />
                {formatDate(request.dueDate)}
              </div>
            </div>
          </div>

          <div className="space-y-4">
            {request.assignedTo && (
              <div>
                <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
                  Assigned To
                  <InfoTooltip content="The data protection officer or team member responsible for this request" />
                </label>
                <div className="flex items-center gap-2 text-text bg-surface p-2 rounded border">
                  <User className="w-4 h-4 text-green-500" />
                  {request.assignedTo}
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
                  Estimated Hours
                  <HelpTooltip content="Initial estimate of time required to complete this request" />
                </label>
                <div className="text-text bg-surface p-2 rounded border text-center">
                  <span className="text-lg font-semibold">{request.estimatedHours}</span>
                  <span className="text-sm text-text-secondary ml-1">hrs</span>
                </div>
              </div>
              {request.actualHours && (
                <div>
                  <label className="flex items-center gap-2 text-sm font-medium text-text-secondary mb-2">
                    Actual Hours
                    <InfoTooltip content="Actual time spent processing this request" />
                  </label>
                  <div className="text-text bg-surface p-2 rounded border text-center">
                    <span className="text-lg font-semibold">{request.actualHours}</span>
                    <span className="text-sm text-text-secondary ml-1">hrs</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </ExpandableSection>

      {/* Compliance Information */}
      <WarningSection title="Compliance Notes" defaultExpanded={false}>
        <div className="space-y-3">
          <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-1">Legal Requirements</h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300">
              This request must be completed within 30 days as per GDPR Article 12. Failure to respond may result in regulatory penalties.
            </p>
          </div>
          <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-1">Data Processing Impact</h4>
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Processing this request may affect {request.dataCategories.length} data categories across multiple systems.
            </p>
          </div>
        </div>
      </WarningSection>
    </div>
  );

  const renderRiskAssessmentDetails = (assessment: RiskAssessment) => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Assessment ID</label>
          <p className="text-text font-mono">{assessment.id}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Category</label>
          <p className="text-text capitalize">{assessment.category.replace('_', ' ')}</p>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Risk Level</label>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(assessment.level)}`}>
            {assessment.level.toUpperCase()}
          </span>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Status</label>
          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(assessment.status)}`}>
            {assessment.status.replace('_', ' ')}
          </span>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-text-secondary mb-1">Title</label>
        <p className="text-text bg-surface p-3 rounded-lg">{assessment.title}</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Probability</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-500 h-2 rounded-full" 
                style={{ width: `${assessment.probability}%` }}
              ></div>
            </div>
            <span className="text-sm text-text">{assessment.probability}%</span>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Impact</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-orange-500 h-2 rounded-full" 
                style={{ width: `${assessment.impact}%` }}
              ></div>
            </div>
            <span className="text-sm text-text">{assessment.impact}%</span>
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Risk Score</label>
          <div className="flex items-center gap-2">
            <div className="flex-1 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-red-500 h-2 rounded-full" 
                style={{ width: `${assessment.riskScore}%` }}
              ></div>
            </div>
            <span className="text-sm text-text">{assessment.riskScore}</span>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Owner</label>
          <div className="flex items-center gap-2 text-text">
            <User className="w-4 h-4" />
            {assessment.owner}
          </div>
        </div>
        <div>
          <label className="block text-sm font-medium text-text-secondary mb-1">Due Date</label>
          <div className="flex items-center gap-2 text-text">
            <Calendar className="w-4 h-4" />
            {formatDate(assessment.dueDate)}
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-text-secondary mb-1">Mitigation Actions</label>
        <ul className="list-disc list-inside space-y-1 text-text bg-surface p-3 rounded-lg">
          {assessment.mitigationActions.map((action, index) => (
            <li key={index}>{action}</li>
          ))}
        </ul>
      </div>

      <div>
        <label className="block text-sm font-medium text-text-secondary mb-1">Last Reviewed</label>
        <div className="flex items-center gap-2 text-text">
          <Clock className="w-4 h-4" />
          {formatDate(assessment.lastReviewed)}
        </div>
      </div>
    </div>
  );

  const renderContent = () => {
    switch (type) {
      case 'gdpr_request':
        return renderGDPRRequestDetails(data as GDPRRequest);
      case 'risk_assessment':
        return renderRiskAssessmentDetails(data as RiskAssessment);
      default:
        return (
          <div className="text-center py-8">
            <FileText className="w-12 h-12 text-text-secondary mx-auto mb-4" />
            <p className="text-text-secondary">Detailed view for this item type is not yet implemented.</p>
          </div>
        );
    }
  };

  if (!isOpen) return null;

  return createPortal(
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-opacity duration-300 ${
        isVisible ? 'opacity-100' : 'opacity-0'
      }`}
      onClick={handleBackdropClick}
    >
      <div className="fixed inset-0 bg-black bg-opacity-50 transition-opacity" />
      <div
        className={`
          relative bg-card rounded-xl shadow-2xl border border-border max-w-4xl w-full max-h-[90vh] overflow-hidden
          transform transition-all duration-300 ${
            isVisible ? 'scale-100 opacity-100' : 'scale-95 opacity-0'
          }
        `}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-border">
          <h2 className="text-xl font-semibold text-text">{title}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-surface rounded-lg transition-colors duration-200"
            aria-label="Close modal"
          >
            <X className="w-5 h-5 text-text-secondary" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {renderContent()}
        </div>
      </div>
    </div>,
    document.body
  );
};

export default InformationModal;
