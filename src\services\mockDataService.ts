/**
 * Comprehensive Mock Data Service
 * Provides realistic dummy data for all dashboard components
 * Enhanced with professional-grade sample data for all compliance metrics
 */

export interface GDPRRequest {
  id: string;
  email: string;
  type: 'access' | 'erasure' | 'rectification' | 'portability' | 'restriction' | 'objection';
  status: 'pending' | 'in_progress' | 'completed' | 'rejected' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  submittedAt: Date;
  dueDate: Date;
  completedAt?: Date;
  assignedTo?: string;
  department: string;
  dataCategories: string[];
  estimatedHours: number;
  actualHours?: number;
}

export interface ComplianceScore {
  framework: string;
  score: number;
  trend: 'up' | 'down' | 'stable';
  change: number;
  lastUpdated: Date;
  details: {
    compliant: number;
    nonCompliant: number;
    inProgress: number;
    total: number;
  };
}

export interface RiskAssessment {
  id: string;
  title: string;
  category: 'data_breach' | 'privacy' | 'security' | 'operational' | 'regulatory';
  level: 'low' | 'medium' | 'high' | 'critical';
  probability: number;
  impact: number;
  riskScore: number;
  status: 'identified' | 'assessed' | 'mitigated' | 'accepted' | 'transferred';
  owner: string;
  dueDate: Date;
  mitigationActions: string[];
  lastReviewed: Date;
}

export interface AuditTrail {
  id: string;
  timestamp: Date;
  user: string;
  action: string;
  resource: string;
  details: string;
  ipAddress: string;
  userAgent: string;
  outcome: 'success' | 'failure' | 'warning';
  category: 'data_access' | 'policy_change' | 'user_management' | 'system_config' | 'compliance';
}

export interface Violation {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'resolved' | 'closed';
  category: 'data_breach' | 'policy_violation' | 'access_violation' | 'retention_violation';
  detectedAt: Date;
  resolvedAt?: Date;
  affectedRecords: number;
  assignedTo: string;
  department: string;
  remediationSteps: string[];
}

export interface PolicyCompliance {
  id: string;
  policyName: string;
  version: string;
  status: 'compliant' | 'non_compliant' | 'under_review' | 'pending_approval';
  complianceScore: number;
  lastReviewed: Date;
  nextReview: Date;
  owner: string;
  department: string;
  applicableFrameworks: string[];
  violations: number;
  exceptions: number;
}

export interface BranchData {
  id: string;
  name: string;
  location: string;
  country: string;
  complianceScore: number;
  status: 'compliant' | 'non_compliant' | 'under_review';
  lastAudit: Date;
  nextAudit: Date;
  employees: number;
  dataProcessingActivities: number;
  riskLevel: 'low' | 'medium' | 'high';
  frameworks: string[];
}

export interface ConsentData {
  id: string;
  userId: string;
  email: string;
  consentType: 'marketing' | 'analytics' | 'functional' | 'necessary';
  status: 'granted' | 'withdrawn' | 'expired' | 'pending';
  grantedAt: Date;
  withdrawnAt?: Date;
  expiresAt?: Date;
  source: 'website' | 'mobile_app' | 'email' | 'phone' | 'in_person';
  ipAddress: string;
  userAgent: string;
  legalBasis: string;
}

export interface ImpactAssessment {
  id: string;
  title: string;
  description: string;
  status: 'draft' | 'in_review' | 'approved' | 'rejected' | 'requires_update';
  riskLevel: 'low' | 'medium' | 'high' | 'very_high';
  dataTypes: string[];
  processingPurposes: string[];
  legalBasis: string[];
  createdAt: Date;
  lastUpdated: Date;
  reviewedBy?: string;
  approvedBy?: string;
  nextReview: Date;
  mitigationMeasures: string[];
}

class MockDataService {
  private static instance: MockDataService;

  public static getInstance(): MockDataService {
    if (!MockDataService.instance) {
      MockDataService.instance = new MockDataService();
    }
    return MockDataService.instance;
  }

  // Generate realistic GDPR requests
  generateGDPRRequests(count: number = 50): GDPRRequest[] {
    const requests: GDPRRequest[] = [];
    const types: GDPRRequest['type'][] = ['access', 'erasure', 'rectification', 'portability', 'restriction', 'objection'];
    const statuses: GDPRRequest['status'][] = ['pending', 'in_progress', 'completed', 'rejected', 'overdue'];
    const priorities: GDPRRequest['priority'][] = ['low', 'medium', 'high', 'critical'];
    const departments = ['HR', 'Marketing', 'Sales', 'IT', 'Finance', 'Legal', 'Operations'];
    const dataCategories = ['Personal Info', 'Contact Details', 'Financial Data', 'Health Records', 'Behavioral Data', 'Location Data'];

    for (let i = 0; i < count; i++) {
      const submittedAt = new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000);
      const dueDate = new Date(submittedAt.getTime() + 30 * 24 * 60 * 60 * 1000);
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      requests.push({
        id: `dsr-${String(i + 1).padStart(3, '0')}`,
        email: `user${i + 1}@example.com`,
        type: types[Math.floor(Math.random() * types.length)],
        status,
        priority: priorities[Math.floor(Math.random() * priorities.length)],
        description: `Data subject request for ${types[Math.floor(Math.random() * types.length)]} - Case #${i + 1}`,
        submittedAt,
        dueDate,
        completedAt: status === 'completed' ? new Date(submittedAt.getTime() + Math.random() * 25 * 24 * 60 * 60 * 1000) : undefined,
        assignedTo: `Officer ${Math.floor(Math.random() * 5) + 1}`,
        department: departments[Math.floor(Math.random() * departments.length)],
        dataCategories: dataCategories.slice(0, Math.floor(Math.random() * 3) + 1),
        estimatedHours: Math.floor(Math.random() * 20) + 2,
        actualHours: status === 'completed' ? Math.floor(Math.random() * 25) + 1 : undefined,
      });
    }

    return requests;
  }

  // Generate compliance scores for different frameworks
  generateComplianceScores(): ComplianceScore[] {
    const frameworks = [
      { name: 'GDPR', baseScore: 94 },
      { name: 'CCPA', baseScore: 89 },
      { name: 'SOX', baseScore: 92 },
      { name: 'ISO 27001', baseScore: 87 },
      { name: 'HIPAA', baseScore: 91 },
      { name: 'PCI DSS', baseScore: 88 },
      { name: 'NIST', baseScore: 85 },
      { name: 'SOC 2', baseScore: 90 }
    ];

    return frameworks.map(framework => {
      const change = (Math.random() - 0.5) * 10;
      const score = Math.max(0, Math.min(100, framework.baseScore + change));
      const total = Math.floor(Math.random() * 100) + 50;
      const compliant = Math.floor(total * (score / 100));
      const nonCompliant = Math.floor(total * 0.1);
      const inProgress = total - compliant - nonCompliant;

      return {
        framework: framework.name,
        score: Math.round(score * 10) / 10,
        trend: change > 1 ? 'up' : change < -1 ? 'down' : 'stable',
        change: Math.round(change * 10) / 10,
        lastUpdated: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
        details: {
          compliant,
          nonCompliant,
          inProgress,
          total
        }
      };
    });
  }

  // Generate risk assessments
  generateRiskAssessments(count: number = 30): RiskAssessment[] {
    const assessments: RiskAssessment[] = [];
    const categories: RiskAssessment['category'][] = ['data_breach', 'privacy', 'security', 'operational', 'regulatory'];
    const levels: RiskAssessment['level'][] = ['low', 'medium', 'high', 'critical'];
    const statuses: RiskAssessment['status'][] = ['identified', 'assessed', 'mitigated', 'accepted', 'transferred'];
    const owners = ['John Smith', 'Sarah Johnson', 'Mike Davis', 'Lisa Chen', 'David Wilson'];

    for (let i = 0; i < count; i++) {
      const probability = Math.random();
      const impact = Math.random();
      const riskScore = probability * impact * 100;
      const level = riskScore > 75 ? 'critical' : riskScore > 50 ? 'high' : riskScore > 25 ? 'medium' : 'low';

      assessments.push({
        id: `risk-${String(i + 1).padStart(3, '0')}`,
        title: `Risk Assessment ${i + 1}`,
        category: categories[Math.floor(Math.random() * categories.length)],
        level,
        probability: Math.round(probability * 100),
        impact: Math.round(impact * 100),
        riskScore: Math.round(riskScore),
        status: statuses[Math.floor(Math.random() * statuses.length)],
        owner: owners[Math.floor(Math.random() * owners.length)],
        dueDate: new Date(Date.now() + Math.random() * 60 * 24 * 60 * 60 * 1000),
        mitigationActions: [
          'Implement additional security controls',
          'Conduct staff training',
          'Update policies and procedures',
          'Regular monitoring and review'
        ].slice(0, Math.floor(Math.random() * 3) + 1),
        lastReviewed: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
      });
    }

    return assessments;
  }

  // Generate audit trail entries
  generateAuditTrail(count: number = 100): AuditTrail[] {
    const entries: AuditTrail[] = [];
    const users = ['<EMAIL>', '<EMAIL>', '<EMAIL>', '<EMAIL>'];
    const actions = ['LOGIN', 'LOGOUT', 'DATA_ACCESS', 'POLICY_UPDATE', 'USER_CREATE', 'USER_DELETE', 'CONFIG_CHANGE'];
    const resources = ['User Management', 'Policy Database', 'Compliance Dashboard', 'GDPR Module', 'Risk Assessment'];
    const outcomes: AuditTrail['outcome'][] = ['success', 'failure', 'warning'];
    const categories: AuditTrail['category'][] = ['data_access', 'policy_change', 'user_management', 'system_config', 'compliance'];

    for (let i = 0; i < count; i++) {
      entries.push({
        id: `audit-${String(i + 1).padStart(4, '0')}`,
        timestamp: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
        user: users[Math.floor(Math.random() * users.length)],
        action: actions[Math.floor(Math.random() * actions.length)],
        resource: resources[Math.floor(Math.random() * resources.length)],
        details: `Audit entry ${i + 1} - System activity logged`,
        ipAddress: `192.168.1.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        outcome: outcomes[Math.floor(Math.random() * outcomes.length)],
        category: categories[Math.floor(Math.random() * categories.length)],
      });
    }

    return entries.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Generate violations
  generateViolations(count: number = 25): Violation[] {
    const violations: Violation[] = [];
    const severities: Violation['severity'][] = ['low', 'medium', 'high', 'critical'];
    const statuses: Violation['status'][] = ['open', 'investigating', 'resolved', 'closed'];
    const categories: Violation['category'][] = ['data_breach', 'policy_violation', 'access_violation', 'retention_violation'];
    const assignees = ['Security Team', 'Compliance Officer', 'IT Administrator', 'Legal Team', 'Data Protection Officer'];
    const departments = ['IT', 'HR', 'Marketing', 'Sales', 'Finance', 'Operations'];

    for (let i = 0; i < count; i++) {
      const detectedAt = new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000);
      const status = statuses[Math.floor(Math.random() * statuses.length)];

      violations.push({
        id: `viol-${String(i + 1).padStart(3, '0')}`,
        title: `Compliance Violation ${i + 1}`,
        description: `Violation detected in system processes - requires immediate attention`,
        severity: severities[Math.floor(Math.random() * severities.length)],
        status,
        category: categories[Math.floor(Math.random() * categories.length)],
        detectedAt,
        resolvedAt: status === 'resolved' || status === 'closed' ?
          new Date(detectedAt.getTime() + Math.random() * 14 * 24 * 60 * 60 * 1000) : undefined,
        affectedRecords: Math.floor(Math.random() * 1000) + 1,
        assignedTo: assignees[Math.floor(Math.random() * assignees.length)],
        department: departments[Math.floor(Math.random() * departments.length)],
        remediationSteps: [
          'Investigate root cause',
          'Implement corrective measures',
          'Update policies',
          'Conduct training',
          'Monitor compliance'
        ].slice(0, Math.floor(Math.random() * 3) + 2),
      });
    }

    return violations;
  }

  // Generate policy compliance data
  generatePolicyCompliance(count: number = 20): PolicyCompliance[] {
    const policies: PolicyCompliance[] = [];
    const policyNames = [
      'Data Retention Policy', 'Privacy Policy', 'Security Policy', 'Access Control Policy',
      'Incident Response Policy', 'Backup Policy', 'GDPR Compliance Policy', 'Employee Handbook'
    ];
    const statuses: PolicyCompliance['status'][] = ['compliant', 'non_compliant', 'under_review', 'pending_approval'];
    const owners = ['Legal Team', 'IT Department', 'HR Department', 'Compliance Officer', 'Security Team'];
    const departments = ['Legal', 'IT', 'HR', 'Compliance', 'Security', 'Operations'];
    const frameworks = ['GDPR', 'CCPA', 'SOX', 'ISO 27001', 'HIPAA', 'PCI DSS'];

    for (let i = 0; i < count; i++) {
      const lastReviewed = new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000);
      const nextReview = new Date(lastReviewed.getTime() + 365 * 24 * 60 * 60 * 1000);

      policies.push({
        id: `policy-${String(i + 1).padStart(3, '0')}`,
        policyName: policyNames[Math.floor(Math.random() * policyNames.length)],
        version: `v${Math.floor(Math.random() * 5) + 1}.${Math.floor(Math.random() * 10)}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        complianceScore: Math.floor(Math.random() * 40) + 60,
        lastReviewed,
        nextReview,
        owner: owners[Math.floor(Math.random() * owners.length)],
        department: departments[Math.floor(Math.random() * departments.length)],
        applicableFrameworks: frameworks.slice(0, Math.floor(Math.random() * 3) + 1),
        violations: Math.floor(Math.random() * 5),
        exceptions: Math.floor(Math.random() * 3),
      });
    }

    return policies;
  }

  // Generate branch data
  generateBranchData(count: number = 15): BranchData[] {
    const branches: BranchData[] = [];
    const locations = [
      { name: 'New York Office', location: 'New York, NY', country: 'USA' },
      { name: 'London Office', location: 'London', country: 'UK' },
      { name: 'Tokyo Office', location: 'Tokyo', country: 'Japan' },
      { name: 'Berlin Office', location: 'Berlin', country: 'Germany' },
      { name: 'Sydney Office', location: 'Sydney', country: 'Australia' },
      { name: 'Toronto Office', location: 'Toronto', country: 'Canada' },
      { name: 'Singapore Office', location: 'Singapore', country: 'Singapore' },
      { name: 'Mumbai Office', location: 'Mumbai', country: 'India' }
    ];
    const statuses: BranchData['status'][] = ['compliant', 'non_compliant', 'under_review'];
    const riskLevels: BranchData['riskLevel'][] = ['low', 'medium', 'high'];
    const frameworks = ['GDPR', 'CCPA', 'SOX', 'ISO 27001', 'Local Regulations'];

    for (let i = 0; i < Math.min(count, locations.length); i++) {
      const location = locations[i];
      const lastAudit = new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000);
      const nextAudit = new Date(lastAudit.getTime() + 365 * 24 * 60 * 60 * 1000);

      branches.push({
        id: `branch-${String(i + 1).padStart(3, '0')}`,
        name: location.name,
        location: location.location,
        country: location.country,
        complianceScore: Math.floor(Math.random() * 30) + 70,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        lastAudit,
        nextAudit,
        employees: Math.floor(Math.random() * 500) + 50,
        dataProcessingActivities: Math.floor(Math.random() * 20) + 5,
        riskLevel: riskLevels[Math.floor(Math.random() * riskLevels.length)],
        frameworks: frameworks.slice(0, Math.floor(Math.random() * 3) + 2),
      });
    }

    return branches;
  }

  // Generate enhanced security metrics
  generateSecurityMetrics() {
    return {
      threatLevel: 'medium',
      activeThreats: Math.floor(Math.random() * 5) + 1,
      blockedAttacks: Math.floor(Math.random() * 100) + 50,
      vulnerabilities: {
        critical: Math.floor(Math.random() * 3),
        high: Math.floor(Math.random() * 8) + 2,
        medium: Math.floor(Math.random() * 15) + 5,
        low: Math.floor(Math.random() * 25) + 10
      },
      lastScan: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
      complianceScore: Math.floor(Math.random() * 20) + 80,
      incidentResponse: {
        averageResponseTime: Math.floor(Math.random() * 30) + 15, // minutes
        resolvedIncidents: Math.floor(Math.random() * 50) + 100,
        openIncidents: Math.floor(Math.random() * 5) + 1
      }
    };
  }

  // Generate comprehensive dashboard metrics
  generateDashboardMetrics() {
    return {
      totalDataSubjects: Math.floor(Math.random() * 10000) + 50000,
      activeConsents: Math.floor(Math.random() * 8000) + 40000,
      pendingRequests: Math.floor(Math.random() * 50) + 10,
      complianceScore: Math.floor(Math.random() * 15) + 85,
      riskScore: Math.floor(Math.random() * 30) + 20,
      dataProcessingActivities: Math.floor(Math.random() * 100) + 200,
      thirdPartyVendors: Math.floor(Math.random() * 50) + 75,
      dataBreaches: Math.floor(Math.random() * 3),
      monthlyTrends: {
        requests: Array.from({ length: 12 }, () => Math.floor(Math.random() * 100) + 50),
        compliance: Array.from({ length: 12 }, () => Math.floor(Math.random() * 10) + 85),
        risks: Array.from({ length: 12 }, () => Math.floor(Math.random() * 20) + 15)
      }
    };
  }

  // Generate realistic user activity data
  generateUserActivity(days: number = 30) {
    const activities = [];
    const actionTypes = [
      'Data Access Request', 'Data Deletion Request', 'Consent Update',
      'Policy Review', 'Risk Assessment', 'Audit Log Review',
      'Compliance Check', 'Security Scan', 'Incident Response'
    ];
    const users = [
      'John Doe', 'Sarah Johnson', 'Michael Chen', 'Emma Wilson',
      'David Rodriguez', 'Lisa Thompson', 'James Miller', 'Anna Garcia'
    ];

    for (let i = 0; i < days * 5; i++) {
      activities.push({
        id: `activity-${i + 1}`,
        user: users[Math.floor(Math.random() * users.length)],
        action: actionTypes[Math.floor(Math.random() * actionTypes.length)],
        timestamp: new Date(Date.now() - Math.random() * days * 24 * 60 * 60 * 1000),
        status: Math.random() > 0.1 ? 'completed' : 'failed',
        details: `Processed ${Math.floor(Math.random() * 100) + 1} records`,
        ipAddress: `192.168.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`,
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      });
    }

    return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  // Generate performance metrics
  generatePerformanceMetrics() {
    return {
      systemUptime: 99.8 + Math.random() * 0.2,
      responseTime: Math.floor(Math.random() * 100) + 50, // ms
      throughput: Math.floor(Math.random() * 1000) + 5000, // requests/hour
      errorRate: Math.random() * 0.5, // percentage
      cpuUsage: Math.floor(Math.random() * 30) + 40,
      memoryUsage: Math.floor(Math.random() * 25) + 60,
      diskUsage: Math.floor(Math.random() * 20) + 70,
      networkLatency: Math.floor(Math.random() * 20) + 10,
      activeConnections: Math.floor(Math.random() * 500) + 1000,
      queueSize: Math.floor(Math.random() * 100) + 50
    };
  }

  // Generate compliance framework data
  generateComplianceFrameworks() {
    return [
      {
        name: 'GDPR',
        fullName: 'General Data Protection Regulation',
        region: 'European Union',
        status: 'active',
        complianceScore: Math.floor(Math.random() * 10) + 90,
        lastAssessment: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
        nextReview: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
        requirements: Math.floor(Math.random() * 20) + 80,
        implemented: Math.floor(Math.random() * 15) + 75,
        gaps: Math.floor(Math.random() * 5) + 2
      },
      {
        name: 'CCPA',
        fullName: 'California Consumer Privacy Act',
        region: 'California, USA',
        status: 'active',
        complianceScore: Math.floor(Math.random() * 15) + 85,
        lastAssessment: new Date(Date.now() - Math.random() * 60 * 24 * 60 * 60 * 1000),
        nextReview: new Date(Date.now() + Math.random() * 120 * 24 * 60 * 60 * 1000),
        requirements: Math.floor(Math.random() * 15) + 45,
        implemented: Math.floor(Math.random() * 10) + 40,
        gaps: Math.floor(Math.random() * 3) + 1
      },
      {
        name: 'SOX',
        fullName: 'Sarbanes-Oxley Act',
        region: 'United States',
        status: 'active',
        complianceScore: Math.floor(Math.random() * 12) + 88,
        lastAssessment: new Date(Date.now() - Math.random() * 120 * 24 * 60 * 60 * 1000),
        nextReview: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
        requirements: Math.floor(Math.random() * 25) + 75,
        implemented: Math.floor(Math.random() * 20) + 70,
        gaps: Math.floor(Math.random() * 5) + 3
      },
      {
        name: 'ISO 27001',
        fullName: 'Information Security Management',
        region: 'International',
        status: 'active',
        complianceScore: Math.floor(Math.random() * 8) + 92,
        lastAssessment: new Date(Date.now() - Math.random() * 180 * 24 * 60 * 60 * 1000),
        nextReview: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000),
        requirements: Math.floor(Math.random() * 30) + 120,
        implemented: Math.floor(Math.random() * 25) + 115,
        gaps: Math.floor(Math.random() * 8) + 2
      }
    ];
  }

  // Generate data processing activities
  generateDataProcessingActivities(count: number = 20) {
    const activities = [];
    const purposes = [
      'Customer Relationship Management', 'Marketing Communications', 'Financial Processing',
      'Employee Management', 'Product Analytics', 'Security Monitoring',
      'Compliance Reporting', 'Service Delivery', 'Quality Assurance', 'Research & Development'
    ];
    const dataTypes = [
      'Personal Identifiers', 'Contact Information', 'Financial Data',
      'Employment Records', 'Behavioral Data', 'Technical Data',
      'Health Information', 'Location Data', 'Communication Records', 'Transaction History'
    ];
    const legalBases = [
      'Consent', 'Contract', 'Legal Obligation', 'Vital Interests', 'Public Task', 'Legitimate Interests'
    ];

    for (let i = 0; i < count; i++) {
      activities.push({
        id: `dpa-${i + 1}`,
        name: purposes[Math.floor(Math.random() * purposes.length)],
        purpose: purposes[Math.floor(Math.random() * purposes.length)],
        dataTypes: dataTypes.slice(0, Math.floor(Math.random() * 4) + 2),
        legalBasis: legalBases[Math.floor(Math.random() * legalBases.length)],
        dataSubjects: Math.floor(Math.random() * 10000) + 1000,
        retentionPeriod: `${Math.floor(Math.random() * 7) + 1} years`,
        thirdParties: Math.floor(Math.random() * 5),
        riskLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)],
        status: ['active', 'inactive', 'under_review'][Math.floor(Math.random() * 3)],
        lastReviewed: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
        nextReview: new Date(Date.now() + Math.random() * 365 * 24 * 60 * 60 * 1000)
      });
    }

    return activities;
  }
}

export const mockDataService = MockDataService.getInstance();

// Enhanced fetch function with comprehensive data
export const fetchComplianceData = async () => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    return {
      gdprRequests: mockDataService.generateGDPRRequests(25),
      complianceScores: mockDataService.generateComplianceScores(),
      riskAssessments: mockDataService.generateRiskAssessments(15),
      auditTrail: mockDataService.generateAuditTrail(50),
      violations: mockDataService.generateViolations(12),
      policyCompliance: mockDataService.generatePolicyCompliance(10),
      branchData: mockDataService.generateBranchData(8),
      securityMetrics: mockDataService.generateSecurityMetrics(),
      dashboardMetrics: mockDataService.generateDashboardMetrics(),
      userActivity: mockDataService.generateUserActivity(30),
      performanceMetrics: mockDataService.generatePerformanceMetrics(),
      complianceFrameworks: mockDataService.generateComplianceFrameworks(),
      dataProcessingActivities: mockDataService.generateDataProcessingActivities(20)
    };
  } catch (error) {
    console.error('Error generating mock compliance data:', error);
    return null;
  }
};