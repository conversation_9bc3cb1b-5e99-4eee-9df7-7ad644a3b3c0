import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithTheme } from '../utils/test-utils';
import { EnhancedComplianceDashboard } from '../../components/GDPR/EnhancedComplianceDashboard';
import { EnhancedComplianceService } from '../../services/enhancedComplianceService';
import { MetricsCalculationService } from '../../services/metricsCalculationService';
import { ValidationService } from '../../utils/validation';

// Mock external dependencies
vi.mock('../../services/enhancedComplianceService');
vi.mock('../../services/metricsCalculationService');

// Mock chart components to avoid rendering issues in tests
vi.mock('../../components/GDPR/MetricsVisualization', () => ({
  MetricsVisualization: ({ departments, policies }: any) => (
    <div data-testid="metrics-visualization">
      <div data-testid="metrics-departments">{departments.length} departments</div>
      <div data-testid="metrics-policies">{policies.length} policies</div>
      <div data-testid="metrics-charts">Charts rendered</div>
    </div>
  )
}));

// Comprehensive mock data
const createMockDepartment = (id: string, name: string, score: number, riskLevel: 'low' | 'medium' | 'high' | 'critical') => ({
  id,
  name,
  description: `${name} department`,
  head: `${name} Manager`,
  headId: `head-${id}`,
  employeeCount: Math.floor(Math.random() * 50) + 10,
  complianceScore: score,
  riskLevel,
  policies: [`policy-${id}`],
  assessments: [`assessment-${id}`],
  lastAudit: new Date('2024-01-01'),
  nextAudit: new Date('2024-06-01'),
  status: 'active' as const,
  contactInfo: {
    email: `${name.toLowerCase()}@company.com`,
    phone: '******-1234',
    location: 'Floor 1'
  },
  complianceMetrics: {
    completedAssessments: Math.floor(Math.random() * 10) + 5,
    pendingAssessments: Math.floor(Math.random() * 5),
    overdueAssessments: Math.floor(Math.random() * 3),
    policyCompliance: score,
    trainingCompletion: Math.floor(Math.random() * 20) + 80
  }
});

const createMockPolicy = (id: string, name: string, complianceRate: number) => ({
  id,
  name,
  description: `${name} description`,
  version: '1.0',
  status: 'active' as const,
  category: 'privacy' as const,
  priority: 'high' as const,
  owner: 'Policy Owner',
  ownerId: `owner-${id}`,
  approver: 'Legal Team',
  approverId: `approver-${id}`,
  effectiveDate: new Date('2024-01-01'),
  expiryDate: new Date('2025-01-01'),
  lastReview: new Date('2024-01-01'),
  nextReview: new Date('2024-07-01'),
  applicableDepartments: ['dept-1', 'dept-2'],
  relatedPolicies: [],
  complianceRequirements: ['GDPR', 'CCPA'],
  attachments: [],
  metrics: {
    complianceRate,
    violationCount: Math.floor(Math.random() * 5),
    lastViolation: Math.random() > 0.7 ? new Date('2024-02-01') : null,
    trainingCompletion: Math.floor(Math.random() * 20) + 80,
    acknowledgmentRate: Math.floor(Math.random() * 15) + 85
  },
  auditTrail: []
});

describe('Compliance System Integration Tests', () => {
  const user = userEvent.setup();

  const mockDepartments = [
    createMockDepartment('1', 'Human Resources', 85, 'medium'),
    createMockDepartment('2', 'Information Technology', 92, 'low'),
    createMockDepartment('3', 'Legal & Compliance', 95, 'low'),
    createMockDepartment('4', 'Marketing', 78, 'high'),
    createMockDepartment('5', 'Finance', 88, 'medium')
  ];

  const mockPolicies = [
    createMockPolicy('1', 'Data Privacy Policy', 85),
    createMockPolicy('2', 'Information Security Policy', 92),
    createMockPolicy('3', 'Employee Handbook', 88),
    createMockPolicy('4', 'Code of Conduct', 90)
  ];

  const mockMetrics = {
    overall: {
      score: 88,
      trend: 'improving' as const,
      lastUpdated: new Date()
    },
    departments: Object.fromEntries(
      mockDepartments.map(dept => [dept.id, {
        score: dept.complianceScore,
        assessments: dept.complianceMetrics.completedAssessments + dept.complianceMetrics.pendingAssessments,
        policies: dept.policies.length,
        risks: 1,
        trend: 'improving' as const
      }])
    ),
    policies: {
      total: mockPolicies.length,
      active: mockPolicies.filter(p => p.status === 'active').length,
      compliant: mockPolicies.filter(p => p.metrics.complianceRate >= 90).length,
      nonCompliant: mockPolicies.filter(p => p.metrics.complianceRate < 90).length,
      pending: 0,
      overdue: 0,
      complianceRate: Math.round(mockPolicies.reduce((sum, p) => sum + p.metrics.complianceRate, 0) / mockPolicies.length)
    },
    assessments: {
      total: 50,
      completed: 40,
      inProgress: 8,
      overdue: 2,
      completionRate: 80,
      averageRiskScore: 65
    },
    risks: {
      total: 15,
      low: 8,
      medium: 5,
      high: 2,
      critical: 0,
      mitigated: 12,
      open: 3
    },
    trends: {
      period: 'month' as const,
      complianceScore: [80, 82, 85, 88],
      assessmentCompletion: [75, 78, 80, 82],
      riskReduction: [60, 65, 68, 70],
      policyCompliance: [82, 84, 85, 88],
      dates: ['2024-01', '2024-02', '2024-03', '2024-04']
    }
  };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock service responses
    vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValue(mockDepartments);
    vi.mocked(EnhancedComplianceService.getAllPolicies).mockResolvedValue(mockPolicies);
    vi.mocked(EnhancedComplianceService.getEnhancedMetrics).mockResolvedValue(mockMetrics);
    
    // Mock metrics calculation service
    vi.mocked(MetricsCalculationService.calculateRealTimeMetrics).mockReturnValue({
      timestamp: new Date(),
      overallScore: 88,
      departmentScores: Object.fromEntries(mockDepartments.map(d => [d.id, d.complianceScore])),
      policyCompliance: 88,
      riskLevel: 'medium',
      trendsData: {
        compliance: [80, 82, 85, 88],
        risks: [20, 18, 15, 12],
        assessments: [75, 78, 80, 82],
        dates: ['2024-01', '2024-02', '2024-03', '2024-04']
      },
      alerts: []
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Full System Workflow', () => {
    it('completes a full compliance review workflow', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // 1. Verify initial dashboard load
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
        expect(screen.getByText('Overall Score')).toBeInTheDocument();
      });

      // 2. Navigate to departments and verify data
      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.getByText('Information Technology')).toBeInTheDocument();
        expect(screen.getByText('Legal & Compliance')).toBeInTheDocument();
      });

      // 3. Search for specific department
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      await user.type(searchInput, 'Human');

      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.queryByText('Information Technology')).not.toBeInTheDocument();
      });

      // 4. Clear search and navigate to policies
      await user.clear(searchInput);
      await user.click(screen.getByText('Policies'));

      await waitFor(() => {
        expect(screen.getByText('Data Privacy Policy')).toBeInTheDocument();
        expect(screen.getByText('Information Security Policy')).toBeInTheDocument();
      });

      // 5. Navigate to metrics and verify visualization
      await user.click(screen.getByText('Metrics'));

      await waitFor(() => {
        expect(screen.getByTestId('metrics-visualization')).toBeInTheDocument();
        expect(screen.getByTestId('metrics-departments')).toHaveTextContent('5 departments');
        expect(screen.getByTestId('metrics-policies')).toHaveTextContent('4 policies');
      });

      // 6. Return to overview
      await user.click(screen.getByText('Overview'));

      await waitFor(() => {
        expect(screen.getByText('Overall Score')).toBeInTheDocument();
        expect(screen.getByText('Policy Compliance')).toBeInTheDocument();
      });
    });

    it('handles policy creation workflow', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Navigate to policies tab
      await user.click(screen.getByText('Policies'));

      await waitFor(() => {
        expect(screen.getByText('New Policy')).toBeInTheDocument();
      });

      // Click new policy button
      await user.click(screen.getByText('New Policy'));

      await waitFor(() => {
        expect(screen.getByText('Create New Policy')).toBeInTheDocument();
      });

      // Verify modal form elements are present
      expect(screen.getByLabelText(/policy name/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/policy owner/i)).toBeInTheDocument();
    });
  });

  describe('Error Recovery Scenarios', () => {
    it('recovers from network failures gracefully', async () => {
      // Simulate network failure
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockRejectedValueOnce(
        new Error('Network error: Failed to fetch')
      );

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Navigate to departments
      await user.click(screen.getByText('Departments'));

      // Should show error state
      await waitFor(() => {
        expect(screen.getByText('Department Loading Error')).toBeInTheDocument();
        expect(screen.getByText(/Network error/)).toBeInTheDocument();
      });

      // Mock successful retry
      vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValueOnce(mockDepartments);

      // Click retry
      await user.click(screen.getByText('Try Again'));

      // Should recover and show data
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.queryByText('Department Loading Error')).not.toBeInTheDocument();
      });
    });

    it('handles partial data loading failures', async () => {
      // Departments load successfully, policies fail
      vi.mocked(EnhancedComplianceService.getAllPolicies).mockRejectedValue(
        new Error('Policy service unavailable')
      );

      renderWithTheme(<EnhancedComplianceDashboard />);

      // Departments should work
      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });

      // Policies should show error
      await user.click(screen.getByText('Policies'));

      await waitFor(() => {
        expect(screen.getByText('Policy Loading Error')).toBeInTheDocument();
      });

      // Metrics should still work (using cached/default data)
      await user.click(screen.getByText('Metrics'));

      await waitFor(() => {
        expect(screen.getByTestId('metrics-visualization')).toBeInTheDocument();
      });
    });
  });

  describe('Performance and Responsiveness', () => {
    it('handles large datasets efficiently', async () => {
      // Create large dataset
      const largeDepartments = Array.from({ length: 100 }, (_, i) => 
        createMockDepartment(`dept-${i}`, `Department ${i}`, Math.floor(Math.random() * 40) + 60, 'medium')
      );

      vi.mocked(EnhancedComplianceService.getAllDepartments).mockResolvedValue(largeDepartments);

      renderWithTheme(<EnhancedComplianceDashboard />);

      await user.click(screen.getByText('Departments'));

      // Should render without performance issues
      await waitFor(() => {
        expect(screen.getByText('Department 0')).toBeInTheDocument();
      }, { timeout: 5000 });

      // Search should still be responsive
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      await user.type(searchInput, 'Department 5');

      await waitFor(() => {
        expect(screen.getByText('Department 5')).toBeInTheDocument();
        expect(screen.queryByText('Department 0')).not.toBeInTheDocument();
      });
    });

    it('debounces search input appropriately', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      await user.click(screen.getByText('Departments'));
      
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
      });

      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      
      // Type rapidly
      await user.type(searchInput, 'Human', { delay: 10 });

      // Should filter correctly after typing
      await waitFor(() => {
        expect(screen.getByText('Human Resources')).toBeInTheDocument();
        expect(screen.queryByText('Information Technology')).not.toBeInTheDocument();
      });
    });
  });

  describe('Data Validation and Sanitization', () => {
    it('validates and sanitizes user input', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      
      // Test XSS prevention
      await user.type(searchInput, '<script>alert("xss")</script>');
      expect(searchInput).toHaveValue('scriptalert("xss")/script');

      // Test length limits
      const longString = 'a'.repeat(150);
      await user.clear(searchInput);
      await user.type(searchInput, longString);
      expect(searchInput.value.length).toBeLessThanOrEqual(100);
    });

    it('validates form data in policy creation', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      await user.click(screen.getByText('Policies'));
      await user.click(screen.getByText('New Policy'));

      await waitFor(() => {
        expect(screen.getByText('Create New Policy')).toBeInTheDocument();
      });

      // Test validation rules
      const nameInput = screen.getByLabelText(/policy name/i);
      const descriptionInput = screen.getByLabelText(/description/i);

      // Try to submit empty form
      const submitButton = screen.getByText('Create Policy');
      await user.click(submitButton);

      // Should show validation errors
      await waitFor(() => {
        expect(screen.getByText(/policy name is required/i)).toBeInTheDocument();
        expect(screen.getByText(/description is required/i)).toBeInTheDocument();
      });

      // Fill with invalid data
      await user.type(nameInput, 'ab'); // Too short
      await user.type(descriptionInput, 'short'); // Too short

      await user.click(submitButton);

      await waitFor(() => {
        expect(screen.getByText(/must be at least 3 characters/i)).toBeInTheDocument();
        expect(screen.getByText(/must be at least 10 characters/i)).toBeInTheDocument();
      });
    });
  });

  describe('Accessibility Compliance', () => {
    it('maintains accessibility standards throughout navigation', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      // Check for proper heading hierarchy
      expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Enhanced Compliance Dashboard');

      // Check for navigation landmarks
      expect(screen.getByRole('navigation')).toBeInTheDocument();

      // Check for proper button labels
      const refreshButton = screen.getByText('Refresh');
      expect(refreshButton).toHaveAttribute('type', 'button');

      // Navigate through tabs and check focus management
      await user.tab();
      expect(document.activeElement).toHaveTextContent('Overview');

      await user.keyboard('{Enter}');
      expect(screen.getByText('Overall Score')).toBeInTheDocument();
    });

    it('provides proper ARIA labels for dynamic content', async () => {
      renderWithTheme(<EnhancedComplianceDashboard />);

      await user.click(screen.getByText('Departments'));

      await waitFor(() => {
        const departmentCards = screen.getAllByText(/compliance score/i);
        expect(departmentCards.length).toBeGreaterThan(0);
      });

      // Check for proper labeling of interactive elements
      const searchInput = screen.getByPlaceholderText(/search departments, policies/i);
      expect(searchInput).toHaveAttribute('type', 'text');
      expect(searchInput).toHaveAttribute('maxLength', '100');
    });
  });

  describe('Theme Consistency', () => {
    it('maintains visual consistency across themes', async () => {
      // Test light theme
      const { rerender } = renderWithTheme(<EnhancedComplianceDashboard />, 'light');
      
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
      });

      // Test dark theme
      rerender(<EnhancedComplianceDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
      });

      // Navigate through different sections to ensure theme consistency
      await user.click(screen.getByText('Departments'));
      await user.click(screen.getByText('Policies'));
      await user.click(screen.getByText('Metrics'));

      // All sections should render without theme-related errors
      expect(screen.getByText('Enhanced Compliance Dashboard')).toBeInTheDocument();
    });
  });
});
