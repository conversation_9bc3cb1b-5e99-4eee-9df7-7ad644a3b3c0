#!/usr/bin/env python3
"""
Simple test script to verify the GDPR Compliance API is working correctly.
Run this script after starting the FastAPI server to test the endpoints.
"""

import requests
import json

API_BASE_URL = "http://127.0.0.1:8000"

def test_health_endpoint():
    """Test the health endpoint"""
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print("✅ Health endpoint working")
            print(f"   Status: {data.get('status')}")
            print(f"   Message: {data.get('message')}")
            return True
        else:
            print(f"❌ Health endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health endpoint error: {e}")
        return False

def test_requirements_endpoint():
    """Test the requirements endpoint"""
    try:
        response = requests.get(f"{API_BASE_URL}/requirements")
        if response.status_code == 200:
            data = response.json()
            print("✅ Requirements endpoint working")
            print(f"   Found {len(data.get('requirements', []))} GDPR requirements")
            return True
        else:
            print(f"❌ Requirements endpoint failed with status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Requirements endpoint error: {e}")
        return False

def test_text_analysis():
    """Test text analysis with a simple privacy policy"""
    sample_policy = """
    Privacy Policy
    
    We collect personal information including your name, email address, and usage data.
    We use this information to provide our services and improve user experience.
    You have the right to access, modify, or delete your personal data.
    We store your data securely and do not share it with third parties without your consent.
    """
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/analyze/text",
            json={
                "policy_text": sample_policy,
                "policy_name": "Test Policy"
            },
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Text analysis endpoint working")
            print(f"   Analysis ID: {data.get('analysis_id')}")
            print(f"   Compliance Score: {data.get('compliance_score')}%")
            print(f"   Total Requirements: {data.get('total_requirements')}")
            return True
        else:
            print(f"❌ Text analysis failed with status: {response.status_code}")
            if response.headers.get('content-type') == 'application/json':
                error_data = response.json()
                print(f"   Error: {error_data.get('detail', 'Unknown error')}")
            return False
    except Exception as e:
        print(f"❌ Text analysis error: {e}")
        return False

def main():
    print("🔍 Testing GDPR Compliance API...")
    print("=" * 50)
    
    # Test endpoints
    health_ok = test_health_endpoint()
    print()
    
    requirements_ok = test_requirements_endpoint()
    print()
    
    if health_ok and requirements_ok:
        print("🧪 Testing text analysis (this may take a moment)...")
        analysis_ok = test_text_analysis()
        print()
        
        if analysis_ok:
            print("🎉 All tests passed! The API is working correctly.")
            print("\n📋 Next steps:")
            print("1. Start the frontend: npm run dev")
            print("2. Open http://localhost:5173 in your browser")
            print("3. Navigate to GDPR Analysis in the sidebar")
        else:
            print("⚠️  Basic endpoints work, but text analysis failed.")
            print("   Check your Cohere API key and GDPR PDF file.")
    else:
        print("❌ Basic API tests failed. Check if the server is running.")
        print("   Start the server with: python gdpr_compliance_fastapi.py")

if __name__ == "__main__":
    main()
