// Security types for comprehensive security monitoring and incident management

export interface SecurityIncident {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'open' | 'investigating' | 'contained' | 'resolved' | 'closed';
  category: 'malware' | 'phishing' | 'data_breach' | 'authentication' | 'network' | 'insider_threat' | 'other';
  source: string; // SIEM, EDR, Manual, etc.
  assignedTo: string;
  createdAt: Date;
  updatedAt: Date;
  resolvedAt?: Date;
  affectedSystems: string[];
  indicators: string[];
  timeline: Array<{
    timestamp: Date;
    action: string;
    user: string;
  }>;
  attachments?: string[];
  tags?: string[];
}

export interface ThreatDetection {
  id: string;
  name: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'active' | 'investigating' | 'mitigated' | 'false_positive';
  source: string;
  detectedAt: Date;
  indicators: string[];
  affectedAssets: string[];
  mitigationActions: string[];
  confidence?: number; // 0-100
  threatActor?: string;
  attackVector?: string;
}

export interface VulnerabilityAssessment {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  cvssScore: number;
  status: 'open' | 'in_progress' | 'resolved' | 'accepted_risk';
  discoveredAt: Date;
  affectedSystems: string[];
  remediation: string;
  dueDate: Date;
  resolvedAt?: Date;
  cve?: string;
  vendor?: string;
  product?: string;
  version?: string;
}

export interface SecurityMetrics {
  totalIncidents: number;
  openIncidents: number;
  resolvedIncidents: number;
  criticalIncidents: number;
  totalThreats: number;
  activeThreats: number;
  mitigatedThreats: number;
  totalVulnerabilities: number;
  criticalVulnerabilities: number;
  highVulnerabilities: number;
  averageResolutionTime: number; // in hours
  securityScore: number; // 0-100
  lastUpdated: Date;
}

export interface SecurityAlert {
  id: string;
  title: string;
  description: string;
  severity: 'info' | 'warning' | 'error' | 'critical';
  source: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
}

export interface SecurityControl {
  id: string;
  name: string;
  description: string;
  category: 'preventive' | 'detective' | 'corrective';
  status: 'active' | 'inactive' | 'testing';
  effectiveness: number; // 0-100
  lastTested: Date;
  nextTestDate: Date;
  owner: string;
  framework: string; // ISO 27001, NIST, etc.
}

export interface ComplianceFramework {
  id: string;
  name: string;
  description: string;
  version: string;
  requirements: ComplianceRequirement[];
  complianceScore: number;
  lastAssessment: Date;
  nextAssessment: Date;
}

export interface ComplianceRequirement {
  id: string;
  title: string;
  description: string;
  status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'not_applicable';
  evidence: string[];
  lastReview: Date;
  nextReview: Date;
  owner: string;
  controls: SecurityControl[];
}

// Site Reliability and SLO Types
export interface ServiceLevelObjective {
  id: string;
  name: string;
  description: string;
  service: string;
  sliType: 'availability' | 'latency' | 'throughput' | 'error_rate';
  target: number; // percentage or value
  timeWindow: '1h' | '24h' | '7d' | '30d';
  status: 'healthy' | 'warning' | 'critical';
  currentValue: number;
  errorBudget: {
    total: number;
    consumed: number;
    remaining: number;
    burnRate: number;
  };
  alerts: SLOAlert[];
  lastUpdated: Date;
}

export interface SLOAlert {
  id: string;
  sloId: string;
  type: 'budget_exhaustion' | 'burn_rate' | 'threshold_breach';
  severity: 'warning' | 'critical';
  message: string;
  triggeredAt: Date;
  resolvedAt?: Date;
  acknowledged: boolean;
}

export interface ServiceHealth {
  service: string;
  status: 'healthy' | 'degraded' | 'down';
  uptime: number; // percentage
  responseTime: number; // milliseconds
  errorRate: number; // percentage
  throughput: number; // requests per second
  lastIncident?: Date;
  dependencies: ServiceDependency[];
}

export interface ServiceDependency {
  service: string;
  type: 'database' | 'api' | 'cache' | 'queue' | 'external';
  status: 'healthy' | 'degraded' | 'down';
  impact: 'low' | 'medium' | 'high' | 'critical';
}

export interface IncidentReport {
  id: string;
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  status: 'investigating' | 'identified' | 'monitoring' | 'resolved';
  affectedServices: string[];
  startTime: Date;
  endTime?: Date;
  duration?: number; // minutes
  rootCause?: string;
  resolution?: string;
  postMortem?: string;
  assignedTo: string;
  timeline: Array<{
    timestamp: Date;
    update: string;
    user: string;
  }>;
}

export interface SystemMetrics {
  timestamp: Date;
  cpu: number; // percentage
  memory: number; // percentage
  disk: number; // percentage
  network: {
    inbound: number; // Mbps
    outbound: number; // Mbps
  };
  requests: number; // per second
  errors: number; // per second
  latency: {
    p50: number; // milliseconds
    p95: number; // milliseconds
    p99: number; // milliseconds
  };
}

export interface MonitoringDashboard {
  id: string;
  name: string;
  description: string;
  widgets: DashboardWidget[];
  refreshInterval: number; // seconds
  owner: string;
  shared: boolean;
  tags: string[];
}

export interface DashboardWidget {
  id: string;
  type: 'metric' | 'chart' | 'table' | 'alert' | 'slo';
  title: string;
  query: string;
  visualization: 'line' | 'bar' | 'pie' | 'gauge' | 'table';
  position: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  config: Record<string, any>;
}
