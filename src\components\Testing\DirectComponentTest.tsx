import React, { useState } from 'react';
import { ErrorBoundary } from '../ui/ErrorBoundary';

// Direct imports to test components without lazy loading
import { EnhancedComplianceMetrics } from '../compliance/EnhancedComplianceMetrics';
import { SecurityOverviewDashboard } from '../Security/SecurityOverviewDashboard';

interface ComponentTestResult {
  name: string;
  loaded: boolean;
  error: string | null;
}

export const DirectComponentTest: React.FC = () => {
  const [testResults, setTestResults] = useState<ComponentTestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    setIsRunning(true);
    setTestResults([]);

    const tests = [
      {
        name: 'Enhanced Compliance Metrics',
        component: EnhancedComplianceMetrics,
        test: () => {
          try {
            // Test if component can be instantiated
            const element = React.createElement(EnhancedComplianceMetrics, {});
            return { loaded: true, error: null };
          } catch (error) {
            return { loaded: false, error: (error as Error).message };
          }
        }
      },
      {
        name: 'Security Overview Dashboard',
        component: SecurityOverviewDashboard,
        test: () => {
          try {
            // Test if component can be instantiated
            const element = React.createElement(SecurityOverviewDashboard, {});
            return { loaded: true, error: null };
          } catch (error) {
            return { loaded: false, error: (error as Error).message };
          }
        }
      }
    ];

    const results: ComponentTestResult[] = [];

    for (const test of tests) {
      try {
        const result = test.test();
        results.push({
          name: test.name,
          loaded: result.loaded,
          error: result.error
        });
      } catch (error) {
        results.push({
          name: test.name,
          loaded: false,
          error: (error as Error).message
        });
      }
    }

    setTestResults(results);
    setIsRunning(false);
  };

  return (
    <div className="p-8 bg-background min-h-screen">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-text mb-4">Direct Component Import Test</h1>
          <p className="text-text-secondary mb-4">
            This test verifies that components can be imported and instantiated directly without lazy loading.
          </p>
          
          <button
            onClick={runTests}
            disabled={isRunning}
            className={`px-6 py-3 rounded-lg font-medium transition-colors ${
              isRunning 
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed' 
                : 'bg-primary text-white hover:bg-primary/90'
            }`}
          >
            {isRunning ? 'Running Tests...' : 'Run Import Tests'}
          </button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-text">Test Results</h2>
            
            {testResults.map((result) => (
              <div 
                key={result.name}
                className={`p-4 rounded-lg border ${
                  result.loaded 
                    ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800' 
                    : 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                }`}
              >
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-text">{result.name}</h3>
                  <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                    result.loaded 
                      ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100' 
                      : 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                  }`}>
                    {result.loaded ? 'PASS' : 'FAIL'}
                  </div>
                </div>
                
                {result.error && (
                  <div className="mt-2 p-3 bg-red-100 dark:bg-red-900/30 rounded border border-red-200 dark:border-red-800">
                    <p className="text-sm text-red-700 dark:text-red-300 font-mono">
                      Error: {result.error}
                    </p>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="mt-8 space-y-6">
          <h2 className="text-xl font-semibold text-text">Live Component Tests</h2>
          
          <div className="space-y-6">
            <div className="bg-surface rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4">Enhanced Compliance Metrics Test</h3>
              <ErrorBoundary
                type="component"
                fallbackTitle="Enhanced Compliance Metrics Failed"
                fallbackMessage="The Enhanced Compliance Metrics component failed to render."
                onError={(error, errorInfo) => {
                  console.error('Enhanced Compliance Metrics Error:', error, errorInfo);
                }}
              >
                <div className="max-h-96 overflow-hidden">
                  <EnhancedComplianceMetrics />
                </div>
              </ErrorBoundary>
            </div>

            <div className="bg-surface rounded-lg p-6 border border-border">
              <h3 className="text-lg font-semibold text-text mb-4">Security Overview Dashboard Test</h3>
              <ErrorBoundary
                type="component"
                fallbackTitle="Security Overview Dashboard Failed"
                fallbackMessage="The Security Overview Dashboard component failed to render."
                onError={(error, errorInfo) => {
                  console.error('Security Overview Dashboard Error:', error, errorInfo);
                }}
              >
                <div className="max-h-96 overflow-hidden">
                  <SecurityOverviewDashboard />
                </div>
              </ErrorBoundary>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DirectComponentTest;
