import React from 'react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { render } from '../../../test/utils/test-utils';
import { ConsentManagementDashboard } from '../ConsentManagementDashboard';
import { ConsentManagementService } from '../../../services/consentManagementService';

// Mock the service
vi.mock('../../../services/consentManagementService');

const mockConsentRecords = [
  {
    id: '1',
    userId: 'user-1',
    email: '<EMAIL>',
    consentGiven: true,
    consentDate: new Date().toISOString(),
    categories: ['marketing', 'analytics'],
    consentMethod: 'website',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0...',
    withdrawalDate: null,
    lastUpdated: new Date().toISOString()
  },
  {
    id: '2',
    userId: 'user-2',
    email: '<EMAIL>',
    consentGiven: false,
    consentDate: new Date().toISOString(),
    categories: ['marketing'],
    consentMethod: 'email',
    ipAddress: '***********',
    userAgent: 'Mozilla/5.0...',
    withdrawalDate: new Date().toISOString(),
    lastUpdated: new Date().toISOString()
  }
];

const mockConsentCategories = [
  {
    id: 'marketing',
    name: 'Marketing Communications',
    description: 'Receive promotional emails and offers',
    required: false,
    consentRate: 75,
    totalUsers: 1000,
    consentedUsers: 750
  },
  {
    id: 'analytics',
    name: 'Analytics & Performance',
    description: 'Help us improve our services',
    required: false,
    consentRate: 90,
    totalUsers: 1000,
    consentedUsers: 900
  }
];

const mockMetrics = {
  totalUsers: 1000,
  consentedUsers: 850,
  consentRate: 85,
  withdrawalRate: 5,
  categoryBreakdown: [
    { category: 'Marketing', consentRate: 75 },
    { category: 'Analytics', consentRate: 90 }
  ],
  consentTrends: {
    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
    datasets: [{
      label: 'Consent Rate',
      data: [80, 82, 85, 87, 85],
      borderColor: '#3B82F6',
      backgroundColor: 'rgba(59, 130, 246, 0.1)'
    }]
  }
};

describe('ConsentManagementDashboard', () => {
  const user = userEvent.setup();

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup service mocks
    vi.mocked(ConsentManagementService.getAllConsentRecords).mockResolvedValue(mockConsentRecords);
    vi.mocked(ConsentManagementService.getConsentCategories).mockResolvedValue(mockConsentCategories);
    vi.mocked(ConsentManagementService.getMetrics).mockResolvedValue(mockMetrics);
    vi.mocked(ConsentManagementService.updateConsentRecord).mockResolvedValue(mockConsentRecords[0]);
    vi.mocked(ConsentManagementService.deleteConsentRecord).mockResolvedValue(undefined);
  });

  describe('Component Rendering', () => {
    it('renders the dashboard with header and navigation', async () => {
      render(<ConsentManagementDashboard />);
      
      expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      expect(screen.getByText('Manage user consents and privacy preferences')).toBeInTheDocument();
      
      // Check navigation tabs
      expect(screen.getByText('Overview')).toBeInTheDocument();
      expect(screen.getByText('Records')).toBeInTheDocument();
      expect(screen.getByText('Categories')).toBeInTheDocument();
      expect(screen.getByText('Analytics')).toBeInTheDocument();
    });

    it('displays metrics cards with correct data', async () => {
      render(<ConsentManagementDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('1,000')).toBeInTheDocument(); // Total users
        expect(screen.getByText('850')).toBeInTheDocument(); // Consented users
        expect(screen.getByText('85%')).toBeInTheDocument(); // Consent rate
        expect(screen.getByText('5%')).toBeInTheDocument(); // Withdrawal rate
      });
    });

    it('shows consent trend chart', async () => {
      render(<ConsentManagementDashboard />);
      
      await waitFor(() => {
        expect(screen.getByTestId('line-chart')).toBeInTheDocument();
      });
    });

    it('displays category breakdown', async () => {
      render(<ConsentManagementDashboard />);
      
      await waitFor(() => {
        expect(screen.getByText('Marketing')).toBeInTheDocument();
        expect(screen.getByText('75%')).toBeInTheDocument();
        expect(screen.getByText('Analytics')).toBeInTheDocument();
        expect(screen.getByText('90%')).toBeInTheDocument();
      });
    });
  });

  describe('Tab Navigation', () => {
    it('switches to Records tab and displays consent records', async () => {
      render(<ConsentManagementDashboard />);
      
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('switches to Categories tab and displays consent categories', async () => {
      render(<ConsentManagementDashboard />);
      
      const categoriesTab = screen.getByText('Categories');
      await user.click(categoriesTab);
      
      await waitFor(() => {
        expect(screen.getByText('Marketing Communications')).toBeInTheDocument();
        expect(screen.getByText('Analytics & Performance')).toBeInTheDocument();
      });
    });

    it('switches to Analytics tab and displays analytics', async () => {
      render(<ConsentManagementDashboard />);
      
      const analyticsTab = screen.getByText('Analytics');
      await user.click(analyticsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Consent Analytics')).toBeInTheDocument();
      });
    });
  });

  describe('Consent Records Management', () => {
    beforeEach(async () => {
      render(<ConsentManagementDashboard />);
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('displays consent records in table format', async () => {
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      
      // Check consent status indicators
      expect(screen.getByText('Consented')).toBeInTheDocument();
      expect(screen.getByText('Withdrawn')).toBeInTheDocument();
    });

    it('shows consent categories for each record', async () => {
      expect(screen.getByText('marketing, analytics')).toBeInTheDocument();
      expect(screen.getByText('marketing')).toBeInTheDocument();
    });

    it('displays consent methods', async () => {
      expect(screen.getByText('website')).toBeInTheDocument();
      expect(screen.getByText('email')).toBeInTheDocument();
    });

    it('opens edit modal when edit button is clicked', async () => {
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      expect(screen.getByText('Edit Consent Record')).toBeInTheDocument();
    });

    it('shows delete confirmation when delete button is clicked', async () => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      expect(screen.getByText(/are you sure you want to delete/i)).toBeInTheDocument();
    });

    it('updates consent record successfully', async () => {
      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      // Toggle consent status
      const consentToggle = screen.getByRole('checkbox', { name: /consent given/i });
      await user.click(consentToggle);
      
      const saveButton = screen.getByRole('button', { name: /update/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(ConsentManagementService.updateConsentRecord).toHaveBeenCalled();
      });
    });

    it('deletes consent record after confirmation', async () => {
      const deleteButtons = screen.getAllByRole('button', { name: /delete/i });
      await user.click(deleteButtons[0]);
      
      const confirmButton = screen.getByRole('button', { name: /confirm/i });
      await user.click(confirmButton);
      
      await waitFor(() => {
        expect(ConsentManagementService.deleteConsentRecord).toHaveBeenCalledWith('1');
      });
    });
  });

  describe('Search and Filtering', () => {
    beforeEach(async () => {
      render(<ConsentManagementDashboard />);
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('filters records by search term', async () => {
      const searchInput = screen.getByPlaceholderText(/search records/i);
      await user.type(searchInput, '<EMAIL>');
      
      // Should filter results
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('filters by consent status', async () => {
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      const consentedFilter = screen.getByLabelText(/consented/i);
      await user.click(consentedFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      // Should show only consented users
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });

    it('filters by consent method', async () => {
      const filterButton = screen.getByRole('button', { name: /filter/i });
      await user.click(filterButton);
      
      const websiteFilter = screen.getByLabelText(/website/i);
      await user.click(websiteFilter);
      
      const applyButton = screen.getByRole('button', { name: /apply/i });
      await user.click(applyButton);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });
    });
  });

  describe('Consent Categories Management', () => {
    beforeEach(async () => {
      render(<ConsentManagementDashboard />);
      const categoriesTab = screen.getByText('Categories');
      await user.click(categoriesTab);
      
      await waitFor(() => {
        expect(screen.getByText('Marketing Communications')).toBeInTheDocument();
      });
    });

    it('displays consent categories with statistics', async () => {
      expect(screen.getByText('Marketing Communications')).toBeInTheDocument();
      expect(screen.getByText('Analytics & Performance')).toBeInTheDocument();
      
      // Check statistics
      expect(screen.getByText('750 / 1,000')).toBeInTheDocument(); // Marketing stats
      expect(screen.getByText('900 / 1,000')).toBeInTheDocument(); // Analytics stats
    });

    it('shows category descriptions', async () => {
      expect(screen.getByText('Receive promotional emails and offers')).toBeInTheDocument();
      expect(screen.getByText('Help us improve our services')).toBeInTheDocument();
    });

    it('displays consent rate progress bars', async () => {
      const progressBars = screen.getAllByRole('progressbar');
      expect(progressBars).toHaveLength(2);
      
      // Check progress values
      expect(progressBars[0]).toHaveAttribute('aria-valuenow', '75');
      expect(progressBars[1]).toHaveAttribute('aria-valuenow', '90');
    });
  });

  describe('Analytics Section', () => {
    beforeEach(async () => {
      render(<ConsentManagementDashboard />);
      const analyticsTab = screen.getByText('Analytics');
      await user.click(analyticsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Consent Analytics')).toBeInTheDocument();
      });
    });

    it('displays consent trend charts', async () => {
      expect(screen.getByTestId('line-chart')).toBeInTheDocument();
    });

    it('shows category breakdown charts', async () => {
      expect(screen.getByTestId('doughnut-chart')).toBeInTheDocument();
    });

    it('displays key metrics summary', async () => {
      expect(screen.getByText('Overall Consent Rate')).toBeInTheDocument();
      expect(screen.getByText('Withdrawal Rate')).toBeInTheDocument();
      expect(screen.getByText('Active Users')).toBeInTheDocument();
    });
  });

  describe('Error Handling', () => {
    it('handles service errors gracefully', async () => {
      vi.mocked(ConsentManagementService.getAllConsentRecords).mockRejectedValue(new Error('Service error'));
      
      render(<ConsentManagementDashboard />);
      
      // Should still render without crashing
      expect(screen.getByText('Consent Management Center')).toBeInTheDocument();
      
      // Should show error message
      await waitFor(() => {
        expect(screen.getByText(/failed to load/i)).toBeInTheDocument();
      });
    });

    it('handles update errors', async () => {
      vi.mocked(ConsentManagementService.updateConsentRecord).mockRejectedValue(new Error('Update failed'));
      
      render(<ConsentManagementDashboard />);
      
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      const editButtons = screen.getAllByRole('button', { name: /edit/i });
      await user.click(editButtons[0]);
      
      const saveButton = screen.getByRole('button', { name: /update/i });
      await user.click(saveButton);
      
      await waitFor(() => {
        expect(screen.getByText(/failed to update/i)).toBeInTheDocument();
      });
    });
  });

  describe('Data Export', () => {
    it('exports consent records to CSV', async () => {
      render(<ConsentManagementDashboard />);
      
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByText('<EMAIL>')).toBeInTheDocument();
      });

      const exportButton = screen.getByRole('button', { name: /export/i });
      await user.click(exportButton);
      
      // Should trigger export functionality
      expect(exportButton).toBeInTheDocument();
    });

    it('exports analytics data', async () => {
      render(<ConsentManagementDashboard />);
      
      const analyticsTab = screen.getByText('Analytics');
      await user.click(analyticsTab);
      
      await waitFor(() => {
        expect(screen.getByText('Consent Analytics')).toBeInTheDocument();
      });

      const exportButton = screen.getByRole('button', { name: /export analytics/i });
      await user.click(exportButton);
      
      expect(exportButton).toBeInTheDocument();
    });
  });

  describe('Accessibility', () => {
    it('has proper table structure for records', async () => {
      render(<ConsentManagementDashboard />);
      
      const recordsTab = screen.getByText('Records');
      await user.click(recordsTab);
      
      await waitFor(() => {
        expect(screen.getByRole('table')).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: /email/i })).toBeInTheDocument();
        expect(screen.getByRole('columnheader', { name: /status/i })).toBeInTheDocument();
      });
    });

    it('supports keyboard navigation', async () => {
      render(<ConsentManagementDashboard />);
      
      const overviewTab = screen.getByText('Overview');
      overviewTab.focus();
      expect(document.activeElement).toBe(overviewTab);
      
      await user.tab();
      const recordsTab = screen.getByText('Records');
      expect(document.activeElement).toBe(recordsTab);
    });

    it('has proper ARIA labels for interactive elements', async () => {
      render(<ConsentManagementDashboard />);
      
      const refreshButton = screen.getByRole('button', { name: /refresh/i });
      expect(refreshButton).toHaveAttribute('aria-label');
    });
  });
});
