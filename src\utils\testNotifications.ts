/**
 * Test Notification System
 * Utility to test and demonstrate the notification system functionality
 */

import { notificationService } from '../services/notificationService';

export const triggerTestNotifications = () => {
  console.log('🔔 Triggering test notifications...');

  // Simulate a new GDPR request
  setTimeout(() => {
    notificationService.triggerNotification('gdpr_request_created', {
      requestId: 'GDPR-2024-001',
      email: '<EMAIL>',
      type: 'data_access',
      urgency: 'high'
    });
    console.log('✅ GDPR request notification triggered');
  }, 2000);

  // Simulate a security violation
  setTimeout(() => {
    notificationService.triggerNotification('security_violation', {
      severity: 'critical',
      source: 'Firewall',
      details: 'Multiple failed login attempts detected',
      ipAddress: '*************'
    });
    console.log('✅ Security violation notification triggered');
  }, 4000);

  // Simulate a compliance violation
  setTimeout(() => {
    notificationService.triggerNotification('compliance_violation', {
      policy: 'Data Retention Policy',
      violation: 'Records exceeding retention period',
      count: 15,
      department: 'Customer Service'
    });
    console.log('✅ Compliance violation notification triggered');
  }, 6000);

  // Simulate a risk assessment due
  setTimeout(() => {
    notificationService.triggerNotification('risk_assessment_due', {
      assessmentId: 'RISK-2024-Q1',
      daysUntilDue: 7,
      assignedTo: 'Risk Management Team',
      priority: 'medium'
    });
    console.log('✅ Risk assessment due notification triggered');
  }, 8000);

  // Simulate a policy update
  setTimeout(() => {
    notificationService.triggerNotification('policy_updated', {
      policyName: 'Privacy Policy',
      version: '2.1',
      changes: 'Updated data retention periods',
      effectiveDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000)
    });
    console.log('✅ Policy update notification triggered');
  }, 10000);

  console.log('🎯 All test notifications scheduled');
};

export const demonstrateNotificationSystem = () => {
  console.log('📊 Notification System Demonstration');
  console.log('=====================================');

  // Show current users
  const users = notificationService.getUsers();
  console.log(`👥 Total Users: ${users.length}`);
  users.forEach(user => {
    console.log(`   - ${user.name} (${user.role}) - ${user.department}`);
  });

  // Show notification rules
  const rules = notificationService.getRules();
  console.log(`📋 Notification Rules: ${rules.length}`);
  rules.forEach(rule => {
    console.log(`   - ${rule.name}: ${rule.description}`);
  });

  // Show current notifications for admin user
  const adminNotifications = notificationService.getNotifications('user-1');
  console.log(`🔔 Current Notifications for Admin: ${adminNotifications.length}`);
  adminNotifications.slice(0, 3).forEach(notif => {
    console.log(`   - ${notif.title} (${notif.severity})`);
  });

  // Show unread count
  const unreadCount = notificationService.getUnreadCount('user-1');
  console.log(`📬 Unread Notifications for Admin: ${unreadCount}`);

  console.log('=====================================');
  console.log('✅ Notification system is fully operational!');
};

// Auto-run demonstration when imported
if (typeof window !== 'undefined') {
  // Only run in browser environment
  setTimeout(() => {
    demonstrateNotificationSystem();
    triggerTestNotifications();
  }, 1000);
}
